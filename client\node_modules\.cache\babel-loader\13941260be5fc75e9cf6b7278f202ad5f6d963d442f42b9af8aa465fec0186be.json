{"ast": null, "code": "import{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useLocation}from'react-router-dom';import{Container,Typography,Box,Fade,Card,CardContent,Divider,List,ListItem,ListItemText,ListItemIcon,useTheme,alpha,Tabs,Tab,Stack}from'@mui/material';import{Info as InfoIcon,Email as EmailIcon,CheckCircle as CheckCircleIcon,Cancel as CancelIcon,Payment as PaymentIcon,Security as SecurityIcon,CurrencyExchange as CurrencyExchangeIcon,Assignment as AssignmentIcon,AccountBalance as AccountBalanceIcon}from'@mui/icons-material';import Layout from'../components/Layout';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PlatformPolicy=()=>{const{t,i18n}=useTranslation();const theme=useTheme();const location=useLocation();const isRtl=i18n.language==='ar';const[activeTab,setActiveTab]=useState(0);// Handle hash navigation\nuseEffect(()=>{const hash=location.hash.replace('#','');switch(hash){case'privacy':setActiveTab(0);break;case'terms':setActiveTab(1);break;case'refund':setActiveTab(2);break;case'payment':setActiveTab(3);break;case'booking':setActiveTab(4);break;case'commitment':setActiveTab(5);break;case'commission':setActiveTab(6);break;default:setActiveTab(0);}},[location.hash]);const handleTabChange=(_,newValue)=>{setActiveTab(newValue);};const SectionCard=_ref=>{let{children,bg}=_ref;return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{mb:4,borderRadius:3,...(isRtl?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:bg||alpha(theme.palette.primary.main,0.02)},children:/*#__PURE__*/_jsx(CardContent,{children:children})});};/**\n   * Safely fetch a translation; returns empty string if key is missing.\n   */const safeT=key=>{const val=t(key);return val&&!val.includes(key)?val:'';};// Privacy Policy Section\nconst renderPrivacyPolicy=()=>{const renderStandardSection=num=>{const subtitle=safeT(`privacy.section${num}.subtitle`);const content=safeT(`privacy.section${num}.content`);// collect bullet items (item1-item10)\nconst bullets=[];for(let i=1;i<=10;i++){const item=safeT(`privacy.section${num}.item${i}`);if(item)bullets.push(item);}return/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t(`privacy.section${num}.title`)}),subtitle&&/*#__PURE__*/_jsx(Typography,{mb:1,children:subtitle}),content&&/*#__PURE__*/_jsx(Typography,{mb:1,children:content}),bullets.length>0&&/*#__PURE__*/_jsx(List,{children:bullets.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{textAlign:isRtl?'right':'left'}})]},idx))})]},num);};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacy.title')}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:t('privacy.intro')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),[1,2,3,4,5,6,7,8,9].map(num=>renderStandardSection(num)),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacy.section10.title')}),/*#__PURE__*/_jsx(Typography,{mb:2,children:t('privacy.section10.content')}),/*#__PURE__*/_jsxs(Box,{sx:{p:2,bgcolor:alpha(theme.palette.info.main,0.1),borderRadius:2,display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(EmailIcon,{color:\"info\"}),/*#__PURE__*/_jsx(Typography,{fontWeight:\"bold\",color:theme.palette.info.main,children:t('privacy.section10.email')})]})]})]});};// Terms and Conditions Section\nconst renderTermsAndConditions=()=>{return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('termsConditions.title')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsx(Stack,{spacing:4,children:t('termsConditions.content').split(/\\n\\s*\\n/).filter(section=>section.trim()!=='').map((section,idx)=>{const lines=section.split('\\n');const title=lines[0];const body=lines.slice(1).join('\\n');return/*#__PURE__*/_jsx(Card,{elevation:3,sx:{borderRadius:3,...(isRtl?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:idx%2===0?alpha(theme.palette.primary.main,0.02):alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{mb:1.5,fontWeight:700,fontFamily:isRtl?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:title}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{whiteSpace:'pre-line',lineHeight:2,fontSize:'1.05rem',fontFamily:isRtl?'Tajawal, sans-serif':'inherit',color:theme.palette.text.primary},children:body})]})},idx);})})]});};// Refund Policy Section\nconst renderRefundPolicy=()=>{const refund=t('policies.refund',{returnObjects:true});return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:refund.title}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:refund.section1.title}),/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:refund.section1.description}),/*#__PURE__*/_jsx(List,{children:refund.section1.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'}})]},idx))})]}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:refund.section2.title}),/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:refund.section2.description}),/*#__PURE__*/_jsx(List,{children:refund.section2.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(CancelIcon,{color:\"error\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'}})]},idx))})]}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:refund.section3.title}),/*#__PURE__*/_jsx(Typography,{sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:refund.section3.description})]}),/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:refund.contact.title}),/*#__PURE__*/_jsxs(Box,{sx:{p:2,bgcolor:alpha(theme.palette.info.main,0.1),borderRadius:2,display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(EmailIcon,{color:\"info\"}),/*#__PURE__*/_jsx(Typography,{fontWeight:\"bold\",color:theme.palette.info.main,children:refund.contact.email})]})]})]});};// Booking Payment Policy Section\nconst renderBookingPaymentPolicy=()=>{const policy=t('policies.bookingPayment',{returnObjects:true});const renderSection=section=>/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:section.title}),section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item})]},idx))}),section.description&&/*#__PURE__*/_jsx(Typography,{children:section.description})]});return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),renderSection(policy.section1),renderSection(policy.section2),renderSection(policy.section3),renderSection(policy.section4),renderSection(policy.section5),renderSection(policy.section6),renderSection(policy.section7),renderSection(policy.section8),/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:1,children:policy.section8.title||policy.section8.description}),/*#__PURE__*/_jsx(Typography,{mb:1,children:policy.section8.description}),/*#__PURE__*/_jsxs(Box,{sx:{p:2,bgcolor:alpha(theme.palette.info.main,0.1),borderRadius:2,display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(EmailIcon,{color:\"info\"}),/*#__PURE__*/_jsx(Typography,{fontWeight:\"bold\",color:theme.palette.info.main,children:policy.section8.email})]})]})]});};// Booking Cancellation Policy Section\nconst renderBookingCancellationPolicy=()=>{var _policy$summary;const policy=t('policies.bookingCancellation',{returnObjects:true});const renderSubSection=subSection=>/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:subSection.title}),subSection.points&&/*#__PURE__*/_jsx(List,{children:subSection.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item})]},idx))})]});return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:policy.studentPolicy.title}),renderSubSection(policy.studentPolicy.booking),renderSubSection(policy.studentPolicy.cancellation),renderSubSection(policy.studentPolicy.rescheduling),renderSubSection(policy.studentPolicy.lateArrival)]}),/*#__PURE__*/_jsxs(SectionCard,{bg:alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02),children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:policy.tutorPolicy.title}),renderSubSection(policy.tutorPolicy.availability),renderSubSection(policy.tutorPolicy.cancellation),renderSubSection(policy.tutorPolicy.rescheduling),renderSubSection(policy.tutorPolicy.lateArrival)]}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:policy.generalNotes.title}),/*#__PURE__*/_jsx(List,{children:policy.generalNotes.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"info\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item})]},idx))})]}),/*#__PURE__*/_jsxs(Box,{sx:{mt:3,p:2,bgcolor:alpha(theme.palette.info.main,0.1),borderRadius:2,display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(InfoIcon,{color:\"info\"}),/*#__PURE__*/_jsx(Typography,{color:theme.palette.info.main,children:((_policy$summary=policy.summary)===null||_policy$summary===void 0?void 0:_policy$summary.importantNote)||(isRtl?'يرجى مراجعة جميع السياسات بعناية قبل الحجز':'Please review all policies carefully before booking')})]})]});};// Teacher Commitment Section\nconst renderTeacherCommitment=()=>{return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('teacher.commitmentTitle')}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:t('teacher.commitmentDescription')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:3,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:isRtl?'بنود التعهد':'Commitment Terms'}),/*#__PURE__*/_jsx(List,{children:[1,2,3,4,5,6].map(num=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left',alignItems:'flex-start',mb:2},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0,mt:0.5},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t(`teacher.commitmentText.point${num}`),sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif','& .MuiListItemText-primary':{fontSize:'1rem',lineHeight:1.6}}})]},num))})]}),/*#__PURE__*/_jsx(SectionCard,{bg:alpha(theme.palette.warning.main,0.05),children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'flex-start',gap:2},children:[/*#__PURE__*/_jsx(InfoIcon,{sx:{color:theme.palette.warning.main,mt:0.5}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,color:theme.palette.warning.main,mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:isRtl?'إقرار مهم':'Important Acknowledgment'}),/*#__PURE__*/_jsx(Typography,{sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif',lineHeight:1.6},children:t('teacher.commitmentText.conclusion')})]})]})})]});};// Commission Policy Section\nconst renderCommissionPolicy=()=>{return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.commissionPolicy.title')}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:t('privacyPolicy.commissionPolicy.description')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:3,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:isRtl?'معدلات العمولة':'Commission Rates'}),/*#__PURE__*/_jsx(List,{children:[3,4,5,6,7].map(rate=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left',mb:1},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(CurrencyExchangeIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t(`privacyPolicy.commissionPolicy.rates.${rate}`),sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif','& .MuiListItemText-primary':{fontSize:'1rem',fontWeight:500}}})]},rate))})]}),/*#__PURE__*/_jsx(SectionCard,{bg:alpha(theme.palette.info.main,0.05),children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'flex-start',gap:2},children:[/*#__PURE__*/_jsx(InfoIcon,{sx:{color:theme.palette.info.main,mt:0.5}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,color:theme.palette.info.main,mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:isRtl?'ملاحظة مهمة':'Important Note'}),/*#__PURE__*/_jsx(Typography,{sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif',lineHeight:1.6,fontStyle:'italic'},children:t('privacyPolicy.commissionPolicy.explanation')})]})]})})]});};const tabs=[{label:isRtl?'سياسة الخصوصية':'Privacy Policy',icon:/*#__PURE__*/_jsx(SecurityIcon,{})},{label:isRtl?'الشروط والأحكام':'Terms & Conditions',icon:/*#__PURE__*/_jsx(InfoIcon,{})},{label:isRtl?'سياسة الاسترداد':'Refund Policy',icon:/*#__PURE__*/_jsx(CurrencyExchangeIcon,{})},{label:isRtl?'سياسة الحجز والدفع':'Booking & Payment Policy',icon:/*#__PURE__*/_jsx(PaymentIcon,{})},{label:isRtl?'سياسة الحجز والإلغاء':'Booking & Cancellation Policy',icon:/*#__PURE__*/_jsx(CancelIcon,{})},{label:isRtl?'تعهد المعلمين':'Teacher Commitment',icon:/*#__PURE__*/_jsx(AssignmentIcon,{})},{label:isRtl?'سياسة العمولة':'Commission Policy',icon:/*#__PURE__*/_jsx(AccountBalanceIcon,{})}];const renderTabContent=()=>{switch(activeTab){case 0:return renderPrivacyPolicy();case 1:return renderTermsAndConditions();case 2:return renderRefundPolicy();case 3:return renderBookingPaymentPolicy();case 4:return renderBookingCancellationPolicy();case 5:return renderTeacherCommitment();case 6:return renderCommissionPolicy();default:return renderPrivacyPolicy();}};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,pt:4,pb:8},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:800,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,md:4},direction:isRtl?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",align:\"center\",sx:{mb:4,fontWeight:800,fontFamily:isRtl?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:isRtl?'سياسات المنصة':'Platform Policies'}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider',mb:4},children:/*#__PURE__*/_jsx(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{'& .MuiTab-root':{fontFamily:isRtl?'Tajawal, sans-serif':'inherit',fontSize:{xs:'0.8rem',sm:'0.9rem',md:'1rem'},minWidth:{xs:120,sm:140,md:160}}},children:tabs.map((tab,index)=>/*#__PURE__*/_jsx(Tab,{label:tab.label,icon:tab.icon,iconPosition:\"start\",sx:{'& .MuiTab-iconWrapper':{mr:isRtl?0:1,ml:isRtl?1:0}}},index))})}),/*#__PURE__*/_jsx(Box,{sx:{mt:3},children:renderTabContent()})]})})})})});};export default PlatformPolicy;", "map": {"version": 3, "names": ["useState", "useEffect", "useTranslation", "useLocation", "Container", "Typography", "Box", "Fade", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "useTheme", "alpha", "Tabs", "Tab", "<PERSON><PERSON>", "Info", "InfoIcon", "Email", "EmailIcon", "CheckCircle", "CheckCircleIcon", "Cancel", "CancelIcon", "Payment", "PaymentIcon", "Security", "SecurityIcon", "CurrencyExchange", "CurrencyExchangeIcon", "Assignment", "AssignmentIcon", "AccountBalance", "AccountBalanceIcon", "Layout", "jsx", "_jsx", "jsxs", "_jsxs", "PlatformPolicy", "t", "i18n", "theme", "location", "isRtl", "language", "activeTab", "setActiveTab", "hash", "replace", "handleTabChange", "_", "newValue", "SectionCard", "_ref", "children", "bg", "elevation", "sx", "mb", "borderRadius", "borderRight", "palette", "primary", "main", "borderLeft", "backgroundColor", "safeT", "key", "val", "includes", "renderPrivacyPolicy", "renderStandardSection", "num", "subtitle", "content", "bullets", "i", "item", "push", "variant", "fontWeight", "color", "fontFamily", "length", "map", "idx", "pl", "pr", "flexDirection", "textAlign", "min<PERSON><PERSON><PERSON>", "mx", "ml", "p", "bgcolor", "info", "display", "alignItems", "gap", "renderTermsAndConditions", "spacing", "split", "filter", "section", "trim", "lines", "title", "body", "slice", "join", "secondary", "light", "whiteSpace", "lineHeight", "fontSize", "text", "renderRefundPolicy", "refund", "returnObjects", "section1", "description", "items", "section2", "section3", "mt", "contact", "email", "renderBookingPaymentPolicy", "policy", "renderSection", "points", "section4", "section5", "section6", "section7", "section8", "renderBookingCancellationPolicy", "_policy$summary", "renderSubSection", "subSection", "studentPolicy", "booking", "cancellation", "rescheduling", "lateArrival", "tutorPolicy", "availability", "generalNotes", "summary", "importantNote", "renderTeacherCommitment", "warning", "renderCommissionPolicy", "rate", "fontStyle", "tabs", "label", "icon", "renderTabContent", "minHeight", "background", "pt", "pb", "max<PERSON><PERSON><PERSON>", "in", "timeout", "xs", "md", "direction", "align", "borderBottom", "borderColor", "value", "onChange", "scrollButtons", "sm", "tab", "index", "iconPosition", "mr"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PlatformPolicy.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useLocation } from 'react-router-dom';\nimport {\n  Container,\n  Typography,\n  Box,\n  Fade,\n  Card,\n  CardContent,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  useTheme,\n  alpha,\n  Tabs,\n  Tab,\n  Stack,\n} from '@mui/material';\nimport {\n  Info as InfoIcon,\n  Email as EmailIcon,\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  Payment as PaymentIcon,\n  Security as SecurityIcon,\n  CurrencyExchange as CurrencyExchangeIcon,\n  Assignment as AssignmentIcon,\n  AccountBalance as AccountBalanceIcon,\n} from '@mui/icons-material';\nimport Layout from '../components/Layout';\n\nconst PlatformPolicy = () => {\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const location = useLocation();\n  const isRtl = i18n.language === 'ar';\n  const [activeTab, setActiveTab] = useState(0);\n\n  // Handle hash navigation\n  useEffect(() => {\n    const hash = location.hash.replace('#', '');\n    switch (hash) {\n      case 'privacy':\n        setActiveTab(0);\n        break;\n      case 'terms':\n        setActiveTab(1);\n        break;\n      case 'refund':\n        setActiveTab(2);\n        break;\n      case 'payment':\n        setActiveTab(3);\n        break;\n      case 'booking':\n        setActiveTab(4);\n        break;\n      case 'commitment':\n        setActiveTab(5);\n        break;\n      case 'commission':\n        setActiveTab(6);\n        break;\n      default:\n        setActiveTab(0);\n    }\n  }, [location.hash]);\n\n  const handleTabChange = (_, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const SectionCard = ({ children, bg }) => (\n    <Card\n      elevation={2}\n      sx={{\n        mb: 4,\n        borderRadius: 3,\n        ...(isRtl\n          ? { borderRight: `6px solid ${theme.palette.primary.main}` }\n          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\n        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),\n      }}\n    >\n      <CardContent>{children}</CardContent>\n    </Card>\n  );\n\n  /**\n   * Safely fetch a translation; returns empty string if key is missing.\n   */\n  const safeT = (key) => {\n    const val = t(key);\n    return val && !val.includes(key) ? val : '';\n  };\n\n  // Privacy Policy Section\n  const renderPrivacyPolicy = () => {\n    const renderStandardSection = (num) => {\n      const subtitle = safeT(`privacy.section${num}.subtitle`);\n      const content = safeT(`privacy.section${num}.content`);\n\n      // collect bullet items (item1-item10)\n      const bullets = [];\n      for (let i = 1; i <= 10; i++) {\n        const item = safeT(`privacy.section${num}.item${i}`);\n        if (item) bullets.push(item);\n      }\n\n      return (\n        <SectionCard key={num}>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {t(`privacy.section${num}.title`)}\n          </Typography>\n          {subtitle && <Typography mb={1}>{subtitle}</Typography>}\n          {content && <Typography mb={1}>{content}</Typography>}\n          {bullets.length > 0 && (\n            <List>\n              {bullets.map((item, idx) => (\n                <ListItem\n                  key={idx}\n                  sx={{\n                    pl: isRtl ? 0 : 2,\n                    pr: isRtl ? 2 : 0,\n                    flexDirection: 'row',\n                    textAlign: isRtl ? 'right' : 'left',\n                  }}\n                >\n                  <ListItemIcon\n                    sx={{\n                      minWidth: 'auto',\n                      mx: isRtl ? 0 : 1,\n                      ml: isRtl ? 1 : 0,\n                    }}\n                  >\n                    <InfoIcon color=\"primary\" />\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item}\n                    sx={{ textAlign: isRtl ? 'right' : 'left' }}\n                  />\n                </ListItem>\n              ))}\n            </List>\n          )}\n        </SectionCard>\n      );\n    };\n\n    return (\n      <Box>\n        <Typography\n          variant=\"h4\"\n          fontWeight={700}\n          mb={2}\n          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n        >\n          {t('privacy.title')}\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\n          {t('privacy.intro')}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => renderStandardSection(num))}\n\n        {/* Section 10 – Contact */}\n        <SectionCard>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {t('privacy.section10.title')}\n          </Typography>\n          <Typography mb={2}>{t('privacy.section10.content')}</Typography>\n          <Box sx={{\n            p: 2,\n            bgcolor: alpha(theme.palette.info.main, 0.1),\n            borderRadius: 2,\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          }}>\n            <EmailIcon color=\"info\" />\n            <Typography fontWeight=\"bold\" color={theme.palette.info.main}>\n              {t('privacy.section10.email')}\n            </Typography>\n          </Box>\n        </SectionCard>\n      </Box>\n    );\n  };\n\n  // Terms and Conditions Section\n  const renderTermsAndConditions = () => {\n    return (\n      <Box>\n        <Typography\n          variant=\"h4\"\n          fontWeight={700}\n          mb={2}\n          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n        >\n          {t('termsConditions.title')}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n        <Stack spacing={4}>\n          {t('termsConditions.content')\n            .split(/\\n\\s*\\n/)\n            .filter((section) => section.trim() !== '')\n            .map((section, idx) => {\n              const lines = section.split('\\n');\n              const title = lines[0];\n              const body = lines.slice(1).join('\\n');\n              return (\n                <Card\n                  key={idx}\n                  elevation={3}\n                  sx={{\n                    borderRadius: 3,\n                    ...(isRtl\n                      ? { borderRight: `6px solid ${theme.palette.primary.main}` }\n                      : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\n                    backgroundColor:\n                      idx % 2 === 0\n                        ? alpha(theme.palette.primary.main, 0.02)\n                        : alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02),\n                  }}\n                >\n                  <CardContent>\n                    <Typography\n                      variant=\"h6\"\n                      sx={{\n                        mb: 1.5,\n                        fontWeight: 700,\n                        fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                        color: theme.palette.primary.main,\n                      }}\n                    >\n                      {title}\n                    </Typography>\n                    <Typography\n                      variant=\"body1\"\n                      sx={{\n                        whiteSpace: 'pre-line',\n                        lineHeight: 2,\n                        fontSize: '1.05rem',\n                        fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                        color: theme.palette.text.primary,\n                      }}\n                    >\n                      {body}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              );\n            })}\n        </Stack>\n      </Box>\n    );\n  };\n\n  // Refund Policy Section\n  const renderRefundPolicy = () => {\n    const refund = t('policies.refund', { returnObjects: true });\n\n    return (\n      <Box>\n        <Typography variant=\"h4\" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n          {refund.title}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Section 1 */}\n        <SectionCard>\n          <Typography variant=\"h5\" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {refund.section1.title}\n          </Typography>\n          <Typography mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {refund.section1.description}\n          </Typography>\n          <List>\n            {refund.section1.items.map((item, idx) => (\n              <ListItem key={idx} sx={{\n                pl: isRtl ? 0 : 2,\n                pr: isRtl ? 2 : 0,\n                flexDirection: 'row',\n                textAlign: isRtl ? 'right' : 'left',\n              }}>\n                <ListItemIcon sx={{\n                  minWidth: 'auto',\n                  mx: isRtl ? 0 : 1,\n                  ml: isRtl ? 1 : 0,\n                }}>\n                  <CheckCircleIcon color=\"success\" />\n                </ListItemIcon>\n                <ListItemText\n                  primary={item}\n                  sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n                />\n              </ListItem>\n            ))}\n          </List>\n        </SectionCard>\n\n        {/* Section 2 */}\n        <SectionCard>\n          <Typography variant=\"h5\" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {refund.section2.title}\n          </Typography>\n          <Typography mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {refund.section2.description}\n          </Typography>\n          <List>\n            {refund.section2.items.map((item, idx) => (\n              <ListItem key={idx} sx={{\n                pl: isRtl ? 0 : 2,\n                pr: isRtl ? 2 : 0,\n                flexDirection: 'row',\n                textAlign: isRtl ? 'right' : 'left',\n              }}>\n                <ListItemIcon sx={{\n                  minWidth: 'auto',\n                  mx: isRtl ? 0 : 1,\n                  ml: isRtl ? 1 : 0,\n                }}>\n                  <CancelIcon color=\"error\" />\n                </ListItemIcon>\n                <ListItemText\n                  primary={item}\n                  sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n                />\n              </ListItem>\n            ))}\n          </List>\n        </SectionCard>\n\n        {/* Section 3 */}\n        <SectionCard>\n          <Typography variant=\"h5\" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {refund.section3.title}\n          </Typography>\n          <Typography sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {refund.section3.description}\n          </Typography>\n        </SectionCard>\n\n        {/* Contact Section */}\n        <Box sx={{ mt: 3 }}>\n          <Typography variant=\"h6\" fontWeight={600} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {refund.contact.title}\n          </Typography>\n          <Box sx={{\n            p: 2,\n            bgcolor: alpha(theme.palette.info.main, 0.1),\n            borderRadius: 2,\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          }}>\n            <EmailIcon color=\"info\" />\n            <Typography fontWeight=\"bold\" color={theme.palette.info.main}>\n              {refund.contact.email}\n            </Typography>\n          </Box>\n        </Box>\n      </Box>\n    );\n  };\n\n  // Booking Payment Policy Section\n  const renderBookingPaymentPolicy = () => {\n    const policy = t('policies.bookingPayment', { returnObjects: true });\n\n    const renderSection = (section) => (\n      <SectionCard>\n        <Typography\n          variant=\"h5\"\n          fontWeight={600}\n          color={theme.palette.primary.main}\n          mb={2}\n          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n        >\n          {section.title}\n        </Typography>\n        {section.points && (\n          <List>\n            {section.points.map((item, idx) => (\n              <ListItem key={idx} sx={{\n                pl: isRtl ? 0 : 2,\n                pr: isRtl ? 2 : 0,\n                flexDirection: 'row',\n                textAlign: isRtl ? 'right' : 'left',\n              }}>\n                <ListItemIcon sx={{\n                  minWidth: 'auto',\n                  mx: isRtl ? 0 : 1,\n                  ml: isRtl ? 1 : 0,\n                }}>\n                  <CheckCircleIcon color=\"success\" />\n                </ListItemIcon>\n                <ListItemText primary={item} />\n              </ListItem>\n            ))}\n          </List>\n        )}\n        {section.description && <Typography>{section.description}</Typography>}\n      </SectionCard>\n    );\n\n    return (\n      <Box>\n        <Typography variant=\"h4\" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n          {policy.title}\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\n          {policy.subtitle}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n        {renderSection(policy.section1)}\n        {renderSection(policy.section2)}\n        {renderSection(policy.section3)}\n        {renderSection(policy.section4)}\n        {renderSection(policy.section5)}\n        {renderSection(policy.section6)}\n        {renderSection(policy.section7)}\n        {renderSection(policy.section8)}\n\n        {/* Contact Section */}\n        <Box sx={{ mt: 3 }}>\n          <Typography variant=\"h6\" fontWeight={600} mb={1}>{policy.section8.title || policy.section8.description}</Typography>\n          <Typography mb={1}>{policy.section8.description}</Typography>\n          <Box sx={{\n            p: 2,\n            bgcolor: alpha(theme.palette.info.main, 0.1),\n            borderRadius: 2,\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          }}>\n            <EmailIcon color=\"info\" />\n            <Typography fontWeight=\"bold\" color={theme.palette.info.main}>\n              {policy.section8.email}\n            </Typography>\n          </Box>\n        </Box>\n      </Box>\n    );\n  };\n\n  // Booking Cancellation Policy Section\n  const renderBookingCancellationPolicy = () => {\n    const policy = t('policies.bookingCancellation', { returnObjects: true });\n\n    const renderSubSection = (subSection) => (\n      <Box sx={{ mb: 3 }}>\n        <Typography\n          variant=\"h6\"\n          sx={{\n            fontWeight: 600,\n            color: theme.palette.primary.main,\n            mb: 1,\n            fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n          }}\n        >\n          {subSection.title}\n        </Typography>\n        {subSection.points && (\n          <List>\n            {subSection.points.map((item, idx) => (\n              <ListItem key={idx} sx={{\n                pl: isRtl ? 0 : 2,\n                pr: isRtl ? 2 : 0,\n                flexDirection: 'row',\n                textAlign: isRtl ? 'right' : 'left',\n              }}>\n                <ListItemIcon sx={{\n                minWidth: 'auto',\n                mx: isRtl ? 0 : 1,\n                ml: isRtl ? 1 : 0,\n              }}><CheckCircleIcon color=\"success\" /></ListItemIcon>\n                <ListItemText primary={item} />\n              </ListItem>\n            ))}\n          </List>\n        )}\n      </Box>\n    );\n\n    return (\n      <Box>\n        <Typography variant=\"h4\" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n          {policy.title}\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\n          {policy.subtitle}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Student Policy */}\n        <SectionCard>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {policy.studentPolicy.title}\n          </Typography>\n          {renderSubSection(policy.studentPolicy.booking)}\n          {renderSubSection(policy.studentPolicy.cancellation)}\n          {renderSubSection(policy.studentPolicy.rescheduling)}\n          {renderSubSection(policy.studentPolicy.lateArrival)}\n        </SectionCard>\n\n        {/* Tutor Policy */}\n        <SectionCard bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {policy.tutorPolicy.title}\n          </Typography>\n          {renderSubSection(policy.tutorPolicy.availability)}\n          {renderSubSection(policy.tutorPolicy.cancellation)}\n          {renderSubSection(policy.tutorPolicy.rescheduling)}\n          {renderSubSection(policy.tutorPolicy.lateArrival)}\n        </SectionCard>\n\n        {/* General Notes */}\n        <SectionCard>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {policy.generalNotes.title}\n          </Typography>\n          <List>\n            {policy.generalNotes.points.map((item, idx) => (\n              <ListItem key={idx} sx={{\n              pl: isRtl ? 0 : 2,\n              pr: isRtl ? 2 : 0,\n              flexDirection: 'row',\n              textAlign: isRtl ? 'right' : 'left',\n            }}>\n                <ListItemIcon sx={{\n              minWidth: 'auto',\n              mx: isRtl ? 0 : 1,\n              ml: isRtl ? 1 : 0,\n            }}><InfoIcon color=\"info\" /></ListItemIcon>\n                <ListItemText primary={item} />\n              </ListItem>\n            ))}\n          </List>\n        </SectionCard>\n\n        {/* Summary Note */}\n        <Box sx={{\n          mt: 3,\n          p: 2,\n          bgcolor: alpha(theme.palette.info.main, 0.1),\n          borderRadius: 2,\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        }}>\n          <InfoIcon color=\"info\" />\n          <Typography color={theme.palette.info.main}>\n            {policy.summary?.importantNote || (isRtl ? 'يرجى مراجعة جميع السياسات بعناية قبل الحجز' : 'Please review all policies carefully before booking')}\n          </Typography>\n        </Box>\n      </Box>\n    );\n  };\n\n  // Teacher Commitment Section\n  const renderTeacherCommitment = () => {\n    return (\n      <Box>\n        <Typography\n          variant=\"h4\"\n          fontWeight={700}\n          mb={2}\n          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n        >\n          {t('teacher.commitmentTitle')}\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\n          {t('teacher.commitmentDescription')}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Commitment Points */}\n        <SectionCard>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={3}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {isRtl ? 'بنود التعهد' : 'Commitment Terms'}\n          </Typography>\n\n          <List>\n            {[1, 2, 3, 4, 5, 6].map((num) => (\n              <ListItem key={num} sx={{\n                pl: isRtl ? 0 : 2,\n                pr: isRtl ? 2 : 0,\n                flexDirection: 'row',\n                textAlign: isRtl ? 'right' : 'left',\n                alignItems: 'flex-start',\n                mb: 2\n              }}>\n                <ListItemIcon sx={{\n                  minWidth: 'auto',\n                  mx: isRtl ? 0 : 1,\n                  ml: isRtl ? 1 : 0,\n                  mt: 0.5\n                }}>\n                  <CheckCircleIcon color=\"primary\" />\n                </ListItemIcon>\n                <ListItemText\n                  primary={t(`teacher.commitmentText.point${num}`)}\n                  sx={{\n                    fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                    '& .MuiListItemText-primary': {\n                      fontSize: '1rem',\n                      lineHeight: 1.6\n                    }\n                  }}\n                />\n              </ListItem>\n            ))}\n          </List>\n        </SectionCard>\n\n        {/* Conclusion */}\n        <SectionCard bg={alpha(theme.palette.warning.main, 0.05)}>\n          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>\n            <InfoIcon sx={{ color: theme.palette.warning.main, mt: 0.5 }} />\n            <Box>\n              <Typography\n                variant=\"h6\"\n                fontWeight={600}\n                color={theme.palette.warning.main}\n                mb={1}\n                sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n              >\n                {isRtl ? 'إقرار مهم' : 'Important Acknowledgment'}\n              </Typography>\n              <Typography\n                sx={{\n                  fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                  lineHeight: 1.6\n                }}\n              >\n                {t('teacher.commitmentText.conclusion')}\n              </Typography>\n            </Box>\n          </Box>\n        </SectionCard>\n      </Box>\n    );\n  };\n\n  // Commission Policy Section\n  const renderCommissionPolicy = () => {\n    return (\n      <Box>\n        <Typography\n          variant=\"h4\"\n          fontWeight={700}\n          mb={2}\n          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n        >\n          {t('privacyPolicy.commissionPolicy.title')}\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\n          {t('privacyPolicy.commissionPolicy.description')}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Commission Rates */}\n        <SectionCard>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={3}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {isRtl ? 'معدلات العمولة' : 'Commission Rates'}\n          </Typography>\n\n          <List>\n            {[3, 4, 5, 6, 7].map((rate) => (\n              <ListItem key={rate} sx={{\n                pl: isRtl ? 0 : 2,\n                pr: isRtl ? 2 : 0,\n                flexDirection: 'row',\n                textAlign: isRtl ? 'right' : 'left',\n                mb: 1\n              }}>\n                <ListItemIcon sx={{\n                  minWidth: 'auto',\n                  mx: isRtl ? 0 : 1,\n                  ml: isRtl ? 1 : 0,\n                }}>\n                  <CurrencyExchangeIcon color=\"success\" />\n                </ListItemIcon>\n                <ListItemText\n                  primary={t(`privacyPolicy.commissionPolicy.rates.${rate}`)}\n                  sx={{\n                    fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                    '& .MuiListItemText-primary': {\n                      fontSize: '1rem',\n                      fontWeight: 500\n                    }\n                  }}\n                />\n              </ListItem>\n            ))}\n          </List>\n        </SectionCard>\n\n        {/* Explanation */}\n        <SectionCard bg={alpha(theme.palette.info.main, 0.05)}>\n          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>\n            <InfoIcon sx={{ color: theme.palette.info.main, mt: 0.5 }} />\n            <Box>\n              <Typography\n                variant=\"h6\"\n                fontWeight={600}\n                color={theme.palette.info.main}\n                mb={1}\n                sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n              >\n                {isRtl ? 'ملاحظة مهمة' : 'Important Note'}\n              </Typography>\n              <Typography\n                sx={{\n                  fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                  lineHeight: 1.6,\n                  fontStyle: 'italic'\n                }}\n              >\n                {t('privacyPolicy.commissionPolicy.explanation')}\n              </Typography>\n            </Box>\n          </Box>\n        </SectionCard>\n      </Box>\n    );\n  };\n\n  const tabs = [\n    { label: isRtl ? 'سياسة الخصوصية' : 'Privacy Policy', icon: <SecurityIcon /> },\n    { label: isRtl ? 'الشروط والأحكام' : 'Terms & Conditions', icon: <InfoIcon /> },\n    { label: isRtl ? 'سياسة الاسترداد' : 'Refund Policy', icon: <CurrencyExchangeIcon /> },\n    { label: isRtl ? 'سياسة الحجز والدفع' : 'Booking & Payment Policy', icon: <PaymentIcon /> },\n    { label: isRtl ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation Policy', icon: <CancelIcon /> },\n    { label: isRtl ? 'تعهد المعلمين' : 'Teacher Commitment', icon: <AssignmentIcon /> },\n    { label: isRtl ? 'سياسة العمولة' : 'Commission Policy', icon: <AccountBalanceIcon /> },\n  ];\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 0:\n        return renderPrivacyPolicy();\n      case 1:\n        return renderTermsAndConditions();\n      case 2:\n        return renderRefundPolicy();\n      case 3:\n        return renderBookingPaymentPolicy();\n      case 4:\n        return renderBookingCancellationPolicy();\n      case 5:\n        return renderTeacherCommitment();\n      case 6:\n        return renderCommissionPolicy();\n      default:\n        return renderPrivacyPolicy();\n    }\n  };\n\n  return (\n    <Layout>\n      <Box\n        sx={{\n          minHeight: '100vh',\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\n          pt: 4,\n          pb: 8,\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Fade in timeout={800}>\n            <Box sx={{ p: { xs: 2, md: 4 }, direction: isRtl ? 'rtl' : 'ltr' }}>\n              {/* Main Title */}\n              <Typography\n                variant=\"h3\"\n                align=\"center\"\n                sx={{\n                  mb: 4,\n                  fontWeight: 800,\n                  fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.primary.main,\n                }}\n              >\n                {isRtl ? 'سياسات المنصة' : 'Platform Policies'}\n              </Typography>\n\n              {/* Tabs */}\n              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>\n                <Tabs\n                  value={activeTab}\n                  onChange={handleTabChange}\n                  variant=\"scrollable\"\n                  scrollButtons=\"auto\"\n                  sx={{\n                    '& .MuiTab-root': {\n                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                      fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },\n                      minWidth: { xs: 120, sm: 140, md: 160 },\n                    },\n                  }}\n                >\n                  {tabs.map((tab, index) => (\n                    <Tab\n                      key={index}\n                      label={tab.label}\n                      icon={tab.icon}\n                      iconPosition=\"start\"\n                      sx={{\n                        '& .MuiTab-iconWrapper': {\n                          mr: isRtl ? 0 : 1,\n                          ml: isRtl ? 1 : 0,\n                        },\n                      }}\n                    />\n                  ))}\n                </Tabs>\n              </Box>\n\n              {/* Tab Content */}\n              <Box sx={{ mt: 3 }}>\n                {renderTabContent()}\n              </Box>\n            </Box>\n          </Fade>\n        </Container>\n      </Box>\n    </Layout>\n  );\n};\n\nexport default PlatformPolicy;\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,OAAO,CACPC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,QAAQ,CACRC,KAAK,CACLC,IAAI,CACJC,GAAG,CACHC,KAAK,KACA,eAAe,CACtB,OACEC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,gBAAgB,GAAI,CAAAC,oBAAoB,CACxCC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,cAAc,GAAI,CAAAC,kBAAkB,KAC/B,qBAAqB,CAC5B,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1C,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG3C,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA4C,KAAK,CAAG/B,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAgC,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6C,KAAK,CAAGH,IAAI,CAACI,QAAQ,GAAK,IAAI,CACpC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGnD,QAAQ,CAAC,CAAC,CAAC,CAE7C;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmD,IAAI,CAAGL,QAAQ,CAACK,IAAI,CAACC,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAC3C,OAAQD,IAAI,EACV,IAAK,SAAS,CACZD,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,OAAO,CACVA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,QAAQ,CACXA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,SAAS,CACZA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,SAAS,CACZA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,YAAY,CACfA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,YAAY,CACfA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,QACEA,YAAY,CAAC,CAAC,CAAC,CACnB,CACF,CAAC,CAAE,CAACJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAEnB,KAAM,CAAAE,eAAe,CAAGA,CAACC,CAAC,CAAEC,QAAQ,GAAK,CACvCL,YAAY,CAACK,QAAQ,CAAC,CACxB,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGC,IAAA,MAAC,CAAEC,QAAQ,CAAEC,EAAG,CAAC,CAAAF,IAAA,oBACnClB,IAAA,CAAChC,IAAI,EACHqD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACf,IAAIhB,KAAK,CACL,CAAEiB,WAAW,CAAE,aAAanB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAavB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEV,EAAE,EAAI5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAC/D,CAAE,CAAAT,QAAA,cAEFnB,IAAA,CAAC/B,WAAW,EAAAkD,QAAA,CAAEA,QAAQ,CAAc,CAAC,CACjC,CAAC,EACR,CAED;AACF;AACA,KACE,KAAM,CAAAY,KAAK,CAAIC,GAAG,EAAK,CACrB,KAAM,CAAAC,GAAG,CAAG7B,CAAC,CAAC4B,GAAG,CAAC,CAClB,MAAO,CAAAC,GAAG,EAAI,CAACA,GAAG,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAGC,GAAG,CAAG,EAAE,CAC7C,CAAC,CAED;AACA,KAAM,CAAAE,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,qBAAqB,CAAIC,GAAG,EAAK,CACrC,KAAM,CAAAC,QAAQ,CAAGP,KAAK,CAAC,kBAAkBM,GAAG,WAAW,CAAC,CACxD,KAAM,CAAAE,OAAO,CAAGR,KAAK,CAAC,kBAAkBM,GAAG,UAAU,CAAC,CAEtD;AACA,KAAM,CAAAG,OAAO,CAAG,EAAE,CAClB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI,EAAE,CAAEA,CAAC,EAAE,CAAE,CAC5B,KAAM,CAAAC,IAAI,CAAGX,KAAK,CAAC,kBAAkBM,GAAG,QAAQI,CAAC,EAAE,CAAC,CACpD,GAAIC,IAAI,CAAEF,OAAO,CAACG,IAAI,CAACD,IAAI,CAAC,CAC9B,CAEA,mBACExC,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEf,CAAC,CAAC,kBAAkBiC,GAAG,QAAQ,CAAC,CACvB,CAAC,CACZC,QAAQ,eAAItC,IAAA,CAACnC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAEmB,QAAQ,CAAa,CAAC,CACtDC,OAAO,eAAIvC,IAAA,CAACnC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAEoB,OAAO,CAAa,CAAC,CACpDC,OAAO,CAACQ,MAAM,CAAG,CAAC,eACjBhD,IAAA,CAAC7B,IAAI,EAAAgD,QAAA,CACFqB,OAAO,CAACS,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBACrBhD,KAAA,CAAC9B,QAAQ,EAEPkD,EAAE,CAAE,CACF6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eAEFnB,IAAA,CAAC1B,YAAY,EACXgD,EAAE,CAAE,CACFiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cAEFnB,IAAA,CAACnB,QAAQ,EAACiE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACf9C,IAAA,CAAC3B,YAAY,EACXsD,OAAO,CAAEe,IAAK,CACdpB,EAAE,CAAE,CAAEgC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAC7C,CAAC,GApBG0C,GAqBG,CACX,CAAC,CACE,CACP,GAxCeb,GAyCL,CAAC,CAElB,CAAC,CAED,mBACEnC,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBtB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEf,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbJ,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACE,KAAK,CAAC,gBAAgB,CAACvB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1Df,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbJ,IAAA,CAAC9B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CACzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC0B,GAAG,CAAEZ,GAAG,EAAKD,qBAAqB,CAACC,GAAG,CAAC,CAAC,cAGrEnC,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEf,CAAC,CAAC,yBAAyB,CAAC,CACnB,CAAC,cACbJ,IAAA,CAACnC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAEf,CAAC,CAAC,2BAA2B,CAAC,CAAa,CAAC,cAChEF,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CACPoC,CAAC,CAAE,CAAC,CACJC,OAAO,CAAEnF,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAI,CAAE,GAAG,CAAC,CAC5CJ,YAAY,CAAE,CAAC,CACfqC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CAAE,CAAA5C,QAAA,eACAnB,IAAA,CAACjB,SAAS,EAAC+D,KAAK,CAAC,MAAM,CAAE,CAAC,cAC1B9C,IAAA,CAACnC,UAAU,EAACgF,UAAU,CAAC,MAAM,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAK,CAAAT,QAAA,CAC1Df,CAAC,CAAC,yBAAyB,CAAC,CACnB,CAAC,EACV,CAAC,EACK,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA4D,wBAAwB,CAAGA,CAAA,GAAM,CACrC,mBACE9D,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBtB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEf,CAAC,CAAC,uBAAuB,CAAC,CACjB,CAAC,cACbJ,IAAA,CAAC9B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BvB,IAAA,CAACrB,KAAK,EAACsF,OAAO,CAAE,CAAE,CAAA9C,QAAA,CACff,CAAC,CAAC,yBAAyB,CAAC,CAC1B8D,KAAK,CAAC,SAAS,CAAC,CAChBC,MAAM,CAAEC,OAAO,EAAKA,OAAO,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC1CpB,GAAG,CAAC,CAACmB,OAAO,CAAElB,GAAG,GAAK,CACrB,KAAM,CAAAoB,KAAK,CAAGF,OAAO,CAACF,KAAK,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAK,KAAK,CAAGD,KAAK,CAAC,CAAC,CAAC,CACtB,KAAM,CAAAE,IAAI,CAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACtC,mBACE1E,IAAA,CAAChC,IAAI,EAEHqD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFE,YAAY,CAAE,CAAC,CACf,IAAIhB,KAAK,CACL,CAAEiB,WAAW,CAAE,aAAanB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAavB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CACboB,GAAG,CAAG,CAAC,GAAK,CAAC,CACT1E,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvCpD,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACiD,SAAS,CAAC/C,IAAI,EAAItB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACiD,KAAK,CAAE,IAAI,CAC/E,CAAE,CAAAzD,QAAA,cAEFjB,KAAA,CAACjC,WAAW,EAAAkD,QAAA,eACVnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZtB,EAAE,CAAE,CACFC,EAAE,CAAE,GAAG,CACPsB,UAAU,CAAE,GAAG,CACfE,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDsC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAT,QAAA,CAEDoD,KAAK,CACI,CAAC,cACbvE,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,OAAO,CACftB,EAAE,CAAE,CACFuD,UAAU,CAAE,UAAU,CACtBC,UAAU,CAAE,CAAC,CACbC,QAAQ,CAAE,SAAS,CACnBhC,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDsC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACsD,IAAI,CAACrD,OAC5B,CAAE,CAAAR,QAAA,CAEDqD,IAAI,CACK,CAAC,EACF,CAAC,EArCTtB,GAsCD,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACL,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA+B,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,MAAM,CAAG9E,CAAC,CAAC,iBAAiB,CAAE,CAAE+E,aAAa,CAAE,IAAK,CAAC,CAAC,CAE5D,mBACEjF,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACtB,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACvH+D,MAAM,CAACX,KAAK,CACH,CAAC,cACbvE,IAAA,CAAC9B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BrB,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAACL,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1J+D,MAAM,CAACE,QAAQ,CAACb,KAAK,CACZ,CAAC,cACbvE,IAAA,CAACnC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzF+D,MAAM,CAACE,QAAQ,CAACC,WAAW,CAClB,CAAC,cACbrF,IAAA,CAAC7B,IAAI,EAAAgD,QAAA,CACF+D,MAAM,CAACE,QAAQ,CAACE,KAAK,CAACrC,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBACnChD,KAAA,CAAC9B,QAAQ,EAAWkD,EAAE,CAAE,CACtB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eACAnB,IAAA,CAAC1B,YAAY,EAACgD,EAAE,CAAE,CAChBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cACAnB,IAAA,CAACf,eAAe,EAAC6D,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACf9C,IAAA,CAAC3B,YAAY,EACXsD,OAAO,CAAEe,IAAK,CACdpB,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAC1E,CAAC,GAhBW0C,GAiBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdhD,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAACL,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1J+D,MAAM,CAACK,QAAQ,CAAChB,KAAK,CACZ,CAAC,cACbvE,IAAA,CAACnC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzF+D,MAAM,CAACK,QAAQ,CAACF,WAAW,CAClB,CAAC,cACbrF,IAAA,CAAC7B,IAAI,EAAAgD,QAAA,CACF+D,MAAM,CAACK,QAAQ,CAACD,KAAK,CAACrC,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBACnChD,KAAA,CAAC9B,QAAQ,EAAWkD,EAAE,CAAE,CACtB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eACAnB,IAAA,CAAC1B,YAAY,EAACgD,EAAE,CAAE,CAChBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cACAnB,IAAA,CAACb,UAAU,EAAC2D,KAAK,CAAC,OAAO,CAAE,CAAC,CAChB,CAAC,cACf9C,IAAA,CAAC3B,YAAY,EACXsD,OAAO,CAAEe,IAAK,CACdpB,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAC1E,CAAC,GAhBW0C,GAiBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdhD,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAACL,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1J+D,MAAM,CAACM,QAAQ,CAACjB,KAAK,CACZ,CAAC,cACbvE,IAAA,CAACnC,UAAU,EAACyD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAClF+D,MAAM,CAACM,QAAQ,CAACH,WAAW,CAClB,CAAC,EACF,CAAC,cAGdnF,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CAAEmE,EAAE,CAAE,CAAE,CAAE,CAAAtE,QAAA,eACjBnB,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACtB,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACvH+D,MAAM,CAACQ,OAAO,CAACnB,KAAK,CACX,CAAC,cACbrE,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CACPoC,CAAC,CAAE,CAAC,CACJC,OAAO,CAAEnF,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAI,CAAE,GAAG,CAAC,CAC5CJ,YAAY,CAAE,CAAC,CACfqC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CAAE,CAAA5C,QAAA,eACAnB,IAAA,CAACjB,SAAS,EAAC+D,KAAK,CAAC,MAAM,CAAE,CAAC,cAC1B9C,IAAA,CAACnC,UAAU,EAACgF,UAAU,CAAC,MAAM,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAK,CAAAT,QAAA,CAC1D+D,MAAM,CAACQ,OAAO,CAACC,KAAK,CACX,CAAC,EACV,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,0BAA0B,CAAGA,CAAA,GAAM,CACvC,KAAM,CAAAC,MAAM,CAAGzF,CAAC,CAAC,yBAAyB,CAAE,CAAE+E,aAAa,CAAE,IAAK,CAAC,CAAC,CAEpE,KAAM,CAAAW,aAAa,CAAI1B,OAAO,eAC5BlE,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEiD,OAAO,CAACG,KAAK,CACJ,CAAC,CACZH,OAAO,CAAC2B,MAAM,eACb/F,IAAA,CAAC7B,IAAI,EAAAgD,QAAA,CACFiD,OAAO,CAAC2B,MAAM,CAAC9C,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBAC5BhD,KAAA,CAAC9B,QAAQ,EAAWkD,EAAE,CAAE,CACtB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eACAnB,IAAA,CAAC1B,YAAY,EAACgD,EAAE,CAAE,CAChBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cACAnB,IAAA,CAACf,eAAe,EAAC6D,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACf9C,IAAA,CAAC3B,YAAY,EAACsD,OAAO,CAAEe,IAAK,CAAE,CAAC,GAblBQ,GAcL,CACX,CAAC,CACE,CACP,CACAkB,OAAO,CAACiB,WAAW,eAAIrF,IAAA,CAACnC,UAAU,EAAAsD,QAAA,CAAEiD,OAAO,CAACiB,WAAW,CAAa,CAAC,EAC3D,CACd,CAED,mBACEnF,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACtB,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACvH0E,MAAM,CAACtB,KAAK,CACH,CAAC,cACbvE,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACE,KAAK,CAAC,gBAAgB,CAACvB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1D0E,MAAM,CAACvD,QAAQ,CACN,CAAC,cACbtC,IAAA,CAAC9B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CACzBuE,aAAa,CAACD,MAAM,CAACT,QAAQ,CAAC,CAC9BU,aAAa,CAACD,MAAM,CAACN,QAAQ,CAAC,CAC9BO,aAAa,CAACD,MAAM,CAACL,QAAQ,CAAC,CAC9BM,aAAa,CAACD,MAAM,CAACG,QAAQ,CAAC,CAC9BF,aAAa,CAACD,MAAM,CAACI,QAAQ,CAAC,CAC9BH,aAAa,CAACD,MAAM,CAACK,QAAQ,CAAC,CAC9BJ,aAAa,CAACD,MAAM,CAACM,QAAQ,CAAC,CAC9BL,aAAa,CAACD,MAAM,CAACO,QAAQ,CAAC,cAG/BlG,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CAAEmE,EAAE,CAAE,CAAE,CAAE,CAAAtE,QAAA,eACjBnB,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACtB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAE0E,MAAM,CAACO,QAAQ,CAAC7B,KAAK,EAAIsB,MAAM,CAACO,QAAQ,CAACf,WAAW,CAAa,CAAC,cACpHrF,IAAA,CAACnC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAE0E,MAAM,CAACO,QAAQ,CAACf,WAAW,CAAa,CAAC,cAC7DnF,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CACPoC,CAAC,CAAE,CAAC,CACJC,OAAO,CAAEnF,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAI,CAAE,GAAG,CAAC,CAC5CJ,YAAY,CAAE,CAAC,CACfqC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CAAE,CAAA5C,QAAA,eACAnB,IAAA,CAACjB,SAAS,EAAC+D,KAAK,CAAC,MAAM,CAAE,CAAC,cAC1B9C,IAAA,CAACnC,UAAU,EAACgF,UAAU,CAAC,MAAM,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAK,CAAAT,QAAA,CAC1D0E,MAAM,CAACO,QAAQ,CAACT,KAAK,CACZ,CAAC,EACV,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAU,+BAA+B,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAC5C,KAAM,CAAAT,MAAM,CAAGzF,CAAC,CAAC,8BAA8B,CAAE,CAAE+E,aAAa,CAAE,IAAK,CAAC,CAAC,CAEzE,KAAM,CAAAoB,gBAAgB,CAAIC,UAAU,eAClCtG,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZtB,EAAE,CAAE,CACFuB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCL,EAAE,CAAE,CAAC,CACLwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAC9C,CAAE,CAAAW,QAAA,CAEDqF,UAAU,CAACjC,KAAK,CACP,CAAC,CACZiC,UAAU,CAACT,MAAM,eAChB/F,IAAA,CAAC7B,IAAI,EAAAgD,QAAA,CACFqF,UAAU,CAACT,MAAM,CAAC9C,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBAC/BhD,KAAA,CAAC9B,QAAQ,EAAWkD,EAAE,CAAE,CACtB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eACAnB,IAAA,CAAC1B,YAAY,EAACgD,EAAE,CAAE,CAClBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cAACnB,IAAA,CAACf,eAAe,EAAC6D,KAAK,CAAC,SAAS,CAAE,CAAC,CAAc,CAAC,cACnD9C,IAAA,CAAC3B,YAAY,EAACsD,OAAO,CAAEe,IAAK,CAAE,CAAC,GAXlBQ,GAYL,CACX,CAAC,CACE,CACP,EACE,CACN,CAED,mBACEhD,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACtB,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACvH0E,MAAM,CAACtB,KAAK,CACH,CAAC,cACbvE,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACE,KAAK,CAAC,gBAAgB,CAACvB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1D0E,MAAM,CAACvD,QAAQ,CACN,CAAC,cACbtC,IAAA,CAAC9B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BrB,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExE0E,MAAM,CAACY,aAAa,CAAClC,KAAK,CACjB,CAAC,CACZgC,gBAAgB,CAACV,MAAM,CAACY,aAAa,CAACC,OAAO,CAAC,CAC9CH,gBAAgB,CAACV,MAAM,CAACY,aAAa,CAACE,YAAY,CAAC,CACnDJ,gBAAgB,CAACV,MAAM,CAACY,aAAa,CAACG,YAAY,CAAC,CACnDL,gBAAgB,CAACV,MAAM,CAACY,aAAa,CAACI,WAAW,CAAC,EACxC,CAAC,cAGd3G,KAAA,CAACe,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACiD,SAAS,CAAC/C,IAAI,EAAItB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACiD,KAAK,CAAE,IAAI,CAAE,CAAAzD,QAAA,eACxFnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExE0E,MAAM,CAACiB,WAAW,CAACvC,KAAK,CACf,CAAC,CACZgC,gBAAgB,CAACV,MAAM,CAACiB,WAAW,CAACC,YAAY,CAAC,CACjDR,gBAAgB,CAACV,MAAM,CAACiB,WAAW,CAACH,YAAY,CAAC,CACjDJ,gBAAgB,CAACV,MAAM,CAACiB,WAAW,CAACF,YAAY,CAAC,CACjDL,gBAAgB,CAACV,MAAM,CAACiB,WAAW,CAACD,WAAW,CAAC,EACtC,CAAC,cAGd3G,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExE0E,MAAM,CAACmB,YAAY,CAACzC,KAAK,CAChB,CAAC,cACbvE,IAAA,CAAC7B,IAAI,EAAAgD,QAAA,CACF0E,MAAM,CAACmB,YAAY,CAACjB,MAAM,CAAC9C,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBACxChD,KAAA,CAAC9B,QAAQ,EAAWkD,EAAE,CAAE,CACxB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eACEnB,IAAA,CAAC1B,YAAY,EAACgD,EAAE,CAAE,CACpBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cAACnB,IAAA,CAACnB,QAAQ,EAACiE,KAAK,CAAC,MAAM,CAAE,CAAC,CAAc,CAAC,cACvC9C,IAAA,CAAC3B,YAAY,EAACsD,OAAO,CAAEe,IAAK,CAAE,CAAC,GAXlBQ,GAYL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdhD,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CACPmE,EAAE,CAAE,CAAC,CACL/B,CAAC,CAAE,CAAC,CACJC,OAAO,CAAEnF,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAI,CAAE,GAAG,CAAC,CAC5CJ,YAAY,CAAE,CAAC,CACfqC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,CACP,CAAE,CAAA5C,QAAA,eACAnB,IAAA,CAACnB,QAAQ,EAACiE,KAAK,CAAC,MAAM,CAAE,CAAC,cACzB9C,IAAA,CAACnC,UAAU,EAACiF,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAK,CAAAT,QAAA,CACxC,EAAAmF,eAAA,CAAAT,MAAM,CAACoB,OAAO,UAAAX,eAAA,iBAAdA,eAAA,CAAgBY,aAAa,IAAK1G,KAAK,CAAG,4CAA4C,CAAG,qDAAqD,CAAC,CACtI,CAAC,EACV,CAAC,EACH,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA2G,uBAAuB,CAAGA,CAAA,GAAM,CACpC,mBACEjH,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBtB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEf,CAAC,CAAC,yBAAyB,CAAC,CACnB,CAAC,cACbJ,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACE,KAAK,CAAC,gBAAgB,CAACvB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1Df,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,cACbJ,IAAA,CAAC9B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BrB,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEX,KAAK,CAAG,aAAa,CAAG,kBAAkB,CACjC,CAAC,cAEbR,IAAA,CAAC7B,IAAI,EAAAgD,QAAA,CACF,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC8B,GAAG,CAAEZ,GAAG,eAC1BnC,KAAA,CAAC9B,QAAQ,EAAWkD,EAAE,CAAE,CACtB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCsD,UAAU,CAAE,YAAY,CACxBvC,EAAE,CAAE,CACN,CAAE,CAAAJ,QAAA,eACAnB,IAAA,CAAC1B,YAAY,EAACgD,EAAE,CAAE,CAChBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiF,EAAE,CAAE,GACN,CAAE,CAAAtE,QAAA,cACAnB,IAAA,CAACf,eAAe,EAAC6D,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACf9C,IAAA,CAAC3B,YAAY,EACXsD,OAAO,CAAEvB,CAAC,CAAC,+BAA+BiC,GAAG,EAAE,CAAE,CACjDf,EAAE,CAAE,CACFyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChE,4BAA4B,CAAE,CAC5BuE,QAAQ,CAAE,MAAM,CAChBD,UAAU,CAAE,GACd,CACF,CAAE,CACH,CAAC,GAzBWzC,GA0BL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdrC,IAAA,CAACiB,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAAC0F,OAAO,CAACxF,IAAI,CAAE,IAAI,CAAE,CAAAT,QAAA,cACvDjB,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CAAEuC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,YAAY,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA5C,QAAA,eAC7DnB,IAAA,CAACnB,QAAQ,EAACyC,EAAE,CAAE,CAAEwB,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAAC0F,OAAO,CAACxF,IAAI,CAAE6D,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cAChEvF,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAAC0F,OAAO,CAACxF,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEX,KAAK,CAAG,WAAW,CAAG,0BAA0B,CACvC,CAAC,cACbR,IAAA,CAACnC,UAAU,EACTyD,EAAE,CAAE,CACFyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEsE,UAAU,CAAE,GACd,CAAE,CAAA3D,QAAA,CAEDf,CAAC,CAAC,mCAAmC,CAAC,CAC7B,CAAC,EACV,CAAC,EACH,CAAC,CACK,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAiH,sBAAsB,CAAGA,CAAA,GAAM,CACnC,mBACEnH,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBtB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEf,CAAC,CAAC,sCAAsC,CAAC,CAChC,CAAC,cACbJ,IAAA,CAACnC,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACE,KAAK,CAAC,gBAAgB,CAACvB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1Df,CAAC,CAAC,4CAA4C,CAAC,CACtC,CAAC,cACbJ,IAAA,CAAC9B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BrB,KAAA,CAACe,WAAW,EAAAE,QAAA,eACVnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEX,KAAK,CAAG,gBAAgB,CAAG,kBAAkB,CACpC,CAAC,cAEbR,IAAA,CAAC7B,IAAI,EAAAgD,QAAA,CACF,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC8B,GAAG,CAAEqE,IAAI,eACxBpH,KAAA,CAAC9B,QAAQ,EAAYkD,EAAE,CAAE,CACvB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCe,EAAE,CAAE,CACN,CAAE,CAAAJ,QAAA,eACAnB,IAAA,CAAC1B,YAAY,EAACgD,EAAE,CAAE,CAChBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cACAnB,IAAA,CAACP,oBAAoB,EAACqD,KAAK,CAAC,SAAS,CAAE,CAAC,CAC5B,CAAC,cACf9C,IAAA,CAAC3B,YAAY,EACXsD,OAAO,CAAEvB,CAAC,CAAC,wCAAwCkH,IAAI,EAAE,CAAE,CAC3DhG,EAAE,CAAE,CACFyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChE,4BAA4B,CAAE,CAC5BuE,QAAQ,CAAE,MAAM,CAChBlC,UAAU,CAAE,GACd,CACF,CAAE,CACH,CAAC,GAvBWyE,IAwBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdtH,IAAA,CAACiB,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAI,CAAE,IAAI,CAAE,CAAAT,QAAA,cACpDjB,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CAAEuC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,YAAY,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA5C,QAAA,eAC7DnB,IAAA,CAACnB,QAAQ,EAACyC,EAAE,CAAE,CAAEwB,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAI,CAAE6D,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cAC7DvF,KAAA,CAACpC,GAAG,EAAAqD,QAAA,eACFnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACkC,IAAI,CAAChC,IAAK,CAC/BL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEX,KAAK,CAAG,aAAa,CAAG,gBAAgB,CAC/B,CAAC,cACbR,IAAA,CAACnC,UAAU,EACTyD,EAAE,CAAE,CACFyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEsE,UAAU,CAAE,GAAG,CACfyC,SAAS,CAAE,QACb,CAAE,CAAApG,QAAA,CAEDf,CAAC,CAAC,4CAA4C,CAAC,CACtC,CAAC,EACV,CAAC,EACH,CAAC,CACK,CAAC,EACX,CAAC,CAEV,CAAC,CAED,KAAM,CAAAoH,IAAI,CAAG,CACX,CAAEC,KAAK,CAAEjH,KAAK,CAAG,gBAAgB,CAAG,gBAAgB,CAAEkH,IAAI,cAAE1H,IAAA,CAACT,YAAY,GAAE,CAAE,CAAC,CAC9E,CAAEkI,KAAK,CAAEjH,KAAK,CAAG,iBAAiB,CAAG,oBAAoB,CAAEkH,IAAI,cAAE1H,IAAA,CAACnB,QAAQ,GAAE,CAAE,CAAC,CAC/E,CAAE4I,KAAK,CAAEjH,KAAK,CAAG,iBAAiB,CAAG,eAAe,CAAEkH,IAAI,cAAE1H,IAAA,CAACP,oBAAoB,GAAE,CAAE,CAAC,CACtF,CAAEgI,KAAK,CAAEjH,KAAK,CAAG,oBAAoB,CAAG,0BAA0B,CAAEkH,IAAI,cAAE1H,IAAA,CAACX,WAAW,GAAE,CAAE,CAAC,CAC3F,CAAEoI,KAAK,CAAEjH,KAAK,CAAG,sBAAsB,CAAG,+BAA+B,CAAEkH,IAAI,cAAE1H,IAAA,CAACb,UAAU,GAAE,CAAE,CAAC,CACjG,CAAEsI,KAAK,CAAEjH,KAAK,CAAG,eAAe,CAAG,oBAAoB,CAAEkH,IAAI,cAAE1H,IAAA,CAACL,cAAc,GAAE,CAAE,CAAC,CACnF,CAAE8H,KAAK,CAAEjH,KAAK,CAAG,eAAe,CAAG,mBAAmB,CAAEkH,IAAI,cAAE1H,IAAA,CAACH,kBAAkB,GAAE,CAAE,CAAC,CACvF,CAED,KAAM,CAAA8H,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,OAAQjH,SAAS,EACf,IAAK,EAAC,CACJ,MAAO,CAAAyB,mBAAmB,CAAC,CAAC,CAC9B,IAAK,EAAC,CACJ,MAAO,CAAA6B,wBAAwB,CAAC,CAAC,CACnC,IAAK,EAAC,CACJ,MAAO,CAAAiB,kBAAkB,CAAC,CAAC,CAC7B,IAAK,EAAC,CACJ,MAAO,CAAAW,0BAA0B,CAAC,CAAC,CACrC,IAAK,EAAC,CACJ,MAAO,CAAAS,+BAA+B,CAAC,CAAC,CAC1C,IAAK,EAAC,CACJ,MAAO,CAAAc,uBAAuB,CAAC,CAAC,CAClC,IAAK,EAAC,CACJ,MAAO,CAAAE,sBAAsB,CAAC,CAAC,CACjC,QACE,MAAO,CAAAlF,mBAAmB,CAAC,CAAC,CAChC,CACF,CAAC,CAED,mBACEnC,IAAA,CAACF,MAAM,EAAAqB,QAAA,cACLnB,IAAA,CAAClC,GAAG,EACFwD,EAAE,CAAE,CACFsG,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBrJ,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAKpD,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHkG,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAA5G,QAAA,cAEFnB,IAAA,CAACpC,SAAS,EAACoK,QAAQ,CAAC,IAAI,CAAA7G,QAAA,cACtBnB,IAAA,CAACjC,IAAI,EAACkK,EAAE,MAACC,OAAO,CAAE,GAAI,CAAA/G,QAAA,cACpBjB,KAAA,CAACpC,GAAG,EAACwD,EAAE,CAAE,CAAEoC,CAAC,CAAE,CAAEyE,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAEC,SAAS,CAAE7H,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAW,QAAA,eAEjEnB,IAAA,CAACnC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZ0F,KAAK,CAAC,QAAQ,CACdhH,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLsB,UAAU,CAAE,GAAG,CACfE,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDsC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAT,QAAA,CAEDX,KAAK,CAAG,eAAe,CAAG,mBAAmB,CACpC,CAAC,cAGbR,IAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEiH,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEjH,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAC1DnB,IAAA,CAACvB,IAAI,EACHgK,KAAK,CAAE/H,SAAU,CACjBgI,QAAQ,CAAE5H,eAAgB,CAC1B8B,OAAO,CAAC,YAAY,CACpB+F,aAAa,CAAC,MAAM,CACpBrH,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChByB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDuE,QAAQ,CAAE,CAAEoD,EAAE,CAAE,QAAQ,CAAES,EAAE,CAAE,QAAQ,CAAER,EAAE,CAAE,MAAO,CAAC,CACpD7E,QAAQ,CAAE,CAAE4E,EAAE,CAAE,GAAG,CAAES,EAAE,CAAE,GAAG,CAAER,EAAE,CAAE,GAAI,CACxC,CACF,CAAE,CAAAjH,QAAA,CAEDqG,IAAI,CAACvE,GAAG,CAAC,CAAC4F,GAAG,CAAEC,KAAK,gBACnB9I,IAAA,CAACtB,GAAG,EAEF+I,KAAK,CAAEoB,GAAG,CAACpB,KAAM,CACjBC,IAAI,CAAEmB,GAAG,CAACnB,IAAK,CACfqB,YAAY,CAAC,OAAO,CACpBzH,EAAE,CAAE,CACF,uBAAuB,CAAE,CACvB0H,EAAE,CAAExI,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CACF,CAAE,EATGsI,KAUN,CACF,CAAC,CACE,CAAC,CACJ,CAAC,cAGN9I,IAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEmE,EAAE,CAAE,CAAE,CAAE,CAAAtE,QAAA,CAChBwG,gBAAgB,CAAC,CAAC,CAChB,CAAC,EACH,CAAC,CACF,CAAC,CACE,CAAC,CACT,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAxH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}