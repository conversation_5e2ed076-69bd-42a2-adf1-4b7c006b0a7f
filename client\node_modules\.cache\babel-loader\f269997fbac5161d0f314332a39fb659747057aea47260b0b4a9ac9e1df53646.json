{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useNavigate}from'react-router-dom';import{useAuth}from'../../contexts/AuthContext';import axios from'../../utils/axios';import{timezones}from'../../utils/constants';import{Button,TextField,Grid,Typography,FormControl,InputLabel,Select,MenuItem,Box,Chip,Accordion,AccordionSummary,AccordionDetails,FormControlLabel,Checkbox,CircularProgress,FormHelperText,InputAdornment,Paper,Avatar,Alert,Card,CardMedia,CardContent,CardActions,Divider,Dialog,DialogActions,DialogContent,DialogContentText,DialogTitle}from'@mui/material';import CloudUploadIcon from'@mui/icons-material/CloudUpload';import CropImageDialog from'../common/CropImageDialog';import ExpandMoreIcon from'@mui/icons-material/ExpandMore';import VideoFileIcon from'@mui/icons-material/VideoFile';import LinkIcon from'@mui/icons-material/Link';import OndemandVideoIcon from'@mui/icons-material/OndemandVideo';import CheckCircleIcon from'@mui/icons-material/CheckCircle';import CancelIcon from'@mui/icons-material/Cancel';// Función para calcular lo que recibirá el profesor después de la comisión\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const calculateTeacherEarnings=price=>{// Convertir a número para asegurarnos\nconst lessonPrice=Number(price);// Si el precio no es válido, devolver 0\nif(!lessonPrice||lessonPrice<3)return 0;// Calcular la comisión según el precio\nlet commissionRate;if(lessonPrice===3){commissionRate=0.333;// 33.3%\n}else if(lessonPrice===4){commissionRate=0.25;// 25%\n}else if(lessonPrice===5){commissionRate=0.20;// 20%\n}else if(lessonPrice===6){commissionRate=0.167;// 16.7%\n}else{commissionRate=0.15;// 15% para $7 o más\n}// Calcular lo que recibe el profesor\nconst teacherEarnings=lessonPrice*(1-commissionRate);return teacherEarnings;};const ApplicationForm=_ref=>{let{onSubmit,isEdit=false}=_ref;const{t}=useTranslation();const navigate=useNavigate();const{currentUser,updateUser}=useAuth();const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[fieldErrors,setFieldErrors]=useState({});const[success,setSuccess]=useState(false);const[categories,setCategories]=useState([]);const[languages,setLanguages]=useState([]);// Crop dialog state\nconst[cropDialogOpen,setCropDialogOpen]=useState(false);const[tempImageSrc,setTempImageSrc]=useState('');const[formData,setFormData]=useState({country:'',residence:'',nativeLanguage:'',teachingLanguages:[],courseTypes:[],qualifications:'',teachingExperience:0,introVideoUrl:'',cv:'',profilePicture:null,profilePicturePreview:'',commitmentAccepted:false,availableHours:{monday:[],tuesday:[],wednesday:[],thursday:[],friday:[],saturday:[],sunday:[]},pricePerLesson:'',trialLessonPrice:'',timezone:'',phone:''});// Estado para el diálogo de compromiso\nconst[commitmentDialogOpen,setCommitmentDialogOpen]=useState(false);const timeSlots=[{key:\"00:00-01:00\",label:t('hours.hour1')},{key:\"01:00-02:00\",label:t('hours.hour2')},{key:\"02:00-03:00\",label:t('hours.hour3')},{key:\"03:00-04:00\",label:t('hours.hour4')},{key:\"04:00-05:00\",label:t('hours.hour5')},{key:\"05:00-06:00\",label:t('hours.hour6')},{key:\"06:00-07:00\",label:t('hours.hour7')},{key:\"07:00-08:00\",label:t('hours.hour8')},{key:\"08:00-09:00\",label:t('hours.hour9')},{key:\"09:00-10:00\",label:t('hours.hour10')},{key:\"10:00-11:00\",label:t('hours.hour11')},{key:\"11:00-12:00\",label:t('hours.hour12')},{key:\"12:00-13:00\",label:t('hours.hour13')},{key:\"13:00-14:00\",label:t('hours.hour14')},{key:\"14:00-15:00\",label:t('hours.hour15')},{key:\"15:00-16:00\",label:t('hours.hour16')},{key:\"16:00-17:00\",label:t('hours.hour17')},{key:\"17:00-18:00\",label:t('hours.hour18')},{key:\"18:00-19:00\",label:t('hours.hour19')},{key:\"19:00-20:00\",label:t('hours.hour20')},{key:\"20:00-21:00\",label:t('hours.hour21')},{key:\"21:00-22:00\",label:t('hours.hour22')},{key:\"22:00-23:00\",label:t('hours.hour23')},{key:\"23:00-00:00\",label:t('hours.hour24')}];const days={monday:t('days.monday'),tuesday:t('days.tuesday'),wednesday:t('days.wednesday'),thursday:t('days.thursday'),friday:t('days.friday'),saturday:t('days.saturday'),sunday:t('days.sunday')};// Load saved form data from localStorage\nuseEffect(()=>{const loadSavedFormData=()=>{try{const savedData=localStorage.getItem('teacherApplicationForm');if(savedData){const parsedData=JSON.parse(savedData);// Merge with default values to ensure all fields exist\n// Registrar los datos cargados\nconsole.log('Loading saved form data:',parsedData);setFormData(prevData=>({...prevData,...parsedData,// Keep these fields as they are\nprofilePicture:prevData.profilePicture,commitmentDocument:prevData.commitmentDocument,// Mantener el objeto File\nprofilePicturePreview:parsedData.profilePicturePreview||prevData.profilePicturePreview}));}// Load video URL for display\nconst savedVideoUrl=localStorage.getItem('teacherVideoUrl');// Load relative video path for form submission\nconst savedVideoRelativePath=localStorage.getItem('teacherVideoRelativePath');if(savedVideoUrl){setFormData(prevData=>({...prevData,// Use full URL for display\nintroVideoUrl:savedVideoUrl,// Use relative path for form submission\nintroVideoRelativePath:savedVideoRelativePath}));}}catch(error){console.error('Error loading saved form data:',error);}};loadSavedFormData();},[currentUser]);// Save form data to localStorage whenever it changes\nuseEffect(()=>{const saveFormData=()=>{try{// Create a copy without file objects which can't be serialized\nconst dataToSave={...formData,profilePicture:null,commitmentDocument:null// No podemos serializar objetos File\n};localStorage.setItem('teacherApplicationForm',JSON.stringify(dataToSave));}catch(error){console.error('Error saving form data:',error);}};// Don't save if form is empty (initial load)\nif(formData.country||formData.residence||formData.teachingLanguages.length>0){saveFormData();}},[formData]);useEffect(()=>{const fetchData=async()=>{try{const[categoriesResponse,languagesResponse]=await Promise.all([axios.get('/api/teacher/categories'),axios.get('/api/teacher/languages')]);setCategories(categoriesResponse.data);setLanguages(languagesResponse.data);}catch(err){console.error('Error fetching data:',err);setError(t('teacher.errorFetchingData'));}};fetchData();},[t]);const handleChange=e=>{const{name,value}=e.target;// Limit teachingExperience field to two numeric digits (0-99)\nif(name==='teachingExperience'){let numeric=String(value).replace(/\\D/g,'');// keep digits only\nif(numeric.length>2){numeric=numeric.slice(0,2);}setFormData(prev=>({...prev,[name]:numeric}));return;}// Update form data first\nconst newFormData={...formData,[name]:value};setFormData(newFormData);// Real-time validation for price fields\nif(name==='pricePerLesson'||name==='trialLessonPrice'){const pricePerLesson=name==='pricePerLesson'?value:newFormData.pricePerLesson;const trialLessonPrice=name==='trialLessonPrice'?value:newFormData.trialLessonPrice;// Clear any existing price-related errors first\nsetFieldErrors(prev=>({...prev,trialLessonPrice:''}));// Validate trial lesson price if both prices are provided\nif(pricePerLesson&&trialLessonPrice){const regularPrice=Number(pricePerLesson);const trialPrice=Number(trialLessonPrice);if(trialPrice>regularPrice){setFieldErrors(prev=>({...prev,trialLessonPrice:t('teacher.trialPriceLessThanRegular')||'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي'}));}}}else{// For non-price fields, just clear any existing error for that field\nsetFieldErrors(prev=>({...prev,[name]:''}));}};const handleLanguageChange=e=>{const{value}=e.target;setFormData(prev=>({...prev,teachingLanguages:value}));};const handleProfilePictureChange=e=>{const file=e.target.files[0];if(file){const url=URL.createObjectURL(file);setTempImageSrc(url);setCropDialogOpen(true);}};// Manejar la apertura del diálogo de compromiso\nconst handleOpenCommitmentDialog=()=>{setCommitmentDialogOpen(true);};// Manejar el cierre del diálogo de compromiso\nconst handleCloseCommitmentDialog=()=>{setCommitmentDialogOpen(false);};// Manejar la aceptación del compromiso\nconst handleAcceptCommitment=()=>{setFormData(prev=>({...prev,commitmentAccepted:true}));// Guardar en localStorage\nconst currentFormData=JSON.parse(localStorage.getItem('teacherApplicationForm')||'{}');localStorage.setItem('teacherApplicationForm',JSON.stringify({...currentFormData,commitmentAccepted:true}));// Limpiar errores\nsetFieldErrors(prev=>({...prev,commitmentAccepted:null}));// Cerrar el diálogo\nsetCommitmentDialogOpen(false);};// Manejar el rechazo del compromiso\nconst handleRejectCommitment=()=>{setFormData(prev=>({...prev,commitmentAccepted:false}));// Guardar en localStorage\nconst currentFormData=JSON.parse(localStorage.getItem('teacherApplicationForm')||'{}');localStorage.setItem('teacherApplicationForm',JSON.stringify({...currentFormData,commitmentAccepted:false}));// Cerrar el diálogo\nsetCommitmentDialogOpen(false);};// Video handling functions removed as we're using a separate page for video upload\n// Removed handleTimeSlotChange and handleSelectAllDay functions\n// These are now handled in the AvailableHours component\nconst handleSubmit=async e=>{e.preventDefault();setError('');setFieldErrors({});setLoading(true);// Validate form fields\nconst errors={};const requiredFields=['country','residence','nativeLanguage','teachingLanguages','courseTypes','qualifications','teachingExperience','cv','pricePerLesson','trialLessonPrice','timezone'];// Check required fields\n// Custom validation for trial lesson price being less than or equal to regular price\nif(formData.trialLessonPrice&&formData.pricePerLesson&&Number(formData.trialLessonPrice)>Number(formData.pricePerLesson)){errors.trialLessonPrice=t('teacher.trialPriceLessThanRegular')||'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي';}requiredFields.forEach(field=>{if(!formData[field]||Array.isArray(formData[field])&&formData[field].length===0||formData[field]===''){errors[field]=t('teacher.required');}});// Check video requirement\nif(!formData.introVideoUrl){errors.introVideoUrl=t('teacher.required');}// Check commitment acceptance\nif(!formData.commitmentAccepted){errors.commitmentAccepted=t('teacher.commitmentRequired')||'يجب الموافقة على التعهد';}// Check profile picture requirement\nif(!formData.profilePicture){errors.profilePicture=t('teacher.profilePictureRequired')||'الصورة الشخصية مطلوبة';}// If there are validation errors, stop submission\nif(Object.keys(errors).length>0){setFieldErrors(errors);setError(t('teacher.formHasErrors'));setLoading(false);return;}try{const data=new FormData();// Log form data before submission\nconsole.log('Form data before submission:',formData);// Check if this is an application update (from localStorage)\nconst isApplicationUpdate=localStorage.getItem('isApplicationUpdate')==='true';if(isApplicationUpdate){console.log('This is an application update - setting updateApplication flag');data.append('updateApplication','true');// NO eliminar la bandera aquí, se eliminará después de enviar el formulario\n}Object.keys(formData).forEach(key=>{if(key==='profilePicture'&&formData[key]){data.append(key,formData[key]);}else if(key==='commitmentAccepted'){// Agregar el estado de aceptación del compromiso\ndata.append(key,formData[key]);}else if(key==='introVideoUrl'){// Skip the full URL version - we'll use the relative path instead\n}else if(key==='introVideoRelativePath'){// Use the relative path for the database\ndata.append('introVideoUrl',formData[key]);}else if(!['profilePicturePreview'].includes(key)){let value=formData[key];// Handle arrays and objects\nif(Array.isArray(value)||typeof value==='object'&&value!==null&&!(value instanceof File)){value=JSON.stringify(value);}// Convert empty strings to null for optional fields\nif(value===''){value=null;}// Only append if value is not null/undefined\nif(value!==undefined&&value!==null){data.append(key,value);}}});// Log FormData contents\nfor(let pair of data.entries()){console.log('FormData entry:',pair[0],pair[1]);}await onSubmit(data);setSuccess(true);// Clear localStorage after successful submission\nlocalStorage.removeItem('teacherApplicationForm');localStorage.removeItem('teacherVideoUrl');localStorage.removeItem('teacherVideoRelativePath');localStorage.removeItem('isApplicationUpdate');// Restaurar el rol original si existe\nconst originalRole=localStorage.getItem('originalTeacherRole');if(originalRole&&updateUser&&currentUser){console.log('Restoring original role:',originalRole);updateUser({...currentUser,role:originalRole});localStorage.removeItem('originalTeacherRole');}// Navigate to appropriate page after successful submission\nsetTimeout(()=>{if(isEdit){navigate('/teacher/profile');}else{// For new applications, don't navigate - let the parent component handle the state\nconsole.log('Application submitted successfully, staying on page');}},2000);}catch(err){console.error('Form submission error:',err);setError(err.message||t('common.errorOccurred'));setSuccess(false);}finally{setLoading(false);}};if(loading&&!categories.length){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"200px\",children:/*#__PURE__*/_jsx(CircularProgress,{})});}return/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:4},children:[error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:2},children:error}),success&&/*#__PURE__*/_jsx(Alert,{severity:\"success\",sx:{mb:2},children:t('teacher.applicationSuccess')}),/*#__PURE__*/_jsxs(\"form\",{onSubmit:handleSubmit,children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:isEdit?t('teacher.editApplication.formTitle')||'تعديل بيانات التقديم':t('teacher.application.title')})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,name:\"country\",label:t('teacher.country'),value:formData.country,onChange:handleChange,error:!!fieldErrors.country,helperText:fieldErrors.country||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,name:\"residence\",label:t('teacher.residence'),value:formData.residence,onChange:handleChange,error:!!fieldErrors.residence,helperText:fieldErrors.residence||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,name:\"nativeLanguage\",label:t('teacher.nativeLanguage'),value:formData.nativeLanguage,onChange:handleChange,error:!!fieldErrors.nativeLanguage,helperText:fieldErrors.nativeLanguage||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,error:!!fieldErrors.teachingLanguages,children:[/*#__PURE__*/_jsx(InputLabel,{children:t('teacher.teachingLanguages')}),/*#__PURE__*/_jsx(Select,{multiple:true,name:\"teachingLanguages\",value:formData.teachingLanguages,onChange:handleLanguageChange,renderValue:selected=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:0.5},children:selected.map(value=>{var _languages$find;return/*#__PURE__*/_jsx(Chip,{label:((_languages$find=languages.find(lang=>lang.id===value))===null||_languages$find===void 0?void 0:_languages$find.name)||value},value);})}),children:languages.map(lang=>/*#__PURE__*/_jsx(MenuItem,{value:lang.id,children:lang.name},lang.id))}),fieldErrors.teachingLanguages&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:fieldErrors.teachingLanguages})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,error:!!fieldErrors.courseTypes,children:[/*#__PURE__*/_jsx(InputLabel,{children:t('teacher.courseTypes')}),/*#__PURE__*/_jsx(Select,{multiple:true,name:\"courseTypes\",value:formData.courseTypes,onChange:handleChange,renderValue:selected=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:0.5},children:selected.map(value=>{var _categories$find;return/*#__PURE__*/_jsx(Chip,{label:((_categories$find=categories.find(cat=>cat.id===value))===null||_categories$find===void 0?void 0:_categories$find.name)||value},value);})}),children:categories.map(category=>/*#__PURE__*/_jsx(MenuItem,{value:category.id,children:category.name},category.id))}),fieldErrors.courseTypes&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:fieldErrors.courseTypes})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,multiline:true,rows:4,name:\"qualifications\",label:t('teacher.qualifications'),placeholder:t('teacher.qualificationsPlaceholder'),value:formData.qualifications,onChange:handleChange,error:!!fieldErrors.qualifications,helperText:fieldErrors.qualifications||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,type:\"number\",name:\"teachingExperience\",label:t('teacher.teachingExperience'),value:formData.teachingExperience,onChange:handleChange,error:!!fieldErrors.teachingExperience,helperText:fieldErrors.teachingExperience||'',inputProps:{min:0,max:99}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',justifyContent:'space-between',mb:1},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:t('teacher.introVideo')})}),formData.introVideoUrl?/*#__PURE__*/_jsxs(Card,{sx:{mb:2},children:[/*#__PURE__*/_jsx(CardMedia,{component:\"video\",controls:true,src:formData.introVideoUrl,sx:{height:300}}),/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('teacher.videoReady')})}),/*#__PURE__*/_jsx(CardActions,{children:/*#__PURE__*/_jsx(Button,{size:\"small\",color:\"error\",onClick:()=>{localStorage.removeItem('teacherVideoUrl');localStorage.removeItem('teacherVideoRelativePath');setFormData(prev=>({...prev,introVideoUrl:'',introVideoRelativePath:''}));},children:t('teacher.deleteVideo')})})]}):/*#__PURE__*/_jsx(Box,{sx:{p:2,border:1,borderColor:'divider',borderRadius:1},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:fieldErrors.introVideoUrl?\"error\":\"textSecondary\",children:t('teacher.videoRequired')}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(OndemandVideoIcon,{}),onClick:()=>navigate('/teacher/upload-video'),children:t('teacher.uploadVideoNow')}),fieldErrors.introVideoUrl&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:fieldErrors.introVideoUrl}),/*#__PURE__*/_jsxs(Box,{sx:{mt:1,p:1,bgcolor:'background.paper',borderRadius:1,border:1,borderColor:'divider',width:'100%'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{fontWeight:'bold',mb:0.5},children:[t('teacher.allowedFormats'),\":\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:t('teacher.videoFormats')}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{fontWeight:'bold',mt:1,mb:0.5},children:[t('teacher.maxFileSize'),\":\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"textSecondary\",children:t('teacher.maxVideoSize')})]})]})})]})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,multiline:true,rows:8,name:\"cv\",label:t('teacher.cv'),placeholder:t('teacher.cvPlaceholder'),value:formData.cv,onChange:handleChange,error:!!fieldErrors.cv,helperText:fieldErrors.cv?fieldErrors.cv:t('teacher.cvHelperText'),inputProps:{maxLength:2000}}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",color:\"textSecondary\",sx:{mt:1,display:'block'},children:[formData.cv.length,\"/2000 \",t('teacher.characters')]})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:1,sx:{p:3,mb:2,border:'1px dashed',borderColor:fieldErrors.commitmentAccepted?'error.main':'primary.main'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:t('teacher.commitment')||'تعهد المعلم'}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",paragraph:true,children:t('teacher.commitmentDescription')||'يجب قراءة التعهد والموافقة عليه للاستمرار في عملية التقديم.'}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:2,mb:2},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:formData.commitmentAccepted?\"success\":\"primary\",onClick:handleOpenCommitmentDialog,startIcon:formData.commitmentAccepted?/*#__PURE__*/_jsx(CheckCircleIcon,{}):null,sx:{mb:1},children:formData.commitmentAccepted?t('teacher.commitmentAccepted')||'تم الموافقة على التعهد':t('teacher.readCommitment')||'قراءة التعهد والموافقة عليه'}),formData.commitmentAccepted!==undefined&&/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',p:1,borderRadius:1,bgcolor:formData.commitmentAccepted?'success.light':'error.light',color:'white'},children:formData.commitmentAccepted?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(CheckCircleIcon,{sx:{mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:t('teacher.commitmentStatus.accepted')||'لقد وافقت على التعهد'})]}):/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(CancelIcon,{sx:{mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:t('teacher.commitmentStatus.rejected')||'لم توافق على التعهد بعد'})]})})]}),fieldErrors.commitmentAccepted&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",sx:{mt:1},children:fieldErrors.commitmentAccepted})]})}),/*#__PURE__*/_jsxs(Dialog,{open:commitmentDialogOpen,onClose:handleCloseCommitmentDialog,scroll:\"paper\",maxWidth:\"md\",fullWidth:true,dir:\"rtl\",children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{bgcolor:'primary.main',color:'white',textAlign:'center',py:2},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"div\",sx:{fontWeight:'bold'},children:t('teacher.commitmentTitle')||'تعهّد المدرّسين في منصة \"علّمني أون لاين بجميع اللغات\"'})}),/*#__PURE__*/_jsx(DialogContent,{dividers:true,sx:{px:4,py:3},children:/*#__PURE__*/_jsxs(DialogContentText,{component:\"div\",children:[/*#__PURE__*/_jsx(Typography,{paragraph:true,sx:{fontSize:'1.1rem',fontWeight:'bold',mb:3},children:t('teacher.commitmentText.intro')}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",sx:{minWidth:'24px'},children:\"1.\"}),/*#__PURE__*/_jsx(Typography,{paragraph:true,sx:{m:0},children:t('teacher.commitmentText.point1')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",sx:{minWidth:'24px'},children:\"2.\"}),/*#__PURE__*/_jsx(Typography,{paragraph:true,sx:{m:0},children:t('teacher.commitmentText.point2')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",sx:{minWidth:'24px'},children:\"3.\"}),/*#__PURE__*/_jsx(Typography,{paragraph:true,sx:{m:0},children:t('teacher.commitmentText.point3')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",sx:{minWidth:'24px'},children:\"4.\"}),/*#__PURE__*/_jsx(Typography,{paragraph:true,sx:{m:0},children:t('teacher.commitmentText.point4')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",sx:{minWidth:'24px'},children:\"5.\"}),/*#__PURE__*/_jsx(Typography,{paragraph:true,sx:{m:0},children:t('teacher.commitmentText.point5')})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary\",sx:{minWidth:'24px'},children:\"6.\"}),/*#__PURE__*/_jsx(Typography,{paragraph:true,sx:{m:0},children:t('teacher.commitmentText.point6')})]})]}),/*#__PURE__*/_jsx(Box,{sx:{mt:3,p:2,bgcolor:'background.paper',borderRadius:1,border:'1px solid',borderColor:'divider'},children:/*#__PURE__*/_jsx(Typography,{paragraph:true,sx:{m:0,fontWeight:'bold'},children:t('teacher.commitmentText.conclusion')})})]})}),/*#__PURE__*/_jsxs(DialogActions,{sx:{justifyContent:'center',p:3,gap:3},children:[/*#__PURE__*/_jsx(Button,{onClick:handleRejectCommitment,color:\"error\",variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),sx:{minWidth:150,py:1},size:\"large\",children:t('teacher.reject')||'أرفض التعهد'}),/*#__PURE__*/_jsx(Button,{onClick:handleAcceptCommitment,color:\"success\",variant:\"contained\",startIcon:/*#__PURE__*/_jsx(CheckCircleIcon,{}),sx:{minWidth:150,py:1},size:\"large\",children:t('teacher.accept')||'أوافق على التعهد'})]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(\"input\",{type:\"file\",accept:\"image/*\",style:{display:'none'},id:\"profile-picture-upload\",onChange:handleProfilePictureChange}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,error:!!fieldErrors.profilePicture,children:[/*#__PURE__*/_jsx(InputLabel,{shrink:true,required:true,children:t('teacher.profilePicture')}),/*#__PURE__*/_jsx(Box,{sx:{mt:1},children:/*#__PURE__*/_jsx(\"label\",{htmlFor:\"profile-picture-upload\",children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",component:\"span\",fullWidth:true,startIcon:/*#__PURE__*/_jsx(CloudUploadIcon,{}),children:formData.profilePicture?formData.profilePicture.name:t('teacher.profilePicturePlaceholder')})})}),formData.profilePicturePreview&&/*#__PURE__*/_jsx(Box,{sx:{mt:2,display:'flex',justifyContent:'center'},children:/*#__PURE__*/_jsx(Avatar,{src:formData.profilePicturePreview,alt:\"Profile preview\",sx:{width:120,height:120}})}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:fieldErrors.profilePicture?\"error\":\"textSecondary\",children:fieldErrors.profilePicture||t('teacher.fileTypes.image')})]})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Box,{sx:{border:1,borderColor:'divider',borderRadius:1,p:2,mb:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:t('teacher.availableHours')}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:()=>{// Guardar los datos actuales del formulario antes de navegar\nconst dataToSave={...formData,profilePicture:null};localStorage.setItem('teacherApplicationForm',JSON.stringify(dataToSave));// Redirigir a la página correcta según el rol del usuario\nif((currentUser===null||currentUser===void 0?void 0:currentUser.role)==='platform_teacher'){navigate('/teacher/manage-hours');}else{navigate('/teacher/available-hours');}},children:t('teacher.manageAvailableHours')})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('teacher.availableHoursDescription')}),/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[Object.entries(formData.availableHours).map(_ref2=>{let[day,slots]=_ref2;return slots.length>0&&/*#__PURE__*/_jsx(Box,{sx:{mb:1},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{fontWeight:'bold'},children:[days[day],\": \",/*#__PURE__*/_jsxs(\"span\",{style:{fontWeight:'normal'},children:[slots.length,\" \",t('teacher.timeSlots')]})]})},day);}),Object.values(formData.availableHours).every(slots=>slots.length===0)&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:t('teacher.noAvailableHours')})]})]})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,type:\"number\",name:\"pricePerLesson\",label:t('teacher.pricePerLesson'),placeholder:t('teacher.pricePerLessonPlaceholder'),value:formData.pricePerLesson,onChange:handleChange,error:!!fieldErrors.pricePerLesson,helperText:fieldErrors.pricePerLesson||'',InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"$\"})},inputProps:{min:3,max:100}}),formData.pricePerLesson>0&&/*#__PURE__*/_jsxs(Box,{sx:{mt:2,p:2,bgcolor:'background.paper',border:'1px dashed',borderColor:'primary.main',borderRadius:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"primary\",gutterBottom:true,children:t('teacher.yourEarnings')||'ما ستحصل عليه بعد خصم العمولة:'}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{fontWeight:'bold',color:'success.main'},children:[\"$\",calculateTeacherEarnings(formData.pricePerLesson).toFixed(2)]})]})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,type:\"number\",name:\"trialLessonPrice\",label:t('teacher.trialLessonPrice')||'سعر الدرس التجريبي',placeholder:t('teacher.trialLessonPricePlaceholder')||'أدخل سعر الدرس التجريبي',value:formData.trialLessonPrice,onChange:handleChange,error:!!fieldErrors.trialLessonPrice,helperText:fieldErrors.trialLessonPrice||'',InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"$\"})},inputProps:{min:1,max:100}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,error:!!fieldErrors.timezone,children:[/*#__PURE__*/_jsx(InputLabel,{children:t('teacher.timezone')}),/*#__PURE__*/_jsx(Select,{name:\"timezone\",value:formData.timezone,onChange:handleChange,children:timezones.map(timezone=>/*#__PURE__*/_jsx(MenuItem,{value:timezone.value,children:timezone.label},timezone.value))}),fieldErrors.timezone&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:fieldErrors.timezone})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,name:\"phone\",label:t('teacher.phone'),value:formData.phone,onChange:handleChange,error:!!fieldErrors.phone,helperText:fieldErrors.phone||t('teacher.phoneHelp'),placeholder:t('teacher.phoneOptional')||'اختياري'})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",color:\"primary\",size:\"large\",fullWidth:true,disabled:loading,children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):isEdit?t('teacher.editApplication.submitButton')||'حفظ التعديلات':t('teacher.application.submit')})})]}),/*#__PURE__*/_jsx(CropImageDialog,{open:cropDialogOpen,imageSrc:tempImageSrc,onClose:()=>setCropDialogOpen(false),onSave:blob=>{const file=new File([blob],'profile.jpg',{type:blob.type});setFormData(prev=>({...prev,profilePicture:file,profilePicturePreview:URL.createObjectURL(blob)}));setCropDialogOpen(false);}})]})]});};export default ApplicationForm;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "useAuth", "axios", "timezones", "<PERSON><PERSON>", "TextField", "Grid", "Typography", "FormControl", "InputLabel", "Select", "MenuItem", "Box", "Chip", "Accordion", "AccordionSummary", "AccordionDetails", "FormControlLabel", "Checkbox", "CircularProgress", "FormHelperText", "InputAdornment", "Paper", "Avatar", "<PERSON><PERSON>", "Card", "CardMedia", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Divider", "Dialog", "DialogActions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogContentText", "DialogTitle", "CloudUploadIcon", "CropImageDialog", "ExpandMoreIcon", "VideoFileIcon", "LinkIcon", "OndemandVideoIcon", "CheckCircleIcon", "CancelIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "calculateTeacherEarnings", "price", "lessonPrice", "Number", "commissionRate", "teacherEarnings", "ApplicationForm", "_ref", "onSubmit", "isEdit", "t", "navigate", "currentUser", "updateUser", "loading", "setLoading", "error", "setError", "fieldErrors", "setFieldErrors", "success", "setSuccess", "categories", "setCategories", "languages", "setLanguages", "cropDialogOpen", "setCropDialogOpen", "tempImageSrc", "setTempImageSrc", "formData", "setFormData", "country", "residence", "nativeLanguage", "teachingLanguages", "courseTypes", "qualifications", "teachingExperience", "introVideoUrl", "cv", "profilePicture", "profilePicturePreview", "commitmentAccepted", "availableHours", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday", "pricePer<PERSON><PERSON>on", "trialLessonPrice", "timezone", "phone", "commitmentDialogOpen", "setCommitmentDialogOpen", "timeSlots", "key", "label", "days", "loadSavedFormData", "savedData", "localStorage", "getItem", "parsedData", "JSON", "parse", "console", "log", "prevData", "commitmentDocument", "savedVideoUrl", "savedVideoRelativePath", "introVideoRelativePath", "saveFormData", "dataToSave", "setItem", "stringify", "length", "fetchData", "categoriesResponse", "languagesResponse", "Promise", "all", "get", "data", "err", "handleChange", "e", "name", "value", "target", "numeric", "String", "replace", "slice", "prev", "newFormData", "regularPrice", "trialPrice", "handleLanguageChange", "handleProfilePictureChange", "file", "files", "url", "URL", "createObjectURL", "handleOpenCommitmentDialog", "handleCloseCommitmentDialog", "handleAcceptCommitment", "currentFormData", "handleRejectCommitment", "handleSubmit", "preventDefault", "errors", "requiredFields", "for<PERSON>ach", "field", "Array", "isArray", "Object", "keys", "FormData", "isApplicationUpdate", "append", "includes", "File", "undefined", "pair", "entries", "removeItem", "originalRole", "role", "setTimeout", "message", "display", "justifyContent", "alignItems", "minHeight", "children", "elevation", "sx", "p", "severity", "mb", "container", "spacing", "item", "xs", "variant", "component", "gutterBottom", "md", "required", "fullWidth", "onChange", "helperText", "multiple", "renderValue", "selected", "flexWrap", "gap", "map", "_languages$find", "find", "lang", "id", "color", "_categories$find", "cat", "category", "multiline", "rows", "placeholder", "type", "inputProps", "min", "max", "controls", "src", "height", "size", "onClick", "border", "borderColor", "borderRadius", "flexDirection", "startIcon", "mt", "bgcolor", "width", "fontWeight", "max<PERSON><PERSON><PERSON>", "paragraph", "mr", "open", "onClose", "scroll", "max<PERSON><PERSON><PERSON>", "dir", "textAlign", "py", "dividers", "px", "fontSize", "min<PERSON><PERSON><PERSON>", "m", "accept", "style", "shrink", "htmlFor", "alt", "_ref2", "day", "slots", "values", "every", "InputProps", "startAdornment", "position", "toFixed", "disabled", "imageSrc", "onSave", "blob"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/components/teacher/ApplicationForm.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport axios from '../../utils/axios';\nimport { timezones } from '../../utils/constants';\nimport {\n  Button,\n  TextField,\n  Grid,\n  Typography,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Box,\n  Chip,\n  Accordion,\n  AccordionSummary,\n  AccordionDetails,\n  FormControlLabel,\n  Checkbox,\n  CircularProgress,\n  FormHelperText,\n  InputAdornment,\n  Paper,\n  Avatar,\n\n  Alert,\n  Card,\n  CardMedia,\n  CardContent,\n  CardActions,\n  Divider,\n  Dialog,\n  DialogActions,\n  DialogContent,\n  DialogContentText,\n  DialogTitle\n} from '@mui/material';\nimport CloudUploadIcon from '@mui/icons-material/CloudUpload';\nimport CropImageDialog from '../common/CropImageDialog';\nimport ExpandMoreIcon from '@mui/icons-material/ExpandMore';\nimport VideoFileIcon from '@mui/icons-material/VideoFile';\nimport LinkIcon from '@mui/icons-material/Link';\nimport OndemandVideoIcon from '@mui/icons-material/OndemandVideo';\nimport CheckCircleIcon from '@mui/icons-material/CheckCircle';\nimport CancelIcon from '@mui/icons-material/Cancel';\n\n// Función para calcular lo que recibirá el profesor después de la comisión\nconst calculateTeacherEarnings = (price) => {\n  // Convertir a número para asegurarnos\n  const lessonPrice = Number(price);\n\n  // Si el precio no es válido, devolver 0\n  if (!lessonPrice || lessonPrice < 3) return 0;\n\n  // Calcular la comisión según el precio\n  let commissionRate;\n  if (lessonPrice === 3) {\n    commissionRate = 0.333; // 33.3%\n  } else if (lessonPrice === 4) {\n    commissionRate = 0.25; // 25%\n  } else if (lessonPrice === 5) {\n    commissionRate = 0.20; // 20%\n  } else if (lessonPrice === 6) {\n    commissionRate = 0.167; // 16.7%\n  } else {\n    commissionRate = 0.15; // 15% para $7 o más\n  }\n\n  // Calcular lo que recibe el profesor\n  const teacherEarnings = lessonPrice * (1 - commissionRate);\n  return teacherEarnings;\n};\n\nconst ApplicationForm = ({ onSubmit, isEdit = false }) => {\n  const { t } = useTranslation();\n  const navigate = useNavigate();\n  const { currentUser, updateUser } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [fieldErrors, setFieldErrors] = useState({});\n  const [success, setSuccess] = useState(false);\n  const [categories, setCategories] = useState([]);\n  const [languages, setLanguages] = useState([]);\n  // Crop dialog state\n  const [cropDialogOpen, setCropDialogOpen] = useState(false);\n  const [tempImageSrc, setTempImageSrc] = useState('');\n\n  const [formData, setFormData] = useState({\n    country: '',\n    residence: '',\n    nativeLanguage: '',\n    teachingLanguages: [],\n    courseTypes: [],\n    qualifications: '',\n    teachingExperience: 0,\n    introVideoUrl: '',\n    cv: '',\n    profilePicture: null,\n    profilePicturePreview: '',\n    commitmentAccepted: false,\n    availableHours: {\n      monday: [],\n      tuesday: [],\n      wednesday: [],\n      thursday: [],\n      friday: [],\n      saturday: [],\n      sunday: []\n    },\n    pricePerLesson: '',\n    trialLessonPrice: '',\n    timezone: '',\n\n    phone: '',\n  });\n\n  // Estado para el diálogo de compromiso\n  const [commitmentDialogOpen, setCommitmentDialogOpen] = useState(false);\n\n\n\n\n\n  const timeSlots = [\n    { key: \"00:00-01:00\", label: t('hours.hour1') },\n    { key: \"01:00-02:00\", label: t('hours.hour2') },\n    { key: \"02:00-03:00\", label: t('hours.hour3') },\n    { key: \"03:00-04:00\", label: t('hours.hour4') },\n    { key: \"04:00-05:00\", label: t('hours.hour5') },\n    { key: \"05:00-06:00\", label: t('hours.hour6') },\n    { key: \"06:00-07:00\", label: t('hours.hour7') },\n    { key: \"07:00-08:00\", label: t('hours.hour8') },\n    { key: \"08:00-09:00\", label: t('hours.hour9') },\n    { key: \"09:00-10:00\", label: t('hours.hour10') },\n    { key: \"10:00-11:00\", label: t('hours.hour11') },\n    { key: \"11:00-12:00\", label: t('hours.hour12') },\n    { key: \"12:00-13:00\", label: t('hours.hour13') },\n    { key: \"13:00-14:00\", label: t('hours.hour14') },\n    { key: \"14:00-15:00\", label: t('hours.hour15') },\n    { key: \"15:00-16:00\", label: t('hours.hour16') },\n    { key: \"16:00-17:00\", label: t('hours.hour17') },\n    { key: \"17:00-18:00\", label: t('hours.hour18') },\n    { key: \"18:00-19:00\", label: t('hours.hour19') },\n    { key: \"19:00-20:00\", label: t('hours.hour20') },\n    { key: \"20:00-21:00\", label: t('hours.hour21') },\n    { key: \"21:00-22:00\", label: t('hours.hour22') },\n    { key: \"22:00-23:00\", label: t('hours.hour23') },\n    { key: \"23:00-00:00\", label: t('hours.hour24') }\n  ];\n\n  const days = {\n    monday: t('days.monday'),\n    tuesday: t('days.tuesday'),\n    wednesday: t('days.wednesday'),\n    thursday: t('days.thursday'),\n    friday: t('days.friday'),\n    saturday: t('days.saturday'),\n    sunday: t('days.sunday')\n  };\n\n  // Load saved form data from localStorage\n  useEffect(() => {\n    const loadSavedFormData = () => {\n      try {\n\n\n        const savedData = localStorage.getItem('teacherApplicationForm');\n        if (savedData) {\n          const parsedData = JSON.parse(savedData);\n\n          // Merge with default values to ensure all fields exist\n          // Registrar los datos cargados\n          console.log('Loading saved form data:', parsedData);\n\n          setFormData(prevData => ({\n            ...prevData,\n            ...parsedData,\n            // Keep these fields as they are\n            profilePicture: prevData.profilePicture,\n            commitmentDocument: prevData.commitmentDocument, // Mantener el objeto File\n            profilePicturePreview: parsedData.profilePicturePreview || prevData.profilePicturePreview\n          }));\n        }\n\n        // Load video URL for display\n        const savedVideoUrl = localStorage.getItem('teacherVideoUrl');\n        // Load relative video path for form submission\n        const savedVideoRelativePath = localStorage.getItem('teacherVideoRelativePath');\n\n        if (savedVideoUrl) {\n          setFormData(prevData => ({\n            ...prevData,\n            // Use full URL for display\n            introVideoUrl: savedVideoUrl,\n            // Use relative path for form submission\n            introVideoRelativePath: savedVideoRelativePath\n          }));\n        }\n      } catch (error) {\n        console.error('Error loading saved form data:', error);\n      }\n    };\n\n    loadSavedFormData();\n  }, [currentUser]);\n\n  // Save form data to localStorage whenever it changes\n  useEffect(() => {\n    const saveFormData = () => {\n      try {\n        // Create a copy without file objects which can't be serialized\n        const dataToSave = {\n          ...formData,\n          profilePicture: null,\n          commitmentDocument: null // No podemos serializar objetos File\n        };\n\n        localStorage.setItem('teacherApplicationForm', JSON.stringify(dataToSave));\n      } catch (error) {\n        console.error('Error saving form data:', error);\n      }\n    };\n\n    // Don't save if form is empty (initial load)\n    if (formData.country || formData.residence || formData.teachingLanguages.length > 0) {\n      saveFormData();\n    }\n  }, [formData]);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        const [categoriesResponse, languagesResponse] = await Promise.all([\n          axios.get('/api/teacher/categories'),\n          axios.get('/api/teacher/languages')\n        ]);\n\n        setCategories(categoriesResponse.data);\n        setLanguages(languagesResponse.data);\n      } catch (err) {\n        console.error('Error fetching data:', err);\n        setError(t('teacher.errorFetchingData'));\n      }\n    };\n\n    fetchData();\n  }, [t]);\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n\n    // Limit teachingExperience field to two numeric digits (0-99)\n    if (name === 'teachingExperience') {\n      let numeric = String(value).replace(/\\D/g, ''); // keep digits only\n      if (numeric.length > 2) {\n        numeric = numeric.slice(0, 2);\n      }\n      setFormData(prev => ({ ...prev, [name]: numeric }));\n      return;\n    }\n\n    // Update form data first\n    const newFormData = {\n      ...formData,\n      [name]: value\n    };\n    setFormData(newFormData);\n\n    // Real-time validation for price fields\n    if (name === 'pricePerLesson' || name === 'trialLessonPrice') {\n      const pricePerLesson = name === 'pricePerLesson' ? value : newFormData.pricePerLesson;\n      const trialLessonPrice = name === 'trialLessonPrice' ? value : newFormData.trialLessonPrice;\n\n      // Clear any existing price-related errors first\n      setFieldErrors(prev => ({\n        ...prev,\n        trialLessonPrice: ''\n      }));\n\n      // Validate trial lesson price if both prices are provided\n      if (pricePerLesson && trialLessonPrice) {\n        const regularPrice = Number(pricePerLesson);\n        const trialPrice = Number(trialLessonPrice);\n\n        if (trialPrice > regularPrice) {\n          setFieldErrors(prev => ({\n            ...prev,\n            trialLessonPrice: t('teacher.trialPriceLessThanRegular') || 'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي'\n          }));\n        }\n      }\n    } else {\n      // For non-price fields, just clear any existing error for that field\n      setFieldErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n\n  const handleLanguageChange = (e) => {\n    const { value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      teachingLanguages: value\n    }));\n  };\n\n  const handleProfilePictureChange = (e) => {\n    const file = e.target.files[0];\n    if (file) {\n      const url = URL.createObjectURL(file);\n      setTempImageSrc(url);\n      setCropDialogOpen(true);\n    }\n  };\n\n  // Manejar la apertura del diálogo de compromiso\n  const handleOpenCommitmentDialog = () => {\n    setCommitmentDialogOpen(true);\n  };\n\n  // Manejar el cierre del diálogo de compromiso\n  const handleCloseCommitmentDialog = () => {\n    setCommitmentDialogOpen(false);\n  };\n\n  // Manejar la aceptación del compromiso\n  const handleAcceptCommitment = () => {\n    setFormData(prev => ({\n      ...prev,\n      commitmentAccepted: true\n    }));\n\n    // Guardar en localStorage\n    const currentFormData = JSON.parse(localStorage.getItem('teacherApplicationForm') || '{}');\n    localStorage.setItem('teacherApplicationForm', JSON.stringify({\n      ...currentFormData,\n      commitmentAccepted: true\n    }));\n\n    // Limpiar errores\n    setFieldErrors(prev => ({\n      ...prev,\n      commitmentAccepted: null\n    }));\n\n    // Cerrar el diálogo\n    setCommitmentDialogOpen(false);\n  };\n\n  // Manejar el rechazo del compromiso\n  const handleRejectCommitment = () => {\n    setFormData(prev => ({\n      ...prev,\n      commitmentAccepted: false\n    }));\n\n    // Guardar en localStorage\n    const currentFormData = JSON.parse(localStorage.getItem('teacherApplicationForm') || '{}');\n    localStorage.setItem('teacherApplicationForm', JSON.stringify({\n      ...currentFormData,\n      commitmentAccepted: false\n    }));\n\n    // Cerrar el diálogo\n    setCommitmentDialogOpen(false);\n  };\n\n  // Video handling functions removed as we're using a separate page for video upload\n\n  // Removed handleTimeSlotChange and handleSelectAllDay functions\n  // These are now handled in the AvailableHours component\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setError('');\n    setFieldErrors({});\n    setLoading(true);\n\n    // Validate form fields\n    const errors = {};\n    const requiredFields = [\n      'country', 'residence', 'nativeLanguage', 'teachingLanguages', 'courseTypes',\n      'qualifications', 'teachingExperience', 'cv', 'pricePerLesson', 'trialLessonPrice', 'timezone'\n    ];\n\n    // Check required fields\n  // Custom validation for trial lesson price being less than or equal to regular price\n  if (formData.trialLessonPrice && formData.pricePerLesson && Number(formData.trialLessonPrice) > Number(formData.pricePerLesson)) {\n    errors.trialLessonPrice = t('teacher.trialPriceLessThanRegular') || 'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي';\n  }\n    requiredFields.forEach(field => {\n      if (!formData[field] ||\n          (Array.isArray(formData[field]) && formData[field].length === 0) ||\n          formData[field] === '') {\n        errors[field] = t('teacher.required');\n      }\n    });\n\n    // Check video requirement\n    if (!formData.introVideoUrl) {\n      errors.introVideoUrl = t('teacher.required');\n    }\n\n    // Check commitment acceptance\n    if (!formData.commitmentAccepted) {\n      errors.commitmentAccepted = t('teacher.commitmentRequired') || 'يجب الموافقة على التعهد';\n    }\n\n    // Check profile picture requirement\n    if (!formData.profilePicture) {\n      errors.profilePicture = t('teacher.profilePictureRequired') || 'الصورة الشخصية مطلوبة';\n    }\n\n    // If there are validation errors, stop submission\n    if (Object.keys(errors).length > 0) {\n      setFieldErrors(errors);\n      setError(t('teacher.formHasErrors'));\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const data = new FormData();\n\n      // Log form data before submission\n      console.log('Form data before submission:', formData);\n\n\n\n      // Check if this is an application update (from localStorage)\n      const isApplicationUpdate = localStorage.getItem('isApplicationUpdate') === 'true';\n      if (isApplicationUpdate) {\n        console.log('This is an application update - setting updateApplication flag');\n        data.append('updateApplication', 'true');\n        // NO eliminar la bandera aquí, se eliminará después de enviar el formulario\n      }\n\n      Object.keys(formData).forEach(key => {\n        if (key === 'profilePicture' && formData[key]) {\n          data.append(key, formData[key]);\n        } else if (key === 'commitmentAccepted') {\n          // Agregar el estado de aceptación del compromiso\n          data.append(key, formData[key]);\n        } else if (key === 'introVideoUrl') {\n          // Skip the full URL version - we'll use the relative path instead\n        } else if (key === 'introVideoRelativePath') {\n          // Use the relative path for the database\n          data.append('introVideoUrl', formData[key]);\n        } else if (!['profilePicturePreview'].includes(key)) {\n          let value = formData[key];\n\n          // Handle arrays and objects\n          if (Array.isArray(value) || (typeof value === 'object' && value !== null && !(value instanceof File))) {\n            value = JSON.stringify(value);\n          }\n\n          // Convert empty strings to null for optional fields\n          if (value === '') {\n            value = null;\n          }\n\n          // Only append if value is not null/undefined\n          if (value !== undefined && value !== null) {\n            data.append(key, value);\n          }\n        }\n      });\n\n      // Log FormData contents\n      for (let pair of data.entries()) {\n        console.log('FormData entry:', pair[0], pair[1]);\n      }\n\n      await onSubmit(data);\n      setSuccess(true);\n\n      // Clear localStorage after successful submission\n      localStorage.removeItem('teacherApplicationForm');\n      localStorage.removeItem('teacherVideoUrl');\n      localStorage.removeItem('teacherVideoRelativePath');\n      localStorage.removeItem('isApplicationUpdate');\n\n      // Restaurar el rol original si existe\n      const originalRole = localStorage.getItem('originalTeacherRole');\n      if (originalRole && updateUser && currentUser) {\n        console.log('Restoring original role:', originalRole);\n        updateUser({\n          ...currentUser,\n          role: originalRole\n        });\n        localStorage.removeItem('originalTeacherRole');\n      }\n\n      // Navigate to appropriate page after successful submission\n      setTimeout(() => {\n        if (isEdit) {\n          navigate('/teacher/profile');\n        } else {\n          // For new applications, don't navigate - let the parent component handle the state\n          console.log('Application submitted successfully, staying on page');\n        }\n      }, 2000);\n    } catch (err) {\n      console.error('Form submission error:', err);\n      setError(err.message || t('common.errorOccurred'));\n      setSuccess(false);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading && !categories.length) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"200px\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  return (\n    <Paper elevation={3} sx={{ p: 4 }}>\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      {success && (\n        <Alert severity=\"success\" sx={{ mb: 2 }}>\n          {t('teacher.applicationSuccess')}\n        </Alert>\n      )}\n\n      <form onSubmit={handleSubmit}>\n        <Grid container spacing={3}>\n          <Grid item xs={12}>\n            <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n              {isEdit ? t('teacher.editApplication.formTitle') || 'تعديل بيانات التقديم' : t('teacher.application.title')}\n            </Typography>\n          </Grid>\n\n          {/* Country */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              required\n              fullWidth\n              name=\"country\"\n              label={t('teacher.country')}\n              value={formData.country}\n              onChange={handleChange}\n              error={!!fieldErrors.country}\n              helperText={fieldErrors.country || ''}\n            />\n          </Grid>\n\n          {/* Region of Residence */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              required\n              fullWidth\n              name=\"residence\"\n              label={t('teacher.residence')}\n              value={formData.residence}\n              onChange={handleChange}\n              error={!!fieldErrors.residence}\n              helperText={fieldErrors.residence || ''}\n            />\n          </Grid>\n\n          {/* Native Language */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              required\n              fullWidth\n              name=\"nativeLanguage\"\n              label={t('teacher.nativeLanguage')}\n              value={formData.nativeLanguage}\n              onChange={handleChange}\n              error={!!fieldErrors.nativeLanguage}\n              helperText={fieldErrors.nativeLanguage || ''}\n            />\n          </Grid>\n\n          {/* Teaching Languages */}\n          <Grid item xs={12}>\n            <FormControl fullWidth required error={!!fieldErrors.teachingLanguages}>\n              <InputLabel>{t('teacher.teachingLanguages')}</InputLabel>\n              <Select\n                multiple\n                name=\"teachingLanguages\"\n                value={formData.teachingLanguages}\n                onChange={handleLanguageChange}\n                renderValue={(selected) => (\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                    {selected.map((value) => (\n                      <Chip\n                        key={value}\n                        label={languages.find(lang => lang.id === value)?.name || value}\n                      />\n                    ))}\n                  </Box>\n                )}\n              >\n                {languages.map((lang) => (\n                  <MenuItem key={lang.id} value={lang.id}>\n                    {lang.name}\n                  </MenuItem>\n                ))}\n              </Select>\n              {fieldErrors.teachingLanguages && (\n                <Typography variant=\"body2\" color=\"error\">\n                  {fieldErrors.teachingLanguages}\n                </Typography>\n              )}\n            </FormControl>\n          </Grid>\n\n          {/* Course Types */}\n          <Grid item xs={12}>\n            <FormControl fullWidth required error={!!fieldErrors.courseTypes}>\n              <InputLabel>{t('teacher.courseTypes')}</InputLabel>\n              <Select\n                multiple\n                name=\"courseTypes\"\n                value={formData.courseTypes}\n                onChange={handleChange}\n                renderValue={(selected) => (\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                    {selected.map((value) => (\n                      <Chip\n                        key={value}\n                        label={categories.find(cat => cat.id === value)?.name || value}\n                      />\n                    ))}\n                  </Box>\n                )}\n              >\n                {categories.map((category) => (\n                  <MenuItem key={category.id} value={category.id}>\n                    {category.name}\n                  </MenuItem>\n                ))}\n              </Select>\n              {fieldErrors.courseTypes && (\n                <Typography variant=\"body2\" color=\"error\">\n                  {fieldErrors.courseTypes}\n                </Typography>\n              )}\n            </FormControl>\n          </Grid>\n\n          {/* Qualifications */}\n          <Grid item xs={12}>\n            <TextField\n              required\n              fullWidth\n              multiline\n              rows={4}\n              name=\"qualifications\"\n              label={t('teacher.qualifications')}\n              placeholder={t('teacher.qualificationsPlaceholder')}\n              value={formData.qualifications}\n              onChange={handleChange}\n              error={!!fieldErrors.qualifications}\n              helperText={fieldErrors.qualifications || ''}\n            />\n          </Grid>\n\n          {/* Teaching Experience */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              required\n              fullWidth\n              type=\"number\"\n              name=\"teachingExperience\"\n              label={t('teacher.teachingExperience')}\n              value={formData.teachingExperience}\n              onChange={handleChange}\n              error={!!fieldErrors.teachingExperience}\n              helperText={fieldErrors.teachingExperience || ''}\n              inputProps={{ min: 0, max: 99 }}\n            />\n          </Grid>\n\n          {/* Introduction Video */}\n          <Grid item xs={12}>\n            <Box sx={{ mb: 2 }}>\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>\n                <Typography variant=\"h6\">{t('teacher.introVideo')}</Typography>\n              </Box>\n\n              {formData.introVideoUrl ? (\n                <Card sx={{ mb: 2 }}>\n                  <CardMedia\n                    component=\"video\"\n                    controls\n                    src={formData.introVideoUrl}\n                    sx={{ height: 300 }}\n                  />\n                  <CardContent>\n                    <Typography variant=\"body2\" color=\"text.secondary\">\n                      {t('teacher.videoReady')}\n                    </Typography>\n                  </CardContent>\n                  <CardActions>\n                    <Button\n                      size=\"small\"\n                      color=\"error\"\n                      onClick={() => {\n                        localStorage.removeItem('teacherVideoUrl');\n                        localStorage.removeItem('teacherVideoRelativePath');\n                        setFormData(prev => ({\n                          ...prev,\n                          introVideoUrl: '',\n                          introVideoRelativePath: ''\n                        }));\n                      }}\n                    >\n                      {t('teacher.deleteVideo')}\n                    </Button>\n                  </CardActions>\n                </Card>\n              ) : (\n                <Box sx={{ p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>\n                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2 }}>\n                    <Typography variant=\"body1\" color={fieldErrors.introVideoUrl ? \"error\" : \"textSecondary\"}>\n                      {t('teacher.videoRequired')}\n                    </Typography>\n\n                    <Button\n                      variant=\"contained\"\n                      color=\"primary\"\n                      startIcon={<OndemandVideoIcon />}\n                      onClick={() => navigate('/teacher/upload-video')}\n                    >\n                      {t('teacher.uploadVideoNow')}\n                    </Button>\n\n                    {fieldErrors.introVideoUrl && (\n                      <Typography variant=\"body2\" color=\"error\">\n                        {fieldErrors.introVideoUrl}\n                      </Typography>\n                    )}\n\n                    <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, border: 1, borderColor: 'divider', width: '100%' }}>\n                      <Typography variant=\"body2\" color=\"textSecondary\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n                        {t('teacher.allowedFormats')}:\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        {t('teacher.videoFormats')}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"textSecondary\" sx={{ fontWeight: 'bold', mt: 1, mb: 0.5 }}>\n                        {t('teacher.maxFileSize')}:\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"textSecondary\">\n                        {t('teacher.maxVideoSize')}\n                      </Typography>\n                    </Box>\n                  </Box>\n                </Box>\n              )}\n            </Box>\n          </Grid>\n\n          {/* CV */}\n          <Grid item xs={12}>\n            <TextField\n              required\n              fullWidth\n              multiline\n              rows={8}\n              name=\"cv\"\n              label={t('teacher.cv')}\n              placeholder={t('teacher.cvPlaceholder')}\n              value={formData.cv}\n              onChange={handleChange}\n              error={!!fieldErrors.cv}\n              helperText={fieldErrors.cv ? fieldErrors.cv : t('teacher.cvHelperText')}\n              inputProps={{ maxLength: 2000 }}\n            />\n            <Typography variant=\"caption\" color=\"textSecondary\" sx={{ mt: 1, display: 'block' }}>\n              {formData.cv.length}/2000 {t('teacher.characters')}\n            </Typography>\n          </Grid>\n\n          {/* Teacher Commitment */}\n          <Grid item xs={12}>\n            <Paper elevation={1} sx={{ p: 3, mb: 2, border: '1px dashed', borderColor: fieldErrors.commitmentAccepted ? 'error.main' : 'primary.main' }}>\n              <Typography variant=\"h6\" gutterBottom>\n                {t('teacher.commitment') || 'تعهد المعلم'}\n              </Typography>\n\n              <Typography variant=\"body2\" paragraph>\n                {t('teacher.commitmentDescription') || 'يجب قراءة التعهد والموافقة عليه للاستمرار في عملية التقديم.'}\n              </Typography>\n\n              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mb: 2 }}>\n                <Button\n                  variant=\"contained\"\n                  color={formData.commitmentAccepted ? \"success\" : \"primary\"}\n                  onClick={handleOpenCommitmentDialog}\n                  startIcon={formData.commitmentAccepted ? <CheckCircleIcon /> : null}\n                  sx={{ mb: 1 }}\n                >\n                  {formData.commitmentAccepted\n                    ? (t('teacher.commitmentAccepted') || 'تم الموافقة على التعهد')\n                    : (t('teacher.readCommitment') || 'قراءة التعهد والموافقة عليه')}\n                </Button>\n\n                {/* Estado de aceptación del compromiso */}\n                {formData.commitmentAccepted !== undefined && (\n                  <Box\n                    sx={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      p: 1,\n                      borderRadius: 1,\n                      bgcolor: formData.commitmentAccepted ? 'success.light' : 'error.light',\n                      color: 'white'\n                    }}\n                  >\n                    {formData.commitmentAccepted ? (\n                      <>\n                        <CheckCircleIcon sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\">\n                          {t('teacher.commitmentStatus.accepted') || 'لقد وافقت على التعهد'}\n                        </Typography>\n                      </>\n                    ) : (\n                      <>\n                        <CancelIcon sx={{ mr: 1 }} />\n                        <Typography variant=\"body2\">\n                          {t('teacher.commitmentStatus.rejected') || 'لم توافق على التعهد بعد'}\n                        </Typography>\n                      </>\n                    )}\n                  </Box>\n                )}\n              </Box>\n\n              {fieldErrors.commitmentAccepted && (\n                <Typography variant=\"body2\" color=\"error\" sx={{ mt: 1 }}>\n                  {fieldErrors.commitmentAccepted}\n                </Typography>\n              )}\n            </Paper>\n          </Grid>\n\n          {/* Commitment Dialog */}\n          <Dialog\n            open={commitmentDialogOpen}\n            onClose={handleCloseCommitmentDialog}\n            scroll=\"paper\"\n            maxWidth=\"md\"\n            fullWidth\n            dir=\"rtl\"\n          >\n            <DialogTitle sx={{ bgcolor: 'primary.main', color: 'white', textAlign: 'center', py: 2 }}>\n              <Typography variant=\"h5\" component=\"div\" sx={{ fontWeight: 'bold' }}>\n                {t('teacher.commitmentTitle') || 'تعهّد المدرّسين في منصة \"علّمني أون لاين بجميع اللغات\"'}\n              </Typography>\n            </DialogTitle>\n            <DialogContent dividers sx={{ px: 4, py: 3 }}>\n              <DialogContentText component=\"div\">\n                <Typography paragraph sx={{ fontSize: '1.1rem', fontWeight: 'bold', mb: 3 }}>\n                  {t('teacher.commitmentText.intro')}\n                </Typography>\n\n                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\n                  <Box sx={{ display: 'flex', gap: 2 }}>\n                    <Typography variant=\"h6\" color=\"primary\" sx={{ minWidth: '24px' }}>1.</Typography>\n                    <Typography paragraph sx={{ m: 0 }}>\n                      {t('teacher.commitmentText.point1')}\n                    </Typography>\n                  </Box>\n\n                  <Box sx={{ display: 'flex', gap: 2 }}>\n                    <Typography variant=\"h6\" color=\"primary\" sx={{ minWidth: '24px' }}>2.</Typography>\n                    <Typography paragraph sx={{ m: 0 }}>\n                      {t('teacher.commitmentText.point2')}\n                    </Typography>\n                  </Box>\n\n                  <Box sx={{ display: 'flex', gap: 2 }}>\n                    <Typography variant=\"h6\" color=\"primary\" sx={{ minWidth: '24px' }}>3.</Typography>\n                    <Typography paragraph sx={{ m: 0 }}>\n                      {t('teacher.commitmentText.point3')}\n                    </Typography>\n                  </Box>\n\n                  <Box sx={{ display: 'flex', gap: 2 }}>\n                    <Typography variant=\"h6\" color=\"primary\" sx={{ minWidth: '24px' }}>4.</Typography>\n                    <Typography paragraph sx={{ m: 0 }}>\n                      {t('teacher.commitmentText.point4')}\n                    </Typography>\n                  </Box>\n\n                  <Box sx={{ display: 'flex', gap: 2 }}>\n                    <Typography variant=\"h6\" color=\"primary\" sx={{ minWidth: '24px' }}>5.</Typography>\n                    <Typography paragraph sx={{ m: 0 }}>\n                      {t('teacher.commitmentText.point5')}\n                    </Typography>\n                  </Box>\n\n                  <Box sx={{ display: 'flex', gap: 2 }}>\n                    <Typography variant=\"h6\" color=\"primary\" sx={{ minWidth: '24px' }}>6.</Typography>\n                    <Typography paragraph sx={{ m: 0 }}>\n                      {t('teacher.commitmentText.point6')}\n                    </Typography>\n                  </Box>\n                </Box>\n\n                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid', borderColor: 'divider' }}>\n                  <Typography paragraph sx={{ m: 0, fontWeight: 'bold' }}>\n                    {t('teacher.commitmentText.conclusion')}\n                  </Typography>\n                </Box>\n              </DialogContentText>\n            </DialogContent>\n            <DialogActions sx={{ justifyContent: 'center', p: 3, gap: 3 }}>\n              <Button\n                onClick={handleRejectCommitment}\n                color=\"error\"\n                variant=\"outlined\"\n                startIcon={<CancelIcon />}\n                sx={{ minWidth: 150, py: 1 }}\n                size=\"large\"\n              >\n                {t('teacher.reject') || 'أرفض التعهد'}\n              </Button>\n              <Button\n                onClick={handleAcceptCommitment}\n                color=\"success\"\n                variant=\"contained\"\n                startIcon={<CheckCircleIcon />}\n                sx={{ minWidth: 150, py: 1 }}\n                size=\"large\"\n              >\n                {t('teacher.accept') || 'أوافق على التعهد'}\n              </Button>\n            </DialogActions>\n          </Dialog>\n\n          {/* Profile Picture Upload */}\n          <Grid item xs={12} md={6}>\n            <input\n              type=\"file\"\n              accept=\"image/*\"\n              style={{ display: 'none' }}\n              id=\"profile-picture-upload\"\n              onChange={handleProfilePictureChange}\n            />\n            <FormControl fullWidth error={!!fieldErrors.profilePicture}>\n              <InputLabel shrink required>{t('teacher.profilePicture')}</InputLabel>\n              <Box sx={{ mt: 1 }}>\n                <label htmlFor=\"profile-picture-upload\">\n                  <Button\n                    variant=\"outlined\"\n                    component=\"span\"\n                    fullWidth\n                    startIcon={<CloudUploadIcon />}\n                  >\n                    {formData.profilePicture ? formData.profilePicture.name : t('teacher.profilePicturePlaceholder')}\n                  </Button>\n                </label>\n              </Box>\n              {formData.profilePicturePreview && (\n                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>\n                  <Avatar src={formData.profilePicturePreview} alt=\"Profile preview\" sx={{ width: 120, height: 120 }} />\n                </Box>\n              )}\n              <Typography variant=\"body2\" color={fieldErrors.profilePicture ? \"error\" : \"textSecondary\"}>\n                {fieldErrors.profilePicture || t('teacher.fileTypes.image')}\n              </Typography>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12}>\n            <Box sx={{ border: 1, borderColor: 'divider', borderRadius: 1, p: 2, mb: 2 }}>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n                <Typography variant=\"h6\">\n                  {t('teacher.availableHours')}\n                </Typography>\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  onClick={() => {\n                    // Guardar los datos actuales del formulario antes de navegar\n                    const dataToSave = {\n                      ...formData,\n                      profilePicture: null\n                    };\n                    localStorage.setItem('teacherApplicationForm', JSON.stringify(dataToSave));\n\n                    // Redirigir a la página correcta según el rol del usuario\n                    if (currentUser?.role === 'platform_teacher') {\n                      navigate('/teacher/manage-hours');\n                    } else {\n                      navigate('/teacher/available-hours');\n                    }\n                  }}\n                >\n                  {t('teacher.manageAvailableHours')}\n                </Button>\n              </Box>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                {t('teacher.availableHoursDescription')}\n              </Typography>\n\n              {/* Show summary of selected hours */}\n              <Box sx={{ mt: 2 }}>\n                {Object.entries(formData.availableHours).map(([day, slots]) => (\n                  slots.length > 0 && (\n                    <Box key={day} sx={{ mb: 1 }}>\n                      <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                        {days[day]}: <span style={{ fontWeight: 'normal' }}>{slots.length} {t('teacher.timeSlots')}</span>\n                      </Typography>\n                    </Box>\n                  )\n                ))}\n\n                {Object.values(formData.availableHours).every(slots => slots.length === 0) && (\n                  <Typography variant=\"body2\" color=\"error\">\n                    {t('teacher.noAvailableHours')}\n                  </Typography>\n                )}\n              </Box>\n            </Box>\n          </Grid>\n\n          {/* Price per Lesson */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              required\n              fullWidth\n              type=\"number\"\n              name=\"pricePerLesson\"\n              label={t('teacher.pricePerLesson')}\n              placeholder={t('teacher.pricePerLessonPlaceholder')}\n              value={formData.pricePerLesson}\n              onChange={handleChange}\n              error={!!fieldErrors.pricePerLesson}\n              helperText={fieldErrors.pricePerLesson || ''}\n              InputProps={{\n                startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n              }}\n              inputProps={{ min: 3, max: 100 }}\n            />\n\n            {/* Teacher's Earnings */}\n            {formData.pricePerLesson > 0 && (\n              <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', border: '1px dashed', borderColor: 'primary.main', borderRadius: 1 }}>\n                <Typography variant=\"subtitle2\" color=\"primary\" gutterBottom>\n                  {t('teacher.yourEarnings') || 'ما ستحصل عليه بعد خصم العمولة:'}\n                </Typography>\n                <Typography variant=\"body1\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                  ${calculateTeacherEarnings(formData.pricePerLesson).toFixed(2)}\n                </Typography>\n              </Box>\n            )}\n          </Grid>\n\n          {/* Trial Lesson Price */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              required\n              fullWidth\n              type=\"number\"\n              name=\"trialLessonPrice\"\n              label={t('teacher.trialLessonPrice') || 'سعر الدرس التجريبي'}\n              placeholder={t('teacher.trialLessonPricePlaceholder') || 'أدخل سعر الدرس التجريبي'}\n              value={formData.trialLessonPrice}\n              onChange={handleChange}\n              error={!!fieldErrors.trialLessonPrice}\n              helperText={fieldErrors.trialLessonPrice || ''}\n              InputProps={{\n                startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n              }}\n              inputProps={{ min: 1, max: 100 }}\n            />\n          </Grid>\n\n          {/* Timezone */}\n          <Grid item xs={12} md={6}>\n            <FormControl fullWidth required error={!!fieldErrors.timezone}>\n              <InputLabel>{t('teacher.timezone')}</InputLabel>\n              <Select\n                name=\"timezone\"\n                value={formData.timezone}\n                onChange={handleChange}\n              >\n                {timezones.map((timezone) => (\n                  <MenuItem key={timezone.value} value={timezone.value}>\n                    {timezone.label}\n                  </MenuItem>\n                ))}\n              </Select>\n              {fieldErrors.timezone && (\n                <Typography variant=\"body2\" color=\"error\">\n                  {fieldErrors.timezone}\n                </Typography>\n              )}\n            </FormControl>\n          </Grid>\n\n\n          <Grid item xs={12} md={6}>\n            <TextField\n              fullWidth\n              name=\"phone\"\n              label={t('teacher.phone')}\n              value={formData.phone}\n              onChange={handleChange}\n              error={!!fieldErrors.phone}\n              helperText={fieldErrors.phone || t('teacher.phoneHelp')}\n              placeholder={t('teacher.phoneOptional') || 'اختياري'}\n            />\n          </Grid>\n\n          <Grid item xs={12}>\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              color=\"primary\"\n              size=\"large\"\n              fullWidth\n              disabled={loading}\n            >\n              {loading ? (\n                <CircularProgress size={24} />\n              ) : isEdit ? (\n                t('teacher.editApplication.submitButton') || 'حفظ التعديلات'\n              ) : (\n                t('teacher.application.submit')\n              )}\n            </Button>\n          </Grid>\n        </Grid>\n\n        {/* Crop dialog for profile picture */}\n        <CropImageDialog\n          open={cropDialogOpen}\n          imageSrc={tempImageSrc}\n          onClose={() => setCropDialogOpen(false)}\n          onSave={(blob) => {\n            const file = new File([blob], 'profile.jpg', { type: blob.type });\n            setFormData(prev => ({\n              ...prev,\n              profilePicture: file,\n              profilePicturePreview: URL.createObjectURL(blob)\n            }));\n            setCropDialogOpen(false);\n          }}\n        />\n      </form>\n    </Paper>\n  );\n};\n\nexport default ApplicationForm;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,KAAK,KAAM,mBAAmB,CACrC,OAASC,SAAS,KAAQ,uBAAuB,CACjD,OACEC,MAAM,CACNC,SAAS,CACTC,IAAI,CACJC,UAAU,CACVC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,GAAG,CACHC,IAAI,CACJC,SAAS,CACTC,gBAAgB,CAChBC,gBAAgB,CAChBC,gBAAgB,CAChBC,QAAQ,CACRC,gBAAgB,CAChBC,cAAc,CACdC,cAAc,CACdC,KAAK,CACLC,MAAM,CAENC,KAAK,CACLC,IAAI,CACJC,SAAS,CACTC,WAAW,CACXC,WAAW,CACXC,OAAO,CACPC,MAAM,CACNC,aAAa,CACbC,aAAa,CACbC,iBAAiB,CACjBC,WAAW,KACN,eAAe,CACtB,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,eAAe,KAAM,2BAA2B,CACvD,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CAEnD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,wBAAwB,CAAIC,KAAK,EAAK,CAC1C;AACA,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACF,KAAK,CAAC,CAEjC;AACA,GAAI,CAACC,WAAW,EAAIA,WAAW,CAAG,CAAC,CAAE,MAAO,EAAC,CAE7C;AACA,GAAI,CAAAE,cAAc,CAClB,GAAIF,WAAW,GAAK,CAAC,CAAE,CACrBE,cAAc,CAAG,KAAK,CAAE;AAC1B,CAAC,IAAM,IAAIF,WAAW,GAAK,CAAC,CAAE,CAC5BE,cAAc,CAAG,IAAI,CAAE;AACzB,CAAC,IAAM,IAAIF,WAAW,GAAK,CAAC,CAAE,CAC5BE,cAAc,CAAG,IAAI,CAAE;AACzB,CAAC,IAAM,IAAIF,WAAW,GAAK,CAAC,CAAE,CAC5BE,cAAc,CAAG,KAAK,CAAE;AAC1B,CAAC,IAAM,CACLA,cAAc,CAAG,IAAI,CAAE;AACzB,CAEA;AACA,KAAM,CAAAC,eAAe,CAAGH,WAAW,EAAI,CAAC,CAAGE,cAAc,CAAC,CAC1D,MAAO,CAAAC,eAAe,CACxB,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGC,IAAA,EAAkC,IAAjC,CAAEC,QAAQ,CAAEC,MAAM,CAAG,KAAM,CAAC,CAAAF,IAAA,CACnD,KAAM,CAAEG,CAAE,CAAC,CAAG5D,cAAc,CAAC,CAAC,CAC9B,KAAM,CAAA6D,QAAQ,CAAG5D,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAE6D,WAAW,CAAEC,UAAW,CAAC,CAAG7D,OAAO,CAAC,CAAC,CAC7C,KAAM,CAAC8D,OAAO,CAAEC,UAAU,CAAC,CAAGnE,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACoE,KAAK,CAAEC,QAAQ,CAAC,CAAGrE,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACsE,WAAW,CAAEC,cAAc,CAAC,CAAGvE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAClD,KAAM,CAACwE,OAAO,CAAEC,UAAU,CAAC,CAAGzE,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC0E,UAAU,CAAEC,aAAa,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC4E,SAAS,CAAEC,YAAY,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAC9C;AACA,KAAM,CAAC8E,cAAc,CAAEC,iBAAiB,CAAC,CAAG/E,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACgF,YAAY,CAAEC,eAAe,CAAC,CAAGjF,QAAQ,CAAC,EAAE,CAAC,CAEpD,KAAM,CAACkF,QAAQ,CAAEC,WAAW,CAAC,CAAGnF,QAAQ,CAAC,CACvCoF,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,cAAc,CAAE,EAAE,CAClBC,iBAAiB,CAAE,EAAE,CACrBC,WAAW,CAAE,EAAE,CACfC,cAAc,CAAE,EAAE,CAClBC,kBAAkB,CAAE,CAAC,CACrBC,aAAa,CAAE,EAAE,CACjBC,EAAE,CAAE,EAAE,CACNC,cAAc,CAAE,IAAI,CACpBC,qBAAqB,CAAE,EAAE,CACzBC,kBAAkB,CAAE,KAAK,CACzBC,cAAc,CAAE,CACdC,MAAM,CAAE,EAAE,CACVC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EAAE,CACVC,QAAQ,CAAE,EAAE,CACZC,MAAM,CAAE,EACV,CAAC,CACDC,cAAc,CAAE,EAAE,CAClBC,gBAAgB,CAAE,EAAE,CACpBC,QAAQ,CAAE,EAAE,CAEZC,KAAK,CAAE,EACT,CAAC,CAAC,CAEF;AACA,KAAM,CAACC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAG7G,QAAQ,CAAC,KAAK,CAAC,CAMvE,KAAM,CAAA8G,SAAS,CAAG,CAChB,CAAEC,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,aAAa,CAAE,CAAC,CAC/C,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CAChD,CAAEiD,GAAG,CAAE,aAAa,CAAEC,KAAK,CAAElD,CAAC,CAAC,cAAc,CAAE,CAAC,CACjD,CAED,KAAM,CAAAmD,IAAI,CAAG,CACXhB,MAAM,CAAEnC,CAAC,CAAC,aAAa,CAAC,CACxBoC,OAAO,CAAEpC,CAAC,CAAC,cAAc,CAAC,CAC1BqC,SAAS,CAAErC,CAAC,CAAC,gBAAgB,CAAC,CAC9BsC,QAAQ,CAAEtC,CAAC,CAAC,eAAe,CAAC,CAC5BuC,MAAM,CAAEvC,CAAC,CAAC,aAAa,CAAC,CACxBwC,QAAQ,CAAExC,CAAC,CAAC,eAAe,CAAC,CAC5ByC,MAAM,CAAEzC,CAAC,CAAC,aAAa,CACzB,CAAC,CAED;AACA7D,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiH,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,GAAI,CAGF,KAAM,CAAAC,SAAS,CAAGC,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,CAChE,GAAIF,SAAS,CAAE,CACb,KAAM,CAAAG,UAAU,CAAGC,IAAI,CAACC,KAAK,CAACL,SAAS,CAAC,CAExC;AACA;AACAM,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAEJ,UAAU,CAAC,CAEnDnC,WAAW,CAACwC,QAAQ,GAAK,CACvB,GAAGA,QAAQ,CACX,GAAGL,UAAU,CACb;AACAzB,cAAc,CAAE8B,QAAQ,CAAC9B,cAAc,CACvC+B,kBAAkB,CAAED,QAAQ,CAACC,kBAAkB,CAAE;AACjD9B,qBAAqB,CAAEwB,UAAU,CAACxB,qBAAqB,EAAI6B,QAAQ,CAAC7B,qBACtE,CAAC,CAAC,CAAC,CACL,CAEA;AACA,KAAM,CAAA+B,aAAa,CAAGT,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC,CAC7D;AACA,KAAM,CAAAS,sBAAsB,CAAGV,YAAY,CAACC,OAAO,CAAC,0BAA0B,CAAC,CAE/E,GAAIQ,aAAa,CAAE,CACjB1C,WAAW,CAACwC,QAAQ,GAAK,CACvB,GAAGA,QAAQ,CACX;AACAhC,aAAa,CAAEkC,aAAa,CAC5B;AACAE,sBAAsB,CAAED,sBAC1B,CAAC,CAAC,CAAC,CACL,CACF,CAAE,MAAO1D,KAAK,CAAE,CACdqD,OAAO,CAACrD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACxD,CACF,CAAC,CAED8C,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,CAAClD,WAAW,CAAC,CAAC,CAEjB;AACA/D,SAAS,CAAC,IAAM,CACd,KAAM,CAAA+H,YAAY,CAAGA,CAAA,GAAM,CACzB,GAAI,CACF;AACA,KAAM,CAAAC,UAAU,CAAG,CACjB,GAAG/C,QAAQ,CACXW,cAAc,CAAE,IAAI,CACpB+B,kBAAkB,CAAE,IAAK;AAC3B,CAAC,CAEDR,YAAY,CAACc,OAAO,CAAC,wBAAwB,CAAEX,IAAI,CAACY,SAAS,CAACF,UAAU,CAAC,CAAC,CAC5E,CAAE,MAAO7D,KAAK,CAAE,CACdqD,OAAO,CAACrD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CACjD,CACF,CAAC,CAED;AACA,GAAIc,QAAQ,CAACE,OAAO,EAAIF,QAAQ,CAACG,SAAS,EAAIH,QAAQ,CAACK,iBAAiB,CAAC6C,MAAM,CAAG,CAAC,CAAE,CACnFJ,YAAY,CAAC,CAAC,CAChB,CACF,CAAC,CAAE,CAAC9C,QAAQ,CAAC,CAAC,CAEdjF,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoI,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF,KAAM,CAACC,kBAAkB,CAAEC,iBAAiB,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CAChEpI,KAAK,CAACqI,GAAG,CAAC,yBAAyB,CAAC,CACpCrI,KAAK,CAACqI,GAAG,CAAC,wBAAwB,CAAC,CACpC,CAAC,CAEF/D,aAAa,CAAC2D,kBAAkB,CAACK,IAAI,CAAC,CACtC9D,YAAY,CAAC0D,iBAAiB,CAACI,IAAI,CAAC,CACtC,CAAE,MAAOC,GAAG,CAAE,CACZnB,OAAO,CAACrD,KAAK,CAAC,sBAAsB,CAAEwE,GAAG,CAAC,CAC1CvE,QAAQ,CAACP,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAC1C,CACF,CAAC,CAEDuE,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAACvE,CAAC,CAAC,CAAC,CAEP,KAAM,CAAA+E,YAAY,CAAIC,CAAC,EAAK,CAC1B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAEhC;AACA,GAAIF,IAAI,GAAK,oBAAoB,CAAE,CACjC,GAAI,CAAAG,OAAO,CAAGC,MAAM,CAACH,KAAK,CAAC,CAACI,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAAE;AAChD,GAAIF,OAAO,CAACd,MAAM,CAAG,CAAC,CAAE,CACtBc,OAAO,CAAGA,OAAO,CAACG,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAC/B,CACAlE,WAAW,CAACmE,IAAI,GAAK,CAAE,GAAGA,IAAI,CAAE,CAACP,IAAI,EAAGG,OAAQ,CAAC,CAAC,CAAC,CACnD,OACF,CAEA;AACA,KAAM,CAAAK,WAAW,CAAG,CAClB,GAAGrE,QAAQ,CACX,CAAC6D,IAAI,EAAGC,KACV,CAAC,CACD7D,WAAW,CAACoE,WAAW,CAAC,CAExB;AACA,GAAIR,IAAI,GAAK,gBAAgB,EAAIA,IAAI,GAAK,kBAAkB,CAAE,CAC5D,KAAM,CAAAvC,cAAc,CAAGuC,IAAI,GAAK,gBAAgB,CAAGC,KAAK,CAAGO,WAAW,CAAC/C,cAAc,CACrF,KAAM,CAAAC,gBAAgB,CAAGsC,IAAI,GAAK,kBAAkB,CAAGC,KAAK,CAAGO,WAAW,CAAC9C,gBAAgB,CAE3F;AACAlC,cAAc,CAAC+E,IAAI,GAAK,CACtB,GAAGA,IAAI,CACP7C,gBAAgB,CAAE,EACpB,CAAC,CAAC,CAAC,CAEH;AACA,GAAID,cAAc,EAAIC,gBAAgB,CAAE,CACtC,KAAM,CAAA+C,YAAY,CAAGjG,MAAM,CAACiD,cAAc,CAAC,CAC3C,KAAM,CAAAiD,UAAU,CAAGlG,MAAM,CAACkD,gBAAgB,CAAC,CAE3C,GAAIgD,UAAU,CAAGD,YAAY,CAAE,CAC7BjF,cAAc,CAAC+E,IAAI,GAAK,CACtB,GAAGA,IAAI,CACP7C,gBAAgB,CAAE3C,CAAC,CAAC,mCAAmC,CAAC,EAAI,iEAC9D,CAAC,CAAC,CAAC,CACL,CACF,CACF,CAAC,IAAM,CACL;AACAS,cAAc,CAAC+E,IAAI,GAAK,CACtB,GAAGA,IAAI,CACP,CAACP,IAAI,EAAG,EACV,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAAW,oBAAoB,CAAIZ,CAAC,EAAK,CAClC,KAAM,CAAEE,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAC1B9D,WAAW,CAACmE,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP/D,iBAAiB,CAAEyD,KACrB,CAAC,CAAC,CAAC,CACL,CAAC,CAED,KAAM,CAAAW,0BAA0B,CAAIb,CAAC,EAAK,CACxC,KAAM,CAAAc,IAAI,CAAGd,CAAC,CAACG,MAAM,CAACY,KAAK,CAAC,CAAC,CAAC,CAC9B,GAAID,IAAI,CAAE,CACR,KAAM,CAAAE,GAAG,CAAGC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC,CACrC3E,eAAe,CAAC6E,GAAG,CAAC,CACpB/E,iBAAiB,CAAC,IAAI,CAAC,CACzB,CACF,CAAC,CAED;AACA,KAAM,CAAAkF,0BAA0B,CAAGA,CAAA,GAAM,CACvCpD,uBAAuB,CAAC,IAAI,CAAC,CAC/B,CAAC,CAED;AACA,KAAM,CAAAqD,2BAA2B,CAAGA,CAAA,GAAM,CACxCrD,uBAAuB,CAAC,KAAK,CAAC,CAChC,CAAC,CAED;AACA,KAAM,CAAAsD,sBAAsB,CAAGA,CAAA,GAAM,CACnChF,WAAW,CAACmE,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPvD,kBAAkB,CAAE,IACtB,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAqE,eAAe,CAAG7C,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAI,IAAI,CAAC,CAC1FD,YAAY,CAACc,OAAO,CAAC,wBAAwB,CAAEX,IAAI,CAACY,SAAS,CAAC,CAC5D,GAAGiC,eAAe,CAClBrE,kBAAkB,CAAE,IACtB,CAAC,CAAC,CAAC,CAEH;AACAxB,cAAc,CAAC+E,IAAI,GAAK,CACtB,GAAGA,IAAI,CACPvD,kBAAkB,CAAE,IACtB,CAAC,CAAC,CAAC,CAEH;AACAc,uBAAuB,CAAC,KAAK,CAAC,CAChC,CAAC,CAED;AACA,KAAM,CAAAwD,sBAAsB,CAAGA,CAAA,GAAM,CACnClF,WAAW,CAACmE,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPvD,kBAAkB,CAAE,KACtB,CAAC,CAAC,CAAC,CAEH;AACA,KAAM,CAAAqE,eAAe,CAAG7C,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAI,IAAI,CAAC,CAC1FD,YAAY,CAACc,OAAO,CAAC,wBAAwB,CAAEX,IAAI,CAACY,SAAS,CAAC,CAC5D,GAAGiC,eAAe,CAClBrE,kBAAkB,CAAE,KACtB,CAAC,CAAC,CAAC,CAEH;AACAc,uBAAuB,CAAC,KAAK,CAAC,CAChC,CAAC,CAED;AAEA;AACA;AAEA,KAAM,CAAAyD,YAAY,CAAG,KAAO,CAAAxB,CAAC,EAAK,CAChCA,CAAC,CAACyB,cAAc,CAAC,CAAC,CAClBlG,QAAQ,CAAC,EAAE,CAAC,CACZE,cAAc,CAAC,CAAC,CAAC,CAAC,CAClBJ,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA,KAAM,CAAAqG,MAAM,CAAG,CAAC,CAAC,CACjB,KAAM,CAAAC,cAAc,CAAG,CACrB,SAAS,CAAE,WAAW,CAAE,gBAAgB,CAAE,mBAAmB,CAAE,aAAa,CAC5E,gBAAgB,CAAE,oBAAoB,CAAE,IAAI,CAAE,gBAAgB,CAAE,kBAAkB,CAAE,UAAU,CAC/F,CAED;AACF;AACA,GAAIvF,QAAQ,CAACuB,gBAAgB,EAAIvB,QAAQ,CAACsB,cAAc,EAAIjD,MAAM,CAAC2B,QAAQ,CAACuB,gBAAgB,CAAC,CAAGlD,MAAM,CAAC2B,QAAQ,CAACsB,cAAc,CAAC,CAAE,CAC/HgE,MAAM,CAAC/D,gBAAgB,CAAG3C,CAAC,CAAC,mCAAmC,CAAC,EAAI,iEAAiE,CACvI,CACE2G,cAAc,CAACC,OAAO,CAACC,KAAK,EAAI,CAC9B,GAAI,CAACzF,QAAQ,CAACyF,KAAK,CAAC,EACfC,KAAK,CAACC,OAAO,CAAC3F,QAAQ,CAACyF,KAAK,CAAC,CAAC,EAAIzF,QAAQ,CAACyF,KAAK,CAAC,CAACvC,MAAM,GAAK,CAAE,EAChElD,QAAQ,CAACyF,KAAK,CAAC,GAAK,EAAE,CAAE,CAC1BH,MAAM,CAACG,KAAK,CAAC,CAAG7G,CAAC,CAAC,kBAAkB,CAAC,CACvC,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAACoB,QAAQ,CAACS,aAAa,CAAE,CAC3B6E,MAAM,CAAC7E,aAAa,CAAG7B,CAAC,CAAC,kBAAkB,CAAC,CAC9C,CAEA;AACA,GAAI,CAACoB,QAAQ,CAACa,kBAAkB,CAAE,CAChCyE,MAAM,CAACzE,kBAAkB,CAAGjC,CAAC,CAAC,4BAA4B,CAAC,EAAI,yBAAyB,CAC1F,CAEA;AACA,GAAI,CAACoB,QAAQ,CAACW,cAAc,CAAE,CAC5B2E,MAAM,CAAC3E,cAAc,CAAG/B,CAAC,CAAC,gCAAgC,CAAC,EAAI,uBAAuB,CACxF,CAEA;AACA,GAAIgH,MAAM,CAACC,IAAI,CAACP,MAAM,CAAC,CAACpC,MAAM,CAAG,CAAC,CAAE,CAClC7D,cAAc,CAACiG,MAAM,CAAC,CACtBnG,QAAQ,CAACP,CAAC,CAAC,uBAAuB,CAAC,CAAC,CACpCK,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,GAAI,CACF,KAAM,CAAAwE,IAAI,CAAG,GAAI,CAAAqC,QAAQ,CAAC,CAAC,CAE3B;AACAvD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAExC,QAAQ,CAAC,CAIrD;AACA,KAAM,CAAA+F,mBAAmB,CAAG7D,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,GAAK,MAAM,CAClF,GAAI4D,mBAAmB,CAAE,CACvBxD,OAAO,CAACC,GAAG,CAAC,gEAAgE,CAAC,CAC7EiB,IAAI,CAACuC,MAAM,CAAC,mBAAmB,CAAE,MAAM,CAAC,CACxC;AACF,CAEAJ,MAAM,CAACC,IAAI,CAAC7F,QAAQ,CAAC,CAACwF,OAAO,CAAC3D,GAAG,EAAI,CACnC,GAAIA,GAAG,GAAK,gBAAgB,EAAI7B,QAAQ,CAAC6B,GAAG,CAAC,CAAE,CAC7C4B,IAAI,CAACuC,MAAM,CAACnE,GAAG,CAAE7B,QAAQ,CAAC6B,GAAG,CAAC,CAAC,CACjC,CAAC,IAAM,IAAIA,GAAG,GAAK,oBAAoB,CAAE,CACvC;AACA4B,IAAI,CAACuC,MAAM,CAACnE,GAAG,CAAE7B,QAAQ,CAAC6B,GAAG,CAAC,CAAC,CACjC,CAAC,IAAM,IAAIA,GAAG,GAAK,eAAe,CAAE,CAClC;AAAA,CACD,IAAM,IAAIA,GAAG,GAAK,wBAAwB,CAAE,CAC3C;AACA4B,IAAI,CAACuC,MAAM,CAAC,eAAe,CAAEhG,QAAQ,CAAC6B,GAAG,CAAC,CAAC,CAC7C,CAAC,IAAM,IAAI,CAAC,CAAC,uBAAuB,CAAC,CAACoE,QAAQ,CAACpE,GAAG,CAAC,CAAE,CACnD,GAAI,CAAAiC,KAAK,CAAG9D,QAAQ,CAAC6B,GAAG,CAAC,CAEzB;AACA,GAAI6D,KAAK,CAACC,OAAO,CAAC7B,KAAK,CAAC,EAAK,MAAO,CAAAA,KAAK,GAAK,QAAQ,EAAIA,KAAK,GAAK,IAAI,EAAI,EAAEA,KAAK,WAAY,CAAAoC,IAAI,CAAE,CAAE,CACrGpC,KAAK,CAAGzB,IAAI,CAACY,SAAS,CAACa,KAAK,CAAC,CAC/B,CAEA;AACA,GAAIA,KAAK,GAAK,EAAE,CAAE,CAChBA,KAAK,CAAG,IAAI,CACd,CAEA;AACA,GAAIA,KAAK,GAAKqC,SAAS,EAAIrC,KAAK,GAAK,IAAI,CAAE,CACzCL,IAAI,CAACuC,MAAM,CAACnE,GAAG,CAAEiC,KAAK,CAAC,CACzB,CACF,CACF,CAAC,CAAC,CAEF;AACA,IAAK,GAAI,CAAAsC,IAAI,GAAI,CAAA3C,IAAI,CAAC4C,OAAO,CAAC,CAAC,CAAE,CAC/B9D,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAE4D,IAAI,CAAC,CAAC,CAAC,CAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAClD,CAEA,KAAM,CAAA1H,QAAQ,CAAC+E,IAAI,CAAC,CACpBlE,UAAU,CAAC,IAAI,CAAC,CAEhB;AACA2C,YAAY,CAACoE,UAAU,CAAC,wBAAwB,CAAC,CACjDpE,YAAY,CAACoE,UAAU,CAAC,iBAAiB,CAAC,CAC1CpE,YAAY,CAACoE,UAAU,CAAC,0BAA0B,CAAC,CACnDpE,YAAY,CAACoE,UAAU,CAAC,qBAAqB,CAAC,CAE9C;AACA,KAAM,CAAAC,YAAY,CAAGrE,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,CAChE,GAAIoE,YAAY,EAAIxH,UAAU,EAAID,WAAW,CAAE,CAC7CyD,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAE+D,YAAY,CAAC,CACrDxH,UAAU,CAAC,CACT,GAAGD,WAAW,CACd0H,IAAI,CAAED,YACR,CAAC,CAAC,CACFrE,YAAY,CAACoE,UAAU,CAAC,qBAAqB,CAAC,CAChD,CAEA;AACAG,UAAU,CAAC,IAAM,CACf,GAAI9H,MAAM,CAAE,CACVE,QAAQ,CAAC,kBAAkB,CAAC,CAC9B,CAAC,IAAM,CACL;AACA0D,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC,CACpE,CACF,CAAC,CAAE,IAAI,CAAC,CACV,CAAE,MAAOkB,GAAG,CAAE,CACZnB,OAAO,CAACrD,KAAK,CAAC,wBAAwB,CAAEwE,GAAG,CAAC,CAC5CvE,QAAQ,CAACuE,GAAG,CAACgD,OAAO,EAAI9H,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAClDW,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,OAAS,CACRN,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,GAAID,OAAO,EAAI,CAACQ,UAAU,CAAC0D,MAAM,CAAE,CACjC,mBACErF,IAAA,CAAChC,GAAG,EAAC8K,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAC,QAAA,cAC/ElJ,IAAA,CAACzB,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA,mBACE2B,KAAA,CAACxB,KAAK,EAACyK,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAAH,QAAA,EAC/B7H,KAAK,eACJrB,IAAA,CAACpB,KAAK,EAAC0K,QAAQ,CAAC,OAAO,CAACF,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,CACnC7H,KAAK,CACD,CACR,CAEAI,OAAO,eACNzB,IAAA,CAACpB,KAAK,EAAC0K,QAAQ,CAAC,SAAS,CAACF,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,CACrCnI,CAAC,CAAC,4BAA4B,CAAC,CAC3B,CACR,cAEDb,KAAA,SAAMW,QAAQ,CAAE0G,YAAa,CAAA2B,QAAA,eAC3BhJ,KAAA,CAACxC,IAAI,EAAC8L,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAP,QAAA,eACzBlJ,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBlJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAZ,QAAA,CACjDpI,MAAM,CAAGC,CAAC,CAAC,mCAAmC,CAAC,EAAI,sBAAsB,CAAGA,CAAC,CAAC,2BAA2B,CAAC,CACjG,CAAC,CACT,CAAC,cAGPf,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,cACvBlJ,IAAA,CAACvC,SAAS,EACRuM,QAAQ,MACRC,SAAS,MACTjE,IAAI,CAAC,SAAS,CACd/B,KAAK,CAAElD,CAAC,CAAC,iBAAiB,CAAE,CAC5BkF,KAAK,CAAE9D,QAAQ,CAACE,OAAQ,CACxB6H,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACc,OAAQ,CAC7B8H,UAAU,CAAE5I,WAAW,CAACc,OAAO,EAAI,EAAG,CACvC,CAAC,CACE,CAAC,cAGPrC,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,cACvBlJ,IAAA,CAACvC,SAAS,EACRuM,QAAQ,MACRC,SAAS,MACTjE,IAAI,CAAC,WAAW,CAChB/B,KAAK,CAAElD,CAAC,CAAC,mBAAmB,CAAE,CAC9BkF,KAAK,CAAE9D,QAAQ,CAACG,SAAU,CAC1B4H,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACe,SAAU,CAC/B6H,UAAU,CAAE5I,WAAW,CAACe,SAAS,EAAI,EAAG,CACzC,CAAC,CACE,CAAC,cAGPtC,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,cACvBlJ,IAAA,CAACvC,SAAS,EACRuM,QAAQ,MACRC,SAAS,MACTjE,IAAI,CAAC,gBAAgB,CACrB/B,KAAK,CAAElD,CAAC,CAAC,wBAAwB,CAAE,CACnCkF,KAAK,CAAE9D,QAAQ,CAACI,cAAe,CAC/B2H,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACgB,cAAe,CACpC4H,UAAU,CAAE5I,WAAW,CAACgB,cAAc,EAAI,EAAG,CAC9C,CAAC,CACE,CAAC,cAGPvC,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBhJ,KAAA,CAACtC,WAAW,EAACqM,SAAS,MAACD,QAAQ,MAAC3I,KAAK,CAAE,CAAC,CAACE,WAAW,CAACiB,iBAAkB,CAAA0G,QAAA,eACrElJ,IAAA,CAACnC,UAAU,EAAAqL,QAAA,CAAEnI,CAAC,CAAC,2BAA2B,CAAC,CAAa,CAAC,cACzDf,IAAA,CAAClC,MAAM,EACLsM,QAAQ,MACRpE,IAAI,CAAC,mBAAmB,CACxBC,KAAK,CAAE9D,QAAQ,CAACK,iBAAkB,CAClC0H,QAAQ,CAAEvD,oBAAqB,CAC/B0D,WAAW,CAAGC,QAAQ,eACpBtK,IAAA,CAAChC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEyB,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,GAAI,CAAE,CAAAtB,QAAA,CACtDoB,QAAQ,CAACG,GAAG,CAAExE,KAAK,OAAAyE,eAAA,oBAClB1K,IAAA,CAAC/B,IAAI,EAEHgG,KAAK,CAAE,EAAAyG,eAAA,CAAA7I,SAAS,CAAC8I,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,EAAE,GAAK5E,KAAK,CAAC,UAAAyE,eAAA,iBAAzCA,eAAA,CAA2C1E,IAAI,GAAIC,KAAM,EAD3DA,KAEN,CAAC,EACH,CAAC,CACC,CACL,CAAAiD,QAAA,CAEDrH,SAAS,CAAC4I,GAAG,CAAEG,IAAI,eAClB5K,IAAA,CAACjC,QAAQ,EAAekI,KAAK,CAAE2E,IAAI,CAACC,EAAG,CAAA3B,QAAA,CACpC0B,IAAI,CAAC5E,IAAI,EADG4E,IAAI,CAACC,EAEV,CACX,CAAC,CACI,CAAC,CACRtJ,WAAW,CAACiB,iBAAiB,eAC5BxC,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,OAAO,CAAA5B,QAAA,CACtC3H,WAAW,CAACiB,iBAAiB,CACpB,CACb,EACU,CAAC,CACV,CAAC,cAGPxC,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBhJ,KAAA,CAACtC,WAAW,EAACqM,SAAS,MAACD,QAAQ,MAAC3I,KAAK,CAAE,CAAC,CAACE,WAAW,CAACkB,WAAY,CAAAyG,QAAA,eAC/DlJ,IAAA,CAACnC,UAAU,EAAAqL,QAAA,CAAEnI,CAAC,CAAC,qBAAqB,CAAC,CAAa,CAAC,cACnDf,IAAA,CAAClC,MAAM,EACLsM,QAAQ,MACRpE,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAE9D,QAAQ,CAACM,WAAY,CAC5ByH,QAAQ,CAAEpE,YAAa,CACvBuE,WAAW,CAAGC,QAAQ,eACpBtK,IAAA,CAAChC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEyB,QAAQ,CAAE,MAAM,CAAEC,GAAG,CAAE,GAAI,CAAE,CAAAtB,QAAA,CACtDoB,QAAQ,CAACG,GAAG,CAAExE,KAAK,OAAA8E,gBAAA,oBAClB/K,IAAA,CAAC/B,IAAI,EAEHgG,KAAK,CAAE,EAAA8G,gBAAA,CAAApJ,UAAU,CAACgJ,IAAI,CAACK,GAAG,EAAIA,GAAG,CAACH,EAAE,GAAK5E,KAAK,CAAC,UAAA8E,gBAAA,iBAAxCA,gBAAA,CAA0C/E,IAAI,GAAIC,KAAM,EAD1DA,KAEN,CAAC,EACH,CAAC,CACC,CACL,CAAAiD,QAAA,CAEDvH,UAAU,CAAC8I,GAAG,CAAEQ,QAAQ,eACvBjL,IAAA,CAACjC,QAAQ,EAAmBkI,KAAK,CAAEgF,QAAQ,CAACJ,EAAG,CAAA3B,QAAA,CAC5C+B,QAAQ,CAACjF,IAAI,EADDiF,QAAQ,CAACJ,EAEd,CACX,CAAC,CACI,CAAC,CACRtJ,WAAW,CAACkB,WAAW,eACtBzC,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,OAAO,CAAA5B,QAAA,CACtC3H,WAAW,CAACkB,WAAW,CACd,CACb,EACU,CAAC,CACV,CAAC,cAGPzC,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBlJ,IAAA,CAACvC,SAAS,EACRuM,QAAQ,MACRC,SAAS,MACTiB,SAAS,MACTC,IAAI,CAAE,CAAE,CACRnF,IAAI,CAAC,gBAAgB,CACrB/B,KAAK,CAAElD,CAAC,CAAC,wBAAwB,CAAE,CACnCqK,WAAW,CAAErK,CAAC,CAAC,mCAAmC,CAAE,CACpDkF,KAAK,CAAE9D,QAAQ,CAACO,cAAe,CAC/BwH,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACmB,cAAe,CACpCyH,UAAU,CAAE5I,WAAW,CAACmB,cAAc,EAAI,EAAG,CAC9C,CAAC,CACE,CAAC,cAGP1C,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,cACvBlJ,IAAA,CAACvC,SAAS,EACRuM,QAAQ,MACRC,SAAS,MACToB,IAAI,CAAC,QAAQ,CACbrF,IAAI,CAAC,oBAAoB,CACzB/B,KAAK,CAAElD,CAAC,CAAC,4BAA4B,CAAE,CACvCkF,KAAK,CAAE9D,QAAQ,CAACQ,kBAAmB,CACnCuH,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACoB,kBAAmB,CACxCwH,UAAU,CAAE5I,WAAW,CAACoB,kBAAkB,EAAI,EAAG,CACjD2I,UAAU,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,EAAG,CAAE,CACjC,CAAC,CACE,CAAC,cAGPxL,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBhJ,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACjBlJ,IAAA,CAAChC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAED,cAAc,CAAE,eAAe,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,cACzFlJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAAAV,QAAA,CAAEnI,CAAC,CAAC,oBAAoB,CAAC,CAAa,CAAC,CAC5D,CAAC,CAELoB,QAAQ,CAACS,aAAa,cACrB1C,KAAA,CAACrB,IAAI,EAACuK,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eAClBlJ,IAAA,CAAClB,SAAS,EACR+K,SAAS,CAAC,OAAO,CACjB4B,QAAQ,MACRC,GAAG,CAAEvJ,QAAQ,CAACS,aAAc,CAC5BwG,EAAE,CAAE,CAAEuC,MAAM,CAAE,GAAI,CAAE,CACrB,CAAC,cACF3L,IAAA,CAACjB,WAAW,EAAAmK,QAAA,cACVlJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAAA5B,QAAA,CAC/CnI,CAAC,CAAC,oBAAoB,CAAC,CACd,CAAC,CACF,CAAC,cACdf,IAAA,CAAChB,WAAW,EAAAkK,QAAA,cACVlJ,IAAA,CAACxC,MAAM,EACLoO,IAAI,CAAC,OAAO,CACZd,KAAK,CAAC,OAAO,CACbe,OAAO,CAAEA,CAAA,GAAM,CACbxH,YAAY,CAACoE,UAAU,CAAC,iBAAiB,CAAC,CAC1CpE,YAAY,CAACoE,UAAU,CAAC,0BAA0B,CAAC,CACnDrG,WAAW,CAACmE,IAAI,GAAK,CACnB,GAAGA,IAAI,CACP3D,aAAa,CAAE,EAAE,CACjBoC,sBAAsB,CAAE,EAC1B,CAAC,CAAC,CAAC,CACL,CAAE,CAAAkE,QAAA,CAEDnI,CAAC,CAAC,qBAAqB,CAAC,CACnB,CAAC,CACE,CAAC,EACV,CAAC,cAEPf,IAAA,CAAChC,GAAG,EAACoL,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEyC,MAAM,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAA9C,QAAA,cACpEhJ,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEmD,aAAa,CAAE,QAAQ,CAAEjD,UAAU,CAAE,QAAQ,CAAEwB,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAClFlJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAEvJ,WAAW,CAACqB,aAAa,CAAG,OAAO,CAAG,eAAgB,CAAAsG,QAAA,CACtFnI,CAAC,CAAC,uBAAuB,CAAC,CACjB,CAAC,cAEbf,IAAA,CAACxC,MAAM,EACLoM,OAAO,CAAC,WAAW,CACnBkB,KAAK,CAAC,SAAS,CACfoB,SAAS,cAAElM,IAAA,CAACJ,iBAAiB,GAAE,CAAE,CACjCiM,OAAO,CAAEA,CAAA,GAAM7K,QAAQ,CAAC,uBAAuB,CAAE,CAAAkI,QAAA,CAEhDnI,CAAC,CAAC,wBAAwB,CAAC,CACtB,CAAC,CAERQ,WAAW,CAACqB,aAAa,eACxB5C,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,OAAO,CAAA5B,QAAA,CACtC3H,WAAW,CAACqB,aAAa,CAChB,CACb,cAED1C,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAC,CAAE9C,CAAC,CAAE,CAAC,CAAE+C,OAAO,CAAE,kBAAkB,CAAEJ,YAAY,CAAE,CAAC,CAAEF,MAAM,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEM,KAAK,CAAE,MAAO,CAAE,CAAAnD,QAAA,eACvHhJ,KAAA,CAACvC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,eAAe,CAAC1B,EAAE,CAAE,CAAEkD,UAAU,CAAE,MAAM,CAAE/C,EAAE,CAAE,GAAI,CAAE,CAAAL,QAAA,EACnFnI,CAAC,CAAC,wBAAwB,CAAC,CAAC,GAC/B,EAAY,CAAC,cACbf,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,eAAe,CAAA5B,QAAA,CAC9CnI,CAAC,CAAC,sBAAsB,CAAC,CAChB,CAAC,cACbb,KAAA,CAACvC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,eAAe,CAAC1B,EAAE,CAAE,CAAEkD,UAAU,CAAE,MAAM,CAAEH,EAAE,CAAE,CAAC,CAAE5C,EAAE,CAAE,GAAI,CAAE,CAAAL,QAAA,EAC1FnI,CAAC,CAAC,qBAAqB,CAAC,CAAC,GAC5B,EAAY,CAAC,cACbf,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,eAAe,CAAA5B,QAAA,CAC9CnI,CAAC,CAAC,sBAAsB,CAAC,CAChB,CAAC,EACV,CAAC,EACH,CAAC,CACH,CACN,EACE,CAAC,CACF,CAAC,cAGPb,KAAA,CAACxC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,eAChBlJ,IAAA,CAACvC,SAAS,EACRuM,QAAQ,MACRC,SAAS,MACTiB,SAAS,MACTC,IAAI,CAAE,CAAE,CACRnF,IAAI,CAAC,IAAI,CACT/B,KAAK,CAAElD,CAAC,CAAC,YAAY,CAAE,CACvBqK,WAAW,CAAErK,CAAC,CAAC,uBAAuB,CAAE,CACxCkF,KAAK,CAAE9D,QAAQ,CAACU,EAAG,CACnBqH,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACsB,EAAG,CACxBsH,UAAU,CAAE5I,WAAW,CAACsB,EAAE,CAAGtB,WAAW,CAACsB,EAAE,CAAG9B,CAAC,CAAC,sBAAsB,CAAE,CACxEuK,UAAU,CAAE,CAAEiB,SAAS,CAAE,IAAK,CAAE,CACjC,CAAC,cACFrM,KAAA,CAACvC,UAAU,EAACiM,OAAO,CAAC,SAAS,CAACkB,KAAK,CAAC,eAAe,CAAC1B,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAC,CAAErD,OAAO,CAAE,OAAQ,CAAE,CAAAI,QAAA,EACjF/G,QAAQ,CAACU,EAAE,CAACwC,MAAM,CAAC,QAAM,CAACtE,CAAC,CAAC,oBAAoB,CAAC,EACxC,CAAC,EACT,CAAC,cAGPf,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBhJ,KAAA,CAACxB,KAAK,EAACyK,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAC,CAAEuC,MAAM,CAAE,YAAY,CAAEC,WAAW,CAAExK,WAAW,CAACyB,kBAAkB,CAAG,YAAY,CAAG,cAAe,CAAE,CAAAkG,QAAA,eAC1IlJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAZ,QAAA,CAClCnI,CAAC,CAAC,oBAAoB,CAAC,EAAI,aAAa,CAC/B,CAAC,cAEbf,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAAC4C,SAAS,MAAAtD,QAAA,CAClCnI,CAAC,CAAC,+BAA+B,CAAC,EAAI,6DAA6D,CAC1F,CAAC,cAEbb,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEmD,aAAa,CAAE,QAAQ,CAAEzB,GAAG,CAAE,CAAC,CAAEjB,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACnElJ,IAAA,CAACxC,MAAM,EACLoM,OAAO,CAAC,WAAW,CACnBkB,KAAK,CAAE3I,QAAQ,CAACa,kBAAkB,CAAG,SAAS,CAAG,SAAU,CAC3D6I,OAAO,CAAE3E,0BAA2B,CACpCgF,SAAS,CAAE/J,QAAQ,CAACa,kBAAkB,cAAGhD,IAAA,CAACH,eAAe,GAAE,CAAC,CAAG,IAAK,CACpEuJ,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,CAEb/G,QAAQ,CAACa,kBAAkB,CACvBjC,CAAC,CAAC,4BAA4B,CAAC,EAAI,wBAAwB,CAC3DA,CAAC,CAAC,wBAAwB,CAAC,EAAI,6BAA8B,CAC5D,CAAC,CAGRoB,QAAQ,CAACa,kBAAkB,GAAKsF,SAAS,eACxCtI,IAAA,CAAChC,GAAG,EACFoL,EAAE,CAAE,CACFN,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBK,CAAC,CAAE,CAAC,CACJ2C,YAAY,CAAE,CAAC,CACfI,OAAO,CAAEjK,QAAQ,CAACa,kBAAkB,CAAG,eAAe,CAAG,aAAa,CACtE8H,KAAK,CAAE,OACT,CAAE,CAAA5B,QAAA,CAED/G,QAAQ,CAACa,kBAAkB,cAC1B9C,KAAA,CAAAE,SAAA,EAAA8I,QAAA,eACElJ,IAAA,CAACH,eAAe,EAACuJ,EAAE,CAAE,CAAEqD,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAClCzM,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAAAV,QAAA,CACxBnI,CAAC,CAAC,mCAAmC,CAAC,EAAI,sBAAsB,CACvD,CAAC,EACb,CAAC,cAEHb,KAAA,CAAAE,SAAA,EAAA8I,QAAA,eACElJ,IAAA,CAACF,UAAU,EAACsJ,EAAE,CAAE,CAAEqD,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC7BzM,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAAAV,QAAA,CACxBnI,CAAC,CAAC,mCAAmC,CAAC,EAAI,yBAAyB,CAC1D,CAAC,EACb,CACH,CACE,CACN,EACE,CAAC,CAELQ,WAAW,CAACyB,kBAAkB,eAC7BhD,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,OAAO,CAAC1B,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAE,CAAE,CAAAjD,QAAA,CACrD3H,WAAW,CAACyB,kBAAkB,CACrB,CACb,EACI,CAAC,CACJ,CAAC,cAGP9C,KAAA,CAAChB,MAAM,EACLwN,IAAI,CAAE7I,oBAAqB,CAC3B8I,OAAO,CAAExF,2BAA4B,CACrCyF,MAAM,CAAC,OAAO,CACdC,QAAQ,CAAC,IAAI,CACb5C,SAAS,MACT6C,GAAG,CAAC,KAAK,CAAA5D,QAAA,eAETlJ,IAAA,CAACV,WAAW,EAAC8J,EAAE,CAAE,CAAEgD,OAAO,CAAE,cAAc,CAAEtB,KAAK,CAAE,OAAO,CAAEiC,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA9D,QAAA,cACvFlJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,KAAK,CAACT,EAAE,CAAE,CAAEkD,UAAU,CAAE,MAAO,CAAE,CAAApD,QAAA,CACjEnI,CAAC,CAAC,yBAAyB,CAAC,EAAI,wDAAwD,CAC/E,CAAC,CACF,CAAC,cACdf,IAAA,CAACZ,aAAa,EAAC6N,QAAQ,MAAC7D,EAAE,CAAE,CAAE8D,EAAE,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAE,CAAE,CAAA9D,QAAA,cAC3ChJ,KAAA,CAACb,iBAAiB,EAACwK,SAAS,CAAC,KAAK,CAAAX,QAAA,eAChClJ,IAAA,CAACrC,UAAU,EAAC6O,SAAS,MAACpD,EAAE,CAAE,CAAE+D,QAAQ,CAAE,QAAQ,CAAEb,UAAU,CAAE,MAAM,CAAE/C,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,CACzEnI,CAAC,CAAC,8BAA8B,CAAC,CACxB,CAAC,cAEbb,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEmD,aAAa,CAAE,QAAQ,CAAEzB,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAC5DhJ,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAE0B,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACnClJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAC1B,EAAE,CAAE,CAAEgE,QAAQ,CAAE,MAAO,CAAE,CAAAlE,QAAA,CAAC,IAAE,CAAY,CAAC,cAClFlJ,IAAA,CAACrC,UAAU,EAAC6O,SAAS,MAACpD,EAAE,CAAE,CAAEiE,CAAC,CAAE,CAAE,CAAE,CAAAnE,QAAA,CAChCnI,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,EACV,CAAC,cAENb,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAE0B,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACnClJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAC1B,EAAE,CAAE,CAAEgE,QAAQ,CAAE,MAAO,CAAE,CAAAlE,QAAA,CAAC,IAAE,CAAY,CAAC,cAClFlJ,IAAA,CAACrC,UAAU,EAAC6O,SAAS,MAACpD,EAAE,CAAE,CAAEiE,CAAC,CAAE,CAAE,CAAE,CAAAnE,QAAA,CAChCnI,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,EACV,CAAC,cAENb,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAE0B,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACnClJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAC1B,EAAE,CAAE,CAAEgE,QAAQ,CAAE,MAAO,CAAE,CAAAlE,QAAA,CAAC,IAAE,CAAY,CAAC,cAClFlJ,IAAA,CAACrC,UAAU,EAAC6O,SAAS,MAACpD,EAAE,CAAE,CAAEiE,CAAC,CAAE,CAAE,CAAE,CAAAnE,QAAA,CAChCnI,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,EACV,CAAC,cAENb,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAE0B,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACnClJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAC1B,EAAE,CAAE,CAAEgE,QAAQ,CAAE,MAAO,CAAE,CAAAlE,QAAA,CAAC,IAAE,CAAY,CAAC,cAClFlJ,IAAA,CAACrC,UAAU,EAAC6O,SAAS,MAACpD,EAAE,CAAE,CAAEiE,CAAC,CAAE,CAAE,CAAE,CAAAnE,QAAA,CAChCnI,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,EACV,CAAC,cAENb,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAE0B,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACnClJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAC1B,EAAE,CAAE,CAAEgE,QAAQ,CAAE,MAAO,CAAE,CAAAlE,QAAA,CAAC,IAAE,CAAY,CAAC,cAClFlJ,IAAA,CAACrC,UAAU,EAAC6O,SAAS,MAACpD,EAAE,CAAE,CAAEiE,CAAC,CAAE,CAAE,CAAE,CAAAnE,QAAA,CAChCnI,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,EACV,CAAC,cAENb,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAE0B,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eACnClJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAACkB,KAAK,CAAC,SAAS,CAAC1B,EAAE,CAAE,CAAEgE,QAAQ,CAAE,MAAO,CAAE,CAAAlE,QAAA,CAAC,IAAE,CAAY,CAAC,cAClFlJ,IAAA,CAACrC,UAAU,EAAC6O,SAAS,MAACpD,EAAE,CAAE,CAAEiE,CAAC,CAAE,CAAE,CAAE,CAAAnE,QAAA,CAChCnI,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,EACV,CAAC,EACH,CAAC,cAENf,IAAA,CAAChC,GAAG,EAACoL,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAC,CAAE9C,CAAC,CAAE,CAAC,CAAE+C,OAAO,CAAE,kBAAkB,CAAEJ,YAAY,CAAE,CAAC,CAAEF,MAAM,CAAE,WAAW,CAAEC,WAAW,CAAE,SAAU,CAAE,CAAA7C,QAAA,cAClHlJ,IAAA,CAACrC,UAAU,EAAC6O,SAAS,MAACpD,EAAE,CAAE,CAAEiE,CAAC,CAAE,CAAC,CAAEf,UAAU,CAAE,MAAO,CAAE,CAAApD,QAAA,CACpDnI,CAAC,CAAC,mCAAmC,CAAC,CAC7B,CAAC,CACV,CAAC,EACW,CAAC,CACP,CAAC,cAChBb,KAAA,CAACf,aAAa,EAACiK,EAAE,CAAE,CAAEL,cAAc,CAAE,QAAQ,CAAEM,CAAC,CAAE,CAAC,CAAEmB,GAAG,CAAE,CAAE,CAAE,CAAAtB,QAAA,eAC5DlJ,IAAA,CAACxC,MAAM,EACLqO,OAAO,CAAEvE,sBAAuB,CAChCwD,KAAK,CAAC,OAAO,CACblB,OAAO,CAAC,UAAU,CAClBsC,SAAS,cAAElM,IAAA,CAACF,UAAU,GAAE,CAAE,CAC1BsJ,EAAE,CAAE,CAAEgE,QAAQ,CAAE,GAAG,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAC7BpB,IAAI,CAAC,OAAO,CAAA1C,QAAA,CAEXnI,CAAC,CAAC,gBAAgB,CAAC,EAAI,aAAa,CAC/B,CAAC,cACTf,IAAA,CAACxC,MAAM,EACLqO,OAAO,CAAEzE,sBAAuB,CAChC0D,KAAK,CAAC,SAAS,CACflB,OAAO,CAAC,WAAW,CACnBsC,SAAS,cAAElM,IAAA,CAACH,eAAe,GAAE,CAAE,CAC/BuJ,EAAE,CAAE,CAAEgE,QAAQ,CAAE,GAAG,CAAEJ,EAAE,CAAE,CAAE,CAAE,CAC7BpB,IAAI,CAAC,OAAO,CAAA1C,QAAA,CAEXnI,CAAC,CAAC,gBAAgB,CAAC,EAAI,kBAAkB,CACpC,CAAC,EACI,CAAC,EACV,CAAC,cAGTb,KAAA,CAACxC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,eACvBlJ,IAAA,UACEqL,IAAI,CAAC,MAAM,CACXiC,MAAM,CAAC,SAAS,CAChBC,KAAK,CAAE,CAAEzE,OAAO,CAAE,MAAO,CAAE,CAC3B+B,EAAE,CAAC,wBAAwB,CAC3BX,QAAQ,CAAEtD,0BAA2B,CACtC,CAAC,cACF1G,KAAA,CAACtC,WAAW,EAACqM,SAAS,MAAC5I,KAAK,CAAE,CAAC,CAACE,WAAW,CAACuB,cAAe,CAAAoG,QAAA,eACzDlJ,IAAA,CAACnC,UAAU,EAAC2P,MAAM,MAACxD,QAAQ,MAAAd,QAAA,CAAEnI,CAAC,CAAC,wBAAwB,CAAC,CAAa,CAAC,cACtEf,IAAA,CAAChC,GAAG,EAACoL,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAE,CAAE,CAAAjD,QAAA,cACjBlJ,IAAA,UAAOyN,OAAO,CAAC,wBAAwB,CAAAvE,QAAA,cACrClJ,IAAA,CAACxC,MAAM,EACLoM,OAAO,CAAC,UAAU,CAClBC,SAAS,CAAC,MAAM,CAChBI,SAAS,MACTiC,SAAS,cAAElM,IAAA,CAACT,eAAe,GAAE,CAAE,CAAA2J,QAAA,CAE9B/G,QAAQ,CAACW,cAAc,CAAGX,QAAQ,CAACW,cAAc,CAACkD,IAAI,CAAGjF,CAAC,CAAC,mCAAmC,CAAC,CAC1F,CAAC,CACJ,CAAC,CACL,CAAC,CACLoB,QAAQ,CAACY,qBAAqB,eAC7B/C,IAAA,CAAChC,GAAG,EAACoL,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAC,CAAErD,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAAG,QAAA,cAC5DlJ,IAAA,CAACrB,MAAM,EAAC+M,GAAG,CAAEvJ,QAAQ,CAACY,qBAAsB,CAAC2K,GAAG,CAAC,iBAAiB,CAACtE,EAAE,CAAE,CAAEiD,KAAK,CAAE,GAAG,CAAEV,MAAM,CAAE,GAAI,CAAE,CAAE,CAAC,CACnG,CACN,cACD3L,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAEvJ,WAAW,CAACuB,cAAc,CAAG,OAAO,CAAG,eAAgB,CAAAoG,QAAA,CACvF3H,WAAW,CAACuB,cAAc,EAAI/B,CAAC,CAAC,yBAAyB,CAAC,CACjD,CAAC,EACF,CAAC,EACV,CAAC,cAEPf,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBhJ,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAE0C,MAAM,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEC,YAAY,CAAE,CAAC,CAAE3C,CAAC,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eAC3EhJ,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAEN,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEO,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACzFlJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,IAAI,CAAAV,QAAA,CACrBnI,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cACbf,IAAA,CAACxC,MAAM,EACLoM,OAAO,CAAC,WAAW,CACnBkB,KAAK,CAAC,SAAS,CACfe,OAAO,CAAEA,CAAA,GAAM,CACb;AACA,KAAM,CAAA3G,UAAU,CAAG,CACjB,GAAG/C,QAAQ,CACXW,cAAc,CAAE,IAClB,CAAC,CACDuB,YAAY,CAACc,OAAO,CAAC,wBAAwB,CAAEX,IAAI,CAACY,SAAS,CAACF,UAAU,CAAC,CAAC,CAE1E;AACA,GAAI,CAAAjE,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAE0H,IAAI,IAAK,kBAAkB,CAAE,CAC5C3H,QAAQ,CAAC,uBAAuB,CAAC,CACnC,CAAC,IAAM,CACLA,QAAQ,CAAC,0BAA0B,CAAC,CACtC,CACF,CAAE,CAAAkI,QAAA,CAEDnI,CAAC,CAAC,8BAA8B,CAAC,CAC5B,CAAC,EACN,CAAC,cACNf,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,gBAAgB,CAAA5B,QAAA,CAC/CnI,CAAC,CAAC,mCAAmC,CAAC,CAC7B,CAAC,cAGbb,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAE,CAAE,CAAAjD,QAAA,EAChBnB,MAAM,CAACS,OAAO,CAACrG,QAAQ,CAACc,cAAc,CAAC,CAACwH,GAAG,CAACkD,KAAA,MAAC,CAACC,GAAG,CAAEC,KAAK,CAAC,CAAAF,KAAA,OACxD,CAAAE,KAAK,CAACxI,MAAM,CAAG,CAAC,eACdrF,IAAA,CAAChC,GAAG,EAAWoL,EAAE,CAAE,CAAEG,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,cAC3BhJ,KAAA,CAACvC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEkD,UAAU,CAAE,MAAO,CAAE,CAAApD,QAAA,EACpDhF,IAAI,CAAC0J,GAAG,CAAC,CAAC,IAAE,cAAA1N,KAAA,SAAMqN,KAAK,CAAE,CAAEjB,UAAU,CAAE,QAAS,CAAE,CAAApD,QAAA,EAAE2E,KAAK,CAACxI,MAAM,CAAC,GAAC,CAACtE,CAAC,CAAC,mBAAmB,CAAC,EAAO,CAAC,EACxF,CAAC,EAHL6M,GAIL,CACN,EACF,CAAC,CAED7F,MAAM,CAAC+F,MAAM,CAAC3L,QAAQ,CAACc,cAAc,CAAC,CAAC8K,KAAK,CAACF,KAAK,EAAIA,KAAK,CAACxI,MAAM,GAAK,CAAC,CAAC,eACxErF,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,OAAO,CAAA5B,QAAA,CACtCnI,CAAC,CAAC,0BAA0B,CAAC,CACpB,CACb,EACE,CAAC,EACH,CAAC,CACF,CAAC,cAGPb,KAAA,CAACxC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,eACvBlJ,IAAA,CAACvC,SAAS,EACRuM,QAAQ,MACRC,SAAS,MACToB,IAAI,CAAC,QAAQ,CACbrF,IAAI,CAAC,gBAAgB,CACrB/B,KAAK,CAAElD,CAAC,CAAC,wBAAwB,CAAE,CACnCqK,WAAW,CAAErK,CAAC,CAAC,mCAAmC,CAAE,CACpDkF,KAAK,CAAE9D,QAAQ,CAACsB,cAAe,CAC/ByG,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACkC,cAAe,CACpC0G,UAAU,CAAE5I,WAAW,CAACkC,cAAc,EAAI,EAAG,CAC7CuK,UAAU,CAAE,CACVC,cAAc,cAAEjO,IAAA,CAACvB,cAAc,EAACyP,QAAQ,CAAC,OAAO,CAAAhF,QAAA,CAAC,GAAC,CAAgB,CACpE,CAAE,CACFoC,UAAU,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAI,CAAE,CAClC,CAAC,CAGDrJ,QAAQ,CAACsB,cAAc,CAAG,CAAC,eAC1BvD,KAAA,CAAClC,GAAG,EAACoL,EAAE,CAAE,CAAE+C,EAAE,CAAE,CAAC,CAAE9C,CAAC,CAAE,CAAC,CAAE+C,OAAO,CAAE,kBAAkB,CAAEN,MAAM,CAAE,YAAY,CAAEC,WAAW,CAAE,cAAc,CAAEC,YAAY,CAAE,CAAE,CAAE,CAAA9C,QAAA,eACxHlJ,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,WAAW,CAACkB,KAAK,CAAC,SAAS,CAAChB,YAAY,MAAAZ,QAAA,CACzDnI,CAAC,CAAC,sBAAsB,CAAC,EAAI,gCAAgC,CACpD,CAAC,cACbb,KAAA,CAACvC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEkD,UAAU,CAAE,MAAM,CAAExB,KAAK,CAAE,cAAe,CAAE,CAAA5B,QAAA,EAAC,GAC5E,CAAC7I,wBAAwB,CAAC8B,QAAQ,CAACsB,cAAc,CAAC,CAAC0K,OAAO,CAAC,CAAC,CAAC,EACpD,CAAC,EACV,CACN,EACG,CAAC,cAGPnO,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,cACvBlJ,IAAA,CAACvC,SAAS,EACRuM,QAAQ,MACRC,SAAS,MACToB,IAAI,CAAC,QAAQ,CACbrF,IAAI,CAAC,kBAAkB,CACvB/B,KAAK,CAAElD,CAAC,CAAC,0BAA0B,CAAC,EAAI,oBAAqB,CAC7DqK,WAAW,CAAErK,CAAC,CAAC,qCAAqC,CAAC,EAAI,yBAA0B,CACnFkF,KAAK,CAAE9D,QAAQ,CAACuB,gBAAiB,CACjCwG,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACmC,gBAAiB,CACtCyG,UAAU,CAAE5I,WAAW,CAACmC,gBAAgB,EAAI,EAAG,CAC/CsK,UAAU,CAAE,CACVC,cAAc,cAAEjO,IAAA,CAACvB,cAAc,EAACyP,QAAQ,CAAC,OAAO,CAAAhF,QAAA,CAAC,GAAC,CAAgB,CACpE,CAAE,CACFoC,UAAU,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAI,CAAE,CAClC,CAAC,CACE,CAAC,cAGPxL,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,cACvBhJ,KAAA,CAACtC,WAAW,EAACqM,SAAS,MAACD,QAAQ,MAAC3I,KAAK,CAAE,CAAC,CAACE,WAAW,CAACoC,QAAS,CAAAuF,QAAA,eAC5DlJ,IAAA,CAACnC,UAAU,EAAAqL,QAAA,CAAEnI,CAAC,CAAC,kBAAkB,CAAC,CAAa,CAAC,cAChDf,IAAA,CAAClC,MAAM,EACLkI,IAAI,CAAC,UAAU,CACfC,KAAK,CAAE9D,QAAQ,CAACwB,QAAS,CACzBuG,QAAQ,CAAEpE,YAAa,CAAAoD,QAAA,CAEtB3L,SAAS,CAACkN,GAAG,CAAE9G,QAAQ,eACtB3D,IAAA,CAACjC,QAAQ,EAAsBkI,KAAK,CAAEtC,QAAQ,CAACsC,KAAM,CAAAiD,QAAA,CAClDvF,QAAQ,CAACM,KAAK,EADFN,QAAQ,CAACsC,KAEd,CACX,CAAC,CACI,CAAC,CACR1E,WAAW,CAACoC,QAAQ,eACnB3D,IAAA,CAACrC,UAAU,EAACiM,OAAO,CAAC,OAAO,CAACkB,KAAK,CAAC,OAAO,CAAA5B,QAAA,CACtC3H,WAAW,CAACoC,QAAQ,CACX,CACb,EACU,CAAC,CACV,CAAC,cAGP3D,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAACI,EAAE,CAAE,CAAE,CAAAb,QAAA,cACvBlJ,IAAA,CAACvC,SAAS,EACRwM,SAAS,MACTjE,IAAI,CAAC,OAAO,CACZ/B,KAAK,CAAElD,CAAC,CAAC,eAAe,CAAE,CAC1BkF,KAAK,CAAE9D,QAAQ,CAACyB,KAAM,CACtBsG,QAAQ,CAAEpE,YAAa,CACvBzE,KAAK,CAAE,CAAC,CAACE,WAAW,CAACqC,KAAM,CAC3BuG,UAAU,CAAE5I,WAAW,CAACqC,KAAK,EAAI7C,CAAC,CAAC,mBAAmB,CAAE,CACxDqK,WAAW,CAAErK,CAAC,CAAC,uBAAuB,CAAC,EAAI,SAAU,CACtD,CAAC,CACE,CAAC,cAEPf,IAAA,CAACtC,IAAI,EAACgM,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAT,QAAA,cAChBlJ,IAAA,CAACxC,MAAM,EACL6N,IAAI,CAAC,QAAQ,CACbzB,OAAO,CAAC,WAAW,CACnBkB,KAAK,CAAC,SAAS,CACfc,IAAI,CAAC,OAAO,CACZ3B,SAAS,MACTmE,QAAQ,CAAEjN,OAAQ,CAAA+H,QAAA,CAEjB/H,OAAO,cACNnB,IAAA,CAACzB,gBAAgB,EAACqN,IAAI,CAAE,EAAG,CAAE,CAAC,CAC5B9K,MAAM,CACRC,CAAC,CAAC,sCAAsC,CAAC,EAAI,eAAe,CAE5DA,CAAC,CAAC,4BAA4B,CAC/B,CACK,CAAC,CACL,CAAC,EACH,CAAC,cAGPf,IAAA,CAACR,eAAe,EACdkN,IAAI,CAAE3K,cAAe,CACrBsM,QAAQ,CAAEpM,YAAa,CACvB0K,OAAO,CAAEA,CAAA,GAAM3K,iBAAiB,CAAC,KAAK,CAAE,CACxCsM,MAAM,CAAGC,IAAI,EAAK,CAChB,KAAM,CAAA1H,IAAI,CAAG,GAAI,CAAAwB,IAAI,CAAC,CAACkG,IAAI,CAAC,CAAE,aAAa,CAAE,CAAElD,IAAI,CAAEkD,IAAI,CAAClD,IAAK,CAAC,CAAC,CACjEjJ,WAAW,CAACmE,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPzD,cAAc,CAAE+D,IAAI,CACpB9D,qBAAqB,CAAEiE,GAAG,CAACC,eAAe,CAACsH,IAAI,CACjD,CAAC,CAAC,CAAC,CACHvM,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAE,CACH,CAAC,EACE,CAAC,EACF,CAAC,CAEZ,CAAC,CAED,cAAe,CAAArB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}