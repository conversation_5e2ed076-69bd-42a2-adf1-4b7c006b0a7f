{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useLocation,useNavigate}from'react-router-dom';import{Container,Typography,Paper,Box,Divider,useTheme,Fade,List,ListItem,ListItemText,ListItemIcon,Tabs,Tab,Card,CardContent,alpha,Stack}from'@mui/material';import{Security as SecurityIcon,Schedule as ScheduleIcon,Person as PersonIcon,School as SchoolIcon,Info as InfoIcon,Payment as PaymentIcon,Refresh as RefreshIcon,Description as DescriptionIcon}from'@mui/icons-material';import Layout from'../components/Layout';import termsConditions from'../i18n/translations/termsConditions';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PlatformPolicy=()=>{const{t,i18n}=useTranslation();const theme=useTheme();const location=useLocation();const navigate=useNavigate();const[isRTL,setIsRTL]=useState(i18n.language==='ar');const[activeTab,setActiveTab]=useState(0);useEffect(()=>{setIsRTL(i18n.language==='ar');document.dir=i18n.language==='ar'?'rtl':'ltr';document.documentElement.dir=i18n.language==='ar'?'rtl':'ltr';document.body.dir=i18n.language==='ar'?'rtl':'ltr';// Handle URL-based navigation to specific sections\nconst hash=location.hash;if(hash==='#terms')setActiveTab(0);else if(hash==='#privacy')setActiveTab(1);else if(hash==='#booking')setActiveTab(2);else if(hash==='#payment')setActiveTab(3);else if(hash==='#refund')setActiveTab(4);},[i18n.language,location.hash]);const handleTabChange=(event,newValue)=>{setActiveTab(newValue);const tabs=['#terms','#privacy','#booking','#payment','#refund'];navigate(`/platform-policy${tabs[newValue]}`,{replace:true});};const tabsConfig=[{label:i18n.language==='ar'?'الشروط والأحكام':'Terms & Conditions',icon:/*#__PURE__*/_jsx(DescriptionIcon,{})},{label:i18n.language==='ar'?'سياسة الخصوصية':'Privacy Policy',icon:/*#__PURE__*/_jsx(SecurityIcon,{})},{label:i18n.language==='ar'?'سياسة الحجز والإلغاء':'Booking & Cancellation',icon:/*#__PURE__*/_jsx(ScheduleIcon,{})},{label:i18n.language==='ar'?'سياسة الدفع':'Payment Policy',icon:/*#__PURE__*/_jsx(PaymentIcon,{})},{label:i18n.language==='ar'?'سياسة الاسترداد':'Refund Policy',icon:/*#__PURE__*/_jsx(RefreshIcon,{})}];// Helper component for section cards\nconst SectionCard=_ref=>{let{title,children,icon,bg}=_ref;return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{mb:4,borderRadius:3,direction:isRTL?'rtl':'ltr',...(isRTL?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:bg||alpha(theme.palette.primary.main,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2,flexDirection:isRTL?'row-reverse':'row',direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[icon&&/*#__PURE__*/_jsx(Box,{sx:{mr:isRTL?0:2,ml:isRTL?2:0,color:theme.palette.primary.main},children:icon}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:title})]}),/*#__PURE__*/_jsx(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:children})]})});};// Helper function to render subsections\nconst renderSubSection=subSection=>{if(!subSection||typeof subSection!=='object')return null;return/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:subSection.title}),subSection.points&&/*#__PURE__*/_jsx(List,{children:subSection.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]});};// Tab panel component\nconst TabPanel=_ref2=>{let{children,value,index}=_ref2;return/*#__PURE__*/_jsx(\"div\",{hidden:value!==index,children:value===index&&/*#__PURE__*/_jsx(Box,{sx:{py:3,direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:children})});};// Render Terms and Conditions\nconst renderTermsAndConditions=()=>{const termsContent=i18n.language==='ar'?termsConditions.ar.termsConditions:termsConditions.en.termsConditions;if(!termsContent||typeof termsContent!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:3,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:termsContent.title||(isRTL?'الشروط والأحكام':'Terms and Conditions')}),/*#__PURE__*/_jsx(Stack,{spacing:3,children:termsContent.content&&termsContent.content.split(/\\n\\s*\\n/).filter(section=>section.trim()!=='').map((section,idx)=>{const lines=section.split('\\n');const title=lines[0];const body=lines.slice(1).join('\\n');return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{borderRadius:3,direction:isRTL?'rtl':'ltr',...(isRTL?{borderRight:`4px solid ${theme.palette.primary.main}`}:{borderLeft:`4px solid ${theme.palette.primary.main}`}),backgroundColor:idx%2===0?alpha(theme.palette.primary.main,0.02):alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:title}),body&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',whiteSpace:'pre-line',textAlign:isRTL?'right':'left'},children:body})]})},idx);})})]});};// Render Privacy Policy\nconst renderPrivacyPolicy=()=>{const privacy=t('privacy',{returnObjects:true});if(!privacy||typeof privacy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}/**\r\n     * Safely fetch a translation; returns empty string if key is missing.\r\n     */const safeT=key=>{const val=t(key);return val&&!val.includes(key)?val:'';};const renderStandardSection=num=>{const subtitle=safeT(`privacy.section${num}.subtitle`);const content=safeT(`privacy.section${num}.description`);const bullets=[1,2,3,4,5].map(i=>safeT(`privacy.section${num}.item${i}`)).filter(item=>item);return/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t(`privacy.section${num}.title`)}),subtitle&&/*#__PURE__*/_jsx(Typography,{mb:1,children:subtitle}),content&&/*#__PURE__*/_jsx(Typography,{mb:1,children:content}),bullets.length>0&&/*#__PURE__*/_jsx(List,{children:bullets.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{textAlign:isRTL?'right':'left'}})]},idx))})]},num);};return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacy.title')}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:t('privacy.intro')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),[1,2,3,4,5,6,7,8,9].map(num=>renderStandardSection(num))]});};// Render Booking & Cancellation Policy\nconst renderBookingPolicy=()=>{const policy=t('policies.bookingCancellation',{returnObjects:true});if(!policy||typeof policy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",sx:{mb:3,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsx(SectionCard,{title:policy.studentPolicy.title,icon:/*#__PURE__*/_jsx(PersonIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[renderSubSection(policy.studentPolicy.booking),renderSubSection(policy.studentPolicy.cancellation),renderSubSection(policy.studentPolicy.rescheduling),renderSubSection(policy.studentPolicy.lateArrival)]})}),/*#__PURE__*/_jsx(SectionCard,{title:policy.tutorPolicy.title,icon:/*#__PURE__*/_jsx(SchoolIcon,{}),bg:alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[renderSubSection(policy.tutorPolicy.availability),renderSubSection(policy.tutorPolicy.cancellation),renderSubSection(policy.tutorPolicy.rescheduling),renderSubSection(policy.tutorPolicy.lateArrival)]})}),/*#__PURE__*/_jsx(SectionCard,{title:policy.generalNotes.title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:policy.generalNotes.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5,textAlign:isRTL?'right':'left',pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"info\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})})]});};// Render Payment Policy\nconst renderPaymentPolicy=()=>{const policy=t('policies.bookingPayment',{returnObjects:true});if(!policy||typeof policy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}const renderSection=section=>/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(PaymentIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))}),section.description&&/*#__PURE__*/_jsx(Typography,{sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.description})]})});return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",sx:{mb:3,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),renderSection(policy.section1),renderSection(policy.section2),renderSection(policy.section3),renderSection(policy.section4),renderSection(policy.section5),renderSection(policy.section6),renderSection(policy.section7),renderSection(policy.section8)]});};// Render Refund Policy\nconst renderRefundPolicy=()=>{const refund=t('policies.refund',{returnObjects:true});if(!refund||typeof refund!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:refund.title}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{title:refund.section1.title,icon:/*#__PURE__*/_jsx(RefreshIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refund.section1.description}),/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:refund.section1.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'}})]},idx))})]}),[2,3,4,5,6,7,8].map(num=>{const section=refund[`section${num}`];if(!section)return null;return/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(RefreshIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.description&&/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:section.description}),section.items&&/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:section.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'}})]},idx))})]})},num);})]});};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Box,{sx:{py:4,minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",sx:{direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:1000,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:2,md:4},mb:4,borderRadius:3,direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left','& *':{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},'& .MuiTypography-root':{textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr'},'& .MuiList-root':{direction:isRTL?'rtl':'ltr'},'& .MuiListItem-root':{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:4,textAlign:'center',direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",component:\"h1\",sx:{mb:2,fontWeight:800,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,direction:isRTL?'rtl':'ltr',textAlign:'center'},children:isRTL?'سياسات المنصة':'Platform Policies'}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',direction:isRTL?'rtl':'ltr',textAlign:'center'},children:isRTL?'جميع السياسات والشروط الخاصة بمنصة علّمني أون لاين في مكان واحد':'All Allemnionline platform policies and terms in one place'})]}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider',mb:3,direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{'& .MuiTab-root':{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',minHeight:64,textAlign:isRTL?'right':'left'},'& .MuiTabs-flexContainer':{direction:isRTL?'rtl':'ltr'}},children:tabsConfig.map((tab,index)=>/*#__PURE__*/_jsx(Tab,{icon:tab.icon,label:tab.label,iconPosition:\"start\",sx:{flexDirection:isRTL?'row-reverse':'row',textAlign:isRTL?'right':'left','& .MuiTab-iconWrapper':{mr:isRTL?0:1,ml:isRTL?1:0}}},index))})}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:0,children:renderTermsAndConditions()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:1,children:renderPrivacyPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:2,children:renderBookingPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:3,children:renderPaymentPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:4,children:renderRefundPolicy()})]})})})})});};export default PlatformPolicy;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useLocation", "useNavigate", "Container", "Typography", "Paper", "Box", "Divider", "useTheme", "Fade", "List", "ListItem", "ListItemText", "ListItemIcon", "Tabs", "Tab", "Card", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "<PERSON><PERSON>", "Security", "SecurityIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Refresh", "RefreshIcon", "Description", "DescriptionIcon", "Layout", "termsConditions", "jsx", "_jsx", "jsxs", "_jsxs", "PlatformPolicy", "t", "i18n", "theme", "location", "navigate", "isRTL", "setIsRTL", "language", "activeTab", "setActiveTab", "document", "dir", "documentElement", "body", "hash", "handleTabChange", "event", "newValue", "tabs", "replace", "tabsConfig", "label", "icon", "SectionCard", "_ref", "title", "children", "bg", "elevation", "sx", "mb", "borderRadius", "direction", "borderRight", "palette", "primary", "main", "borderLeft", "backgroundColor", "display", "alignItems", "flexDirection", "textAlign", "mr", "ml", "color", "variant", "fontWeight", "fontFamily", "renderSubSection", "subSection", "points", "map", "item", "idx", "pl", "pr", "min<PERSON><PERSON><PERSON>", "mx", "TabPanel", "_ref2", "value", "index", "hidden", "py", "renderTermsAndConditions", "termsContent", "ar", "en", "spacing", "content", "split", "filter", "section", "trim", "lines", "slice", "join", "secondary", "light", "lineHeight", "whiteSpace", "renderPrivacyPolicy", "privacy", "returnObjects", "safeT", "key", "val", "includes", "renderStandardSection", "num", "subtitle", "bullets", "i", "length", "renderBookingPolicy", "policy", "studentPolicy", "booking", "cancellation", "rescheduling", "lateArrival", "tutorPolicy", "availability", "generalNotes", "renderPaymentPolicy", "renderSection", "description", "section1", "section2", "section3", "section4", "section5", "section6", "section7", "section8", "renderRefundPolicy", "refund", "items", "minHeight", "background", "max<PERSON><PERSON><PERSON>", "in", "timeout", "p", "xs", "md", "component", "borderBottom", "borderColor", "onChange", "scrollButtons", "tab", "iconPosition"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PlatformPolicy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Paper,\r\n  Box,\r\n  Divider,\r\n  useTheme,\r\n  Fade,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Tabs,\r\n  Tab,\r\n  Card,\r\n  CardContent,\r\n  alpha,\r\n  Stack,\r\n} from '@mui/material';\r\nimport {\r\n  Security as SecurityIcon,\r\n  Schedule as ScheduleIcon,\r\n  Person as PersonIcon,\r\n  School as SchoolIcon,\r\n  Info as InfoIcon,\r\n  Payment as PaymentIcon,\r\n  Refresh as RefreshIcon,\r\n  Description as DescriptionIcon,\r\n} from '@mui/icons-material';\r\nimport Layout from '../components/Layout';\r\nimport termsConditions from '../i18n/translations/termsConditions';\r\n\r\nconst PlatformPolicy = () => {\r\n  const { t, i18n } = useTranslation();\r\n  const theme = useTheme();\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const [isRTL, setIsRTL] = useState(i18n.language === 'ar');\r\n  const [activeTab, setActiveTab] = useState(0);\r\n\r\n  useEffect(() => {\r\n    setIsRTL(i18n.language === 'ar');\r\n    document.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n    document.body.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n\r\n    // Handle URL-based navigation to specific sections\r\n    const hash = location.hash;\r\n    if (hash === '#terms') setActiveTab(0);\r\n    else if (hash === '#privacy') setActiveTab(1);\r\n    else if (hash === '#booking') setActiveTab(2);\r\n    else if (hash === '#payment') setActiveTab(3);\r\n    else if (hash === '#refund') setActiveTab(4);\r\n  }, [i18n.language, location.hash]);\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    const tabs = ['#terms', '#privacy', '#booking', '#payment', '#refund'];\r\n    navigate(`/platform-policy${tabs[newValue]}`, { replace: true });\r\n  };\r\n\r\n  const tabsConfig = [\r\n    { label: i18n.language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions', icon: <DescriptionIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy', icon: <SecurityIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation', icon: <ScheduleIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الدفع' : 'Payment Policy', icon: <PaymentIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الاسترداد' : 'Refund Policy', icon: <RefreshIcon /> },\r\n  ];\r\n\r\n  // Helper component for section cards\r\n  const SectionCard = ({ title, children, icon, bg }) => (\r\n    <Card\r\n      elevation={2}\r\n      sx={{\r\n        mb: 4,\r\n        borderRadius: 3,\r\n        direction: isRTL ? 'rtl' : 'ltr',\r\n        ...(isRTL\r\n          ? { borderRight: `6px solid ${theme.palette.primary.main}` }\r\n          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\r\n        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),\r\n      }}\r\n    >\r\n      <CardContent sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n        <Box sx={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          mb: 2,\r\n          flexDirection: isRTL ? 'row-reverse' : 'row',\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n          textAlign: isRTL ? 'right' : 'left'\r\n        }}>\r\n          {icon && (\r\n            <Box sx={{ mr: isRTL ? 0 : 2, ml: isRTL ? 2 : 0, color: theme.palette.primary.main }}>\r\n              {icon}\r\n            </Box>\r\n          )}\r\n          <Typography\r\n            variant=\"h5\"\r\n            fontWeight={600}\r\n            color={theme.palette.primary.main}\r\n            sx={{\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n              direction: isRTL ? 'rtl' : 'ltr',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {title}\r\n          </Typography>\r\n        </Box>\r\n        <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n          {children}\r\n        </Box>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n\r\n  // Helper function to render subsections\r\n  const renderSubSection = (subSection) => {\r\n    if (!subSection || typeof subSection !== 'object') return null;\r\n\r\n    return (\r\n      <Box sx={{ mb: 3 }}>\r\n        <Typography\r\n          variant=\"h6\"\r\n          sx={{\r\n            fontWeight: 600,\r\n            color: theme.palette.primary.main,\r\n            mb: 1,\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {subSection.title}\r\n        </Typography>\r\n        {subSection.points && (\r\n          <List>\r\n            {subSection.points.map((item, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                pl: isRTL ? 0 : 2,\r\n                pr: isRTL ? 2 : 0,\r\n                flexDirection: 'row',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mx: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                }}>\r\n                  <InfoIcon color=\"success\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        )}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Tab panel component\r\n  const TabPanel = ({ children, value, index }) => (\r\n    <div hidden={value !== index}>\r\n      {value === index && (\r\n        <Box sx={{\r\n          py: 3,\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n          textAlign: isRTL ? 'right' : 'left'\r\n        }}>\r\n          {children}\r\n        </Box>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  // Render Terms and Conditions\r\n  const renderTermsAndConditions = () => {\r\n    const termsContent = i18n.language === 'ar' ? termsConditions.ar.termsConditions : termsConditions.en.termsConditions;\r\n\r\n    if (!termsContent || typeof termsContent !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={3}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {termsContent.title || (isRTL ? 'الشروط والأحكام' : 'Terms and Conditions')}\r\n        </Typography>\r\n        <Stack spacing={3}>\r\n          {termsContent.content && termsContent.content\r\n            .split(/\\n\\s*\\n/)\r\n            .filter((section) => section.trim() !== '')\r\n            .map((section, idx) => {\r\n              const lines = section.split('\\n');\r\n              const title = lines[0];\r\n              const body = lines.slice(1).join('\\n');\r\n              return (\r\n                <Card\r\n                  key={idx}\r\n                  elevation={2}\r\n                  sx={{\r\n                    borderRadius: 3,\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    ...(isRTL\r\n                      ? { borderRight: `4px solid ${theme.palette.primary.main}` }\r\n                      : { borderLeft: `4px solid ${theme.palette.primary.main}` }),\r\n                    backgroundColor: idx % 2 === 0\r\n                      ? alpha(theme.palette.primary.main, 0.02)\r\n                      : alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02),\r\n                  }}\r\n                >\r\n                  <CardContent>\r\n                    <Typography\r\n                      variant=\"h6\"\r\n                      fontWeight={600}\r\n                      mb={2}\r\n                      sx={{\r\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                        color: theme.palette.primary.main,\r\n                        textAlign: isRTL ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {title}\r\n                    </Typography>\r\n                    {body && (\r\n                      <Typography\r\n                        variant=\"body1\"\r\n                        sx={{\r\n                          lineHeight: 1.8,\r\n                          fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                          whiteSpace: 'pre-line',\r\n                          textAlign: isRTL ? 'right' : 'left'\r\n                        }}\r\n                      >\r\n                        {body}\r\n                      </Typography>\r\n                    )}\r\n                  </CardContent>\r\n                </Card>\r\n              );\r\n            })}\r\n        </Stack>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Privacy Policy\r\n  const renderPrivacyPolicy = () => {\r\n    const privacy = t('privacy', { returnObjects: true });\r\n\r\n    if (!privacy || typeof privacy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    /**\r\n     * Safely fetch a translation; returns empty string if key is missing.\r\n     */\r\n    const safeT = (key) => {\r\n      const val = t(key);\r\n      return val && !val.includes(key) ? val : '';\r\n    };\r\n\r\n    const renderStandardSection = (num) => {\r\n      const subtitle = safeT(`privacy.section${num}.subtitle`);\r\n      const content = safeT(`privacy.section${num}.description`);\r\n      const bullets = [1, 2, 3, 4, 5]\r\n        .map((i) => safeT(`privacy.section${num}.item${i}`))\r\n        .filter((item) => item);\r\n\r\n      return (\r\n        <SectionCard key={num}>\r\n          <Typography\r\n            variant=\"h5\"\r\n            fontWeight={600}\r\n            color={theme.palette.primary.main}\r\n            mb={2}\r\n            sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\r\n          >\r\n            {t(`privacy.section${num}.title`)}\r\n          </Typography>\r\n          {subtitle && <Typography mb={1}>{subtitle}</Typography>}\r\n          {content && <Typography mb={1}>{content}</Typography>}\r\n          {bullets.length > 0 && (\r\n            <List>\r\n              {bullets.map((item, idx) => (\r\n                <ListItem\r\n                  key={idx}\r\n                  sx={{\r\n                    pl: isRTL ? 0 : 2,\r\n                    pr: isRTL ? 2 : 0,\r\n                    flexDirection: 'row',\r\n                    textAlign: isRTL ? 'right' : 'left',\r\n                  }}\r\n                >\r\n                  <ListItemIcon\r\n                    sx={{\r\n                      minWidth: 'auto',\r\n                      mx: isRTL ? 0 : 1,\r\n                      ml: isRTL ? 1 : 0,\r\n                    }}\r\n                  >\r\n                    <InfoIcon color=\"primary\" />\r\n                  </ListItemIcon>\r\n                  <ListItemText\r\n                    primary={item}\r\n                    sx={{ textAlign: isRTL ? 'right' : 'left' }}\r\n                  />\r\n                </ListItem>\r\n              ))}\r\n            </List>\r\n          )}\r\n        </SectionCard>\r\n      );\r\n    };\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        {/* Title & Intro */}\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\r\n        >\r\n          {t('privacy.title')}\r\n        </Typography>\r\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\r\n          {t('privacy.intro')}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {/* Sections 1-9 */}\r\n        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => renderStandardSection(num))}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Booking & Cancellation Policy\r\n  const renderBookingPolicy = () => {\r\n    const policy = t('policies.bookingCancellation', { returnObjects: true });\r\n\r\n    if (!policy || typeof policy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.title}\r\n        </Typography>\r\n        <Typography\r\n          variant=\"subtitle1\"\r\n          color=\"text.secondary\"\r\n          sx={{\r\n            mb: 3,\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.subtitle}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {/* Student Policy */}\r\n        <SectionCard title={policy.studentPolicy.title} icon={<PersonIcon />}>\r\n          <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {renderSubSection(policy.studentPolicy.booking)}\r\n            {renderSubSection(policy.studentPolicy.cancellation)}\r\n            {renderSubSection(policy.studentPolicy.rescheduling)}\r\n            {renderSubSection(policy.studentPolicy.lateArrival)}\r\n          </Box>\r\n        </SectionCard>\r\n\r\n        {/* Tutor Policy */}\r\n        <SectionCard\r\n          title={policy.tutorPolicy.title}\r\n          icon={<SchoolIcon />}\r\n          bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}\r\n        >\r\n          <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {renderSubSection(policy.tutorPolicy.availability)}\r\n            {renderSubSection(policy.tutorPolicy.cancellation)}\r\n            {renderSubSection(policy.tutorPolicy.rescheduling)}\r\n            {renderSubSection(policy.tutorPolicy.lateArrival)}\r\n          </Box>\r\n        </SectionCard>\r\n\r\n        {/* General Notes */}\r\n        <SectionCard title={policy.generalNotes.title} icon={<InfoIcon />}>\r\n          <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {policy.generalNotes.points.map((item, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                py: 0.5,\r\n                textAlign: isRTL ? 'right' : 'left',\r\n                pl: isRTL ? 0 : 2,\r\n                pr: isRTL ? 2 : 0,\r\n                flexDirection: 'row',\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mx: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                }}>\r\n                  <InfoIcon color=\"info\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </SectionCard>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Payment Policy\r\n  const renderPaymentPolicy = () => {\r\n    const policy = t('policies.bookingPayment', { returnObjects: true });\r\n\r\n    if (!policy || typeof policy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    const renderSection = (section) => (\r\n      <SectionCard title={section.title} icon={<PaymentIcon />}>\r\n        <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n          {section.points && (\r\n            <List>\r\n              {section.points.map((item, idx) => (\r\n                <ListItem key={idx} sx={{\r\n                  pl: isRTL ? 0 : 2,\r\n                  pr: isRTL ? 2 : 0,\r\n                  flexDirection: 'row',\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                }}>\r\n                  <ListItemIcon sx={{\r\n                    minWidth: 'auto',\r\n                    mx: isRTL ? 0 : 1,\r\n                    ml: isRTL ? 1 : 0,\r\n                  }}>\r\n                    <InfoIcon color=\"success\" />\r\n                  </ListItemIcon>\r\n                  <ListItemText\r\n                    primary={item}\r\n                    sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                  />\r\n                </ListItem>\r\n              ))}\r\n            </List>\r\n          )}\r\n          {section.description && (\r\n            <Typography\r\n              sx={{\r\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                textAlign: isRTL ? 'right' : 'left'\r\n              }}\r\n            >\r\n              {section.description}\r\n            </Typography>\r\n          )}\r\n        </Box>\r\n      </SectionCard>\r\n    );\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.title}\r\n        </Typography>\r\n        <Typography\r\n          variant=\"subtitle1\"\r\n          color=\"text.secondary\"\r\n          sx={{\r\n            mb: 3,\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.subtitle}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {renderSection(policy.section1)}\r\n        {renderSection(policy.section2)}\r\n        {renderSection(policy.section3)}\r\n        {renderSection(policy.section4)}\r\n        {renderSection(policy.section5)}\r\n        {renderSection(policy.section6)}\r\n        {renderSection(policy.section7)}\r\n        {renderSection(policy.section8)}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Refund Policy\r\n  const renderRefundPolicy = () => {\r\n    const refund = t('policies.refund', { returnObjects: true });\r\n\r\n    if (!refund || typeof refund !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {refund.title}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {/* القسم الأول */}\r\n        <SectionCard title={refund.section1.title} icon={<RefreshIcon />}>\r\n          <Typography\r\n            mb={2}\r\n            sx={{\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {refund.section1.description}\r\n          </Typography>\r\n          <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {refund.section1.items.map((item, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                pl: isRTL ? 0 : 2,\r\n                pr: isRTL ? 2 : 0,\r\n                flexDirection: 'row',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mx: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                }}>\r\n                  <InfoIcon color=\"success\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    textAlign: isRTL ? 'right' : 'left'\r\n                  }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </SectionCard>\r\n\r\n        {/* باقي الأقسام */}\r\n        {[2, 3, 4, 5, 6, 7, 8].map((num) => {\r\n          const section = refund[`section${num}`];\r\n          if (!section) return null;\r\n\r\n          return (\r\n            <SectionCard key={num} title={section.title} icon={<RefreshIcon />}>\r\n              <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                {section.description && (\r\n                  <Typography\r\n                    mb={2}\r\n                    sx={{\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n                      textAlign: isRTL ? 'right' : 'left'\r\n                    }}\r\n                  >\r\n                    {section.description}\r\n                  </Typography>\r\n                )}\r\n                {section.items && (\r\n                  <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                    {section.items.map((item, idx) => (\r\n                      <ListItem key={idx} sx={{\r\n                        pl: isRTL ? 0 : 2,\r\n                        pr: isRTL ? 2 : 0,\r\n                        flexDirection: 'row',\r\n                        textAlign: isRTL ? 'right' : 'left',\r\n                      }}>\r\n                        <ListItemIcon sx={{\r\n                          minWidth: 'auto',\r\n                          mx: isRTL ? 0 : 1,\r\n                          ml: isRTL ? 1 : 0,\r\n                        }}>\r\n                          <InfoIcon color=\"success\" />\r\n                        </ListItemIcon>\r\n                        <ListItemText\r\n                          primary={item}\r\n                          sx={{\r\n                            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                            textAlign: isRTL ? 'right' : 'left'\r\n                          }}\r\n                        />\r\n                      </ListItem>\r\n                    ))}\r\n                  </List>\r\n                )}\r\n              </Box>\r\n            </SectionCard>\r\n          );\r\n        })}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Layout>\r\n      <Box\r\n        sx={{\r\n          py: 4,\r\n          minHeight: '100vh',\r\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n        }}\r\n      >\r\n        <Container maxWidth=\"lg\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n          <Fade in timeout={1000}>\r\n            <Paper\r\n              elevation={3}\r\n              sx={{\r\n                p: { xs: 2, md: 4 },\r\n                mb: 4,\r\n                borderRadius: 3,\r\n                direction: isRTL ? 'rtl' : 'ltr',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n                '& *': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                },\r\n                '& .MuiTypography-root': {\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                },\r\n                '& .MuiList-root': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                },\r\n                '& .MuiListItem-root': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                },\r\n              }}\r\n            >\r\n              {/* Header */}\r\n              <Box sx={{\r\n                mb: 4,\r\n                textAlign: 'center',\r\n                direction: isRTL ? 'rtl' : 'ltr'\r\n              }}>\r\n                <Typography\r\n                  variant=\"h3\"\r\n                  component=\"h1\"\r\n                  sx={{\r\n                    mb: 2,\r\n                    fontWeight: 800,\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    color: theme.palette.primary.main,\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    textAlign: 'center',\r\n                  }}\r\n                >\r\n                  {isRTL ? 'سياسات المنصة' : 'Platform Policies'}\r\n                </Typography>\r\n                <Typography\r\n                  variant=\"subtitle1\"\r\n                  color=\"text.secondary\"\r\n                  sx={{\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    textAlign: 'center',\r\n                  }}\r\n                >\r\n                  {isRTL\r\n                    ? 'جميع السياسات والشروط الخاصة بمنصة علّمني أون لاين في مكان واحد'\r\n                    : 'All Allemnionline platform policies and terms in one place'\r\n                  }\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Tabs */}\r\n              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3, direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                <Tabs\r\n                  value={activeTab}\r\n                  onChange={handleTabChange}\r\n                  variant=\"scrollable\"\r\n                  scrollButtons=\"auto\"\r\n                  sx={{\r\n                    '& .MuiTab-root': {\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                      minHeight: 64,\r\n                      textAlign: isRTL ? 'right' : 'left',\r\n                    },\r\n                    '& .MuiTabs-flexContainer': {\r\n                      direction: isRTL ? 'rtl' : 'ltr',\r\n                    }\r\n                  }}\r\n                >\r\n                  {tabsConfig.map((tab, index) => (\r\n                    <Tab\r\n                      key={index}\r\n                      icon={tab.icon}\r\n                      label={tab.label}\r\n                      iconPosition=\"start\"\r\n                      sx={{\r\n                        flexDirection: isRTL ? 'row-reverse' : 'row',\r\n                        textAlign: isRTL ? 'right' : 'left',\r\n                        '& .MuiTab-iconWrapper': {\r\n                          mr: isRTL ? 0 : 1,\r\n                          ml: isRTL ? 1 : 0,\r\n                        }\r\n                      }}\r\n                    />\r\n                  ))}\r\n                </Tabs>\r\n              </Box>\r\n\r\n              {/* Tab Panels */}\r\n              <TabPanel value={activeTab} index={0}>\r\n                {renderTermsAndConditions()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={1}>\r\n                {renderPrivacyPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={2}>\r\n                {renderBookingPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={3}>\r\n                {renderPaymentPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={4}>\r\n                {renderRefundPolicy()}\r\n              </TabPanel>\r\n\r\n            </Paper>\r\n          </Fade>\r\n        </Container>\r\n      </Box>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport default PlatformPolicy;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,SAAS,CACTC,UAAU,CACVC,KAAK,CACLC,GAAG,CACHC,OAAO,CACPC,QAAQ,CACRC,IAAI,CACJC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,IAAI,CACJC,GAAG,CACHC,IAAI,CACJC,WAAW,CACXC,KAAK,CACLC,KAAK,KACA,eAAe,CACtB,OACEC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,WAAW,GAAI,CAAAC,eAAe,KACzB,qBAAqB,CAC5B,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG5C,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA6C,KAAK,CAAGrC,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAsC,QAAQ,CAAG7C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8C,QAAQ,CAAG7C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC8C,KAAK,CAAEC,QAAQ,CAAC,CAAGnD,QAAQ,CAAC8C,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAC1D,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,CAAC,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACdkD,QAAQ,CAACL,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAChCG,QAAQ,CAACC,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CACrDG,QAAQ,CAACE,eAAe,CAACD,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CACrEG,QAAQ,CAACG,IAAI,CAACF,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CAE1D;AACA,KAAM,CAAAO,IAAI,CAAGX,QAAQ,CAACW,IAAI,CAC1B,GAAIA,IAAI,GAAK,QAAQ,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IAClC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,SAAS,CAAEL,YAAY,CAAC,CAAC,CAAC,CAC9C,CAAC,CAAE,CAACR,IAAI,CAACM,QAAQ,CAAEJ,QAAQ,CAACW,IAAI,CAAC,CAAC,CAElC,KAAM,CAAAC,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC3CR,YAAY,CAACQ,QAAQ,CAAC,CACtB,KAAM,CAAAC,IAAI,CAAG,CAAC,QAAQ,CAAE,UAAU,CAAE,UAAU,CAAE,UAAU,CAAE,SAAS,CAAC,CACtEd,QAAQ,CAAC,mBAAmBc,IAAI,CAACD,QAAQ,CAAC,EAAE,CAAE,CAAEE,OAAO,CAAE,IAAK,CAAC,CAAC,CAClE,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEC,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,iBAAiB,CAAG,oBAAoB,CAAEe,IAAI,cAAE1B,IAAA,CAACJ,eAAe,GAAE,CAAE,CAAC,CACvG,CAAE6B,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,gBAAgB,CAAG,gBAAgB,CAAEe,IAAI,cAAE1B,IAAA,CAAClB,YAAY,GAAE,CAAE,CAAC,CAC/F,CAAE2C,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,sBAAsB,CAAG,wBAAwB,CAAEe,IAAI,cAAE1B,IAAA,CAAChB,YAAY,GAAE,CAAE,CAAC,CAC7G,CAAEyC,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,aAAa,CAAG,gBAAgB,CAAEe,IAAI,cAAE1B,IAAA,CAACR,WAAW,GAAE,CAAE,CAAC,CAC3F,CAAEiC,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,iBAAiB,CAAG,eAAe,CAAEe,IAAI,cAAE1B,IAAA,CAACN,WAAW,GAAE,CAAE,CAAC,CAC/F,CAED;AACA,KAAM,CAAAiC,WAAW,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,QAAQ,CAAEJ,IAAI,CAAEK,EAAG,CAAC,CAAAH,IAAA,oBAChD5B,IAAA,CAACvB,IAAI,EACHuD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChC,IAAIA,KAAK,CACL,CAAE4B,WAAW,CAAE,aAAa/B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAanC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEX,EAAE,EAAIpD,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAC/D,CAAE,CAAAV,QAAA,cAEF5B,KAAA,CAACxB,WAAW,EAACuD,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,eACpD5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CACPU,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBV,EAAE,CAAE,CAAC,CACLW,aAAa,CAAEpC,KAAK,CAAG,aAAa,CAAG,KAAK,CAC5C2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,EACCJ,IAAI,eACH1B,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEc,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAV,QAAA,CAClFJ,IAAI,CACF,CACN,cACD1B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCP,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChE2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDD,KAAK,CACI,CAAC,EACV,CAAC,cACN7B,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,CAChFA,QAAQ,CACN,CAAC,EACK,CAAC,CACV,CAAC,EACR,CAED;AACA,KAAM,CAAAuB,gBAAgB,CAAIC,UAAU,EAAK,CACvC,GAAI,CAACA,UAAU,EAAI,MAAO,CAAAA,UAAU,GAAK,QAAQ,CAAE,MAAO,KAAI,CAE9D,mBACEpD,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjB9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZjB,EAAE,CAAE,CACFkB,UAAU,CAAE,GAAG,CACfF,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCN,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwB,UAAU,CAACzB,KAAK,CACP,CAAC,CACZyB,UAAU,CAACC,MAAM,eAChBvD,IAAA,CAAC7B,IAAI,EAAA2D,QAAA,CACFwB,UAAU,CAACC,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC/BxD,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACA9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACA9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfjD,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWiD,GAiBL,CACX,CAAC,CACE,CACP,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAK,QAAQ,CAAGC,KAAA,MAAC,CAAElC,QAAQ,CAAEmC,KAAK,CAAEC,KAAM,CAAC,CAAAF,KAAA,oBAC1ChE,IAAA,QAAKmE,MAAM,CAAEF,KAAK,GAAKC,KAAM,CAAApC,QAAA,CAC1BmC,KAAK,GAAKC,KAAK,eACdlE,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CACPmC,EAAE,CAAE,CAAC,CACLhC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CACCA,QAAQ,CACN,CACN,CACE,CAAC,EACP,CAED;AACA,KAAM,CAAAuC,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAC,YAAY,CAAGjE,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAGb,eAAe,CAACyE,EAAE,CAACzE,eAAe,CAAGA,eAAe,CAAC0E,EAAE,CAAC1E,eAAe,CAErH,GAAI,CAACwE,YAAY,EAAI,MAAO,CAAAA,YAAY,GAAK,QAAQ,CAAE,CACrD,mBACEtE,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwC,YAAY,CAACzC,KAAK,GAAKpB,KAAK,CAAG,iBAAiB,CAAG,sBAAsB,CAAC,CACjE,CAAC,cACbT,IAAA,CAACpB,KAAK,EAAC6F,OAAO,CAAE,CAAE,CAAA3C,QAAA,CACfwC,YAAY,CAACI,OAAO,EAAIJ,YAAY,CAACI,OAAO,CAC1CC,KAAK,CAAC,SAAS,CAAC,CAChBC,MAAM,CAAEC,OAAO,EAAKA,OAAO,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC1CtB,GAAG,CAAC,CAACqB,OAAO,CAAEnB,GAAG,GAAK,CACrB,KAAM,CAAAqB,KAAK,CAAGF,OAAO,CAACF,KAAK,CAAC,IAAI,CAAC,CACjC,KAAM,CAAA9C,KAAK,CAAGkD,KAAK,CAAC,CAAC,CAAC,CACtB,KAAM,CAAA9D,IAAI,CAAG8D,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACtC,mBACEjF,IAAA,CAACvB,IAAI,EAEHuD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFE,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChC,IAAIA,KAAK,CACL,CAAE4B,WAAW,CAAE,aAAa/B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAanC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEgB,GAAG,CAAG,CAAC,GAAK,CAAC,CAC1B/E,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvC7D,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAAC4C,SAAS,CAAC1C,IAAI,EAAIlC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAAC4C,KAAK,CAAE,IAAI,CAC7E,CAAE,CAAArD,QAAA,cAEF5B,KAAA,CAACxB,WAAW,EAAAoD,QAAA,eACV9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDD,KAAK,CACI,CAAC,CACZZ,IAAI,eACHjB,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFmD,UAAU,CAAE,GAAG,CACfhC,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD4E,UAAU,CAAE,UAAU,CACtBvC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDb,IAAI,CACK,CACb,EACU,CAAC,EAvCTyC,GAwCD,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACL,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA4B,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,OAAO,CAAGnF,CAAC,CAAC,SAAS,CAAE,CAAEoF,aAAa,CAAE,IAAK,CAAC,CAAC,CAErD,GAAI,CAACD,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,CAC3C,mBACEvF,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA;AACJ;AACA,OACI,KAAM,CAAAgF,KAAK,CAAIC,GAAG,EAAK,CACrB,KAAM,CAAAC,GAAG,CAAGvF,CAAC,CAACsF,GAAG,CAAC,CAClB,MAAO,CAAAC,GAAG,EAAI,CAACA,GAAG,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAGC,GAAG,CAAG,EAAE,CAC7C,CAAC,CAED,KAAM,CAAAE,qBAAqB,CAAIC,GAAG,EAAK,CACrC,KAAM,CAAAC,QAAQ,CAAGN,KAAK,CAAC,kBAAkBK,GAAG,WAAW,CAAC,CACxD,KAAM,CAAApB,OAAO,CAAGe,KAAK,CAAC,kBAAkBK,GAAG,cAAc,CAAC,CAC1D,KAAM,CAAAE,OAAO,CAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5BxC,GAAG,CAAEyC,CAAC,EAAKR,KAAK,CAAC,kBAAkBK,GAAG,QAAQG,CAAC,EAAE,CAAC,CAAC,CACnDrB,MAAM,CAAEnB,IAAI,EAAKA,IAAI,CAAC,CAEzB,mBACEvD,KAAA,CAACyB,WAAW,EAAAG,QAAA,eACV9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCN,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAqB,QAAA,CAExE1B,CAAC,CAAC,kBAAkB0F,GAAG,QAAQ,CAAC,CACvB,CAAC,CACZC,QAAQ,eAAI/F,IAAA,CAACnC,UAAU,EAACqE,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAEiE,QAAQ,CAAa,CAAC,CACtDrB,OAAO,eAAI1E,IAAA,CAACnC,UAAU,EAACqE,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAE4C,OAAO,CAAa,CAAC,CACpDsB,OAAO,CAACE,MAAM,CAAG,CAAC,eACjBlG,IAAA,CAAC7B,IAAI,EAAA2D,QAAA,CACFkE,OAAO,CAACxC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACrBxD,KAAA,CAAC9B,QAAQ,EAEP6D,EAAE,CAAE,CACF0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eAEF9B,IAAA,CAAC1B,YAAY,EACX2D,EAAE,CAAE,CACF4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cAEF9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfjD,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CAAEa,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAC7C,CAAC,GApBGiD,GAqBG,CACX,CAAC,CACE,CACP,GAxCeoC,GAyCL,CAAC,CAElB,CAAC,CAED,mBACE5F,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eAEjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAqB,QAAA,CAExE1B,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbJ,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,WAAW,CAACD,KAAK,CAAC,gBAAgB,CAACf,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1D1B,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbJ,IAAA,CAAChC,OAAO,EAACiE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACsB,GAAG,CAAEsC,GAAG,EAAKD,qBAAqB,CAACC,GAAG,CAAC,CAAC,EAClE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAK,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,MAAM,CAAGhG,CAAC,CAAC,8BAA8B,CAAE,CAAEoF,aAAa,CAAE,IAAK,CAAC,CAAC,CAEzE,GAAI,CAACY,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACEpG,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDsE,MAAM,CAACvE,KAAK,CACH,CAAC,cACb7B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,gBAAgB,CACtBhB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDsE,MAAM,CAACL,QAAQ,CACN,CAAC,cACb/F,IAAA,CAAChC,OAAO,EAACiE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BlC,IAAA,CAAC2B,WAAW,EAACE,KAAK,CAAEuE,MAAM,CAACC,aAAa,CAACxE,KAAM,CAACH,IAAI,cAAE1B,IAAA,CAACd,UAAU,GAAE,CAAE,CAAA4C,QAAA,cACnE5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3CuB,gBAAgB,CAAC+C,MAAM,CAACC,aAAa,CAACC,OAAO,CAAC,CAC9CjD,gBAAgB,CAAC+C,MAAM,CAACC,aAAa,CAACE,YAAY,CAAC,CACnDlD,gBAAgB,CAAC+C,MAAM,CAACC,aAAa,CAACG,YAAY,CAAC,CACnDnD,gBAAgB,CAAC+C,MAAM,CAACC,aAAa,CAACI,WAAW,CAAC,EAChD,CAAC,CACK,CAAC,cAGdzG,IAAA,CAAC2B,WAAW,EACVE,KAAK,CAAEuE,MAAM,CAACM,WAAW,CAAC7E,KAAM,CAChCH,IAAI,cAAE1B,IAAA,CAACZ,UAAU,GAAE,CAAE,CACrB2C,EAAE,CAAEpD,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAAC4C,SAAS,CAAC1C,IAAI,EAAIlC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAAC4C,KAAK,CAAE,IAAI,CAAE,CAAArD,QAAA,cAE7E5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3CuB,gBAAgB,CAAC+C,MAAM,CAACM,WAAW,CAACC,YAAY,CAAC,CACjDtD,gBAAgB,CAAC+C,MAAM,CAACM,WAAW,CAACH,YAAY,CAAC,CACjDlD,gBAAgB,CAAC+C,MAAM,CAACM,WAAW,CAACF,YAAY,CAAC,CACjDnD,gBAAgB,CAAC+C,MAAM,CAACM,WAAW,CAACD,WAAW,CAAC,EAC9C,CAAC,CACK,CAAC,cAGdzG,IAAA,CAAC2B,WAAW,EAACE,KAAK,CAAEuE,MAAM,CAACQ,YAAY,CAAC/E,KAAM,CAACH,IAAI,cAAE1B,IAAA,CAACV,QAAQ,GAAE,CAAE,CAAAwC,QAAA,cAChE9B,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5CsE,MAAM,CAACQ,YAAY,CAACrD,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACxCxD,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CACtBmC,EAAE,CAAE,GAAG,CACPtB,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCkD,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KACjB,CAAE,CAAAf,QAAA,eACA9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACA9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,MAAM,CAAE,CAAC,CACb,CAAC,cACfjD,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAjBWiD,GAkBL,CACX,CAAC,CACE,CAAC,CACI,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAmD,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAT,MAAM,CAAGhG,CAAC,CAAC,yBAAyB,CAAE,CAAEoF,aAAa,CAAE,IAAK,CAAC,CAAC,CAEpE,GAAI,CAACY,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACEpG,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,KAAM,CAAAqG,aAAa,CAAIjC,OAAO,eAC5B7E,IAAA,CAAC2B,WAAW,EAACE,KAAK,CAAEgD,OAAO,CAAChD,KAAM,CAACH,IAAI,cAAE1B,IAAA,CAACR,WAAW,GAAE,CAAE,CAAAsC,QAAA,cACvD5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3C+C,OAAO,CAACtB,MAAM,eACbvD,IAAA,CAAC7B,IAAI,EAAA2D,QAAA,CACF+C,OAAO,CAACtB,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC5BxD,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACA9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACA9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfjD,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWiD,GAiBL,CACX,CAAC,CACE,CACP,CACAmB,OAAO,CAACkC,WAAW,eAClB/G,IAAA,CAACnC,UAAU,EACToE,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED+C,OAAO,CAACkC,WAAW,CACV,CACb,EACE,CAAC,CACK,CACd,CAED,mBACE7G,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDsE,MAAM,CAACvE,KAAK,CACH,CAAC,cACb7B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,gBAAgB,CACtBhB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDsE,MAAM,CAACL,QAAQ,CACN,CAAC,cACb/F,IAAA,CAAChC,OAAO,EAACiE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAEzB4E,aAAa,CAACV,MAAM,CAACY,QAAQ,CAAC,CAC9BF,aAAa,CAACV,MAAM,CAACa,QAAQ,CAAC,CAC9BH,aAAa,CAACV,MAAM,CAACc,QAAQ,CAAC,CAC9BJ,aAAa,CAACV,MAAM,CAACe,QAAQ,CAAC,CAC9BL,aAAa,CAACV,MAAM,CAACgB,QAAQ,CAAC,CAC9BN,aAAa,CAACV,MAAM,CAACiB,QAAQ,CAAC,CAC9BP,aAAa,CAACV,MAAM,CAACkB,QAAQ,CAAC,CAC9BR,aAAa,CAACV,MAAM,CAACmB,QAAQ,CAAC,EAC5B,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,MAAM,CAAGrH,CAAC,CAAC,iBAAiB,CAAE,CAAEoF,aAAa,CAAE,IAAK,CAAC,CAAC,CAE5D,GAAI,CAACiC,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACEzH,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED2F,MAAM,CAAC5F,KAAK,CACH,CAAC,cACb7B,IAAA,CAAChC,OAAO,EAACiE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BhC,KAAA,CAACyB,WAAW,EAACE,KAAK,CAAE4F,MAAM,CAACT,QAAQ,CAACnF,KAAM,CAACH,IAAI,cAAE1B,IAAA,CAACN,WAAW,GAAE,CAAE,CAAAoC,QAAA,eAC/D9B,IAAA,CAACnC,UAAU,EACTqE,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED2F,MAAM,CAACT,QAAQ,CAACD,WAAW,CAClB,CAAC,cACb/G,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5C2F,MAAM,CAACT,QAAQ,CAACU,KAAK,CAAClE,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACnCxD,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACA9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACA9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfjD,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CACH,CAAC,GAnBWiD,GAoBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,CAGb,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACF,GAAG,CAAEsC,GAAG,EAAK,CAClC,KAAM,CAAAjB,OAAO,CAAG4C,MAAM,CAAC,UAAU3B,GAAG,EAAE,CAAC,CACvC,GAAI,CAACjB,OAAO,CAAE,MAAO,KAAI,CAEzB,mBACE7E,IAAA,CAAC2B,WAAW,EAAWE,KAAK,CAAEgD,OAAO,CAAChD,KAAM,CAACH,IAAI,cAAE1B,IAAA,CAACN,WAAW,GAAE,CAAE,CAAAoC,QAAA,cACjE5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3C+C,OAAO,CAACkC,WAAW,eAClB/G,IAAA,CAACnC,UAAU,EACTqE,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED+C,OAAO,CAACkC,WAAW,CACV,CACb,CACAlC,OAAO,CAAC6C,KAAK,eACZ1H,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5C+C,OAAO,CAAC6C,KAAK,CAAClE,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC3BxD,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACA9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACA9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfjD,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CACH,CAAC,GAnBWiD,GAoBL,CACX,CAAC,CACE,CACP,EACE,CAAC,EAxCUoC,GAyCL,CAAC,CAElB,CAAC,CAAC,EACC,CAAC,CAEV,CAAC,CAED,mBACE9F,IAAA,CAACH,MAAM,EAAAiC,QAAA,cACL9B,IAAA,CAACjC,GAAG,EACFkE,EAAE,CAAE,CACFmC,EAAE,CAAE,CAAC,CACLuD,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBjJ,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAK7D,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHJ,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAqB,QAAA,cAEF9B,IAAA,CAACpC,SAAS,EAACiK,QAAQ,CAAC,IAAI,CAAC5F,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,cAChE9B,IAAA,CAAC9B,IAAI,EAAC4J,EAAE,MAACC,OAAO,CAAE,IAAK,CAAAjG,QAAA,cACrB5B,KAAA,CAACpC,KAAK,EACJkE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACF+F,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBhG,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC,KAAK,CAAE,CACL2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAC,CACD,uBAAuB,CAAE,CACvBqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAC,CACD,iBAAiB,CAAE,CACjB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAC,CACD,qBAAqB,CAAE,CACrB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CACF,CAAE,CAAAqB,QAAA,eAGF5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CACPC,EAAE,CAAE,CAAC,CACLY,SAAS,CAAE,QAAQ,CACnBV,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAqB,QAAA,eACA9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZiF,SAAS,CAAC,IAAI,CACdlG,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLiB,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCJ,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAE,QACb,CAAE,CAAAhB,QAAA,CAEDrB,KAAK,CAAG,eAAe,CAAG,mBAAmB,CACpC,CAAC,cACbT,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,gBAAgB,CACtBhB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAE,QACb,CAAE,CAAAhB,QAAA,CAEDrB,KAAK,CACF,iEAAiE,CACjE,4DAA4D,CAEtD,CAAC,EACV,CAAC,cAGNT,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEmG,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEnG,EAAE,CAAE,CAAC,CAAEE,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,cAC5F9B,IAAA,CAACzB,IAAI,EACH0F,KAAK,CAAErD,SAAU,CACjB0H,QAAQ,CAAEnH,eAAgB,CAC1B+B,OAAO,CAAC,YAAY,CACpBqF,aAAa,CAAC,MAAM,CACpBtG,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChBmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDkH,SAAS,CAAE,EAAE,CACb7E,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAC,CACD,0BAA0B,CAAE,CAC1B2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CACF,CAAE,CAAAqB,QAAA,CAEDN,UAAU,CAACgC,GAAG,CAAC,CAACgF,GAAG,CAAEtE,KAAK,gBACzBlE,IAAA,CAACxB,GAAG,EAEFkD,IAAI,CAAE8G,GAAG,CAAC9G,IAAK,CACfD,KAAK,CAAE+G,GAAG,CAAC/G,KAAM,CACjBgH,YAAY,CAAC,OAAO,CACpBxG,EAAE,CAAE,CACFY,aAAa,CAAEpC,KAAK,CAAG,aAAa,CAAG,KAAK,CAC5CqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC,uBAAuB,CAAE,CACvBsC,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CACF,CAAE,EAXGyD,KAYN,CACF,CAAC,CACE,CAAC,CACJ,CAAC,cAGNlE,IAAA,CAAC+D,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClCuC,wBAAwB,CAAC,CAAC,CACnB,CAAC,cAEXrE,IAAA,CAAC+D,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClCwD,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXtF,IAAA,CAAC+D,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClCqE,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXnG,IAAA,CAAC+D,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClC+E,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEX7G,IAAA,CAAC+D,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClC0F,kBAAkB,CAAC,CAAC,CACb,CAAC,EAEN,CAAC,CACJ,CAAC,CACE,CAAC,CACT,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAArH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}