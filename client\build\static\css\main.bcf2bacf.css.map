{"version": 3, "file": "static/css/main.bcf2bacf.css", "mappings": ";;AAEA;;CAAc,CAId,MACE,uBAAwB,CACxB,sBACF,CAEA,KACE,aACF,CAEA,KAEE,yCAGF,CAEA,iBACE,qfACF,CAEA,iBACE,GAAK,uBAA4B,CACjC,IAAM,2BAA8B,CACpC,GAAO,uBAA4B,CACrC,CAEA,mBACE,uCACF,CAEA,oBACE,GAAK,yBAA6B,CAClC,IAAM,4BAA+B,CACrC,GAAO,yBAA6B,CACtC,CAEA,qBAEE,oCAAqC,CADrC,yBAEF,CAEA,aACE,uBACF,CAEA,eAGE,6BAAoC,CAFpC,oBAAqB,CACrB,4BAEF,CCvDA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,wJAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,2CAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,qBAAoB,CAApB,mDAAoB,EAApB,mDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EAEpB,wCAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,sBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,YAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,cAAmB,CAAnB,gBAAmB,CAAnB,wCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,4CAAmB,CAAnB,4CAAmB,CAAnB,qBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,2BAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,mBAAmB,CAAnB,sBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,qBAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,wCAAmB,CAAnB,mOAAmB,CAAnB,wCAAmB,CAAnB,mCAAmB,CAAnB,2NAAmB,CAAnB,uCAAmB,CAAnB,uCAAmB,CAAnB,0NAAmB,CAAnB,yBAAmB,CAAnB,cAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,kNAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,8BAAmB,CAAnB,8BAAmB,CAAnB,qCAAmB,CAAnB,gBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,4CAAmB,CAAnB,iCAAmB,CAAnB,oCAAmB,CAAnB,4DAAmB,CAAnB,4BAAmB,CAAnB,+BAAmB,CAAnB,yBAAmB,CAAnB,mCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,oHAAmB,CAAnB,wEAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,gCAAmB,CAAnB,4BAAmB,CAAnB,gCAAmB,CAAnB,oCAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,yCAAmB,CAAnB,yCAAmB,CAAnB,qCAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,oCAAmB,CAAnB,kCAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,gCAAmB,CAAnB,0CAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,yCAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,qCAAmB,CAAnB,iBAAmB,CAAnB,qDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,mCAAmB,CAAnB,iBAAmB,CAAnB,wDAAmB,CAAnB,kCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,kDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,oDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,mCAAmB,CAAnB,8BAAmB,CAAnB,cAAmB,CAAnB,mBAAmB,CAAnB,uBAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,wBAAmB,CAAnB,kBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,8CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,8CAAmB,CAAnB,sBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,yBAAmB,CAAnB,2BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,mCAAmB,CAAnB,sDAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,kBAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,4BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,yBAAmB,CAAnB,6BAAmB,CAAnB,sCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,sCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,yCAAmB,CAAnB,wCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,yCAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,qCAAmB,CAAnB,kBAAmB,CAAnB,4BAAmB,CAAnB,kHAAmB,CAAnB,wGAAmB,CAAnB,uEAAmB,CAAnB,wFAAmB,CAAnB,+BAAmB,CAAnB,mDAAmB,CAAnB,kCAAmB,CAAnB,sDAAmB,CAAnB,sCAAmB,CAAnB,yBAAmB,CAAnB,mMAAmB,CAAnB,+FAAmB,CAAnB,wLAAmB,CAAnB,0LAAmB,CAAnB,6IAAmB,CAAnB,qKAAmB,CAAnB,kDAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,4GAAmB,CAAnB,kDAAmB,CAAnB,yDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,0DAAmB,CAAnB,+DAAmB,CAAnB,2DAAmB,CAEnB,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,oBACE,SACF,CAEA,0BACE,wBAEF,CAEA,0BACE,oBACF,CAEA,gCACE,kBACF,CAEA,KACE,uEAEF,CAEA,YACE,oBACF,CACA,qBACE,kBAAmB,CACnB,eACF,CACA,eAEE,iBAAkB,CAClB,eAAgB,CAFhB,YAGF,CAEA,oBAEE,iBAAkB,CAClB,eAAgB,CAFhB,YAGF,CAEA,mBAEE,gBAAiB,CADjB,iBAGF,CAEA,MACE,oBACF,CAEA,qCACA,qBAAsB,CACtB,+BACA,CAGA,gCACC,UACD,CAGA,eAGE,QAAW,CACX,MAAS,CAET,eAAgB,CAChB,mBAAoB,CANpB,cAAe,CAIf,OAAU,CAHV,KAAQ,CAMR,wBAAiB,CAAjB,gBAAiB,CACjB,UACF,CACA,OAEE,QAAW,CAEX,cAAe,CAGf,WAAY,CAJZ,QAAS,CAET,aAAc,CAJd,iBAAkB,CAKlB,UAEF,CACA,gBACE,uEAEF,CACA,gBACE,uEAEF,CACA,kBACE,GACE,UAAW,CACX,SACF,CACF,CACA,oBACE,GACE,iBACF,CACA,GACE,gBACF,CACF,CACA,oBACE,GACE,gBACF,CACA,GACE,iBACF,CACF,CAMA,0BAHE,iBAOF,CAJA,gBAEE,eAAgB,CAChB,UACF,CAEA,sCAEE,SAAY,CADZ,eAEF,CAEA,eAEE,SAAU,CADV,iBAAkB,CAGlB,+BACF,CAEA,mBACE,aAAc,CACd,WAAY,CACZ,cACF,CAEA,kBACE,oBAA+B,CAC/B,iBAAkB,CAClB,UAAW,CASX,cAAe,CAPf,YAAa,CACb,cAAe,CACf,WAAY,CACZ,gBAAiB,CACjB,iBAAkB,CAMlB,iBAAkB,CALlB,OAAQ,CACR,kCAAqC,CAGrC,0BAA6B,CAE7B,UAAU,CACV,UACF,CAEA,uBACE,OACF,CAEA,uBACE,QACF,CAEA,wBACE,gBAA8B,CAC9B,UACF,CAEA,6FAGE,aACF,CAjMA,sDAiMC,CAjMD,wCAiMC,CAjMD,qBAiMC,CAjMD,kDAiMC,CAjMD,2CAiMC,CAjMD,wBAiMC,CAjMD,sDAiMC,CAjMD,2CAiMC,CAjMD,wBAiMC,CAjMD,qDAiMC,CAjMD,2CAiMC,CAjMD,wBAiMC,CAjMD,qDAiMC,CAjMD,4CAiMC,CAjMD,wBAiMC,CAjMD,sDAiMC,CAjMD,6CAiMC,CAjMD,wBAiMC,CAjMD,sDAiMC,CAjMD,0CAiMC,CAjMD,wBAiMC,CAjMD,sDAiMC,CAjMD,4CAiMC,CAjMD,UAiMC,CAjMD,+CAiMC,CAjMD,+HAiMC,CAjMD,wGAiMC,CAjMD,uEAiMC,CAjMD,wFAiMC,CAjMD,+CAiMC,CAjMD,sDAiMC,CAjMD,kDAiMC,CAjMD,kBAiMC,CAjMD,8DAiMC,CAjMD,kEAiMC,CAjMD,yCAiMC,CAjMD,uBAiMC,CAjMD,qBAiMC,CAjMD,wBAiMC,CAjMD,eAiMC,CAjMD,8BAiMC,CAjMD,+BAiMC,EAjMD,wDAiMC,CAjMD,+BAiMC,CAjMD,sBAiMC,CAjMD,kBAiMC,CAjMD,oBAiMC,CAjMD,gBAiMC,CAjMD,yCAiMC,CAjMD,yCAiMC,CAjMD,yCAiMC,CAjMD,yCAiMC,CAjMD,uBAiMC,CAjMD,qBAiMC,CAjMD,2BAiMC,CAjMD,sBAiMC,CAjMD,4BAiMC,CAjMD,sBAiMC,CAjMD,sBAiMC,CAjMD,qBAiMC,CAjMD,8BAiMC,CAjMD,6BAiMC,CAjMD,wCAiMC,CAjMD,gCAiMC,CAjMD,4BAiMC,CAjMD,0BAiMC,CAjMD,wBAiMC,CAjMD,eAiMC,CAjMD,8BAiMC,CAjMD,oBAiMC,CAjMD,4BAiMC,CAjMD,kBAiMC,CAjMD,gDAiMC,CAjMD,8CAiMC,CAjMD,8CAiMC,CAjMD,2BAiMC,CAjMD,4BAiMC,EAjMD,gDAiMC,CAjMD,uBAiMC,CAjMD,4BAiMC,CAjMD,uBAiMC,CAjMD,4BAiMC,CAjMD,4BAiMC,CAjMD,mBAiMC,EAjMD,qDAiMC,CAjMD,qBAiMC,CAjMD,sBAiMC,CAjMD,0BAiMC,CAjMD,sBAiMC,CAjMD,qBAiMC,CAjMD,qBAiMC,CAjMD,qBAiMC,CAjMD,uBAiMC,CAjMD,6BAiMC,CAjMD,oBAiMC,CAjMD,+BAiMC,CAjMD,mBAiMC,EAjMD,sEAiMC,CAjMD,4CAiMC,CAjMD,8BAiMC,CAjMD,6BAiMC,CAjMD,iCAiMC,CAjMD,yCAiMC,CAjMD,iCAiMC,CAjMD,mBAiMC,EAjMD,uEAiMC,CAjMD,wBAiMC,CAjMD,qDAiMC", "sources": ["index.css", "components/meeting/index.css"], "sourcesContent": ["@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Noto+Naskh+Arabic:wght@400;500;600;700&display=swap');\n\n@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n:root {\n  --primary-color: #1B5E20;\n  --secondary-color: #FFD700;\n}\n\nhtml {\n  direction: rtl;\n}\n\nbody {\n  margin: 0;\n  font-family: 'Noto Naskh Arabic', 'Amiri', serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.pattern-islamic {\n  background-image: url(\"data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M50 50c0-5.523 4.477-10 10-10s10 4.477 10 10-4.477 10-10 10c0 5.523-4.477 10-10 10s-10-4.477-10-10 4.477-10 10-10zM10 10c0-5.523 4.477-10 10-10s10 4.477 10 10-4.477 10-10 10c0 5.523-4.477 10-10 10S0 25.523 0 20s4.477-10 10-10zm10 8c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8zm40 40c4.418 0 8-3.582 8-8s-3.582-8-8-8-8 3.582-8 8 3.582 8 8 8z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E\");\n}\n\n@keyframes float {\n  0% { transform: translateY(0px); }\n  50% { transform: translateY(-20px); }\n  100% { transform: translateY(0px); }\n}\n\n.hover-float:hover {\n  animation: float 6s ease-in-out infinite;\n}\n\n@keyframes gradient {\n  0% { background-position: 0% 50%; }\n  50% { background-position: 100% 50%; }\n  100% { background-position: 0% 50%; }\n}\n\n.bg-gradient-animate {\n  background-size: 200% 200%;\n  animation: gradient 15s ease infinite;\n}\n\n.font-arabic {\n  font-family: 'Amiri', serif;\n}\n\n.text-gradient {\n  background-clip: text;\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n}\n", "@tailwind base;\r\n\r\n@tailwind components;\r\n\r\n@tailwind utilities;\r\n\r\nbody {\r\n  margin: 0;\r\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\",\r\n    \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\",\r\n    sans-serif;\r\n  -webkit-font-smoothing: antialiased;\r\n  -moz-osx-font-smoothing: grayscale;\r\n}\r\n\r\n::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background: \"transparent\";\r\n  /* #212032; */\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background: #1178f880;\r\n}\r\n\r\n::-webkit-scrollbar-thumb:hover {\r\n  background: #1178f8;\r\n}\r\n\r\ncode {\r\n  font-family: source-code-pro, Menlo, Monaco, Consolas, \"Courier New\",\r\n    monospace;\r\n}\r\n\r\ndiv .button {\r\n  margin: 0 15px 15px 0;\r\n}\r\n.video-contain video {\r\n  object-fit: contain;\r\n  overflow:hidden\r\n}\r\ntextarea:focus{\r\n  outline: none;\r\n  border-color: none;\r\n  box-shadow: none;\r\n}\r\n\r\ninput:focus-visible{\r\n  outline: none;\r\n  border-color: none;\r\n  box-shadow: none;\r\n}\r\n\r\n.video-cover video {\r\n  position: absolute;\r\n  object-fit: cover;\r\n\r\n}\r\n\r\n.flip {\r\n  transform: scaleX(-1);\r\n}\r\n\r\ninput[type=\"radio\"]:checked + label span {\r\nbackground-color: #fff; \r\nbox-shadow: 0px 0px 0px 2px black inset;\r\n}\r\n\r\n\r\ninput[type=\"radio\"]:checked + label{\r\n color: #fff; \r\n}\r\n\r\n\r\n.flying-emojis {\r\n  position: fixed;\r\n  top: 0px;\r\n  bottom: 0px;\r\n  left: 0px;\r\n  right: 0px;\r\n  overflow: hidden;\r\n  pointer-events: none;\r\n  user-select: none;\r\n  z-index: 99;\r\n}\r\n.emoji {\r\n  position: absolute;\r\n  bottom: 0px;\r\n  left: 50%;\r\n  font-size: 48px;\r\n  line-height: 1;\r\n  width: 48px;\r\n  height: 48px;\r\n}\r\n.emoji.wiggle-1 {\r\n  animation: emerge 3s forwards,\r\n    wiggle-1 1s ease-in-out infinite alternate;\r\n}\r\n.emoji.wiggle-2 {\r\n  animation: emerge 3s forwards,\r\n    wiggle-2 1s ease-in-out infinite alternate;\r\n}\r\n@keyframes emerge {\r\n  to {\r\n    bottom: 85%;\r\n    opacity: 0;\r\n  }\r\n}\r\n@keyframes wiggle-1 {\r\n  from {\r\n    margin-left: -50px;\r\n  }\r\n  to {\r\n    margin-left: 50px;\r\n  }\r\n}\r\n@keyframes wiggle-2 {\r\n  from {\r\n    margin-left: 50px;\r\n  }\r\n  to {\r\n    margin-left: -50px;\r\n  }\r\n}\r\n\r\n.carousel {\r\n  position: relative;\r\n}\r\n\r\n.carousel-inner {\r\n  position: relative;\r\n  overflow: hidden;\r\n  width: 100%;\r\n}\r\n\r\n.carousel-open:checked + .carousel-item {\r\n  position: static;\r\n  opacity: 100;\r\n}\r\n\r\n.carousel-item {\r\n  position: absolute;\r\n  opacity: 0;\r\n  -webkit-transition: opacity 0.6s ease-out;\r\n  transition: opacity 0.6s ease-out;\r\n}\r\n\r\n.carousel-item img {\r\n  display: block;\r\n  height: auto;\r\n  max-width: 100%;\r\n}\r\n\r\n.carousel-control {\r\n  background: rgba(0, 0, 0, 0.28);\r\n  border-radius: 50%;\r\n  color: #fff;\r\n  cursor: pointer;\r\n  display: none;\r\n  font-size: 30px;\r\n  height: 35px;\r\n  line-height: 30px;\r\n  position: absolute;\r\n  top: 25%;\r\n  -webkit-transform: translate(0, -50%);\r\n  cursor: pointer;\r\n  -ms-transform: translate(0, -50%);\r\n  transform: translate(0, -50%);\r\n  text-align: center;\r\n  width:35px;\r\n  z-index: 10;\r\n}\r\n\r\n.carousel-control.prev {\r\n  left: 2%;\r\n}\r\n\r\n.carousel-control.next {\r\n  right: 2%;\r\n}\r\n\r\n.carousel-control:hover {\r\n  background: rgba(0, 0, 0, 0.8);\r\n  color: #aaaaaa;\r\n}\r\n\r\n#carousel-1:checked ~ .control-1,\r\n#carousel-2:checked ~ .control-2,\r\n#carousel-3:checked ~ .control-3 {\r\n  display: block;\r\n}"], "names": [], "sourceRoot": ""}