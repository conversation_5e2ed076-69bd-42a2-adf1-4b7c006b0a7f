[{"D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js": "1", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js": "2", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js": "3", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js": "4", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js": "5", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js": "6", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js": "7", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js": "8", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js": "9", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js": "10", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js": "11", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js": "12", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js": "13", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js": "14", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js": "15", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js": "16", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js": "17", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js": "18", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js": "19", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js": "20", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js": "21", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js": "22", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js": "23", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js": "24", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js": "25", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js": "26", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js": "27", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js": "28", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js": "29", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js": "30", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js": "31", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js": "32", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js": "33", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js": "34", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js": "35", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js": "36", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js": "37", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js": "38", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js": "39", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js": "40", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js": "41", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js": "42", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js": "43", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js": "44", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js": "45", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js": "46", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js": "47", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js": "48", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js": "49", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js": "50", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js": "51", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js": "52", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js": "53", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js": "54", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js": "55", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js": "56", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js": "57", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js": "58", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js": "59", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js": "60", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js": "61", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js": "62", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js": "63", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js": "64", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js": "65", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js": "66", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js": "67", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js": "68", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js": "69", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js": "70", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js": "71", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js": "72", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js": "73", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js": "74", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js": "75", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js": "76", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js": "77", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js": "78", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js": "79", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js": "80", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js": "81", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js": "82", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js": "83", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js": "84", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js": "85", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js": "86", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js": "87", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js": "88", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js": "89", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js": "90", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js": "91", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js": "92", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js": "93", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js": "94", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js": "95", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js": "96", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js": "97", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js": "98", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js": "99", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js": "100", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js": "101", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js": "102", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js": "103", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js": "104", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js": "105", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js": "106", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js": "107", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js": "108", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js": "109", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js": "110", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js": "111", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js": "112", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js": "113", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx": "114", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx": "115", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js": "116", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js": "117", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js": "118", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js": "119", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js": "120", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js": "121", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js": "122", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js": "123", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js": "124", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js": "125", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js": "126", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js": "127", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js": "128", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx": "129", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx": "130", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx": "131", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx": "132", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx": "133", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx": "134", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx": "135", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx": "136", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx": "137", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx": "138", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx": "139", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx": "140", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js": "141", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js": "142", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js": "143", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js": "144", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js": "145", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js": "146", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js": "147", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js": "148", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js": "149", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js": "150", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js": "151", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js": "152", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js": "153", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js": "154", "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\RescheduleDialog.js": "155"}, {"size": 641, "mtime": 1742576862818, "results": "156", "hashOfConfig": "157"}, {"size": 24379, "mtime": 1753730571998, "results": "158", "hashOfConfig": "157"}, {"size": 1456, "mtime": 1746732811228, "results": "159", "hashOfConfig": "157"}, {"size": 217701, "mtime": 1753728803631, "results": "160", "hashOfConfig": "157"}, {"size": 362, "mtime": 1742419325522, "results": "161", "hashOfConfig": "157"}, {"size": 1029, "mtime": 1744808128594, "results": "162", "hashOfConfig": "157"}, {"size": 325, "mtime": 1743221258725, "results": "163", "hashOfConfig": "157"}, {"size": 8026, "mtime": 1749482015181, "results": "164", "hashOfConfig": "157"}, {"size": 7654, "mtime": 1751398254796, "results": "165", "hashOfConfig": "157"}, {"size": 3432, "mtime": 1751285065551, "results": "166", "hashOfConfig": "157"}, {"size": 3326, "mtime": 1751040721930, "results": "167", "hashOfConfig": "157"}, {"size": 943, "mtime": 1749309630963, "results": "168", "hashOfConfig": "157"}, {"size": 24114, "mtime": 1751284599336, "results": "169", "hashOfConfig": "157"}, {"size": 36606, "mtime": 1753386956890, "results": "170", "hashOfConfig": "157"}, {"size": 4768, "mtime": 1751278844207, "results": "171", "hashOfConfig": "157"}, {"size": 10446, "mtime": 1751293932223, "results": "172", "hashOfConfig": "157"}, {"size": 28837, "mtime": 1753731053731, "results": "173", "hashOfConfig": "157"}, {"size": 30616, "mtime": 1751485922771, "results": "174", "hashOfConfig": "157"}, {"size": 15736, "mtime": 1751486831358, "results": "175", "hashOfConfig": "157"}, {"size": 15894, "mtime": 1749479492637, "results": "176", "hashOfConfig": "157"}, {"size": 15959, "mtime": 1749479507046, "results": "177", "hashOfConfig": "157"}, {"size": 12132, "mtime": 1751303763974, "results": "178", "hashOfConfig": "157"}, {"size": 7902, "mtime": 1747334952040, "results": "179", "hashOfConfig": "157"}, {"size": 10135, "mtime": 1747335101462, "results": "180", "hashOfConfig": "157"}, {"size": 7229, "mtime": 1747334912778, "results": "181", "hashOfConfig": "157"}, {"size": 9247, "mtime": 1742785011813, "results": "182", "hashOfConfig": "157"}, {"size": 13648, "mtime": 1751303763967, "results": "183", "hashOfConfig": "157"}, {"size": 29493, "mtime": 1744817248860, "results": "184", "hashOfConfig": "157"}, {"size": 13701, "mtime": 1744022069977, "results": "185", "hashOfConfig": "157"}, {"size": 12085, "mtime": 1746733346280, "results": "186", "hashOfConfig": "157"}, {"size": 11112, "mtime": 1744799843410, "results": "187", "hashOfConfig": "157"}, {"size": 30331, "mtime": 1751480317262, "results": "188", "hashOfConfig": "157"}, {"size": 6641, "mtime": 1742964048040, "results": "189", "hashOfConfig": "157"}, {"size": 6618, "mtime": 1744022276817, "results": "190", "hashOfConfig": "157"}, {"size": 22625, "mtime": 1751041436226, "results": "191", "hashOfConfig": "157"}, {"size": 11838, "mtime": 1749581734964, "results": "192", "hashOfConfig": "157"}, {"size": 7554, "mtime": 1744093753745, "results": "193", "hashOfConfig": "157"}, {"size": 19961, "mtime": 1746026434563, "results": "194", "hashOfConfig": "157"}, {"size": 10379, "mtime": 1749855699542, "results": "195", "hashOfConfig": "157"}, {"size": 7520, "mtime": 1742769302670, "results": "196", "hashOfConfig": "157"}, {"size": 12485, "mtime": 1749828882192, "results": "197", "hashOfConfig": "157"}, {"size": 12535, "mtime": 1749506609302, "results": "198", "hashOfConfig": "157"}, {"size": 24173, "mtime": 1750508377674, "results": "199", "hashOfConfig": "157"}, {"size": 6673, "mtime": 1749331131513, "results": "200", "hashOfConfig": "157"}, {"size": 35597, "mtime": 1749498641458, "results": "201", "hashOfConfig": "157"}, {"size": 32081, "mtime": 1749321424781, "results": "202", "hashOfConfig": "157"}, {"size": 10975, "mtime": 1749337833332, "results": "203", "hashOfConfig": "157"}, {"size": 33527, "mtime": 1749322312984, "results": "204", "hashOfConfig": "157"}, {"size": 15465, "mtime": 1750848677426, "results": "205", "hashOfConfig": "157"}, {"size": 34185, "mtime": 1753480100105, "results": "206", "hashOfConfig": "157"}, {"size": 15303, "mtime": 1750870211486, "results": "207", "hashOfConfig": "157"}, {"size": 59692, "mtime": 1753723359743, "results": "208", "hashOfConfig": "157"}, {"size": 12480, "mtime": 1752698192448, "results": "209", "hashOfConfig": "157"}, {"size": 8011, "mtime": 1744093709651, "results": "210", "hashOfConfig": "157"}, {"size": 4974, "mtime": 1744028374118, "results": "211", "hashOfConfig": "157"}, {"size": 17010, "mtime": 1749916559907, "results": "212", "hashOfConfig": "157"}, {"size": 10864, "mtime": 1746026292144, "results": "213", "hashOfConfig": "157"}, {"size": 21665, "mtime": 1750041191553, "results": "214", "hashOfConfig": "157"}, {"size": 35014, "mtime": 1753632113764, "results": "215", "hashOfConfig": "157"}, {"size": 4214, "mtime": 1742913454633, "results": "216", "hashOfConfig": "157"}, {"size": 8524, "mtime": 1749491950143, "results": "217", "hashOfConfig": "157"}, {"size": 34718, "mtime": 1751485938458, "results": "218", "hashOfConfig": "157"}, {"size": 35898, "mtime": 1753286098780, "results": "219", "hashOfConfig": "157"}, {"size": 5814, "mtime": 1744024131309, "results": "220", "hashOfConfig": "157"}, {"size": 15814, "mtime": 1749589859006, "results": "221", "hashOfConfig": "157"}, {"size": 5024, "mtime": 1746026315740, "results": "222", "hashOfConfig": "157"}, {"size": 11083, "mtime": 1746026269030, "results": "223", "hashOfConfig": "157"}, {"size": 2721, "mtime": 1750156638150, "results": "224", "hashOfConfig": "157"}, {"size": 7189, "mtime": 1750568564445, "results": "225", "hashOfConfig": "157"}, {"size": 13724, "mtime": 1751567205888, "results": "226", "hashOfConfig": "157"}, {"size": 14721, "mtime": 1749819465295, "results": "227", "hashOfConfig": "157"}, {"size": 40555, "mtime": 1753286098776, "results": "228", "hashOfConfig": "157"}, {"size": 40659, "mtime": 1753453083171, "results": "229", "hashOfConfig": "157"}, {"size": 57051, "mtime": 1746744695128, "results": "230", "hashOfConfig": "157"}, {"size": 14257, "mtime": 1749585929387, "results": "231", "hashOfConfig": "157"}, {"size": 24555, "mtime": 1752956536040, "results": "232", "hashOfConfig": "157"}, {"size": 3262, "mtime": 1753034610621, "results": "233", "hashOfConfig": "157"}, {"size": 7726, "mtime": 1753129936379, "results": "234", "hashOfConfig": "157"}, {"size": 56521, "mtime": 1753443150818, "results": "235", "hashOfConfig": "157"}, {"size": 5549, "mtime": 1749490160444, "results": "236", "hashOfConfig": "157"}, {"size": 25593, "mtime": 1753442449956, "results": "237", "hashOfConfig": "157"}, {"size": 3697, "mtime": 1749774472728, "results": "238", "hashOfConfig": "157"}, {"size": 13801, "mtime": 1743218310347, "results": "239", "hashOfConfig": "157"}, {"size": 7971, "mtime": 1746023858919, "results": "240", "hashOfConfig": "157"}, {"size": 2877, "mtime": 1747400985303, "results": "241", "hashOfConfig": "157"}, {"size": 2530, "mtime": 1750507091156, "results": "242", "hashOfConfig": "157"}, {"size": 1488, "mtime": 1742858201413, "results": "243", "hashOfConfig": "157"}, {"size": 42233, "mtime": 1753723325313, "results": "244", "hashOfConfig": "157"}, {"size": 153, "mtime": 1742445554097, "results": "245", "hashOfConfig": "157"}, {"size": 7365, "mtime": 1750226849238, "results": "246", "hashOfConfig": "157"}, {"size": 3181, "mtime": 1744798236742, "results": "247", "hashOfConfig": "157"}, {"size": 2614, "mtime": 1750185563183, "results": "248", "hashOfConfig": "157"}, {"size": 3068, "mtime": 1751036509706, "results": "249", "hashOfConfig": "157"}, {"size": 11091, "mtime": 1752283251033, "results": "250", "hashOfConfig": "157"}, {"size": 20466, "mtime": 1750170525751, "results": "251", "hashOfConfig": "157"}, {"size": 2219, "mtime": 1750163211180, "results": "252", "hashOfConfig": "157"}, {"size": 836, "mtime": 1750184890252, "results": "253", "hashOfConfig": "157"}, {"size": 4725, "mtime": 1750162079006, "results": "254", "hashOfConfig": "157"}, {"size": 11306, "mtime": 1750569740310, "results": "255", "hashOfConfig": "157"}, {"size": 18054, "mtime": 1750163343664, "results": "256", "hashOfConfig": "157"}, {"size": 2950, "mtime": 1750162078997, "results": "257", "hashOfConfig": "157"}, {"size": 938, "mtime": 1750162079095, "results": "258", "hashOfConfig": "157"}, {"size": 211, "mtime": 1750162079017, "results": "259", "hashOfConfig": "157"}, {"size": 6398, "mtime": 1753615402623, "results": "260", "hashOfConfig": "157"}, {"size": 225, "mtime": 1750162079019, "results": "261", "hashOfConfig": "157"}, {"size": 4612, "mtime": 1750486746007, "results": "262", "hashOfConfig": "157"}, {"size": 831, "mtime": 1750162079019, "results": "263", "hashOfConfig": "157"}, {"size": 4793, "mtime": 1750163289799, "results": "264", "hashOfConfig": "157"}, {"size": 12075, "mtime": 1750163272446, "results": "265", "hashOfConfig": "157"}, {"size": 6897, "mtime": 1750163306463, "results": "266", "hashOfConfig": "157"}, {"size": 3348, "mtime": 1750162079002, "results": "267", "hashOfConfig": "157"}, {"size": 1056, "mtime": 1750162079048, "results": "268", "hashOfConfig": "157"}, {"size": 1842, "mtime": 1750162079037, "results": "269", "hashOfConfig": "157"}, {"size": 3656, "mtime": 1750162079039, "results": "270", "hashOfConfig": "157"}, {"size": 4224, "mtime": 1750162079030, "results": "271", "hashOfConfig": "157"}, {"size": 1616, "mtime": 1750162079025, "results": "272", "hashOfConfig": "157"}, {"size": 937, "mtime": 1750162079029, "results": "273", "hashOfConfig": "157"}, {"size": 1738, "mtime": 1750162079038, "results": "274", "hashOfConfig": "157"}, {"size": 662, "mtime": 1750162079047, "results": "275", "hashOfConfig": "157"}, {"size": 550, "mtime": 1750162079020, "results": "276", "hashOfConfig": "157"}, {"size": 519, "mtime": 1750162079047, "results": "277", "hashOfConfig": "157"}, {"size": 1921, "mtime": 1750265054387, "results": "278", "hashOfConfig": "157"}, {"size": 577, "mtime": 1750162079039, "results": "279", "hashOfConfig": "157"}, {"size": 1773, "mtime": 1750162079025, "results": "280", "hashOfConfig": "157"}, {"size": 503, "mtime": 1750523557957, "results": "281", "hashOfConfig": "157"}, {"size": 6750, "mtime": 1750251645533, "results": "282", "hashOfConfig": "157"}, {"size": 4294, "mtime": 1753615413502, "results": "283", "hashOfConfig": "157"}, {"size": 3765, "mtime": 1750163361559, "results": "284", "hashOfConfig": "157"}, {"size": 615, "mtime": 1750162079034, "results": "285", "hashOfConfig": "157"}, {"size": 853, "mtime": 1750162079036, "results": "286", "hashOfConfig": "157"}, {"size": 857, "mtime": 1750162079033, "results": "287", "hashOfConfig": "157"}, {"size": 564, "mtime": 1750162079037, "results": "288", "hashOfConfig": "157"}, {"size": 594, "mtime": 1750162079035, "results": "289", "hashOfConfig": "157"}, {"size": 750, "mtime": 1750162079035, "results": "290", "hashOfConfig": "157"}, {"size": 589, "mtime": 1750162079034, "results": "291", "hashOfConfig": "157"}, {"size": 492, "mtime": 1750162079042, "results": "292", "hashOfConfig": "157"}, {"size": 810, "mtime": 1750162079041, "results": "293", "hashOfConfig": "157"}, {"size": 490, "mtime": 1750162079042, "results": "294", "hashOfConfig": "157"}, {"size": 492, "mtime": 1750162079041, "results": "295", "hashOfConfig": "157"}, {"size": 840, "mtime": 1750162079043, "results": "296", "hashOfConfig": "157"}, {"size": 1776, "mtime": 1750162079045, "results": "297", "hashOfConfig": "157"}, {"size": 896, "mtime": 1750162079044, "results": "298", "hashOfConfig": "157"}, {"size": 548, "mtime": 1750162079044, "results": "299", "hashOfConfig": "157"}, {"size": 825, "mtime": 1750162079046, "results": "300", "hashOfConfig": "157"}, {"size": 1014, "mtime": 1750162079045, "results": "301", "hashOfConfig": "157"}, {"size": 5638, "mtime": 1753617596678, "results": "302", "hashOfConfig": "157"}, {"size": 5701, "mtime": 1753621506072, "results": "303", "hashOfConfig": "157"}, {"size": 6823, "mtime": 1751280498212, "results": "304", "hashOfConfig": "157"}, {"size": 4794, "mtime": 1751272383355, "results": "305", "hashOfConfig": "157"}, {"size": 3832, "mtime": 1751274488484, "results": "306", "hashOfConfig": "157"}, {"size": 6866, "mtime": 1751269538235, "results": "307", "hashOfConfig": "157"}, {"size": 2091, "mtime": 1751304469748, "results": "308", "hashOfConfig": "157"}, {"size": 4523, "mtime": 1752327988910, "results": "309", "hashOfConfig": "157"}, {"size": 21251, "mtime": 1752953246619, "results": "310", "hashOfConfig": "157"}, {"size": 41014, "mtime": 1753477116038, "results": "311", "hashOfConfig": "157"}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1kyl3u4", {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 41, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "612", "messages": "613", "suppressedMessages": "614", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "615", "messages": "616", "suppressedMessages": "617", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "618", "messages": "619", "suppressedMessages": "620", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "621", "messages": "622", "suppressedMessages": "623", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "624", "messages": "625", "suppressedMessages": "626", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "627", "messages": "628", "suppressedMessages": "629", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "630", "messages": "631", "suppressedMessages": "632", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "633", "messages": "634", "suppressedMessages": "635", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "636", "messages": "637", "suppressedMessages": "638", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "639", "messages": "640", "suppressedMessages": "641", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "642", "messages": "643", "suppressedMessages": "644", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "645", "messages": "646", "suppressedMessages": "647", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "648", "messages": "649", "suppressedMessages": "650", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "651", "messages": "652", "suppressedMessages": "653", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "654", "messages": "655", "suppressedMessages": "656", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "657", "messages": "658", "suppressedMessages": "659", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "660", "messages": "661", "suppressedMessages": "662", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "663", "messages": "664", "suppressedMessages": "665", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "666", "messages": "667", "suppressedMessages": "668", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "669", "messages": "670", "suppressedMessages": "671", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "672", "messages": "673", "suppressedMessages": "674", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "675", "messages": "676", "suppressedMessages": "677", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "678", "messages": "679", "suppressedMessages": "680", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "681", "messages": "682", "suppressedMessages": "683", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "684", "messages": "685", "suppressedMessages": "686", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "687", "messages": "688", "suppressedMessages": "689", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "690", "messages": "691", "suppressedMessages": "692", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "693", "messages": "694", "suppressedMessages": "695", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "696", "messages": "697", "suppressedMessages": "698", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "699", "messages": "700", "suppressedMessages": "701", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "702", "messages": "703", "suppressedMessages": "704", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "705", "messages": "706", "suppressedMessages": "707", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "708", "messages": "709", "suppressedMessages": "710", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "711", "messages": "712", "suppressedMessages": "713", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "714", "messages": "715", "suppressedMessages": "716", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "717", "messages": "718", "suppressedMessages": "719", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "720", "messages": "721", "suppressedMessages": "722", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "723", "messages": "724", "suppressedMessages": "725", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "726", "messages": "727", "suppressedMessages": "728", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "729", "messages": "730", "suppressedMessages": "731", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "732", "messages": "733", "suppressedMessages": "734", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "735", "messages": "736", "suppressedMessages": "737", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "738", "messages": "739", "suppressedMessages": "740", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "741", "messages": "742", "suppressedMessages": "743", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "744", "messages": "745", "suppressedMessages": "746", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "747", "messages": "748", "suppressedMessages": "749", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "750", "messages": "751", "suppressedMessages": "752", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "753", "messages": "754", "suppressedMessages": "755", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "756", "messages": "757", "suppressedMessages": "758", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "759", "messages": "760", "suppressedMessages": "761", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "762", "messages": "763", "suppressedMessages": "764", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "765", "messages": "766", "suppressedMessages": "767", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "768", "messages": "769", "suppressedMessages": "770", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "771", "messages": "772", "suppressedMessages": "773", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\xampp\\htdocs\\allemnionline\\client\\src\\index.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\App.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\config\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\i18n.js", ["777", "778", "779", "780", "781", "782", "783", "784", "785", "786", "787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808", "809", "810", "811", "812", "813", "814", "815", "816", "817"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\reportWebVitals.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\ResizeObserverFix.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\resizeObserver.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\AuthContext.js", ["818"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Footer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\SocketContext.js", ["819"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\contexts\\UnreadMessagesContext.js", ["820"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AuthenticatedFooter.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\layout\\Header.js", ["821", "822", "823"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\Home.js", ["824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PrivacyPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\AboutUs.js", ["838", "839", "840", "841", "842", "843"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\PlatformPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\FindTeacher.js", ["844", "845", "846", "847", "848", "849", "850"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TeacherDetails.js", ["851", "852", "853", "854"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\StudentRegister.js", ["855"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\TeacherRegister.js", ["856"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\Login.js", ["857", "858"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyResetCode.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ResetPassword.js", ["859"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\ForgotPassword.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\RegisterChoice.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\auth\\VerifyEmail.js", ["860", "861"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Dashboard.js", ["862", "863", "864"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Students.js", ["865", "866"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Profile.js", ["867"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Teachers.js", ["868", "869", "870", "871", "872"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\TeacherApplications.js", ["873", "874", "875", "876", "877", "878", "879", "880", "881", "882", "883", "884"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Languages.js", ["885"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Categories.js", ["886"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\ProfileUpdates.js", ["887", "888"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingSessions.js", ["889", "890"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Wallet.js", ["891"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\Messages.js", ["892", "893"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\AdminEarnings.js", ["894"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Dashboard.js", ["895"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\WithdrawalManagement.js", ["896"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditVideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\EditApplication.js", ["897", "898", "899", "900", "901"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Application.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\TeacherAvailableHours.js", ["902", "903", "904", "905", "906", "907", "908", "909", "910", "911", "912", "913", "914", "915", "916", "917", "918", "919"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ViewAvailableHours.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\VideoUpload.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\AvailableHours.js", ["920", "921", "922", "923", "924", "925", "926", "927"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Meetings.js", ["928", "929", "930", "931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Bookings.js", ["945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956", "957", "958", "959"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Chat.js", ["960", "961", "962", "963"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Profile.js", ["964", "965", "966", "967", "968", "969", "970", "971"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyLessons.js", ["972"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Wallet.js", ["973", "974"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Withdrawal.js", ["975"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\MyMessages.js", ["976"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\teacher\\Reviews.js", ["977", "978", "979", "980", "981", "982", "983", "984", "985"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Profile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Dashboard.js", ["986"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\CompleteProfile.js", ["987"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\FindTeacher.js", ["988", "989", "990", "991", "992", "993", "994", "995", "996", "997", "998", "999", "1000", "1001", "1002", "1003"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\TeacherProfile.js", ["1004", "1005", "1006", "1007", "1008", "1009", "1010", "1011", "1012", "1013", "1014", "1015", "1016"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ChatEmbed.js", ["1017", "1018"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Chat.js", ["1019", "1020", "1021"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\ContactUs.js", ["1022"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyMessages.js", ["1023"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\JoinMeeting.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\MyTeachers.js", ["1024", "1025", "1026"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Meetings.js", ["1027", "1028", "1029", "1030", "1031", "1032", "1033", "1034", "1035"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Wallet.js", ["1036"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\BookingPage.js", ["1037", "1038", "1039", "1040", "1041", "1042", "1043", "1044", "1045", "1046", "1047", "1048", "1049", "1050", "1051"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\Bookings.js", ["1052", "1053", "1054", "1055", "1056", "1057", "1058", "1059", "1060", "1061", "1062", "1063", "1064", "1065"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\student\\WriteReview.js", ["1066", "1067", "1068", "1069"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\AvailableHoursTable.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\Layout.js", ["1070", "1071", "1072", "1073", "1074"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\axios.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\timezone.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\WeeklyBookingsTable.js", ["1075", "1076", "1077", "1078"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\utils\\constants.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\BookableHoursTable.js", ["1079", "1080", "1081", "1082", "1083"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\StripePayment.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatWindow.js", ["1084", "1085", "1086"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\chat\\ChatList.js", ["1087", "1088"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\auth\\GenderDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\CropImageDialog.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationStatus.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\teacher\\ApplicationForm.js", ["1089", "1090", "1091", "1092", "1093", "1094", "1095", "1096", "1097", "1098", "1099"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\common\\Spinner.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\VideoSDKMeeting.js", ["1100", "1101", "1102", "1103", "1104", "1105", "1106", "1107"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\student\\ProfileCompletionAlert.js", ["1108"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\api.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingAppContext.js", ["1109"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\MeetingContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\JoiningScreen.js", ["1110", "1111", "1112", "1113", "1114"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\WaitingToJoinScreen.js", ["1115"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\screens\\LeaveScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\PresenterView.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\SimpleBottomBar.js", ["1116"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ParticipantView.js", ["1117"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\ConfirmBox.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\helper.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsMobile.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\SidebarContainer.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useIsTab.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\MeetingDetailsScreen.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useMediaStream.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownCam.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDown.js", ["1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\DropDownSpeaker.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\NetworkStats.js", ["1126"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\WebcamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\CameraPermissionDenied.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\WebcamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\MicOffSmallIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\SpeakerIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\hooks\\useWindowSize.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ScreenShareIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\utils\\common.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\Bottombar\\PipIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ChatPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\NotesPanel.js", ["1127"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\components\\sidebar\\ParticipantPanel.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropMIC.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMicOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropCAM.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\TestMic.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\PauseButton.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\DropDown\\DropSpeaker.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\UploadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshCheck.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\RefreshIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\DownloadIcon.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\NetworkStats\\WifiOff.jsx", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\RaiseHand.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\MicOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOnIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\meeting\\icons\\ParticipantTabPanel\\VideoCamOffIcon.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingPaymentPolicy.js", ["1128", "1129", "1130", "1131", "1132", "1133"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\RefundPolicy.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\BookingCancellationPolicy.js", ["1134", "1135", "1136", "1137", "1138", "1139"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\ContactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\TermsAndConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\termsConditions.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\i18n\\translations\\contactUs.js", [], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\MeetingFeedbackDialog.js", ["1140"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\pages\\admin\\MeetingIssues.js", ["1141"], [], "D:\\xampp\\htdocs\\allemnionline\\client\\src\\components\\RescheduleDialog.js", ["1142", "1143", "1144", "1145"], [], {"ruleId": "1146", "severity": 1, "message": "1147", "line": 3, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 3, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1150", "line": 4, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 4, "endColumn": 23}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 63, "column": 24, "nodeType": "1153", "messageId": "1154", "endLine": 63, "endColumn": 66}, {"ruleId": "1155", "severity": 1, "message": "1156", "line": 299, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 299, "endColumn": 16}, {"ruleId": "1155", "severity": 1, "message": "1159", "line": 329, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 329, "endColumn": 17}, {"ruleId": "1155", "severity": 1, "message": "1160", "line": 587, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 587, "endColumn": 24}, {"ruleId": "1155", "severity": 1, "message": "1161", "line": 595, "column": 7, "nodeType": "1157", "messageId": "1158", "endLine": 595, "endColumn": 14}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 627, "column": 30, "nodeType": "1153", "messageId": "1154", "endLine": 627, "endColumn": 107}, {"ruleId": "1155", "severity": 1, "message": "1162", "line": 646, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 646, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1163", "line": 715, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 715, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1164", "line": 827, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 827, "endColumn": 20}, {"ruleId": "1155", "severity": 1, "message": "1165", "line": 828, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 828, "endColumn": 27}, {"ruleId": "1155", "severity": 1, "message": "1166", "line": 915, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 915, "endColumn": 22}, {"ruleId": "1155", "severity": 1, "message": "1167", "line": 1066, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 1066, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1168", "line": 1067, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 1067, "endColumn": 26}, {"ruleId": "1155", "severity": 1, "message": "1169", "line": 1069, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 1069, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1170", "line": 1128, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 1128, "endColumn": 22}, {"ruleId": "1155", "severity": 1, "message": "1171", "line": 1135, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 1135, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1172", "line": 1136, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 1136, "endColumn": 20}, {"ruleId": "1155", "severity": 1, "message": "1173", "line": 1261, "column": 7, "nodeType": "1157", "messageId": "1158", "endLine": 1261, "endColumn": 12}, {"ruleId": "1155", "severity": 1, "message": "1174", "line": 1913, "column": 7, "nodeType": "1157", "messageId": "1158", "endLine": 1913, "endColumn": 12}, {"ruleId": "1151", "severity": 1, "message": "1152", "line": 1995, "column": 24, "nodeType": "1153", "messageId": "1154", "endLine": 1995, "endColumn": 58}, {"ruleId": "1155", "severity": 1, "message": "1175", "line": 2013, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2013, "endColumn": 24}, {"ruleId": "1155", "severity": 1, "message": "1162", "line": 2103, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2103, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1156", "line": 2240, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2240, "endColumn": 16}, {"ruleId": "1155", "severity": 1, "message": "1160", "line": 2526, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2526, "endColumn": 24}, {"ruleId": "1155", "severity": 1, "message": "1161", "line": 2534, "column": 7, "nodeType": "1157", "messageId": "1158", "endLine": 2534, "endColumn": 14}, {"ruleId": "1155", "severity": 1, "message": "1162", "line": 2586, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2586, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1163", "line": 2655, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2655, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1164", "line": 2771, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2771, "endColumn": 20}, {"ruleId": "1155", "severity": 1, "message": "1165", "line": 2772, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2772, "endColumn": 27}, {"ruleId": "1155", "severity": 1, "message": "1166", "line": 2855, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 2855, "endColumn": 22}, {"ruleId": "1155", "severity": 1, "message": "1167", "line": 3006, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 3006, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1168", "line": 3007, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 3007, "endColumn": 26}, {"ruleId": "1155", "severity": 1, "message": "1169", "line": 3009, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 3009, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1170", "line": 3077, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 3077, "endColumn": 22}, {"ruleId": "1155", "severity": 1, "message": "1171", "line": 3095, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 3095, "endColumn": 23}, {"ruleId": "1155", "severity": 1, "message": "1172", "line": 3096, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 3096, "endColumn": 20}, {"ruleId": "1155", "severity": 1, "message": "1173", "line": 3204, "column": 7, "nodeType": "1157", "messageId": "1158", "endLine": 3204, "endColumn": 12}, {"ruleId": "1155", "severity": 1, "message": "1174", "line": 3677, "column": 7, "nodeType": "1157", "messageId": "1158", "endLine": 3677, "endColumn": 12}, {"ruleId": "1155", "severity": 1, "message": "1176", "line": 4162, "column": 9, "nodeType": "1157", "messageId": "1158", "endLine": 4162, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1178", "line": 55, "column": 6, "nodeType": "1179", "endLine": 55, "endColumn": 8, "suggestions": "1180"}, {"ruleId": "1177", "severity": 1, "message": "1181", "line": 97, "column": 6, "nodeType": "1179", "endLine": 97, "endColumn": 43, "suggestions": "1182"}, {"ruleId": "1177", "severity": 1, "message": "1183", "line": 101, "column": 6, "nodeType": "1179", "endLine": 101, "endColumn": 40, "suggestions": "1184"}, {"ruleId": "1155", "severity": 1, "message": "1185", "line": 431, "column": 19, "nodeType": "1157", "messageId": "1158", "endLine": 431, "endColumn": 22}, {"ruleId": "1155", "severity": 1, "message": "1185", "line": 453, "column": 19, "nodeType": "1157", "messageId": "1158", "endLine": 453, "endColumn": 22}, {"ruleId": "1155", "severity": 1, "message": "1185", "line": 519, "column": 21, "nodeType": "1157", "messageId": "1158", "endLine": 519, "endColumn": 24}, {"ruleId": "1146", "severity": 1, "message": "1186", "line": 13, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 13, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1187", "line": 14, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 14, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1188", "line": 19, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 19, "endColumn": 8}, {"ruleId": "1146", "severity": 1, "message": "1189", "line": 23, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 23, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1190", "line": 28, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 28, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1191", "line": 33, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 33, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1192", "line": 34, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 34, "endColumn": 11}, {"ruleId": "1146", "severity": 1, "message": "1193", "line": 35, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 35, "endColumn": 15}, {"ruleId": "1146", "severity": 1, "message": "1194", "line": 38, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 38, "endColumn": 11}, {"ruleId": "1146", "severity": 1, "message": "1195", "line": 40, "column": 16, "nodeType": "1148", "messageId": "1149", "endLine": 40, "endColumn": 29}, {"ruleId": "1146", "severity": 1, "message": "1196", "line": 42, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 42, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1197", "line": 327, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 327, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1198", "line": 368, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 368, "endColumn": 29}, {"ruleId": "1199", "severity": 1, "message": "1200", "line": 371, "column": 5, "nodeType": "1148", "messageId": "1201", "endLine": 371, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1202", "line": 6, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 6, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1203", "line": 7, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 7, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1190", "line": 17, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 17, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1204", "line": 18, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 18, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1205", "line": 20, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 20, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1206", "line": 21, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 21, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1207", "line": 9, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 9, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1208", "line": 26, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 26, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 31, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 31, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1210", "line": 34, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 34, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1211", "line": 40, "column": 11, "nodeType": "1148", "messageId": "1149", "endLine": 40, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1212", "line": 310, "column": 6, "nodeType": "1179", "endLine": 310, "endColumn": 54, "suggestions": "1213"}, {"ruleId": "1146", "severity": 1, "message": "1214", "line": 367, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 367, "endColumn": 26}, {"ruleId": "1146", "severity": 1, "message": "1215", "line": 40, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 40, "endColumn": 14}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 93, "column": 66, "nodeType": "1153", "messageId": "1218", "endLine": 93, "endColumn": 67, "suggestions": "1219"}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 93, "column": 75, "nodeType": "1153", "messageId": "1218", "endLine": 93, "endColumn": 76, "suggestions": "1220"}, {"ruleId": "1216", "severity": 1, "message": "1221", "line": 93, "column": 77, "nodeType": "1153", "messageId": "1218", "endLine": 93, "endColumn": 78, "suggestions": "1222"}, {"ruleId": "1146", "severity": 1, "message": "1223", "line": 37, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 37, "endColumn": 15}, {"ruleId": "1146", "severity": 1, "message": "1223", "line": 37, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 37, "endColumn": 15}, {"ruleId": "1146", "severity": 1, "message": "1224", "line": 1, "column": 27, "nodeType": "1148", "messageId": "1149", "endLine": 1, "endColumn": 33}, {"ruleId": "1146", "severity": 1, "message": "1225", "line": 29, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 29, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1226", "line": 2, "column": 23, "nodeType": "1148", "messageId": "1149", "endLine": 2, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1227", "line": 23, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 23, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1223", "line": 40, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 40, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1228", "line": 32, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 32, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1229", "line": 36, "column": 18, "nodeType": "1148", "messageId": "1149", "endLine": 36, "endColumn": 33}, {"ruleId": "1146", "severity": 1, "message": "1230", "line": 37, "column": 20, "nodeType": "1148", "messageId": "1149", "endLine": 37, "endColumn": 37}, {"ruleId": "1146", "severity": 1, "message": "1231", "line": 27, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 27, "endColumn": 8}, {"ruleId": "1177", "severity": 1, "message": "1232", "line": 82, "column": 6, "nodeType": "1179", "endLine": 82, "endColumn": 41, "suggestions": "1233"}, {"ruleId": "1146", "severity": 1, "message": "1234", "line": 15, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 15, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1235", "line": 26, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 26, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1236", "line": 27, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 27, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1237", "line": 28, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 28, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1238", "line": 29, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 29, "endColumn": 11}, {"ruleId": "1177", "severity": 1, "message": "1239", "line": 84, "column": 6, "nodeType": "1179", "endLine": 84, "endColumn": 41, "suggestions": "1240"}, {"ruleId": "1146", "severity": 1, "message": "1202", "line": 21, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 21, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1203", "line": 22, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 22, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 32, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 32, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1241", "line": 39, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 39, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1242", "line": 58, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 58, "endColumn": 16}, {"ruleId": "1146", "severity": 1, "message": "1243", "line": 70, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 70, "endColumn": 25}, {"ruleId": "1146", "severity": 1, "message": "1244", "line": 190, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 190, "endColumn": 30}, {"ruleId": "1146", "severity": 1, "message": "1245", "line": 194, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 194, "endColumn": 31}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 203, "column": 66, "nodeType": "1153", "messageId": "1218", "endLine": 203, "endColumn": 67, "suggestions": "1246"}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 203, "column": 75, "nodeType": "1153", "messageId": "1218", "endLine": 203, "endColumn": 76, "suggestions": "1247"}, {"ruleId": "1216", "severity": 1, "message": "1221", "line": 203, "column": 77, "nodeType": "1153", "messageId": "1218", "endLine": 203, "endColumn": 78, "suggestions": "1248"}, {"ruleId": "1146", "severity": 1, "message": "1249", "line": 253, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 253, "endColumn": 22}, {"ruleId": "1177", "severity": 1, "message": "1250", "line": 43, "column": 6, "nodeType": "1179", "endLine": 43, "endColumn": 8, "suggestions": "1251"}, {"ruleId": "1177", "severity": 1, "message": "1252", "line": 43, "column": 6, "nodeType": "1179", "endLine": 43, "endColumn": 8, "suggestions": "1253"}, {"ruleId": "1146", "severity": 1, "message": "1254", "line": 39, "column": 11, "nodeType": "1148", "messageId": "1149", "endLine": 39, "endColumn": 12}, {"ruleId": "1177", "severity": 1, "message": "1255", "line": 88, "column": 6, "nodeType": "1179", "endLine": 88, "endColumn": 39, "suggestions": "1256"}, {"ruleId": "1146", "severity": 1, "message": "1257", "line": 40, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 40, "endColumn": 24}, {"ruleId": "1177", "severity": 1, "message": "1258", "line": 107, "column": 6, "nodeType": "1179", "endLine": 107, "endColumn": 15, "suggestions": "1259"}, {"ruleId": "1177", "severity": 1, "message": "1260", "line": 45, "column": 6, "nodeType": "1179", "endLine": 45, "endColumn": 45, "suggestions": "1261"}, {"ruleId": "1146", "severity": 1, "message": "1262", "line": 33, "column": 12, "nodeType": "1148", "messageId": "1149", "endLine": 33, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1263", "line": 103, "column": 6, "nodeType": "1179", "endLine": 103, "endColumn": 42, "suggestions": "1264"}, {"ruleId": "1177", "severity": 1, "message": "1265", "line": 50, "column": 6, "nodeType": "1179", "endLine": 50, "endColumn": 25, "suggestions": "1266"}, {"ruleId": "1146", "severity": 1, "message": "1267", "line": 54, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 54, "endColumn": 17}, {"ruleId": "1177", "severity": 1, "message": "1268", "line": 60, "column": 6, "nodeType": "1179", "endLine": 60, "endColumn": 39, "suggestions": "1269"}, {"ruleId": "1146", "severity": 1, "message": "1270", "line": 31, "column": 24, "nodeType": "1148", "messageId": "1149", "endLine": 31, "endColumn": 34}, {"ruleId": "1177", "severity": 1, "message": "1271", "line": 110, "column": 6, "nodeType": "1179", "endLine": 110, "endColumn": 22, "suggestions": "1272"}, {"ruleId": "1177", "severity": 1, "message": "1271", "line": 133, "column": 6, "nodeType": "1179", "endLine": 133, "endColumn": 19, "suggestions": "1273"}, {"ruleId": "1177", "severity": 1, "message": "1271", "line": 145, "column": 6, "nodeType": "1179", "endLine": 145, "endColumn": 19, "suggestions": "1274"}, {"ruleId": "1177", "severity": 1, "message": "1271", "line": 162, "column": 6, "nodeType": "1179", "endLine": 162, "endColumn": 19, "suggestions": "1275"}, {"ruleId": "1146", "severity": 1, "message": "1276", "line": 10, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 10, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1277", "line": 13, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 13, "endColumn": 8}, {"ruleId": "1146", "severity": 1, "message": "1278", "line": 14, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 14, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1279", "line": 15, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 15, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1280", "line": 16, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 16, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1281", "line": 17, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 17, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1282", "line": 18, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 18, "endColumn": 11}, {"ruleId": "1146", "severity": 1, "message": "1187", "line": 20, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 20, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 22, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 22, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1283", "line": 31, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 31, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1284", "line": 52, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 52, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1285", "line": 52, "column": 23, "nodeType": "1148", "messageId": "1149", "endLine": 52, "endColumn": 37}, {"ruleId": "1146", "severity": 1, "message": "1286", "line": 53, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 53, "endColumn": 22}, {"ruleId": "1146", "severity": 1, "message": "1287", "line": 53, "column": 24, "nodeType": "1148", "messageId": "1149", "endLine": 53, "endColumn": 39}, {"ruleId": "1146", "severity": 1, "message": "1288", "line": 227, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 227, "endColumn": 24}, {"ruleId": "1146", "severity": 1, "message": "1289", "line": 236, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 236, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1290", "line": 245, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 245, "endColumn": 29}, {"ruleId": "1146", "severity": 1, "message": "1291", "line": 258, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 258, "endColumn": 28}, {"ruleId": "1146", "severity": 1, "message": "1292", "line": 2, "column": 23, "nodeType": "1148", "messageId": "1149", "endLine": 2, "endColumn": 34}, {"ruleId": "1146", "severity": 1, "message": "1293", "line": 4, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 4, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1187", "line": 13, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 13, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 15, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 15, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1284", "line": 45, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 45, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1285", "line": 45, "column": 23, "nodeType": "1148", "messageId": "1149", "endLine": 45, "endColumn": 37}, {"ruleId": "1146", "severity": 1, "message": "1286", "line": 46, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 46, "endColumn": 22}, {"ruleId": "1146", "severity": 1, "message": "1287", "line": 46, "column": 24, "nodeType": "1148", "messageId": "1149", "endLine": 46, "endColumn": 39}, {"ruleId": "1146", "severity": 1, "message": "1294", "line": 5, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 5, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1207", "line": 7, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 7, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1202", "line": 9, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 9, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1203", "line": 10, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 10, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1276", "line": 11, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 11, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1235", "line": 16, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 16, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1236", "line": 17, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 17, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1237", "line": 18, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 18, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1238", "line": 19, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 19, "endColumn": 11}, {"ruleId": "1146", "severity": 1, "message": "1295", "line": 33, "column": 18, "nodeType": "1148", "messageId": "1149", "endLine": 33, "endColumn": 33}, {"ruleId": "1146", "severity": 1, "message": "1296", "line": 46, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 46, "endColumn": 33}, {"ruleId": "1146", "severity": 1, "message": "1297", "line": 46, "column": 35, "nodeType": "1148", "messageId": "1149", "endLine": 46, "endColumn": 59}, {"ruleId": "1146", "severity": 1, "message": "1298", "line": 51, "column": 11, "nodeType": "1148", "messageId": "1149", "endLine": 51, "endColumn": 22}, {"ruleId": "1146", "severity": 1, "message": "1299", "line": 58, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 58, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1300", "line": 110, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 110, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1301", "line": 154, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 154, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1302", "line": 215, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 215, "endColumn": 26}, {"ruleId": "1146", "severity": 1, "message": "1235", "line": 21, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 21, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1236", "line": 22, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 22, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1237", "line": 23, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 23, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1238", "line": 24, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 24, "endColumn": 11}, {"ruleId": "1146", "severity": 1, "message": "1296", "line": 46, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 46, "endColumn": 33}, {"ruleId": "1146", "severity": 1, "message": "1297", "line": 46, "column": 64, "nodeType": "1148", "messageId": "1149", "endLine": 46, "endColumn": 88}, {"ruleId": "1146", "severity": 1, "message": "1299", "line": 67, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 67, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1303", "line": 72, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 72, "endColumn": 30}, {"ruleId": "1146", "severity": 1, "message": "1284", "line": 73, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 73, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1304", "line": 74, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 74, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1305", "line": 75, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 75, "endColumn": 22}, {"ruleId": "1146", "severity": 1, "message": "1306", "line": 75, "column": 24, "nodeType": "1148", "messageId": "1149", "endLine": 75, "endColumn": 39}, {"ruleId": "1177", "severity": 1, "message": "1307", "line": 171, "column": 6, "nodeType": "1179", "endLine": 171, "endColumn": 16, "suggestions": "1308"}, {"ruleId": "1177", "severity": 1, "message": "1309", "line": 178, "column": 6, "nodeType": "1179", "endLine": 178, "endColumn": 31, "suggestions": "1310"}, {"ruleId": "1146", "severity": 1, "message": "1311", "line": 478, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 478, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1212", "line": 180, "column": 6, "nodeType": "1179", "endLine": 180, "endColumn": 28, "suggestions": "1312"}, {"ruleId": "1177", "severity": 1, "message": "1313", "line": 217, "column": 6, "nodeType": "1179", "endLine": 217, "endColumn": 28, "suggestions": "1314"}, {"ruleId": "1177", "severity": 1, "message": "1315", "line": 257, "column": 6, "nodeType": "1179", "endLine": 257, "endColumn": 14, "suggestions": "1316"}, {"ruleId": "1177", "severity": 1, "message": "1317", "line": 324, "column": 6, "nodeType": "1179", "endLine": 324, "endColumn": 53, "suggestions": "1318"}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 14, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 14, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1190", "line": 30, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 30, "endColumn": 27}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 71, "column": 64, "nodeType": "1153", "messageId": "1218", "endLine": 71, "endColumn": 65, "suggestions": "1319"}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 71, "column": 73, "nodeType": "1153", "messageId": "1218", "endLine": 71, "endColumn": 74, "suggestions": "1320"}, {"ruleId": "1216", "severity": 1, "message": "1221", "line": 71, "column": 75, "nodeType": "1153", "messageId": "1218", "endLine": 71, "endColumn": 76, "suggestions": "1321"}, {"ruleId": "1146", "severity": 1, "message": "1270", "line": 92, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 92, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1223", "line": 93, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 93, "endColumn": 15}, {"ruleId": "1146", "severity": 1, "message": "1322", "line": 93, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 93, "endColumn": 25}, {"ruleId": "1146", "severity": 1, "message": "1323", "line": 25, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 25, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1229", "line": 22, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 22, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1260", "line": 46, "column": 6, "nodeType": "1179", "endLine": 46, "endColumn": 45, "suggestions": "1324"}, {"ruleId": "1177", "severity": 1, "message": "1325", "line": 65, "column": 6, "nodeType": "1179", "endLine": 65, "endColumn": 45, "suggestions": "1326"}, {"ruleId": "1177", "severity": 1, "message": "1263", "line": 83, "column": 6, "nodeType": "1179", "endLine": 83, "endColumn": 32, "suggestions": "1327"}, {"ruleId": "1146", "severity": 1, "message": "1328", "line": 17, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 17, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1329", "line": 29, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 29, "endColumn": 11}, {"ruleId": "1146", "severity": 1, "message": "1211", "line": 32, "column": 11, "nodeType": "1148", "messageId": "1149", "endLine": 32, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1330", "line": 33, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 33, "endColumn": 31}, {"ruleId": "1146", "severity": 1, "message": "1331", "line": 40, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 40, "endColumn": 31}, {"ruleId": "1146", "severity": 1, "message": "1332", "line": 41, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 41, "endColumn": 31}, {"ruleId": "1146", "severity": 1, "message": "1223", "line": 49, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 49, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1333", "line": 68, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 68, "endColumn": 25}, {"ruleId": "1146", "severity": 1, "message": "1334", "line": 289, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 289, "endColumn": 29}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 4, "column": 29, "nodeType": "1148", "messageId": "1149", "endLine": 4, "endColumn": 36}, {"ruleId": "1146", "severity": 1, "message": "1298", "line": 28, "column": 11, "nodeType": "1148", "messageId": "1149", "endLine": 28, "endColumn": 22}, {"ruleId": "1146", "severity": 1, "message": "1207", "line": 14, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 14, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1208", "line": 31, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 31, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 36, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 36, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1210", "line": 39, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 39, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1335", "line": 42, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 42, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1336", "line": 43, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 43, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1337", "line": 44, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 44, "endColumn": 16}, {"ruleId": "1146", "severity": 1, "message": "1338", "line": 45, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 45, "endColumn": 16}, {"ruleId": "1146", "severity": 1, "message": "1211", "line": 51, "column": 11, "nodeType": "1148", "messageId": "1149", "endLine": 51, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1267", "line": 174, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 174, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1339", "line": 203, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 203, "endColumn": 19}, {"ruleId": "1177", "severity": 1, "message": "1239", "line": 298, "column": 6, "nodeType": "1179", "endLine": 298, "endColumn": 48, "suggestions": "1340"}, {"ruleId": "1146", "severity": 1, "message": "1214", "line": 337, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 337, "endColumn": 26}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 422, "column": 68, "nodeType": "1153", "messageId": "1218", "endLine": 422, "endColumn": 69, "suggestions": "1341"}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 422, "column": 77, "nodeType": "1153", "messageId": "1218", "endLine": 422, "endColumn": 78, "suggestions": "1342"}, {"ruleId": "1216", "severity": 1, "message": "1221", "line": 422, "column": 79, "nodeType": "1153", "messageId": "1218", "endLine": 422, "endColumn": 80, "suggestions": "1343"}, {"ruleId": "1146", "severity": 1, "message": "1202", "line": 15, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 15, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1203", "line": 16, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 16, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1338", "line": 30, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 30, "endColumn": 16}, {"ruleId": "1146", "severity": 1, "message": "1344", "line": 31, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 31, "endColumn": 30}, {"ruleId": "1146", "severity": 1, "message": "1197", "line": 63, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 63, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1345", "line": 71, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 71, "endColumn": 24}, {"ruleId": "1146", "severity": 1, "message": "1346", "line": 72, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 72, "endColumn": 22}, {"ruleId": "1347", "severity": 1, "message": "1348", "line": 200, "column": 7, "nodeType": "1148", "messageId": "1349", "endLine": 200, "endColumn": 13}, {"ruleId": "1177", "severity": 1, "message": "1350", "line": 315, "column": 6, "nodeType": "1179", "endLine": 315, "endColumn": 39, "suggestions": "1351"}, {"ruleId": "1146", "severity": 1, "message": "1352", "line": 397, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 397, "endColumn": 23}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 487, "column": 68, "nodeType": "1153", "messageId": "1218", "endLine": 487, "endColumn": 69, "suggestions": "1353"}, {"ruleId": "1216", "severity": 1, "message": "1217", "line": 487, "column": 77, "nodeType": "1153", "messageId": "1218", "endLine": 487, "endColumn": 78, "suggestions": "1354"}, {"ruleId": "1216", "severity": 1, "message": "1221", "line": 487, "column": 79, "nodeType": "1153", "messageId": "1218", "endLine": 487, "endColumn": 80, "suggestions": "1355"}, {"ruleId": "1146", "severity": 1, "message": "1356", "line": 7, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 7, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1357", "line": 22, "column": 19, "nodeType": "1148", "messageId": "1149", "endLine": 22, "endColumn": 30}, {"ruleId": "1177", "severity": 1, "message": "1212", "line": 196, "column": 6, "nodeType": "1179", "endLine": 196, "endColumn": 28, "suggestions": "1358"}, {"ruleId": "1177", "severity": 1, "message": "1313", "line": 233, "column": 6, "nodeType": "1179", "endLine": 233, "endColumn": 28, "suggestions": "1359"}, {"ruleId": "1177", "severity": 1, "message": "1315", "line": 273, "column": 6, "nodeType": "1179", "endLine": 273, "endColumn": 14, "suggestions": "1360"}, {"ruleId": "1146", "severity": 1, "message": "1361", "line": 24, "column": 24, "nodeType": "1148", "messageId": "1149", "endLine": 24, "endColumn": 29}, {"ruleId": "1177", "severity": 1, "message": "1263", "line": 84, "column": 6, "nodeType": "1179", "endLine": 84, "endColumn": 32, "suggestions": "1362"}, {"ruleId": "1146", "severity": 1, "message": "1276", "line": 5, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 5, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1202", "line": 6, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 6, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1226", "line": 20, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 20, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1363", "line": 27, "column": 18, "nodeType": "1148", "messageId": "1149", "endLine": 27, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1364", "line": 29, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 29, "endColumn": 15}, {"ruleId": "1146", "severity": 1, "message": "1299", "line": 44, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 44, "endColumn": 21}, {"ruleId": "1177", "severity": 1, "message": "1365", "line": 66, "column": 6, "nodeType": "1179", "endLine": 66, "endColumn": 19, "suggestions": "1366"}, {"ruleId": "1146", "severity": 1, "message": "1367", "line": 127, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 127, "endColumn": 40}, {"ruleId": "1146", "severity": 1, "message": "1368", "line": 135, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 135, "endColumn": 43}, {"ruleId": "1146", "severity": 1, "message": "1369", "line": 147, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 147, "endColumn": 22}, {"ruleId": "1146", "severity": 1, "message": "1370", "line": 156, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 156, "endColumn": 40}, {"ruleId": "1146", "severity": 1, "message": "1371", "line": 184, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 184, "endColumn": 23}, {"ruleId": "1177", "severity": 1, "message": "1260", "line": 74, "column": 6, "nodeType": "1179", "endLine": 74, "endColumn": 45, "suggestions": "1372"}, {"ruleId": "1146", "severity": 1, "message": "1328", "line": 14, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 14, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1373", "line": 17, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 17, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 18, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 18, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1374", "line": 19, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 19, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1375", "line": 49, "column": 40, "nodeType": "1148", "messageId": "1149", "endLine": 49, "endColumn": 49}, {"ruleId": "1146", "severity": 1, "message": "1376", "line": 51, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 51, "endColumn": 32}, {"ruleId": "1146", "severity": 1, "message": "1297", "line": 51, "column": 34, "nodeType": "1148", "messageId": "1149", "endLine": 51, "endColumn": 58}, {"ruleId": "1146", "severity": 1, "message": "1296", "line": 51, "column": 89, "nodeType": "1148", "messageId": "1149", "endLine": 51, "endColumn": 112}, {"ruleId": "1146", "severity": 1, "message": "1267", "line": 59, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 59, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1197", "line": 62, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 62, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1370", "line": 278, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 278, "endColumn": 40}, {"ruleId": "1146", "severity": 1, "message": "1377", "line": 409, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 409, "endColumn": 28}, {"ruleId": "1146", "severity": 1, "message": "1378", "line": 428, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 428, "endColumn": 35}, {"ruleId": "1146", "severity": 1, "message": "1379", "line": 451, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 451, "endColumn": 36}, {"ruleId": "1146", "severity": 1, "message": "1380", "line": 712, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 712, "endColumn": 29}, {"ruleId": "1146", "severity": 1, "message": "1235", "line": 25, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 25, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1236", "line": 26, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 26, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1237", "line": 27, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 27, "endColumn": 9}, {"ruleId": "1146", "severity": 1, "message": "1238", "line": 28, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 28, "endColumn": 11}, {"ruleId": "1146", "severity": 1, "message": "1381", "line": 33, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 33, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1303", "line": 88, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 88, "endColumn": 30}, {"ruleId": "1146", "severity": 1, "message": "1284", "line": 89, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 89, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1304", "line": 90, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 90, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1305", "line": 91, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 91, "endColumn": 22}, {"ruleId": "1146", "severity": 1, "message": "1306", "line": 91, "column": 24, "nodeType": "1148", "messageId": "1149", "endLine": 91, "endColumn": 39}, {"ruleId": "1146", "severity": 1, "message": "1311", "line": 285, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 285, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1382", "line": 622, "column": 12, "nodeType": "1148", "messageId": "1149", "endLine": 622, "endColumn": 20}, {"ruleId": "1146", "severity": 1, "message": "1311", "line": 643, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 643, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1383", "line": 666, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 666, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1211", "line": 27, "column": 11, "nodeType": "1148", "messageId": "1149", "endLine": 27, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1267", "line": 36, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 36, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1223", "line": 37, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 37, "endColumn": 14}, {"ruleId": "1177", "severity": 1, "message": "1384", "line": 467, "column": 6, "nodeType": "1179", "endLine": 467, "endColumn": 32, "suggestions": "1385"}, {"ruleId": "1146", "severity": 1, "message": "1386", "line": 18, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 18, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1241", "line": 22, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 22, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1226", "line": 34, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 34, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1387", "line": 49, "column": 20, "nodeType": "1148", "messageId": "1149", "endLine": 49, "endColumn": 37}, {"ruleId": "1177", "severity": 1, "message": "1388", "line": 110, "column": 6, "nodeType": "1179", "endLine": 110, "endColumn": 38, "suggestions": "1389"}, {"ruleId": "1146", "severity": 1, "message": "1328", "line": 8, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 8, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1187", "line": 17, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 17, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1390", "line": 21, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 21, "endColumn": 31}, {"ruleId": "1146", "severity": 1, "message": "1391", "line": 28, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 28, "endColumn": 14}, {"ruleId": "1146", "severity": 1, "message": "1392", "line": 18, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 18, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1393", "line": 148, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 148, "endColumn": 25}, {"ruleId": "1146", "severity": 1, "message": "1394", "line": 158, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 158, "endColumn": 24}, {"ruleId": "1146", "severity": 1, "message": "1395", "line": 177, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 177, "endColumn": 29}, {"ruleId": "1146", "severity": 1, "message": "1396", "line": 306, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 306, "endColumn": 30}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 11, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 11, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1397", "line": 26, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 26, "endColumn": 29}, {"ruleId": "1146", "severity": 1, "message": "1398", "line": 27, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 27, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1397", "line": 21, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 21, "endColumn": 29}, {"ruleId": "1146", "severity": 1, "message": "1398", "line": 22, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 22, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1399", "line": 18, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 18, "endColumn": 12}, {"ruleId": "1146", "severity": 1, "message": "1400", "line": 19, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 19, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1401", "line": 20, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 20, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1210", "line": 21, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 21, "endColumn": 19}, {"ruleId": "1146", "severity": 1, "message": "1402", "line": 22, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 22, "endColumn": 11}, {"ruleId": "1146", "severity": 1, "message": "1403", "line": 24, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 24, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 34, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 34, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1331", "line": 43, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 43, "endColumn": 22}, {"ruleId": "1146", "severity": 1, "message": "1404", "line": 44, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 44, "endColumn": 21}, {"ruleId": "1146", "severity": 1, "message": "1405", "line": 45, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 45, "endColumn": 16}, {"ruleId": "1146", "severity": 1, "message": "1406", "line": 127, "column": 9, "nodeType": "1148", "messageId": "1149", "endLine": 127, "endColumn": 18}, {"ruleId": "1146", "severity": 1, "message": "1224", "line": 1, "column": 38, "nodeType": "1148", "messageId": "1149", "endLine": 1, "endColumn": 44}, {"ruleId": "1146", "severity": 1, "message": "1407", "line": 1, "column": 46, "nodeType": "1148", "messageId": "1149", "endLine": 1, "endColumn": 53}, {"ruleId": "1146", "severity": 1, "message": "1408", "line": 4, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 4, "endColumn": 18}, {"ruleId": "1146", "severity": 1, "message": "1409", "line": 5, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 5, "endColumn": 13}, {"ruleId": "1146", "severity": 1, "message": "1410", "line": 6, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 6, "endColumn": 17}, {"ruleId": "1146", "severity": 1, "message": "1411", "line": 9, "column": 46, "nodeType": "1148", "messageId": "1149", "endLine": 9, "endColumn": 71}, {"ruleId": "1146", "severity": 1, "message": "1412", "line": 9, "column": 73, "nodeType": "1148", "messageId": "1149", "endLine": 9, "endColumn": 88}, {"ruleId": "1146", "severity": 1, "message": "1413", "line": 14, "column": 8, "nodeType": "1148", "messageId": "1149", "endLine": 14, "endColumn": 27}, {"ruleId": "1177", "severity": 1, "message": "1414", "line": 49, "column": 6, "nodeType": "1179", "endLine": 49, "endColumn": 22, "suggestions": "1415"}, {"ruleId": "1177", "severity": 1, "message": "1416", "line": 40, "column": 8, "nodeType": "1179", "endLine": 40, "endColumn": 33, "suggestions": "1417"}, {"ruleId": "1177", "severity": 1, "message": "1418", "line": 128, "column": 6, "nodeType": "1179", "endLine": 128, "endColumn": 33, "suggestions": "1419"}, {"ruleId": "1177", "severity": 1, "message": "1420", "line": 132, "column": 6, "nodeType": "1179", "endLine": 132, "endColumn": 37, "suggestions": "1421"}, {"ruleId": "1177", "severity": 1, "message": "1422", "line": 137, "column": 6, "nodeType": "1179", "endLine": 137, "endColumn": 8, "suggestions": "1423"}, {"ruleId": "1177", "severity": 1, "message": "1424", "line": 300, "column": 6, "nodeType": "1179", "endLine": 300, "endColumn": 8, "suggestions": "1425"}, {"ruleId": "1177", "severity": 1, "message": "1420", "line": 403, "column": 6, "nodeType": "1179", "endLine": 403, "endColumn": 8, "suggestions": "1426"}, {"ruleId": "1177", "severity": 1, "message": "1427", "line": 28, "column": 6, "nodeType": "1179", "endLine": 28, "endColumn": 8, "suggestions": "1428"}, {"ruleId": "1177", "severity": 1, "message": "1429", "line": 230, "column": 6, "nodeType": "1179", "endLine": 230, "endColumn": 34, "suggestions": "1430"}, {"ruleId": "1177", "severity": 1, "message": "1431", "line": 220, "column": 6, "nodeType": "1179", "endLine": 220, "endColumn": 50, "suggestions": "1432"}, {"ruleId": "1177", "severity": 1, "message": "1433", "line": 59, "column": 6, "nodeType": "1179", "endLine": 59, "endColumn": 23, "suggestions": "1434"}, {"ruleId": "1435", "severity": 1, "message": "1436", "line": 148, "column": 37, "nodeType": "1437", "messageId": "1158", "endLine": 148, "endColumn": 39}, {"ruleId": "1435", "severity": 1, "message": "1438", "line": 173, "column": 82, "nodeType": "1437", "messageId": "1158", "endLine": 173, "endColumn": 84}, {"ruleId": "1435", "severity": 1, "message": "1438", "line": 228, "column": 104, "nodeType": "1437", "messageId": "1158", "endLine": 228, "endColumn": 106}, {"ruleId": "1435", "severity": 1, "message": "1438", "line": 257, "column": 44, "nodeType": "1437", "messageId": "1158", "endLine": 257, "endColumn": 46}, {"ruleId": "1435", "severity": 1, "message": "1438", "line": 261, "column": 44, "nodeType": "1437", "messageId": "1158", "endLine": 261, "endColumn": 46}, {"ruleId": "1435", "severity": 1, "message": "1438", "line": 265, "column": 44, "nodeType": "1437", "messageId": "1158", "endLine": 265, "endColumn": 46}, {"ruleId": "1435", "severity": 1, "message": "1438", "line": 271, "column": 44, "nodeType": "1437", "messageId": "1158", "endLine": 271, "endColumn": 46}, {"ruleId": "1439", "severity": 1, "message": "1440", "line": 9, "column": 23, "nodeType": "1441", "messageId": "1158", "endLine": 9, "endColumn": 26}, {"ruleId": "1177", "severity": 1, "message": "1442", "line": 32, "column": 6, "nodeType": "1179", "endLine": 32, "endColumn": 28, "suggestions": "1443"}, {"ruleId": "1146", "severity": 1, "message": "1328", "line": 17, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 17, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1444", "line": 23, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 23, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1445", "line": 25, "column": 14, "nodeType": "1148", "messageId": "1149", "endLine": 25, "endColumn": 25}, {"ruleId": "1146", "severity": 1, "message": "1446", "line": 28, "column": 14, "nodeType": "1148", "messageId": "1149", "endLine": 28, "endColumn": 25}, {"ruleId": "1146", "severity": 1, "message": "1390", "line": 30, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 30, "endColumn": 31}, {"ruleId": "1146", "severity": 1, "message": "1447", "line": 31, "column": 17, "nodeType": "1148", "messageId": "1149", "endLine": 31, "endColumn": 31}, {"ruleId": "1146", "severity": 1, "message": "1328", "line": 15, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 15, "endColumn": 7}, {"ruleId": "1146", "severity": 1, "message": "1231", "line": 20, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 20, "endColumn": 8}, {"ruleId": "1146", "severity": 1, "message": "1444", "line": 23, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 23, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1446", "line": 26, "column": 14, "nodeType": "1148", "messageId": "1149", "endLine": 26, "endColumn": 25}, {"ruleId": "1146", "severity": 1, "message": "1381", "line": 28, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 28, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1448", "line": 29, "column": 13, "nodeType": "1148", "messageId": "1149", "endLine": 29, "endColumn": 23}, {"ruleId": "1146", "severity": 1, "message": "1296", "line": 19, "column": 10, "nodeType": "1148", "messageId": "1149", "endLine": 19, "endColumn": 33}, {"ruleId": "1146", "severity": 1, "message": "1444", "line": 33, "column": 15, "nodeType": "1148", "messageId": "1149", "endLine": 33, "endColumn": 27}, {"ruleId": "1146", "severity": 1, "message": "1209", "line": 20, "column": 3, "nodeType": "1148", "messageId": "1149", "endLine": 20, "endColumn": 10}, {"ruleId": "1146", "severity": 1, "message": "1449", "line": 104, "column": 11, "nodeType": "1148", "messageId": "1149", "endLine": 104, "endColumn": 25}, {"ruleId": "1177", "severity": 1, "message": "1450", "line": 225, "column": 7, "nodeType": "1451", "endLine": 225, "endColumn": 48}, {"ruleId": "1146", "severity": 1, "message": "1452", "line": 836, "column": 33, "nodeType": "1148", "messageId": "1149", "endLine": 836, "endColumn": 42}, "no-unused-vars", "'meetingIssuesEn' is defined but never used.", "Identifier", "unusedVar", "'meetingIssuesAr' is defined but never used.", "no-template-curly-in-string", "Unexpected template string expression.", "Literal", "unexpectedTemplateExpression", "no-dupe-keys", "Duplicate key 'details'.", "ObjectExpression", "unexpected", "Duplicate key 'editInfo'.", "Duplicate key 'noTeachersFound'.", "Duplicate key 'booking'.", "Duplicate key 'selectDuration'.", "Duplicate key 'currentBooking'.", "Duplicate key 'invalidCode'.", "Duplicate key 'verificationFailed'.", "Duplicate key 'updateProfile'.", "Duplicate key 'nativeLanguage'.", "Duplicate key 'teachingLanguages'.", "Duplicate key 'qualifications'.", "Duplicate key 'formHasErrors'.", "Duplicate key 'allowedFormats'.", "Duplicate key 'maxFileSize'.", "Duplicate key 'admin'.", "Duplicate key 'about'.", "Duplicate key 'errorCancelling'.", "Duplicate key 'earnings'.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'handleLogout'. Either include it or remove the dependency array.", "ArrayExpression", ["1453"], "React Hook useEffect has a missing dependency: 'socket'. Either include it or remove the dependency array.", ["1454"], "React Hook useEffect has a missing dependency: 'fetchUnreadCount'. Either include it or remove the dependency array.", ["1455"], "Duplicate key 'gap'.", "'CardMedia' is defined but never used.", "'IconButton' is defined but never used.", "'Slide' is defined but never used.", "'Zoom' is defined but never used.", "'LanguageIcon' is defined but never used.", "'Star' is defined but never used.", "'Timeline' is defined but never used.", "'LocalLibrary' is defined but never used.", "'Language' is defined but never used.", "'TranslateIcon' is defined but never used.", "'useAuth' is defined but never used.", "'isMobile' is assigned a value but never used.", "'handleLanguageChange' is assigned a value but never used.", "no-const-assign", "'isRtl' is constant.", "const", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'PeopleIcon' is defined but never used.", "'PublicIcon' is defined but never used.", "'SecurityIcon' is defined but never used.", "'TextField' is defined but never used.", "'Drawer' is defined but never used.", "'Divider' is defined but never used.", "'FormControlLabel' is defined but never used.", "'StarIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 't'. Either include it or remove the dependency array.", ["1456"], "'getPriceRangeText' is assigned a value but never used.", "'theme' is assigned a value but never used.", "no-useless-escape", "Unnecessary escape character: \\&.", "unnecessaryEscape", ["1457", "1458"], ["1459", "1460"], "Unnecessary escape character: \\?.", ["1461", "1462"], "'isRtl' is assigned a value but never used.", "'useRef' is defined but never used.", "'GoogleIcon' is defined but never used.", "'Link' is defined but never used.", "'VerifiedIcon' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'ArrowUpwardIcon' is defined but never used.", "'ArrowDownwardIcon' is defined but never used.", "'Stack' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchStudents'. Either include it or remove the dependency array.", ["1463"], "'CircularProgress' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'MenuItem' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchTeachers'. Either include it or remove the dependency array.", ["1464"], "'Tooltip' is defined but never used.", "'debounce' is defined but never used.", "'openVideoDialog' is assigned a value but never used.", "'handleOpenVideoDialog' is assigned a value but never used.", "'handleCloseVideoDialog' is assigned a value but never used.", ["1465", "1466"], ["1467", "1468"], ["1469", "1470"], "'safeParseJSON' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLanguages'. Either include it or remove the dependency array.", ["1471"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array.", ["1472"], "'t' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUpdates'. Either include it or remove the dependency array.", ["1473"], "'recentSessions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchSessions'. Either include it or remove the dependency array.", ["1474"], "React Hook useEffect has missing dependencies: 'fetchBalance' and 'fetchTransactions'. Either include them or remove the dependency array.", ["1475"], "'CheckIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array.", ["1476"], "React Hook useEffect has a missing dependency: 'fetchEarnings'. Either include it or remove the dependency array.", ["1477"], "'navigate' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchWithdrawals'. Either include it or remove the dependency array.", ["1478"], "'updateUser' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchProfileData'. Either include it or remove the dependency array.", ["1479"], ["1480"], ["1481"], ["1482"], "'Grid' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'EventBusyIcon' is defined but never used.", "'selectedDay' is assigned a value but never used.", "'setSelectedDay' is assigned a value but never used.", "'selectedHour' is assigned a value but never used.", "'setSelectedHour' is assigned a value but never used.", "'selectAllForDay' is assigned a value but never used.", "'clearAllForDay' is assigned a value but never used.", "'selectTimeForAllDays' is assigned a value but never used.", "'clearTimeForAllDays' is assigned a value but never used.", "'useLocation' is defined but never used.", "'axios' is defined but never used.", "'Button' is defined but never used.", "'ContentCopyIcon' is defined but never used.", "'convertFromDatabaseTime' is defined but never used.", "'getCurrentTimeInTimezone' is defined but never used.", "'currentUser' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "'calculatePrice' is assigned a value but never used.", "'handleCopyLink' is assigned a value but never used.", "'getMeetingActions' is assigned a value but never used.", "'availableTimesForDay' is assigned a value but never used.", "'loadingDays' is assigned a value but never used.", "'loadingTimes' is assigned a value but never used.", "'setLoadingTimes' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchAvailableHours' and 'fetchWeeklyBreaks'. Either include them or remove the dependency array.", ["1483"], "React Hook useEffect has a missing dependency: 'fetchWeeklyBreaks'. Either include it or remove the dependency array.", ["1484"], "'response' is assigned a value but never used.", ["1485"], "React Hook useEffect has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1486"], "React Hook useCallback has a missing dependency: 'decreaseUnreadCount'. Either include it or remove the dependency array.", ["1487"], "React Hook useCallback has an unnecessary dependency: 'messages'. Either exclude it or remove the dependency array.", ["1488"], ["1489", "1490"], ["1491", "1492"], ["1493", "1494"], "'setIsRtl' is assigned a value but never used.", "'i18n' is defined but never used.", ["1495"], "React Hook useEffect has missing dependencies: 'fetchBalance', 'fetchSettings', and 'fetchWithdrawals'. Either include them or remove the dependency array.", ["1496"], ["1497"], "'Chip' is defined but never used.", "'Collapse' is defined but never used.", "'StarBorderIcon' is defined but never used.", "'ExpandMoreIcon' is defined but never used.", "'ExpandLessIcon' is defined but never used.", "'expandedReplies' is assigned a value but never used.", "'toggleReplyExpansion' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'fullScreen' is assigned a value but never used.", ["1498"], ["1499", "1500"], ["1501", "1502"], ["1503", "1504"], "'MuiIconButton' is defined but never used.", "'availableSlots' is assigned a value but never used.", "'loadingSlots' is assigned a value but never used.", "no-use-before-define", "'socket' was used before it was defined.", "usedBeforeDefined", "React Hook useCallback has an unnecessary dependency: 'id'. Either exclude it or remove the dependency array.", ["1505"], "'handleBookSlot' is assigned a value but never used.", ["1506", "1507"], ["1508", "1509"], ["1510", "1511"], "'Typography' is defined but never used.", "'isConnected' is assigned a value but never used.", ["1512"], ["1513"], ["1514"], "'token' is assigned a value but never used.", ["1515"], "'MoneyIcon' is defined but never used.", "'toast' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchMeetings'. Either include it or remove the dependency array.", ["1516"], "'getMeetingDateInStudentTimezone' is assigned a value but never used.", "'formatMeetingDateInStudentTimezone' is assigned a value but never used.", "'dateFnsFormat' is assigned a value but never used.", "'getCurrentTimeInStudentTimezone' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", ["1517"], "'CardActions' is defined but never used.", "'Avatar' is defined but never used.", "'isSameDay' is defined but never used.", "'convertBookingDateTime' is defined but never used.", "'isFullHourAvailable' is assigned a value but never used.", "'checkCrossHourAvailability' is assigned a value but never used.", "'checkSecondHalfAvailability' is assigned a value but never used.", "'renderBookingSuccess' is assigned a value but never used.", "'PersonIcon' is defined but never used.", "'datePart' is assigned a value but never used.", "'renderBookings' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'teachers'. Either include it or remove the dependency array.", ["1518"], "'ListItemButton' is defined but never used.", "'AccountCircleIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchPending'. Either include it or remove the dependency array.", ["1519"], "'AccessTimeIcon' is defined but never used.", "'moment' is defined but never used.", "'CheckCircleIcon' is defined but never used.", "'isPartOfFullHour' is assigned a value but never used.", "'isFullHourStart' is assigned a value but never used.", "'isFullHourSecondSlot' is assigned a value but never used.", "'formattedDate' is defined but never used.", "'formatDistanceToNow' is defined but never used.", "'ar' is defined but never used.", "'Accordion' is defined but never used.", "'AccordionSummary' is defined but never used.", "'AccordionDetails' is defined but never used.", "'Checkbox' is defined but never used.", "'FormHelperText' is defined but never used.", "'VideoFileIcon' is defined but never used.", "'LinkIcon' is defined but never used.", "'timeSlots' is assigned a value but never used.", "'useMemo' is defined but never used.", "'MeetingConsumer' is defined but never used.", "'useMeeting' is defined but never used.", "'useParticipant' is defined but never used.", "'createMeetingWithCustomId' is defined but never used.", "'validateMeeting' is defined but never used.", "'WaitingToJoinScreen' is defined but never used.", "React Hook useEffect has a missing dependency: 'isExemptPage'. Either include it or remove the dependency array.", ["1520"], "React Hook useEffect has an unnecessary dependency: 'raisedHandsParticipants'. Either exclude it or remove the dependency array. Outer scope values like 'raisedHandsParticipants' aren't valid dependencies because mutating them doesn't re-render the component.", ["1521"], "React Hook useEffect has a missing dependency: 'getCameraDevices'. Either include it or remove the dependency array.", ["1522"], "React Hook useEffect has a missing dependency: 'getAudioDevices'. Either include it or remove the dependency array.", ["1523"], "React Hook useEffect has a missing dependency: 'checkMediaPermission'. Either include it or remove the dependency array.", ["1524"], "React Hook useEffect has a missing dependency: 'onDeviceChanged'. Either include it or remove the dependency array.", ["1525"], ["1526"], "React Hook useEffect has a missing dependency: 'waitingMessages'. Either include it or remove the dependency array.", ["1527"], "React Hook useEffect has missing dependencies: 'leave', 'onClose', and 'setIsMeetingLeft'. Either include them or remove the dependency array. If 'setIsMeetingLeft' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1528"], "React Hook useEffect has a missing dependency: 'updateStats'. Either include it or remove the dependency array.", ["1529"], "React Hook useEffect has a missing dependency: 'setDidDeviceChange'. Either include it or remove the dependency array. If 'setDidDeviceChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1530"], "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "Expected '===' and instead saw '=='.", "no-empty-pattern", "Unexpected empty object pattern.", "ObjectPattern", "React Hook useEffect has a missing dependency: 'meetingId'. Either include it or remove the dependency array.", ["1531"], "'ScheduleIcon' is defined but never used.", "'PaymentIcon' is defined but never used.", "'WarningIcon' is defined but never used.", "'CreditCardIcon' is defined but never used.", "'SchoolIcon' is defined but never used.", "'oneHourFromNow' is assigned a value but never used.", "React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "'isEvening' is assigned a value but never used.", {"desc": "1532", "fix": "1533"}, {"desc": "1534", "fix": "1535"}, {"desc": "1536", "fix": "1537"}, {"desc": "1538", "fix": "1539"}, {"messageId": "1540", "fix": "1541", "desc": "1542"}, {"messageId": "1543", "fix": "1544", "desc": "1545"}, {"messageId": "1540", "fix": "1546", "desc": "1542"}, {"messageId": "1543", "fix": "1547", "desc": "1545"}, {"messageId": "1540", "fix": "1548", "desc": "1542"}, {"messageId": "1543", "fix": "1549", "desc": "1545"}, {"desc": "1550", "fix": "1551"}, {"desc": "1552", "fix": "1553"}, {"messageId": "1540", "fix": "1554", "desc": "1542"}, {"messageId": "1543", "fix": "1555", "desc": "1545"}, {"messageId": "1540", "fix": "1556", "desc": "1542"}, {"messageId": "1543", "fix": "1557", "desc": "1545"}, {"messageId": "1540", "fix": "1558", "desc": "1542"}, {"messageId": "1543", "fix": "1559", "desc": "1545"}, {"desc": "1560", "fix": "1561"}, {"desc": "1562", "fix": "1563"}, {"desc": "1564", "fix": "1565"}, {"desc": "1566", "fix": "1567"}, {"desc": "1568", "fix": "1569"}, {"desc": "1570", "fix": "1571"}, {"desc": "1572", "fix": "1573"}, {"desc": "1574", "fix": "1575"}, {"desc": "1576", "fix": "1577"}, {"desc": "1578", "fix": "1579"}, {"desc": "1578", "fix": "1580"}, {"desc": "1578", "fix": "1581"}, {"desc": "1582", "fix": "1583"}, {"desc": "1584", "fix": "1585"}, {"desc": "1586", "fix": "1587"}, {"desc": "1588", "fix": "1589"}, {"desc": "1590", "fix": "1591"}, {"desc": "1592", "fix": "1593"}, {"messageId": "1540", "fix": "1594", "desc": "1542"}, {"messageId": "1543", "fix": "1595", "desc": "1545"}, {"messageId": "1540", "fix": "1596", "desc": "1542"}, {"messageId": "1543", "fix": "1597", "desc": "1545"}, {"messageId": "1540", "fix": "1598", "desc": "1542"}, {"messageId": "1543", "fix": "1599", "desc": "1545"}, {"desc": "1568", "fix": "1600"}, {"desc": "1601", "fix": "1602"}, {"desc": "1603", "fix": "1604"}, {"desc": "1605", "fix": "1606"}, {"messageId": "1540", "fix": "1607", "desc": "1542"}, {"messageId": "1543", "fix": "1608", "desc": "1545"}, {"messageId": "1540", "fix": "1609", "desc": "1542"}, {"messageId": "1543", "fix": "1610", "desc": "1545"}, {"messageId": "1540", "fix": "1611", "desc": "1542"}, {"messageId": "1543", "fix": "1612", "desc": "1545"}, {"desc": "1613", "fix": "1614"}, {"messageId": "1540", "fix": "1615", "desc": "1542"}, {"messageId": "1543", "fix": "1616", "desc": "1545"}, {"messageId": "1540", "fix": "1617", "desc": "1542"}, {"messageId": "1543", "fix": "1618", "desc": "1545"}, {"messageId": "1540", "fix": "1619", "desc": "1542"}, {"messageId": "1543", "fix": "1620", "desc": "1545"}, {"desc": "1586", "fix": "1621"}, {"desc": "1588", "fix": "1622"}, {"desc": "1590", "fix": "1623"}, {"desc": "1603", "fix": "1624"}, {"desc": "1625", "fix": "1626"}, {"desc": "1568", "fix": "1627"}, {"desc": "1628", "fix": "1629"}, {"desc": "1630", "fix": "1631"}, {"desc": "1632", "fix": "1633"}, {"desc": "1634", "fix": "1635"}, {"desc": "1636", "fix": "1637"}, {"desc": "1638", "fix": "1639"}, {"desc": "1640", "fix": "1641"}, {"desc": "1642", "fix": "1643"}, {"desc": "1644", "fix": "1645"}, {"desc": "1646", "fix": "1647"}, {"desc": "1648", "fix": "1649"}, {"desc": "1650", "fix": "1651"}, {"desc": "1652", "fix": "1653"}, {"desc": "1654", "fix": "1655"}, "Update the dependencies array to be: [handleLogout]", {"range": "1656", "text": "1657"}, "Update the dependencies array to be: [isAuthenticated, token, currentUser, socket]", {"range": "1658", "text": "1659"}, "Update the dependencies array to be: [socket, isConnected, currentUser, fetchUnreadCount]", {"range": "1660", "text": "1661"}, "Update the dependencies array to be: [appliedFilters, page, searchFilters.priceRange, t]", {"range": "1662", "text": "1663"}, "removeEscape", {"range": "1664", "text": "1665"}, "Remove the `\\`. This maintains the current functionality.", "escape<PERSON><PERSON><PERSON><PERSON>", {"range": "1666", "text": "1667"}, "Replace the `\\` with `\\\\` to include the actual backslash character.", {"range": "1668", "text": "1665"}, {"range": "1669", "text": "1667"}, {"range": "1670", "text": "1665"}, {"range": "1671", "text": "1667"}, "Update the dependencies array to be: [fetchStudents, page, rowsPerPage, searchQuery, t]", {"range": "1672", "text": "1673"}, "Update the dependencies array to be: [fetchTeachers, page, rowsPerPage, searchQuery, t]", {"range": "1674", "text": "1675"}, {"range": "1676", "text": "1665"}, {"range": "1677", "text": "1667"}, {"range": "1678", "text": "1665"}, {"range": "1679", "text": "1667"}, {"range": "1680", "text": "1665"}, {"range": "1681", "text": "1667"}, "Update the dependencies array to be: [fetchLanguages]", {"range": "1682", "text": "1683"}, "Update the dependencies array to be: [fetchCategories]", {"range": "1684", "text": "1685"}, "Update the dependencies array to be: [fetchUpdates, page, rowsPerPage, statusFilter]", {"range": "1686", "text": "1687"}, "Update the dependencies array to be: [fetchSessions, filters]", {"range": "1688", "text": "1689"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", {"range": "1690", "text": "1691"}, "Update the dependencies array to be: [token, tabValue, page, rowsPerPage, fetchMessages]", {"range": "1692", "text": "1693"}, "Update the dependencies array to be: [fetchEarnings, page, rowsPerPage]", {"range": "1694", "text": "1695"}, "Update the dependencies array to be: [fetchWithdrawals, page, rowsPerPage, statusFilter]", {"range": "1696", "text": "1697"}, "Update the dependencies array to be: [t, currentUser, fetchProfileData]", {"range": "1698", "text": "1699"}, "Update the dependencies array to be: [currentUser, fetchProfileData]", {"range": "1700", "text": "1701"}, {"range": "1702", "text": "1701"}, {"range": "1703", "text": "1701"}, "Update the dependencies array to be: [token, t, fetchAvailableHours, fetchWeeklyBreaks]", {"range": "1704", "text": "1705"}, "Update the dependencies array to be: [currentWeekStart, fetchWeeklyBreaks, token]", {"range": "1706", "text": "1707"}, "Update the dependencies array to be: [socket, selectedChat, t]", {"range": "1708", "text": "1709"}, "Update the dependencies array to be: [socket, selectedChat, decreaseUnreadCount]", {"range": "1710", "text": "1711"}, "Update the dependencies array to be: [decreaseUnreadCount, socket]", {"range": "1712", "text": "1713"}, "Update the dependencies array to be: [socket, isConnected, currentUser, t]", {"range": "1714", "text": "1715"}, {"range": "1716", "text": "1665"}, {"range": "1717", "text": "1667"}, {"range": "1718", "text": "1665"}, {"range": "1719", "text": "1667"}, {"range": "1720", "text": "1665"}, {"range": "1721", "text": "1667"}, {"range": "1722", "text": "1691"}, "Update the dependencies array to be: [currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", {"range": "1723", "text": "1724"}, "Update the dependencies array to be: [token, page, rowsPerPage, fetchMessages]", {"range": "1725", "text": "1726"}, "Update the dependencies array to be: [page, currentUser, token, appliedFilters, fetchTeachers]", {"range": "1727", "text": "1728"}, {"range": "1729", "text": "1665"}, {"range": "1730", "text": "1667"}, {"range": "1731", "text": "1665"}, {"range": "1732", "text": "1667"}, {"range": "1733", "text": "1665"}, {"range": "1734", "text": "1667"}, "Update the dependencies array to be: [socket, chatId, currentUser]", {"range": "1735", "text": "1736"}, {"range": "1737", "text": "1665"}, {"range": "1738", "text": "1667"}, {"range": "1739", "text": "1665"}, {"range": "1740", "text": "1667"}, {"range": "1741", "text": "1665"}, {"range": "1742", "text": "1667"}, {"range": "1743", "text": "1709"}, {"range": "1744", "text": "1711"}, {"range": "1745", "text": "1713"}, {"range": "1746", "text": "1726"}, "Update the dependencies array to be: [currentUser, fetchMeetings]", {"range": "1747", "text": "1748"}, {"range": "1749", "text": "1691"}, "Update the dependencies array to be: [success, editingReviewId, teachers]", {"range": "1750", "text": "1751"}, "Update the dependencies array to be: [currentUser, fetchPending, location.pathname]", {"range": "1752", "text": "1753"}, "Update the dependencies array to be: [t, exemptPages, isExemptPage]", {"range": "1754", "text": "1755"}, "Update the dependencies array to be: []", {"range": "1756", "text": "1757"}, "Update the dependencies array to be: [getCameraDevices, isCameraPermissionAllowed]", {"range": "1758", "text": "1759"}, "Update the dependencies array to be: [getAudioDevices, isMicrophonePermissionAllowed]", {"range": "1760", "text": "1761"}, "Update the dependencies array to be: [checkMediaPermission]", {"range": "1762", "text": "1763"}, "Update the dependencies array to be: [onDeviceChanged]", {"range": "1764", "text": "1765"}, "Update the dependencies array to be: [getAudioDevices]", {"range": "1766", "text": "1767"}, "Update the dependencies array to be: [waitingMessages]", {"range": "1768", "text": "1769"}, "Update the dependencies array to be: [leave, meetingData, onClose, participantTz, setIsMeetingLeft]", {"range": "1770", "text": "1771"}, "Update the dependencies array to be: [webcamStream, micStream, screenShareStream, updateStats]", {"range": "1772", "text": "1773"}, "Update the dependencies array to be: [didDeviceChange, setDidDeviceChange]", {"range": "1774", "text": "1775"}, "Update the dependencies array to be: [teacherId, studentId, meetingId]", {"range": "1776", "text": "1777"}, [1993, 1995], "[handleLogout]", [3199, 3236], "[isAuthenticated, token, currentUser, socket]", [3029, 3063], "[socket, isConnected, currentUser, fetchUnreadCount]", [9275, 9323], "[appliedFilters, page, searchFilters.priceRange, t]", [2348, 2349], "", [2348, 2348], "\\", [2357, 2358], [2357, 2357], [2359, 2360], [2359, 2359], [2116, 2151], "[fetchStudents, page, rowsPerPage, searchQuery, t]", [2095, 2130], "[fetchTeachers, page, rowsPerPage, searchQuery, t]", [5698, 5699], [5698, 5698], [5707, 5708], [5707, 5707], [5709, 5710], [5709, 5709], [1057, 1059], "[fetchLanguages]", [1069, 1071], "[fetchCategories]", [2347, 2380], "[fetchUpdates, page, rowsPerPage, statusFilter]", [2831, 2840], "[fetchSessions, filters]", [1299, 1338], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchTransactions]", [2708, 2744], "[token, tabValue, page, rowsPerPage, fetchMessages]", [1259, 1278], "[fetchEarnings, page, rowsPerPage]", [1628, 1661], "[fetchWithdrawals, page, rowsPerPage, statusFilter]", [3445, 3461], "[t, currentUser, fetchProfileData]", [4176, 4189], "[currentUser, fetchProfileData]", [4567, 4580], [5070, 5083], [5750, 5760], "[token, t, fetchAvailableHours, fetchWeeklyBreaks]", [5882, 5907], "[currentWeekStart, fetchWeeklyBreaks, token]", [6755, 6777], "[socket, selectedChat, t]", [8233, 8255], "[socket, selectedChat, decreaseUnreadCount]", [9760, 9768], "[decreaseUnreadCount, socket]", [11815, 11862], "[socket, isConnected, currentUser, t]", [1819, 1820], [1819, 1819], [1828, 1829], [1828, 1828], [1830, 1831], [1830, 1830], [1366, 1405], [1858, 1897], "[currentUser, token, page, rowsPerPage, fetchBalance, fetchWithdrawals, fetchSettings]", [2027, 2053], "[token, page, rowsPerPage, fetchMessages]", [8622, 8664], "[page, currentUser, token, appliedFilters, fetchTeachers]", [12227, 12228], [12227, 12227], [12236, 12237], [12236, 12236], [12238, 12239], [12238, 12238], [9071, 9104], "[socket, chatId, currentUser]", [14109, 14110], [14109, 14109], [14118, 14119], [14118, 14118], [14120, 14121], [14120, 14120], [7027, 7049], [8505, 8527], [10032, 10040], [2113, 2139], [1892, 1905], "[current<PERSON><PERSON>, fetchMeetings]", [2364, 2403], [20708, 20734], "[success, editing<PERSON><PERSON><PERSON>wId, teachers]", [3072, 3104], "[currentUser, fetchPending, location.pathname]", [1680, 1696], "[t, exemptPages, isExemptPage]", [1678, 1703], "[]", [4052, 4079], "[getCameraDevices, isCameraPermissionAllowed]", [4135, 4166], "[getAudioDevices, isMicrophonePermissionAllowed]", [4250, 4252], "[checkMediaPermission]", [9249, 9251], "[onDevice<PERSON>hanged]", [12542, 12544], "[getAudioDevices]", [845, 847], "[waitingMessages]", [6972, 7000], "[leave, meeting<PERSON><PERSON>, onClose, participantTz, setIsMeetingLeft]", [6514, 6558], "[webcamStream, micStream, screenShareStream, updateStats]", [1859, 1876], "[didD<PERSON><PERSON><PERSON><PERSON><PERSON>, setDidDeviceChange]", [1198, 1220], "[teacherId, studentId, meetingId]"]