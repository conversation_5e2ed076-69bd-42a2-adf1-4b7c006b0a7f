import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Fade,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  alpha,
  Tabs,
  Tab,
  Stack,
} from '@mui/material';
import {
  Info as InfoIcon,
  Email as EmailIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Payment as PaymentIcon,
  Security as SecurityIcon,
  CurrencyExchange as CurrencyExchangeIcon,
} from '@mui/icons-material';
import Layout from '../components/Layout';

const PlatformPolicy = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const location = useLocation();
  const isRtl = i18n.language === 'ar';
  const [activeTab, setActiveTab] = useState(0);

  // Handle hash navigation
  useEffect(() => {
    const hash = location.hash.replace('#', '');
    switch (hash) {
      case 'privacy':
        setActiveTab(0);
        break;
      case 'terms':
        setActiveTab(1);
        break;
      case 'refund':
        setActiveTab(2);
        break;
      case 'payment':
        setActiveTab(3);
        break;
      case 'booking':
        setActiveTab(4);
        break;
      default:
        setActiveTab(0);
    }
  }, [location.hash]);

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  const SectionCard = ({ children, bg }) => (
    <Card
      elevation={2}
      sx={{
        mb: 4,
        borderRadius: 3,
        ...(isRtl
          ? { borderRight: `6px solid ${theme.palette.primary.main}` }
          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),
        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),
      }}
    >
      <CardContent>{children}</CardContent>
    </Card>
  );

  /**
   * Safely fetch a translation; returns empty string if key is missing.
   */
  const safeT = (key) => {
    const val = t(key);
    return val && !val.includes(key) ? val : '';
  };

  // Privacy Policy Section
  const renderPrivacyPolicy = () => {
    const renderStandardSection = (num) => {
      const subtitle = safeT(`privacy.section${num}.subtitle`);
      const content = safeT(`privacy.section${num}.content`);

      // collect bullet items (item1-item10)
      const bullets = [];
      for (let i = 1; i <= 10; i++) {
        const item = safeT(`privacy.section${num}.item${i}`);
        if (item) bullets.push(item);
      }

      return (
        <SectionCard key={num}>
          <Typography
            variant="h5"
            fontWeight={600}
            color={theme.palette.primary.main}
            mb={2}
            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
          >
            {t(`privacy.section${num}.title`)}
          </Typography>
          {subtitle && <Typography mb={1}>{subtitle}</Typography>}
          {content && <Typography mb={1}>{content}</Typography>}
          {bullets.length > 0 && (
            <List>
              {bullets.map((item, idx) => (
                <ListItem
                  key={idx}
                  sx={{
                    pl: isRtl ? 0 : 2,
                    pr: isRtl ? 2 : 0,
                    flexDirection: 'row',
                    textAlign: isRtl ? 'right' : 'left',
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: 'auto',
                      mx: isRtl ? 0 : 1,
                      ml: isRtl ? 1 : 0,
                    }}
                  >
                    <InfoIcon color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary={item}
                    sx={{ textAlign: isRtl ? 'right' : 'left' }}
                  />
                </ListItem>
              ))}
            </List>
          )}
        </SectionCard>
      );
    };

    return (
      <Box>
        <Typography
          variant="h4"
          fontWeight={700}
          mb={2}
          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
        >
          {t('privacy.title')}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" mb={3}>
          {t('privacy.intro')}
        </Typography>
        <Divider sx={{ mb: 3 }} />
        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => renderStandardSection(num))}

        {/* Section 10 – Contact */}
        <SectionCard>
          <Typography
            variant="h5"
            fontWeight={600}
            color={theme.palette.primary.main}
            mb={2}
            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
          >
            {t('privacy.section10.title')}
          </Typography>
          <Typography mb={2}>{t('privacy.section10.content')}</Typography>
          <Box sx={{
            p: 2,
            bgcolor: alpha(theme.palette.info.main, 0.1),
            borderRadius: 2,
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <EmailIcon color="info" />
            <Typography fontWeight="bold" color={theme.palette.info.main}>
              {t('privacy.section10.email')}
            </Typography>
          </Box>
        </SectionCard>
      </Box>
    );
  };

  // Terms and Conditions Section
  const renderTermsAndConditions = () => {
    return (
      <Box>
        <Typography
          variant="h4"
          fontWeight={700}
          mb={2}
          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
        >
          {t('termsConditions.title')}
        </Typography>
        <Divider sx={{ mb: 3 }} />
        <Stack spacing={4}>
          {t('termsConditions.content')
            .split(/\n\s*\n/)
            .filter((section) => section.trim() !== '')
            .map((section, idx) => {
              const lines = section.split('\n');
              const title = lines[0];
              const body = lines.slice(1).join('\n');
              return (
                <Card
                  key={idx}
                  elevation={3}
                  sx={{
                    borderRadius: 3,
                    ...(isRtl
                      ? { borderRight: `6px solid ${theme.palette.primary.main}` }
                      : { borderLeft: `6px solid ${theme.palette.primary.main}` }),
                    backgroundColor:
                      idx % 2 === 0
                        ? alpha(theme.palette.primary.main, 0.02)
                        : alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02),
                  }}
                >
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{
                        mb: 1.5,
                        fontWeight: 700,
                        fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',
                        color: theme.palette.primary.main,
                      }}
                    >
                      {title}
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{
                        whiteSpace: 'pre-line',
                        lineHeight: 2,
                        fontSize: '1.05rem',
                        fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',
                        color: theme.palette.text.primary,
                      }}
                    >
                      {body}
                    </Typography>
                  </CardContent>
                </Card>
              );
            })}
        </Stack>
      </Box>
    );
  };

  // Refund Policy Section
  const renderRefundPolicy = () => {
    const refund = t('policies.refund', { returnObjects: true });

    return (
      <Box>
        <Typography variant="h4" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
          {refund.title}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        {/* Section 1 */}
        <SectionCard>
          <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
            {refund.section1.title}
          </Typography>
          <Typography mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
            {refund.section1.description}
          </Typography>
          <List>
            {refund.section1.items.map((item, idx) => (
              <ListItem key={idx} sx={{
                pl: isRtl ? 0 : 2,
                pr: isRtl ? 2 : 0,
                flexDirection: 'row',
                textAlign: isRtl ? 'right' : 'left',
              }}>
                <ListItemIcon sx={{
                  minWidth: 'auto',
                  mx: isRtl ? 0 : 1,
                  ml: isRtl ? 1 : 0,
                }}>
                  <CheckCircleIcon color="success" />
                </ListItemIcon>
                <ListItemText
                  primary={item}
                  sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
                />
              </ListItem>
            ))}
          </List>
        </SectionCard>

        {/* Section 2 */}
        <SectionCard>
          <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
            {refund.section2.title}
          </Typography>
          <Typography mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
            {refund.section2.description}
          </Typography>
          <List>
            {refund.section2.items.map((item, idx) => (
              <ListItem key={idx} sx={{
                pl: isRtl ? 0 : 2,
                pr: isRtl ? 2 : 0,
                flexDirection: 'row',
                textAlign: isRtl ? 'right' : 'left',
              }}>
                <ListItemIcon sx={{
                  minWidth: 'auto',
                  mx: isRtl ? 0 : 1,
                  ml: isRtl ? 1 : 0,
                }}>
                  <CancelIcon color="error" />
                </ListItemIcon>
                <ListItemText
                  primary={item}
                  sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
                />
              </ListItem>
            ))}
          </List>
        </SectionCard>

        {/* Section 3 */}
        <SectionCard>
          <Typography variant="h5" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
            {refund.section3.title}
          </Typography>
          <Typography sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
            {refund.section3.description}
          </Typography>
        </SectionCard>

        {/* Contact Section */}
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" fontWeight={600} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
            {refund.contact.title}
          </Typography>
          <Box sx={{
            p: 2,
            bgcolor: alpha(theme.palette.info.main, 0.1),
            borderRadius: 2,
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <EmailIcon color="info" />
            <Typography fontWeight="bold" color={theme.palette.info.main}>
              {refund.contact.email}
            </Typography>
          </Box>
        </Box>
      </Box>
    );
  };

  // Booking Payment Policy Section
  const renderBookingPaymentPolicy = () => {
    const policy = t('policies.bookingPayment', { returnObjects: true });

    const renderSection = (section) => (
      <SectionCard>
        <Typography
          variant="h5"
          fontWeight={600}
          color={theme.palette.primary.main}
          mb={2}
          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
        >
          {section.title}
        </Typography>
        {section.points && (
          <List>
            {section.points.map((item, idx) => (
              <ListItem key={idx} sx={{
                pl: isRtl ? 0 : 2,
                pr: isRtl ? 2 : 0,
                flexDirection: 'row',
                textAlign: isRtl ? 'right' : 'left',
              }}>
                <ListItemIcon sx={{
                  minWidth: 'auto',
                  mx: isRtl ? 0 : 1,
                  ml: isRtl ? 1 : 0,
                }}>
                  <CheckCircleIcon color="success" />
                </ListItemIcon>
                <ListItemText primary={item} />
              </ListItem>
            ))}
          </List>
        )}
        {section.description && <Typography>{section.description}</Typography>}
      </SectionCard>
    );

    return (
      <Box>
        <Typography variant="h4" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
          {policy.title}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" mb={3}>
          {policy.subtitle}
        </Typography>
        <Divider sx={{ mb: 3 }} />
        {renderSection(policy.section1)}
        {renderSection(policy.section2)}
        {renderSection(policy.section3)}
        {renderSection(policy.section4)}
        {renderSection(policy.section5)}
        {renderSection(policy.section6)}
        {renderSection(policy.section7)}
        {renderSection(policy.section8)}

        {/* Contact Section */}
        <Box sx={{ mt: 3 }}>
          <Typography variant="h6" fontWeight={600} mb={1}>{policy.section8.title || policy.section8.description}</Typography>
          <Typography mb={1}>{policy.section8.description}</Typography>
          <Box sx={{
            p: 2,
            bgcolor: alpha(theme.palette.info.main, 0.1),
            borderRadius: 2,
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <EmailIcon color="info" />
            <Typography fontWeight="bold" color={theme.palette.info.main}>
              {policy.section8.email}
            </Typography>
          </Box>
        </Box>
      </Box>
    );
  };

  // Booking Cancellation Policy Section
  const renderBookingCancellationPolicy = () => {
    const policy = t('policies.bookingCancellation', { returnObjects: true });

    const renderSubSection = (subSection) => (
      <Box sx={{ mb: 3 }}>
        <Typography
          variant="h6"
          sx={{
            fontWeight: 600,
            color: theme.palette.primary.main,
            mb: 1,
            fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',
          }}
        >
          {subSection.title}
        </Typography>
        {subSection.points && (
          <List>
            {subSection.points.map((item, idx) => (
              <ListItem key={idx} sx={{
                pl: isRtl ? 0 : 2,
                pr: isRtl ? 2 : 0,
                flexDirection: 'row',
                textAlign: isRtl ? 'right' : 'left',
              }}>
                <ListItemIcon sx={{
                minWidth: 'auto',
                mx: isRtl ? 0 : 1,
                ml: isRtl ? 1 : 0,
              }}><CheckCircleIcon color="success" /></ListItemIcon>
                <ListItemText primary={item} />
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    );

    return (
      <Box>
        <Typography variant="h4" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>
          {policy.title}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary" mb={3}>
          {policy.subtitle}
        </Typography>
        <Divider sx={{ mb: 3 }} />

        {/* Student Policy */}
        <SectionCard>
          <Typography
            variant="h5"
            fontWeight={600}
            color={theme.palette.primary.main}
            mb={2}
            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
          >
            {policy.studentPolicy.title}
          </Typography>
          {renderSubSection(policy.studentPolicy.booking)}
          {renderSubSection(policy.studentPolicy.cancellation)}
          {renderSubSection(policy.studentPolicy.rescheduling)}
          {renderSubSection(policy.studentPolicy.lateArrival)}
        </SectionCard>

        {/* Tutor Policy */}
        <SectionCard bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}>
          <Typography
            variant="h5"
            fontWeight={600}
            color={theme.palette.primary.main}
            mb={2}
            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
          >
            {policy.tutorPolicy.title}
          </Typography>
          {renderSubSection(policy.tutorPolicy.availability)}
          {renderSubSection(policy.tutorPolicy.cancellation)}
          {renderSubSection(policy.tutorPolicy.rescheduling)}
          {renderSubSection(policy.tutorPolicy.lateArrival)}
        </SectionCard>

        {/* General Notes */}
        <SectionCard>
          <Typography
            variant="h5"
            fontWeight={600}
            color={theme.palette.primary.main}
            mb={2}
            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}
          >
            {policy.generalNotes.title}
          </Typography>
          <List>
            {policy.generalNotes.points.map((item, idx) => (
              <ListItem key={idx} sx={{
              pl: isRtl ? 0 : 2,
              pr: isRtl ? 2 : 0,
              flexDirection: 'row',
              textAlign: isRtl ? 'right' : 'left',
            }}>
                <ListItemIcon sx={{
              minWidth: 'auto',
              mx: isRtl ? 0 : 1,
              ml: isRtl ? 1 : 0,
            }}><InfoIcon color="info" /></ListItemIcon>
                <ListItemText primary={item} />
              </ListItem>
            ))}
          </List>
        </SectionCard>

        {/* Summary Note */}
        <Box sx={{
          mt: 3,
          p: 2,
          bgcolor: alpha(theme.palette.info.main, 0.1),
          borderRadius: 2,
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          <InfoIcon color="info" />
          <Typography color={theme.palette.info.main}>
            {policy.summary?.importantNote || (isRtl ? 'يرجى مراجعة جميع السياسات بعناية قبل الحجز' : 'Please review all policies carefully before booking')}
          </Typography>
        </Box>
      </Box>
    );
  };

  const tabs = [
    { label: isRtl ? 'سياسة الخصوصية' : 'Privacy Policy', icon: <SecurityIcon /> },
    { label: isRtl ? 'الشروط والأحكام' : 'Terms & Conditions', icon: <InfoIcon /> },
    { label: isRtl ? 'سياسة الاسترداد' : 'Refund Policy', icon: <CurrencyExchangeIcon /> },
    { label: isRtl ? 'سياسة الحجز والدفع' : 'Booking & Payment Policy', icon: <PaymentIcon /> },
    { label: isRtl ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation Policy', icon: <CancelIcon /> },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return renderPrivacyPolicy();
      case 1:
        return renderTermsAndConditions();
      case 2:
        return renderRefundPolicy();
      case 3:
        return renderBookingPaymentPolicy();
      case 4:
        return renderBookingCancellationPolicy();
      default:
        return renderPrivacyPolicy();
    }
  };

  return (
    <Layout>
      <Box
        sx={{
          minHeight: '100vh',
          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,
          pt: 4,
          pb: 8,
        }}
      >
        <Container maxWidth="lg">
          <Fade in timeout={800}>
            <Box sx={{ p: { xs: 2, md: 4 }, direction: isRtl ? 'rtl' : 'ltr' }}>
              {/* Main Title */}
              <Typography
                variant="h3"
                align="center"
                sx={{
                  mb: 4,
                  fontWeight: 800,
                  fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',
                  color: theme.palette.primary.main,
                }}
              >
                {isRtl ? 'سياسات المنصة' : 'Platform Policies'}
              </Typography>

              {/* Tabs */}
              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>
                <Tabs
                  value={activeTab}
                  onChange={handleTabChange}
                  variant="scrollable"
                  scrollButtons="auto"
                  sx={{
                    '& .MuiTab-root': {
                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',
                      fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },
                      minWidth: { xs: 120, sm: 140, md: 160 },
                    },
                  }}
                >
                  {tabs.map((tab, index) => (
                    <Tab
                      key={index}
                      label={tab.label}
                      icon={tab.icon}
                      iconPosition="start"
                      sx={{
                        '& .MuiTab-iconWrapper': {
                          mr: isRtl ? 0 : 1,
                          ml: isRtl ? 1 : 0,
                        },
                      }}
                    />
                  ))}
                </Tabs>
              </Box>

              {/* Tab Content */}
              <Box sx={{ mt: 3 }}>
                {renderTabContent()}
              </Box>
            </Box>
          </Fade>
        </Container>
      </Box>
    </Layout>
  );
};

export default PlatformPolicy;
