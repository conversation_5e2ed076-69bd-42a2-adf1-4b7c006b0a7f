{"ast": null, "code": "import{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useLocation}from'react-router-dom';import{Container,Typography,Box,Fade,Card,CardContent,Divider,List,ListItem,ListItemText,ListItemIcon,useTheme,alpha,Tabs,Tab,Stack}from'@mui/material';import{Info as InfoIcon,Email as EmailIcon,CheckCircle as CheckCircleIcon,Cancel as CancelIcon,Payment as PaymentIcon,Security as SecurityIcon,CurrencyExchange as CurrencyExchangeIcon}from'@mui/icons-material';import Layout from'../components/Layout';import termsConditions from'../i18n/translations/termsConditions';import i18n from'../i18n/i18n';// Merge the Terms & Conditions keys into existing translation bundles\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";i18n.addResourceBundle('en','translation',termsConditions.en,true,true);i18n.addResourceBundle('ar','translation',termsConditions.ar,true,true);const PlatformPolicy=()=>{const{t,i18n}=useTranslation();const theme=useTheme();const location=useLocation();const isRtl=i18n.language==='ar';const[activeTab,setActiveTab]=useState(0);// Handle hash navigation\nuseEffect(()=>{const hash=location.hash.replace('#','');switch(hash){case'privacy':setActiveTab(0);break;case'terms':setActiveTab(1);break;case'refund':setActiveTab(2);break;case'payment':setActiveTab(3);break;case'booking':setActiveTab(4);break;default:setActiveTab(0);}},[location.hash]);const handleTabChange=(_,newValue)=>{setActiveTab(newValue);};const SectionCard=_ref=>{let{children,bg}=_ref;return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{mb:4,borderRadius:3,...(isRtl?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:bg||alpha(theme.palette.primary.main,0.02)},children:/*#__PURE__*/_jsx(CardContent,{children:children})});};/**\n   * Safely fetch a translation; returns empty string if key is missing.\n   */const safeT=key=>{const val=t(key);return val&&!val.includes(key)?val:'';};// Privacy Policy Section\nconst renderPrivacyPolicy=()=>{const renderStandardSection=num=>{const subtitle=safeT(`privacy.section${num}.subtitle`);const content=safeT(`privacy.section${num}.content`);// collect bullet items (item1-item10)\nconst bullets=[];for(let i=1;i<=10;i++){const item=safeT(`privacy.section${num}.item${i}`);if(item)bullets.push(item);}return/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t(`privacy.section${num}.title`)}),subtitle&&/*#__PURE__*/_jsx(Typography,{mb:1,children:subtitle}),content&&/*#__PURE__*/_jsx(Typography,{mb:1,children:content}),bullets.length>0&&/*#__PURE__*/_jsx(List,{children:bullets.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{textAlign:isRtl?'right':'left'}})]},idx))})]},num);};return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacy.title')}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:t('privacy.intro')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),[1,2,3,4,5,6,7,8,9].map(num=>renderStandardSection(num)),/*#__PURE__*/_jsxs(SectionCard,{bg:alpha(theme.palette.info.main,0.05),children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(EmailIcon,{sx:{mr:isRtl?0:1,ml:isRtl?1:0,color:theme.palette.info.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.info.main,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacy.contact.title')})]}),/*#__PURE__*/_jsx(Typography,{mb:1,children:t('privacy.contact.content')}),/*#__PURE__*/_jsx(Typography,{fontWeight:\"bold\",color:theme.palette.primary.main,children:t('privacy.contact.email')})]})]});};// Terms and Conditions Section\nconst renderTermsAndConditions=()=>{return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('termsConditions.title')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsx(Stack,{spacing:4,children:t('termsConditions.content').split(/\\n\\s*\\n/).filter(section=>section.trim()!=='').map((section,idx)=>{const lines=section.split('\\n');const title=lines[0];const body=lines.slice(1).join('\\n');return/*#__PURE__*/_jsx(Card,{elevation:3,sx:{borderRadius:3,...(isRtl?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:idx%2===0?alpha(theme.palette.primary.main,0.02):alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{mb:1.5,fontWeight:700,fontFamily:isRtl?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:title}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{whiteSpace:'pre-line',lineHeight:2,fontSize:'1.05rem',fontFamily:isRtl?'Tajawal, sans-serif':'inherit',color:theme.palette.text.primary},children:body})]})},idx);})})]});};// Refund Policy Section\nconst renderRefundPolicy=()=>{return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:isRtl?'سياسة الاسترداد':'Refund Policy'}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.title')}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.cancellation.title')}),/*#__PURE__*/_jsx(List,{children:t('privacyPolicy.studentPolicy.cancellation.points',{returnObjects:true}).map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'}})]},idx))})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.rescheduling.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.rescheduling.description')})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.lateArrival.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.lateArrival.description')})]})]}),/*#__PURE__*/_jsxs(SectionCard,{bg:alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02),children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.title')}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.cancellation.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.cancellation.description')})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.attendance.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.attendance.description')})]})]}),/*#__PURE__*/_jsxs(SectionCard,{bg:alpha(theme.palette.info.main,0.05),children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.info.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.generalNotes.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.generalNotes.timeZone')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.generalNotes.platformFees')})]})]});};// Booking Payment Policy Section\nconst renderBookingPaymentPolicy=()=>{return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:isRtl?'سياسة الحجز والدفع':'Booking & Payment Policy'}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:t('privacyPolicy.intro')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.bookingPolicy.title')}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.booking.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.booking.description')})]})]}),/*#__PURE__*/_jsxs(SectionCard,{bg:alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02),children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.commissionPolicy.title')}),/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.commissionPolicy.description')}),/*#__PURE__*/_jsxs(List,{children:[/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(PaymentIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('privacyPolicy.commissionPolicy.rates.3'),sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'}})]}),/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(PaymentIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('privacyPolicy.commissionPolicy.rates.4'),sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'}})]}),/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(PaymentIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('privacyPolicy.commissionPolicy.rates.5'),sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'}})]}),/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(PaymentIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('privacyPolicy.commissionPolicy.rates.6'),sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'}})]}),/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(PaymentIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('privacyPolicy.commissionPolicy.rates.7'),sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'}})]})]}),/*#__PURE__*/_jsx(Typography,{mt:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif',fontStyle:'italic'},children:t('privacyPolicy.commissionPolicy.explanation')})]})]});};// Booking Cancellation Policy Section\nconst renderBookingCancellationPolicy=()=>{return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:isRtl?'سياسة الحجز والإلغاء':'Booking & Cancellation Policy'}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:t('privacyPolicy.intro')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.title')}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.booking.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.booking.description')})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.cancellation.title')}),/*#__PURE__*/_jsx(List,{children:t('privacyPolicy.studentPolicy.cancellation.points',{returnObjects:true}).map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item})]},idx))})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.rescheduling.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.rescheduling.description')})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.lateArrival.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.studentPolicy.lateArrival.description')})]})]}),/*#__PURE__*/_jsxs(SectionCard,{bg:alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02),children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.title')}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.cancellation.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.cancellation.description')})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.rescheduling.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.rescheduling.description')})]}),/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.attendance.title')}),/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.tutorPolicy.attendance.description')})]})]}),/*#__PURE__*/_jsxs(SectionCard,{bg:alpha(theme.palette.info.main,0.05),children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.info.main,mb:2,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacyPolicy.generalNotes.title')}),/*#__PURE__*/_jsxs(List,{children:[/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"info\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('privacyPolicy.generalNotes.timeZone')})]}),/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRtl?0:2,pr:isRtl?2:0,flexDirection:'row',textAlign:isRtl?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRtl?0:1,ml:isRtl?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"info\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:t('privacyPolicy.generalNotes.platformFees')})]})]})]})]});};const tabs=[{label:isRtl?'سياسة الخصوصية':'Privacy Policy',icon:/*#__PURE__*/_jsx(SecurityIcon,{})},{label:isRtl?'الشروط والأحكام':'Terms & Conditions',icon:/*#__PURE__*/_jsx(InfoIcon,{})},{label:isRtl?'سياسة الاسترداد':'Refund Policy',icon:/*#__PURE__*/_jsx(CurrencyExchangeIcon,{})},{label:isRtl?'سياسة الحجز والدفع':'Booking & Payment Policy',icon:/*#__PURE__*/_jsx(PaymentIcon,{})},{label:isRtl?'سياسة الحجز والإلغاء':'Booking & Cancellation Policy',icon:/*#__PURE__*/_jsx(CancelIcon,{})}];const renderTabContent=()=>{switch(activeTab){case 0:return renderPrivacyPolicy();case 1:return renderTermsAndConditions();case 2:return renderRefundPolicy();case 3:return renderBookingPaymentPolicy();case 4:return renderBookingCancellationPolicy();default:return renderPrivacyPolicy();}};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,pt:4,pb:8},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:800,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,md:4},direction:isRtl?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",align:\"center\",sx:{mb:4,fontWeight:800,fontFamily:isRtl?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:isRtl?'سياسات المنصة':'Platform Policies'}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider',mb:4},children:/*#__PURE__*/_jsx(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{'& .MuiTab-root':{fontFamily:isRtl?'Tajawal, sans-serif':'inherit',fontSize:{xs:'0.8rem',sm:'0.9rem',md:'1rem'},minWidth:{xs:120,sm:140,md:160}}},children:tabs.map((tab,index)=>/*#__PURE__*/_jsx(Tab,{label:tab.label,icon:tab.icon,iconPosition:\"start\",sx:{'& .MuiTab-iconWrapper':{mr:isRtl?0:1,ml:isRtl?1:0}}},index))})}),/*#__PURE__*/_jsx(Box,{sx:{mt:3},children:renderTabContent()})]})})})})});};export default PlatformPolicy;", "map": {"version": 3, "names": ["useState", "useEffect", "useTranslation", "useLocation", "Container", "Typography", "Box", "Fade", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "useTheme", "alpha", "Tabs", "Tab", "<PERSON><PERSON>", "Info", "InfoIcon", "Email", "EmailIcon", "CheckCircle", "CheckCircleIcon", "Cancel", "CancelIcon", "Payment", "PaymentIcon", "Security", "SecurityIcon", "CurrencyExchange", "CurrencyExchangeIcon", "Layout", "termsConditions", "i18n", "jsx", "_jsx", "jsxs", "_jsxs", "addResourceBundle", "en", "ar", "PlatformPolicy", "t", "theme", "location", "isRtl", "language", "activeTab", "setActiveTab", "hash", "replace", "handleTabChange", "_", "newValue", "SectionCard", "_ref", "children", "bg", "elevation", "sx", "mb", "borderRadius", "borderRight", "palette", "primary", "main", "borderLeft", "backgroundColor", "safeT", "key", "val", "includes", "renderPrivacyPolicy", "renderStandardSection", "num", "subtitle", "content", "bullets", "i", "item", "push", "variant", "fontWeight", "color", "fontFamily", "length", "map", "idx", "pl", "pr", "flexDirection", "textAlign", "min<PERSON><PERSON><PERSON>", "mx", "ml", "info", "display", "alignItems", "mr", "renderTermsAndConditions", "spacing", "split", "filter", "section", "trim", "lines", "title", "body", "slice", "join", "secondary", "light", "whiteSpace", "lineHeight", "fontSize", "text", "renderRefundPolicy", "returnObjects", "renderBookingPaymentPolicy", "mt", "fontStyle", "renderBookingCancellationPolicy", "tabs", "label", "icon", "renderTabContent", "minHeight", "background", "pt", "pb", "max<PERSON><PERSON><PERSON>", "in", "timeout", "p", "xs", "md", "direction", "align", "borderBottom", "borderColor", "value", "onChange", "scrollButtons", "sm", "tab", "index", "iconPosition"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PlatformPolicy.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useLocation } from 'react-router-dom';\nimport {\n  Container,\n  Typography,\n  Box,\n  Fade,\n  Card,\n  CardContent,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  useTheme,\n  alpha,\n  Tabs,\n  Tab,\n  Stack,\n} from '@mui/material';\nimport {\n  Info as InfoIcon,\n  Email as EmailIcon,\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  Payment as PaymentIcon,\n  Security as SecurityIcon,\n  CurrencyExchange as CurrencyExchangeIcon,\n} from '@mui/icons-material';\nimport Layout from '../components/Layout';\nimport termsConditions from '../i18n/translations/termsConditions';\nimport i18n from '../i18n/i18n';\n\n// Merge the Terms & Conditions keys into existing translation bundles\ni18n.addResourceBundle('en', 'translation', termsConditions.en, true, true);\ni18n.addResourceBundle('ar', 'translation', termsConditions.ar, true, true);\n\nconst PlatformPolicy = () => {\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const location = useLocation();\n  const isRtl = i18n.language === 'ar';\n  const [activeTab, setActiveTab] = useState(0);\n\n  // Handle hash navigation\n  useEffect(() => {\n    const hash = location.hash.replace('#', '');\n    switch (hash) {\n      case 'privacy':\n        setActiveTab(0);\n        break;\n      case 'terms':\n        setActiveTab(1);\n        break;\n      case 'refund':\n        setActiveTab(2);\n        break;\n      case 'payment':\n        setActiveTab(3);\n        break;\n      case 'booking':\n        setActiveTab(4);\n        break;\n      default:\n        setActiveTab(0);\n    }\n  }, [location.hash]);\n\n  const handleTabChange = (_, newValue) => {\n    setActiveTab(newValue);\n  };\n\n  const SectionCard = ({ children, bg }) => (\n    <Card\n      elevation={2}\n      sx={{\n        mb: 4,\n        borderRadius: 3,\n        ...(isRtl\n          ? { borderRight: `6px solid ${theme.palette.primary.main}` }\n          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\n        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),\n      }}\n    >\n      <CardContent>{children}</CardContent>\n    </Card>\n  );\n\n  /**\n   * Safely fetch a translation; returns empty string if key is missing.\n   */\n  const safeT = (key) => {\n    const val = t(key);\n    return val && !val.includes(key) ? val : '';\n  };\n\n  // Privacy Policy Section\n  const renderPrivacyPolicy = () => {\n    const renderStandardSection = (num) => {\n      const subtitle = safeT(`privacy.section${num}.subtitle`);\n      const content = safeT(`privacy.section${num}.content`);\n\n      // collect bullet items (item1-item10)\n      const bullets = [];\n      for (let i = 1; i <= 10; i++) {\n        const item = safeT(`privacy.section${num}.item${i}`);\n        if (item) bullets.push(item);\n      }\n\n      return (\n        <SectionCard key={num}>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {t(`privacy.section${num}.title`)}\n          </Typography>\n          {subtitle && <Typography mb={1}>{subtitle}</Typography>}\n          {content && <Typography mb={1}>{content}</Typography>}\n          {bullets.length > 0 && (\n            <List>\n              {bullets.map((item, idx) => (\n                <ListItem\n                  key={idx}\n                  sx={{\n                    pl: isRtl ? 0 : 2,\n                    pr: isRtl ? 2 : 0,\n                    flexDirection: 'row',\n                    textAlign: isRtl ? 'right' : 'left',\n                  }}\n                >\n                  <ListItemIcon\n                    sx={{\n                      minWidth: 'auto',\n                      mx: isRtl ? 0 : 1,\n                      ml: isRtl ? 1 : 0,\n                    }}\n                  >\n                    <InfoIcon color=\"primary\" />\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item}\n                    sx={{ textAlign: isRtl ? 'right' : 'left' }}\n                  />\n                </ListItem>\n              ))}\n            </List>\n          )}\n        </SectionCard>\n      );\n    };\n\n    return (\n      <Box>\n        <Typography\n          variant=\"h4\"\n          fontWeight={700}\n          mb={2}\n          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n        >\n          {t('privacy.title')}\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\n          {t('privacy.intro')}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => renderStandardSection(num))}\n        \n        {/* Contact Section */}\n        <SectionCard bg={alpha(theme.palette.info.main, 0.05)}>\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            <EmailIcon sx={{ mr: isRtl ? 0 : 1, ml: isRtl ? 1 : 0, color: theme.palette.info.main }} />\n            <Typography\n              variant=\"h5\"\n              fontWeight={600}\n              color={theme.palette.info.main}\n              sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n            >\n              {t('privacy.contact.title')}\n            </Typography>\n          </Box>\n          <Typography mb={1}>{t('privacy.contact.content')}</Typography>\n          <Typography fontWeight=\"bold\" color={theme.palette.primary.main}>\n            {t('privacy.contact.email')}\n          </Typography>\n        </SectionCard>\n      </Box>\n    );\n  };\n\n  // Terms and Conditions Section\n  const renderTermsAndConditions = () => {\n    return (\n      <Box>\n        <Typography\n          variant=\"h4\"\n          fontWeight={700}\n          mb={2}\n          sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n        >\n          {t('termsConditions.title')}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n        <Stack spacing={4}>\n          {t('termsConditions.content')\n            .split(/\\n\\s*\\n/)\n            .filter((section) => section.trim() !== '')\n            .map((section, idx) => {\n              const lines = section.split('\\n');\n              const title = lines[0];\n              const body = lines.slice(1).join('\\n');\n              return (\n                <Card\n                  key={idx}\n                  elevation={3}\n                  sx={{\n                    borderRadius: 3,\n                    ...(isRtl\n                      ? { borderRight: `6px solid ${theme.palette.primary.main}` }\n                      : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\n                    backgroundColor:\n                      idx % 2 === 0\n                        ? alpha(theme.palette.primary.main, 0.02)\n                        : alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02),\n                  }}\n                >\n                  <CardContent>\n                    <Typography\n                      variant=\"h6\"\n                      sx={{\n                        mb: 1.5,\n                        fontWeight: 700,\n                        fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                        color: theme.palette.primary.main,\n                      }}\n                    >\n                      {title}\n                    </Typography>\n                    <Typography\n                      variant=\"body1\"\n                      sx={{\n                        whiteSpace: 'pre-line',\n                        lineHeight: 2,\n                        fontSize: '1.05rem',\n                        fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                        color: theme.palette.text.primary,\n                      }}\n                    >\n                      {body}\n                    </Typography>\n                  </CardContent>\n                </Card>\n              );\n            })}\n        </Stack>\n      </Box>\n    );\n  };\n\n  // Refund Policy Section\n  const renderRefundPolicy = () => {\n    return (\n      <Box>\n        <Typography variant=\"h4\" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n          {isRtl ? 'سياسة الاسترداد' : 'Refund Policy'}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Student Refund Policy */}\n        <SectionCard>\n          <Typography variant=\"h5\" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {t('privacyPolicy.studentPolicy.title')}\n          </Typography>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.cancellation.title')}\n            </Typography>\n            <List>\n              {t('privacyPolicy.studentPolicy.cancellation.points', { returnObjects: true }).map((item, idx) => (\n                <ListItem key={idx} sx={{\n                  pl: isRtl ? 0 : 2,\n                  pr: isRtl ? 2 : 0,\n                  flexDirection: 'row',\n                  textAlign: isRtl ? 'right' : 'left',\n                }}>\n                  <ListItemIcon sx={{\n                    minWidth: 'auto',\n                    mx: isRtl ? 0 : 1,\n                    ml: isRtl ? 1 : 0,\n                  }}>\n                    <CheckCircleIcon color=\"success\" />\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item}\n                    sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n                  />\n                </ListItem>\n              ))}\n            </List>\n          </Box>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.rescheduling.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.rescheduling.description')}\n            </Typography>\n          </Box>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.lateArrival.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.lateArrival.description')}\n            </Typography>\n          </Box>\n        </SectionCard>\n\n        {/* Teacher Policy */}\n        <SectionCard bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}>\n          <Typography variant=\"h5\" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {t('privacyPolicy.tutorPolicy.title')}\n          </Typography>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.cancellation.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.cancellation.description')}\n            </Typography>\n          </Box>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.attendance.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.attendance.description')}\n            </Typography>\n          </Box>\n        </SectionCard>\n\n        {/* General Notes */}\n        <SectionCard bg={alpha(theme.palette.info.main, 0.05)}>\n          <Typography variant=\"h5\" fontWeight={600} color={theme.palette.info.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {t('privacyPolicy.generalNotes.title')}\n          </Typography>\n          <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {t('privacyPolicy.generalNotes.timeZone')}\n          </Typography>\n          <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {t('privacyPolicy.generalNotes.platformFees')}\n          </Typography>\n        </SectionCard>\n      </Box>\n    );\n  };\n\n  // Booking Payment Policy Section\n  const renderBookingPaymentPolicy = () => {\n    return (\n      <Box>\n        <Typography variant=\"h4\" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n          {isRtl ? 'سياسة الحجز والدفع' : 'Booking & Payment Policy'}\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\n          {t('privacyPolicy.intro')}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Booking Policy */}\n        <SectionCard>\n          <Typography variant=\"h5\" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {t('privacyPolicy.bookingPolicy.title')}\n          </Typography>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.booking.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.booking.description')}\n            </Typography>\n          </Box>\n        </SectionCard>\n\n        {/* Commission Policy */}\n        <SectionCard bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}>\n          <Typography variant=\"h5\" fontWeight={600} color={theme.palette.primary.main} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {t('privacyPolicy.commissionPolicy.title')}\n          </Typography>\n          <Typography mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n            {t('privacyPolicy.commissionPolicy.description')}\n          </Typography>\n          <List>\n            <ListItem sx={{ pl: isRtl ? 0 : 2, pr: isRtl ? 2 : 0, flexDirection: 'row', textAlign: isRtl ? 'right' : 'left' }}>\n              <ListItemIcon sx={{ minWidth: 'auto', mx: isRtl ? 0 : 1, ml: isRtl ? 1 : 0 }}>\n                <PaymentIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText primary={t('privacyPolicy.commissionPolicy.rates.3')} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }} />\n            </ListItem>\n            <ListItem sx={{ pl: isRtl ? 0 : 2, pr: isRtl ? 2 : 0, flexDirection: 'row', textAlign: isRtl ? 'right' : 'left' }}>\n              <ListItemIcon sx={{ minWidth: 'auto', mx: isRtl ? 0 : 1, ml: isRtl ? 1 : 0 }}>\n                <PaymentIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText primary={t('privacyPolicy.commissionPolicy.rates.4')} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }} />\n            </ListItem>\n            <ListItem sx={{ pl: isRtl ? 0 : 2, pr: isRtl ? 2 : 0, flexDirection: 'row', textAlign: isRtl ? 'right' : 'left' }}>\n              <ListItemIcon sx={{ minWidth: 'auto', mx: isRtl ? 0 : 1, ml: isRtl ? 1 : 0 }}>\n                <PaymentIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText primary={t('privacyPolicy.commissionPolicy.rates.5')} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }} />\n            </ListItem>\n            <ListItem sx={{ pl: isRtl ? 0 : 2, pr: isRtl ? 2 : 0, flexDirection: 'row', textAlign: isRtl ? 'right' : 'left' }}>\n              <ListItemIcon sx={{ minWidth: 'auto', mx: isRtl ? 0 : 1, ml: isRtl ? 1 : 0 }}>\n                <PaymentIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText primary={t('privacyPolicy.commissionPolicy.rates.6')} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }} />\n            </ListItem>\n            <ListItem sx={{ pl: isRtl ? 0 : 2, pr: isRtl ? 2 : 0, flexDirection: 'row', textAlign: isRtl ? 'right' : 'left' }}>\n              <ListItemIcon sx={{ minWidth: 'auto', mx: isRtl ? 0 : 1, ml: isRtl ? 1 : 0 }}>\n                <PaymentIcon color=\"primary\" />\n              </ListItemIcon>\n              <ListItemText primary={t('privacyPolicy.commissionPolicy.rates.7')} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }} />\n            </ListItem>\n          </List>\n          <Typography mt={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif', fontStyle: 'italic' }}>\n            {t('privacyPolicy.commissionPolicy.explanation')}\n          </Typography>\n        </SectionCard>\n      </Box>\n    );\n  };\n\n  // Booking Cancellation Policy Section\n  const renderBookingCancellationPolicy = () => {\n    return (\n      <Box>\n        <Typography variant=\"h4\" fontWeight={700} mb={2} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n          {isRtl ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation Policy'}\n        </Typography>\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\n          {t('privacyPolicy.intro')}\n        </Typography>\n        <Divider sx={{ mb: 3 }} />\n\n        {/* Student Policy */}\n        <SectionCard>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {t('privacyPolicy.studentPolicy.title')}\n          </Typography>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.booking.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.booking.description')}\n            </Typography>\n          </Box>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.cancellation.title')}\n            </Typography>\n            <List>\n              {t('privacyPolicy.studentPolicy.cancellation.points', { returnObjects: true }).map((item, idx) => (\n                <ListItem key={idx} sx={{\n                  pl: isRtl ? 0 : 2,\n                  pr: isRtl ? 2 : 0,\n                  flexDirection: 'row',\n                  textAlign: isRtl ? 'right' : 'left',\n                }}>\n                  <ListItemIcon sx={{\n                    minWidth: 'auto',\n                    mx: isRtl ? 0 : 1,\n                    ml: isRtl ? 1 : 0,\n                  }}>\n                    <CheckCircleIcon color=\"success\" />\n                  </ListItemIcon>\n                  <ListItemText primary={item} />\n                </ListItem>\n              ))}\n            </List>\n          </Box>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.rescheduling.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.rescheduling.description')}\n            </Typography>\n          </Box>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.lateArrival.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.studentPolicy.lateArrival.description')}\n            </Typography>\n          </Box>\n        </SectionCard>\n\n        {/* Tutor Policy */}\n        <SectionCard bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.primary.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {t('privacyPolicy.tutorPolicy.title')}\n          </Typography>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.cancellation.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.cancellation.description')}\n            </Typography>\n          </Box>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.rescheduling.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.rescheduling.description')}\n            </Typography>\n          </Box>\n\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, color: theme.palette.primary.main, mb: 1, fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.attendance.title')}\n            </Typography>\n            <Typography mb={1} sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}>\n              {t('privacyPolicy.tutorPolicy.attendance.description')}\n            </Typography>\n          </Box>\n        </SectionCard>\n\n        {/* General Notes */}\n        <SectionCard bg={alpha(theme.palette.info.main, 0.05)}>\n          <Typography\n            variant=\"h5\"\n            fontWeight={600}\n            color={theme.palette.info.main}\n            mb={2}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n          >\n            {t('privacyPolicy.generalNotes.title')}\n          </Typography>\n          <List>\n            <ListItem sx={{ pl: isRtl ? 0 : 2, pr: isRtl ? 2 : 0, flexDirection: 'row', textAlign: isRtl ? 'right' : 'left' }}>\n              <ListItemIcon sx={{ minWidth: 'auto', mx: isRtl ? 0 : 1, ml: isRtl ? 1 : 0 }}>\n                <InfoIcon color=\"info\" />\n              </ListItemIcon>\n              <ListItemText primary={t('privacyPolicy.generalNotes.timeZone')} />\n            </ListItem>\n            <ListItem sx={{ pl: isRtl ? 0 : 2, pr: isRtl ? 2 : 0, flexDirection: 'row', textAlign: isRtl ? 'right' : 'left' }}>\n              <ListItemIcon sx={{ minWidth: 'auto', mx: isRtl ? 0 : 1, ml: isRtl ? 1 : 0 }}>\n                <InfoIcon color=\"info\" />\n              </ListItemIcon>\n              <ListItemText primary={t('privacyPolicy.generalNotes.platformFees')} />\n            </ListItem>\n          </List>\n        </SectionCard>\n      </Box>\n    );\n  };\n\n  const tabs = [\n    { label: isRtl ? 'سياسة الخصوصية' : 'Privacy Policy', icon: <SecurityIcon /> },\n    { label: isRtl ? 'الشروط والأحكام' : 'Terms & Conditions', icon: <InfoIcon /> },\n    { label: isRtl ? 'سياسة الاسترداد' : 'Refund Policy', icon: <CurrencyExchangeIcon /> },\n    { label: isRtl ? 'سياسة الحجز والدفع' : 'Booking & Payment Policy', icon: <PaymentIcon /> },\n    { label: isRtl ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation Policy', icon: <CancelIcon /> },\n  ];\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 0:\n        return renderPrivacyPolicy();\n      case 1:\n        return renderTermsAndConditions();\n      case 2:\n        return renderRefundPolicy();\n      case 3:\n        return renderBookingPaymentPolicy();\n      case 4:\n        return renderBookingCancellationPolicy();\n      default:\n        return renderPrivacyPolicy();\n    }\n  };\n\n  return (\n    <Layout>\n      <Box\n        sx={{\n          minHeight: '100vh',\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\n          pt: 4,\n          pb: 8,\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Fade in timeout={800}>\n            <Box sx={{ p: { xs: 2, md: 4 }, direction: isRtl ? 'rtl' : 'ltr' }}>\n              {/* Main Title */}\n              <Typography\n                variant=\"h3\"\n                align=\"center\"\n                sx={{\n                  mb: 4,\n                  fontWeight: 800,\n                  fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.primary.main,\n                }}\n              >\n                {isRtl ? 'سياسات المنصة' : 'Platform Policies'}\n              </Typography>\n\n              {/* Tabs */}\n              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 4 }}>\n                <Tabs\n                  value={activeTab}\n                  onChange={handleTabChange}\n                  variant=\"scrollable\"\n                  scrollButtons=\"auto\"\n                  sx={{\n                    '& .MuiTab-root': {\n                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                      fontSize: { xs: '0.8rem', sm: '0.9rem', md: '1rem' },\n                      minWidth: { xs: 120, sm: 140, md: 160 },\n                    },\n                  }}\n                >\n                  {tabs.map((tab, index) => (\n                    <Tab\n                      key={index}\n                      label={tab.label}\n                      icon={tab.icon}\n                      iconPosition=\"start\"\n                      sx={{\n                        '& .MuiTab-iconWrapper': {\n                          mr: isRtl ? 0 : 1,\n                          ml: isRtl ? 1 : 0,\n                        },\n                      }}\n                    />\n                  ))}\n                </Tabs>\n              </Box>\n\n              {/* Tab Content */}\n              <Box sx={{ mt: 3 }}>\n                {renderTabContent()}\n              </Box>\n            </Box>\n          </Fade>\n        </Container>\n      </Box>\n    </Layout>\n  );\n};\n\nexport default PlatformPolicy;\n"], "mappings": "AAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,OAAO,CACPC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,QAAQ,CACRC,KAAK,CACLC,IAAI,CACJC,GAAG,CACHC,KAAK,KACA,eAAe,CACtB,OACEC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,gBAAgB,GAAI,CAAAC,oBAAoB,KACnC,qBAAqB,CAC5B,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAClE,MAAO,CAAAC,IAAI,KAAM,cAAc,CAE/B;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAJ,IAAI,CAACK,iBAAiB,CAAC,IAAI,CAAE,aAAa,CAAEN,eAAe,CAACO,EAAE,CAAE,IAAI,CAAE,IAAI,CAAC,CAC3EN,IAAI,CAACK,iBAAiB,CAAC,IAAI,CAAE,aAAa,CAAEN,eAAe,CAACQ,EAAE,CAAE,IAAI,CAAE,IAAI,CAAC,CAE3E,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAET,IAAK,CAAC,CAAGlC,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA4C,KAAK,CAAG/B,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAgC,QAAQ,CAAG5C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA6C,KAAK,CAAGZ,IAAI,CAACa,QAAQ,GAAK,IAAI,CACpC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGnD,QAAQ,CAAC,CAAC,CAAC,CAE7C;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmD,IAAI,CAAGL,QAAQ,CAACK,IAAI,CAACC,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAC3C,OAAQD,IAAI,EACV,IAAK,SAAS,CACZD,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,OAAO,CACVA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,QAAQ,CACXA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,SAAS,CACZA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,SAAS,CACZA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,QACEA,YAAY,CAAC,CAAC,CAAC,CACnB,CACF,CAAC,CAAE,CAACJ,QAAQ,CAACK,IAAI,CAAC,CAAC,CAEnB,KAAM,CAAAE,eAAe,CAAGA,CAACC,CAAC,CAAEC,QAAQ,GAAK,CACvCL,YAAY,CAACK,QAAQ,CAAC,CACxB,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGC,IAAA,MAAC,CAAEC,QAAQ,CAAEC,EAAG,CAAC,CAAAF,IAAA,oBACnCpB,IAAA,CAAC9B,IAAI,EACHqD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACf,IAAIhB,KAAK,CACL,CAAEiB,WAAW,CAAE,aAAanB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAavB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEV,EAAE,EAAI5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAC/D,CAAE,CAAAT,QAAA,cAEFrB,IAAA,CAAC7B,WAAW,EAAAkD,QAAA,CAAEA,QAAQ,CAAc,CAAC,CACjC,CAAC,EACR,CAED;AACF;AACA,KACE,KAAM,CAAAY,KAAK,CAAIC,GAAG,EAAK,CACrB,KAAM,CAAAC,GAAG,CAAG5B,CAAC,CAAC2B,GAAG,CAAC,CAClB,MAAO,CAAAC,GAAG,EAAI,CAACA,GAAG,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAGC,GAAG,CAAG,EAAE,CAC7C,CAAC,CAED;AACA,KAAM,CAAAE,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,qBAAqB,CAAIC,GAAG,EAAK,CACrC,KAAM,CAAAC,QAAQ,CAAGP,KAAK,CAAC,kBAAkBM,GAAG,WAAW,CAAC,CACxD,KAAM,CAAAE,OAAO,CAAGR,KAAK,CAAC,kBAAkBM,GAAG,UAAU,CAAC,CAEtD;AACA,KAAM,CAAAG,OAAO,CAAG,EAAE,CAClB,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI,EAAE,CAAEA,CAAC,EAAE,CAAE,CAC5B,KAAM,CAAAC,IAAI,CAAGX,KAAK,CAAC,kBAAkBM,GAAG,QAAQI,CAAC,EAAE,CAAC,CACpD,GAAIC,IAAI,CAAEF,OAAO,CAACG,IAAI,CAACD,IAAI,CAAC,CAC9B,CAEA,mBACE1C,KAAA,CAACiB,WAAW,EAAAE,QAAA,eACVrB,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEd,CAAC,CAAC,kBAAkBgC,GAAG,QAAQ,CAAC,CACvB,CAAC,CACZC,QAAQ,eAAIxC,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAEmB,QAAQ,CAAa,CAAC,CACtDC,OAAO,eAAIzC,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAEoB,OAAO,CAAa,CAAC,CACpDC,OAAO,CAACQ,MAAM,CAAG,CAAC,eACjBlD,IAAA,CAAC3B,IAAI,EAAAgD,QAAA,CACFqB,OAAO,CAACS,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBACrBlD,KAAA,CAAC5B,QAAQ,EAEPkD,EAAE,CAAE,CACF6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eAEFrB,IAAA,CAACxB,YAAY,EACXgD,EAAE,CAAE,CACFiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cAEFrB,IAAA,CAACjB,QAAQ,EAACiE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfhD,IAAA,CAACzB,YAAY,EACXsD,OAAO,CAAEe,IAAK,CACdpB,EAAE,CAAE,CAAEgC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAC7C,CAAC,GApBG0C,GAqBG,CACX,CAAC,CACE,CACP,GAxCeb,GAyCL,CAAC,CAElB,CAAC,CAED,mBACErC,KAAA,CAAClC,GAAG,EAAAqD,QAAA,eACFrB,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBtB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEd,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACE,KAAK,CAAC,gBAAgB,CAACvB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1Dd,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbP,IAAA,CAAC5B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CACzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC0B,GAAG,CAAEZ,GAAG,EAAKD,qBAAqB,CAACC,GAAG,CAAC,CAAC,cAGrErC,KAAA,CAACiB,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACgC,IAAI,CAAC9B,IAAI,CAAE,IAAI,CAAE,CAAAT,QAAA,eACpDnB,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEqC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAErC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACxDrB,IAAA,CAACf,SAAS,EAACuC,EAAE,CAAE,CAAEuC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEsC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACgC,IAAI,CAAC9B,IAAK,CAAE,CAAE,CAAC,cAC3F9B,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACgC,IAAI,CAAC9B,IAAK,CAC/BN,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEd,CAAC,CAAC,uBAAuB,CAAC,CACjB,CAAC,EACV,CAAC,cACNP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAEd,CAAC,CAAC,yBAAyB,CAAC,CAAa,CAAC,cAC9DP,IAAA,CAACjC,UAAU,EAACgF,UAAU,CAAC,MAAM,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAAAT,QAAA,CAC7Dd,CAAC,CAAC,uBAAuB,CAAC,CACjB,CAAC,EACF,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAyD,wBAAwB,CAAGA,CAAA,GAAM,CACrC,mBACE9D,KAAA,CAAClC,GAAG,EAAAqD,QAAA,eACFrB,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBtB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEd,CAAC,CAAC,uBAAuB,CAAC,CACjB,CAAC,cACbP,IAAA,CAAC5B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BzB,IAAA,CAACnB,KAAK,EAACoF,OAAO,CAAE,CAAE,CAAA5C,QAAA,CACfd,CAAC,CAAC,yBAAyB,CAAC,CAC1B2D,KAAK,CAAC,SAAS,CAAC,CAChBC,MAAM,CAAEC,OAAO,EAAKA,OAAO,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC1ClB,GAAG,CAAC,CAACiB,OAAO,CAAEhB,GAAG,GAAK,CACrB,KAAM,CAAAkB,KAAK,CAAGF,OAAO,CAACF,KAAK,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAK,KAAK,CAAGD,KAAK,CAAC,CAAC,CAAC,CACtB,KAAM,CAAAE,IAAI,CAAGF,KAAK,CAACG,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACtC,mBACE1E,IAAA,CAAC9B,IAAI,EAEHqD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFE,YAAY,CAAE,CAAC,CACf,IAAIhB,KAAK,CACL,CAAEiB,WAAW,CAAE,aAAanB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAavB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CACboB,GAAG,CAAG,CAAC,GAAK,CAAC,CACT1E,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvCpD,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAAC+C,SAAS,CAAC7C,IAAI,EAAItB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAAC+C,KAAK,CAAE,IAAI,CAC/E,CAAE,CAAAvD,QAAA,cAEFnB,KAAA,CAAC/B,WAAW,EAAAkD,QAAA,eACVrB,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZtB,EAAE,CAAE,CACFC,EAAE,CAAE,GAAG,CACPsB,UAAU,CAAE,GAAG,CACfE,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDsC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAT,QAAA,CAEDkD,KAAK,CACI,CAAC,cACbvE,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,OAAO,CACftB,EAAE,CAAE,CACFqD,UAAU,CAAE,UAAU,CACtBC,UAAU,CAAE,CAAC,CACbC,QAAQ,CAAE,SAAS,CACnB9B,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDsC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACoD,IAAI,CAACnD,OAC5B,CAAE,CAAAR,QAAA,CAEDmD,IAAI,CACK,CAAC,EACF,CAAC,EArCTpB,GAsCD,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACL,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA6B,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,mBACE/E,KAAA,CAAClC,GAAG,EAAAqD,QAAA,eACFrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACtB,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACvHX,KAAK,CAAG,iBAAiB,CAAG,eAAe,CAClC,CAAC,cACbV,IAAA,CAAC5B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BvB,KAAA,CAACiB,WAAW,EAAAE,QAAA,eACVrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAACL,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,mCAAmC,CAAC,CAC7B,CAAC,cAEbL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,gDAAgD,CAAC,CAC1C,CAAC,cACbP,IAAA,CAAC3B,IAAI,EAAAgD,QAAA,CACFd,CAAC,CAAC,iDAAiD,CAAE,CAAE2E,aAAa,CAAE,IAAK,CAAC,CAAC,CAAC/B,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBAC3FlD,KAAA,CAAC5B,QAAQ,EAAWkD,EAAE,CAAE,CACtB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eACArB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAChBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cACArB,IAAA,CAACb,eAAe,EAAC6D,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACfhD,IAAA,CAACzB,YAAY,EACXsD,OAAO,CAAEe,IAAK,CACdpB,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAC1E,CAAC,GAhBW0C,GAiBL,CACX,CAAC,CACE,CAAC,EACJ,CAAC,cAENlD,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,gDAAgD,CAAC,CAC1C,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,sDAAsD,CAAC,CAChD,CAAC,EACV,CAAC,cAENL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,+CAA+C,CAAC,CACzC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,qDAAqD,CAAC,CAC/C,CAAC,EACV,CAAC,EACK,CAAC,cAGdL,KAAA,CAACiB,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAAC+C,SAAS,CAAC7C,IAAI,EAAItB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAAC+C,KAAK,CAAE,IAAI,CAAE,CAAAvD,QAAA,eACxFrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAACL,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,iCAAiC,CAAC,CAC3B,CAAC,cAEbL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,8CAA8C,CAAC,CACxC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,oDAAoD,CAAC,CAC9C,CAAC,EACV,CAAC,cAENL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,4CAA4C,CAAC,CACtC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,kDAAkD,CAAC,CAC5C,CAAC,EACV,CAAC,EACK,CAAC,cAGdL,KAAA,CAACiB,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACgC,IAAI,CAAC9B,IAAI,CAAE,IAAI,CAAE,CAAAT,QAAA,eACpDrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACgC,IAAI,CAAC9B,IAAK,CAACL,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACvJd,CAAC,CAAC,kCAAkC,CAAC,CAC5B,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,qCAAqC,CAAC,CAC/B,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,yCAAyC,CAAC,CACnC,CAAC,EACF,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA4E,0BAA0B,CAAGA,CAAA,GAAM,CACvC,mBACEjF,KAAA,CAAClC,GAAG,EAAAqD,QAAA,eACFrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACtB,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACvHX,KAAK,CAAG,oBAAoB,CAAG,0BAA0B,CAChD,CAAC,cACbV,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACE,KAAK,CAAC,gBAAgB,CAACvB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1Dd,CAAC,CAAC,qBAAqB,CAAC,CACf,CAAC,cACbP,IAAA,CAAC5B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BvB,KAAA,CAACiB,WAAW,EAAAE,QAAA,eACVrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAACL,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,mCAAmC,CAAC,CAC7B,CAAC,cAEbL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,2CAA2C,CAAC,CACrC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,iDAAiD,CAAC,CAC3C,CAAC,EACV,CAAC,EACK,CAAC,cAGdL,KAAA,CAACiB,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAAC+C,SAAS,CAAC7C,IAAI,EAAItB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAAC+C,KAAK,CAAE,IAAI,CAAE,CAAAvD,QAAA,eACxFrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAACL,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,sCAAsC,CAAC,CAChC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,4CAA4C,CAAC,CACtC,CAAC,cACbL,KAAA,CAAC7B,IAAI,EAAAgD,QAAA,eACHnB,KAAA,CAAC5B,QAAQ,EAACkD,EAAE,CAAE,CAAE6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE6C,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAW,QAAA,eAChHrB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAW,QAAA,cAC3ErB,IAAA,CAACT,WAAW,EAACyD,KAAK,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,cACfhD,IAAA,CAACzB,YAAY,EAACsD,OAAO,CAAEtB,CAAC,CAAC,wCAAwC,CAAE,CAACiB,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAE,CAAC,EACxI,CAAC,cACXR,KAAA,CAAC5B,QAAQ,EAACkD,EAAE,CAAE,CAAE6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE6C,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAW,QAAA,eAChHrB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAW,QAAA,cAC3ErB,IAAA,CAACT,WAAW,EAACyD,KAAK,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,cACfhD,IAAA,CAACzB,YAAY,EAACsD,OAAO,CAAEtB,CAAC,CAAC,wCAAwC,CAAE,CAACiB,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAE,CAAC,EACxI,CAAC,cACXR,KAAA,CAAC5B,QAAQ,EAACkD,EAAE,CAAE,CAAE6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE6C,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAW,QAAA,eAChHrB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAW,QAAA,cAC3ErB,IAAA,CAACT,WAAW,EAACyD,KAAK,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,cACfhD,IAAA,CAACzB,YAAY,EAACsD,OAAO,CAAEtB,CAAC,CAAC,wCAAwC,CAAE,CAACiB,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAE,CAAC,EACxI,CAAC,cACXR,KAAA,CAAC5B,QAAQ,EAACkD,EAAE,CAAE,CAAE6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE6C,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAW,QAAA,eAChHrB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAW,QAAA,cAC3ErB,IAAA,CAACT,WAAW,EAACyD,KAAK,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,cACfhD,IAAA,CAACzB,YAAY,EAACsD,OAAO,CAAEtB,CAAC,CAAC,wCAAwC,CAAE,CAACiB,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAE,CAAC,EACxI,CAAC,cACXR,KAAA,CAAC5B,QAAQ,EAACkD,EAAE,CAAE,CAAE6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE6C,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAW,QAAA,eAChHrB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAW,QAAA,cAC3ErB,IAAA,CAACT,WAAW,EAACyD,KAAK,CAAC,SAAS,CAAE,CAAC,CACnB,CAAC,cACfhD,IAAA,CAACzB,YAAY,EAACsD,OAAO,CAAEtB,CAAC,CAAC,wCAAwC,CAAE,CAACiB,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAE,CAAC,EACxI,CAAC,EACP,CAAC,cACPV,IAAA,CAACjC,UAAU,EAACqH,EAAE,CAAE,CAAE,CAAC5D,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAAE2E,SAAS,CAAE,QAAS,CAAE,CAAAhE,QAAA,CAC9Gd,CAAC,CAAC,4CAA4C,CAAC,CACtC,CAAC,EACF,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA+E,+BAA+B,CAAGA,CAAA,GAAM,CAC5C,mBACEpF,KAAA,CAAClC,GAAG,EAAAqD,QAAA,eACFrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACtB,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACvHX,KAAK,CAAG,sBAAsB,CAAG,+BAA+B,CACvD,CAAC,cACbV,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACE,KAAK,CAAC,gBAAgB,CAACvB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1Dd,CAAC,CAAC,qBAAqB,CAAC,CACf,CAAC,cACbP,IAAA,CAAC5B,OAAO,EAACoD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BvB,KAAA,CAACiB,WAAW,EAAAE,QAAA,eACVrB,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEd,CAAC,CAAC,mCAAmC,CAAC,CAC7B,CAAC,cAEbL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,2CAA2C,CAAC,CACrC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,iDAAiD,CAAC,CAC3C,CAAC,EACV,CAAC,cAENL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,gDAAgD,CAAC,CAC1C,CAAC,cACbP,IAAA,CAAC3B,IAAI,EAAAgD,QAAA,CACFd,CAAC,CAAC,iDAAiD,CAAE,CAAE2E,aAAa,CAAE,IAAK,CAAC,CAAC,CAAC/B,GAAG,CAAC,CAACP,IAAI,CAAEQ,GAAG,gBAC3FlD,KAAA,CAAC5B,QAAQ,EAAWkD,EAAE,CAAE,CACtB6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB6C,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAW,QAAA,eACArB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAChBiC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAW,QAAA,cACArB,IAAA,CAACb,eAAe,EAAC6D,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACfhD,IAAA,CAACzB,YAAY,EAACsD,OAAO,CAAEe,IAAK,CAAE,CAAC,GAblBQ,GAcL,CACX,CAAC,CACE,CAAC,EACJ,CAAC,cAENlD,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,gDAAgD,CAAC,CAC1C,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,sDAAsD,CAAC,CAChD,CAAC,EACV,CAAC,cAENL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,+CAA+C,CAAC,CACzC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,qDAAqD,CAAC,CAC/C,CAAC,EACV,CAAC,EACK,CAAC,cAGdL,KAAA,CAACiB,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAAC+C,SAAS,CAAC7C,IAAI,EAAItB,KAAK,CAACoB,OAAO,CAACC,OAAO,CAAC+C,KAAK,CAAE,IAAI,CAAE,CAAAvD,QAAA,eACxFrB,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEd,CAAC,CAAC,iCAAiC,CAAC,CAC3B,CAAC,cAEbL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,8CAA8C,CAAC,CACxC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,oDAAoD,CAAC,CAC9C,CAAC,EACV,CAAC,cAENL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,8CAA8C,CAAC,CACxC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,oDAAoD,CAAC,CAC9C,CAAC,EACV,CAAC,cAENL,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBrB,IAAA,CAACjC,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACtB,EAAE,CAAE,CAAEuB,UAAU,CAAE,GAAG,CAAEC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEL,EAAE,CAAE,CAAC,CAAEwB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAC1Jd,CAAC,CAAC,4CAA4C,CAAC,CACtC,CAAC,cACbP,IAAA,CAACjC,UAAU,EAAC0D,EAAE,CAAE,CAAE,CAACD,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CACzFd,CAAC,CAAC,kDAAkD,CAAC,CAC5C,CAAC,EACV,CAAC,EACK,CAAC,cAGdL,KAAA,CAACiB,WAAW,EAACG,EAAE,CAAE5C,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACgC,IAAI,CAAC9B,IAAI,CAAE,IAAI,CAAE,CAAAT,QAAA,eACpDrB,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACgC,IAAI,CAAC9B,IAAK,CAC/BL,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEyB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAW,QAAA,CAExEd,CAAC,CAAC,kCAAkC,CAAC,CAC5B,CAAC,cACbL,KAAA,CAAC7B,IAAI,EAAAgD,QAAA,eACHnB,KAAA,CAAC5B,QAAQ,EAACkD,EAAE,CAAE,CAAE6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE6C,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAW,QAAA,eAChHrB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAW,QAAA,cAC3ErB,IAAA,CAACjB,QAAQ,EAACiE,KAAK,CAAC,MAAM,CAAE,CAAC,CACb,CAAC,cACfhD,IAAA,CAACzB,YAAY,EAACsD,OAAO,CAAEtB,CAAC,CAAC,qCAAqC,CAAE,CAAE,CAAC,EAC3D,CAAC,cACXL,KAAA,CAAC5B,QAAQ,EAACkD,EAAE,CAAE,CAAE6B,EAAE,CAAE3C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE4C,EAAE,CAAE5C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE6C,aAAa,CAAE,KAAK,CAAEC,SAAS,CAAE9C,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAW,QAAA,eAChHrB,IAAA,CAACxB,YAAY,EAACgD,EAAE,CAAE,CAAEiC,QAAQ,CAAE,MAAM,CAAEC,EAAE,CAAEhD,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAW,QAAA,cAC3ErB,IAAA,CAACjB,QAAQ,EAACiE,KAAK,CAAC,MAAM,CAAE,CAAC,CACb,CAAC,cACfhD,IAAA,CAACzB,YAAY,EAACsD,OAAO,CAAEtB,CAAC,CAAC,yCAAyC,CAAE,CAAE,CAAC,EAC/D,CAAC,EACP,CAAC,EACI,CAAC,EACX,CAAC,CAEV,CAAC,CAED,KAAM,CAAAgF,IAAI,CAAG,CACX,CAAEC,KAAK,CAAE9E,KAAK,CAAG,gBAAgB,CAAG,gBAAgB,CAAE+E,IAAI,cAAEzF,IAAA,CAACP,YAAY,GAAE,CAAE,CAAC,CAC9E,CAAE+F,KAAK,CAAE9E,KAAK,CAAG,iBAAiB,CAAG,oBAAoB,CAAE+E,IAAI,cAAEzF,IAAA,CAACjB,QAAQ,GAAE,CAAE,CAAC,CAC/E,CAAEyG,KAAK,CAAE9E,KAAK,CAAG,iBAAiB,CAAG,eAAe,CAAE+E,IAAI,cAAEzF,IAAA,CAACL,oBAAoB,GAAE,CAAE,CAAC,CACtF,CAAE6F,KAAK,CAAE9E,KAAK,CAAG,oBAAoB,CAAG,0BAA0B,CAAE+E,IAAI,cAAEzF,IAAA,CAACT,WAAW,GAAE,CAAE,CAAC,CAC3F,CAAEiG,KAAK,CAAE9E,KAAK,CAAG,sBAAsB,CAAG,+BAA+B,CAAE+E,IAAI,cAAEzF,IAAA,CAACX,UAAU,GAAE,CAAE,CAAC,CAClG,CAED,KAAM,CAAAqG,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,OAAQ9E,SAAS,EACf,IAAK,EAAC,CACJ,MAAO,CAAAyB,mBAAmB,CAAC,CAAC,CAC9B,IAAK,EAAC,CACJ,MAAO,CAAA2B,wBAAwB,CAAC,CAAC,CACnC,IAAK,EAAC,CACJ,MAAO,CAAAiB,kBAAkB,CAAC,CAAC,CAC7B,IAAK,EAAC,CACJ,MAAO,CAAAE,0BAA0B,CAAC,CAAC,CACrC,IAAK,EAAC,CACJ,MAAO,CAAAG,+BAA+B,CAAC,CAAC,CAC1C,QACE,MAAO,CAAAjD,mBAAmB,CAAC,CAAC,CAChC,CACF,CAAC,CAED,mBACErC,IAAA,CAACJ,MAAM,EAAAyB,QAAA,cACLrB,IAAA,CAAChC,GAAG,EACFwD,EAAE,CAAE,CACFmE,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBlH,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAKpD,KAAK,CAAC8B,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpH+D,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAAzE,QAAA,cAEFrB,IAAA,CAAClC,SAAS,EAACiI,QAAQ,CAAC,IAAI,CAAA1E,QAAA,cACtBrB,IAAA,CAAC/B,IAAI,EAAC+H,EAAE,MAACC,OAAO,CAAE,GAAI,CAAA5E,QAAA,cACpBnB,KAAA,CAAClC,GAAG,EAACwD,EAAE,CAAE,CAAE0E,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAEC,SAAS,CAAE3F,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAW,QAAA,eAEjErB,IAAA,CAACjC,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZwD,KAAK,CAAC,QAAQ,CACd9E,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLsB,UAAU,CAAE,GAAG,CACfE,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDsC,KAAK,CAAExC,KAAK,CAACoB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAT,QAAA,CAEDX,KAAK,CAAG,eAAe,CAAG,mBAAmB,CACpC,CAAC,cAGbV,IAAA,CAAChC,GAAG,EAACwD,EAAE,CAAE,CAAE+E,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAE/E,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAC1DrB,IAAA,CAACrB,IAAI,EACH8H,KAAK,CAAE7F,SAAU,CACjB8F,QAAQ,CAAE1F,eAAgB,CAC1B8B,OAAO,CAAC,YAAY,CACpB6D,aAAa,CAAC,MAAM,CACpBnF,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChByB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqE,QAAQ,CAAE,CAAEoB,EAAE,CAAE,QAAQ,CAAES,EAAE,CAAE,QAAQ,CAAER,EAAE,CAAE,MAAO,CAAC,CACpD3C,QAAQ,CAAE,CAAE0C,EAAE,CAAE,GAAG,CAAES,EAAE,CAAE,GAAG,CAAER,EAAE,CAAE,GAAI,CACxC,CACF,CAAE,CAAA/E,QAAA,CAEDkE,IAAI,CAACpC,GAAG,CAAC,CAAC0D,GAAG,CAAEC,KAAK,gBACnB9G,IAAA,CAACpB,GAAG,EAEF4G,KAAK,CAAEqB,GAAG,CAACrB,KAAM,CACjBC,IAAI,CAAEoB,GAAG,CAACpB,IAAK,CACfsB,YAAY,CAAC,OAAO,CACpBvF,EAAE,CAAE,CACF,uBAAuB,CAAE,CACvBuC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBiD,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAClB,CACF,CAAE,EATGoG,KAUN,CACF,CAAC,CACE,CAAC,CACJ,CAAC,cAGN9G,IAAA,CAAChC,GAAG,EAACwD,EAAE,CAAE,CAAE4D,EAAE,CAAE,CAAE,CAAE,CAAA/D,QAAA,CAChBqE,gBAAgB,CAAC,CAAC,CAChB,CAAC,EACH,CAAC,CACF,CAAC,CACE,CAAC,CACT,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAApF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}