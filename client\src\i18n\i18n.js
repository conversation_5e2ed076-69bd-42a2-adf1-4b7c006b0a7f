import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import meetingIssuesEn from './adminMeetingIssues.en.json';
import meetingIssuesAr from './adminMeetingIssues.ar.json';

const resources = {
  en: {
    translation: {
      appName: 'Allemnionline',
      aboutUs: 'About Us',
      wallet: {
        title: 'Wallet',
        balance: 'Balance',
        currentBalance: 'Current Balance',
        transactionHistory: 'Transaction History',
        allTransactions: 'All Transactions',
        noTransactions: 'No transactions found',
        errorFetchingTransactions: 'Error fetching transactions. Please try again.',
        date: 'Date',
        description: 'Description',
        amount: 'Amount',
        status: 'Status',
        debit: 'Debit',
        credit: 'Credit',
        pending: 'Pending',
        completed: 'Completed',
        failed: 'Failed',
        cancelled: 'Cancelled',
        payment: 'Payment',
        lessonWith: 'Lesson with {{teacher}}',
        lessonFrom: 'Lesson from {{student}}',
        student: 'Student',
        teacher: 'Teacher',
        rowsPerPage: 'Rows per page',
        addMoney: 'Add Money',
        payWithStripe: 'Pay with Credit Card',
        pay: 'Pay',
        depositSuccess: 'Deposit successful! Your wallet has been updated.',
        errorProcessingPayment: 'Error processing payment. Please try again.',
        paypalDeposit: 'PayPal Deposit',
        stripeDeposit: 'Stripe Deposit',
        walletDeposit: 'Wallet Deposit'
      },
      withdrawal: {
        title: 'Withdrawal',
        availableBalance: 'Available Balance',
        requestWithdrawal: 'Request Withdrawal',
        withdrawalHistory: 'Withdrawal History',
        noWithdrawals: 'No withdrawal requests found',
        date: 'Date',
        amount: 'Amount',
        paypalEmail: 'PayPal Email',
        status: 'Status',
        actions: 'Actions',
        cancel: 'Cancel',
        submit: 'Submit',
        pending: 'Pending',
        processing: 'Processing',
        completed: 'Completed',
        failed: 'Failed',
        cancelled: 'Cancelled',
        fillAllFields: 'Please fill all required fields',
        minimumAmount: 'Minimum withdrawal amount is ${{amount}}',
        insufficientBalance: 'Insufficient balance',
        requestSubmitted: 'Withdrawal request submitted successfully',
        requestCancelled: 'Withdrawal request cancelled successfully',
        errorSubmitting: 'Error submitting withdrawal request',
        errorCancelling: 'Error cancelling withdrawal request',
        errorFetchingWithdrawals: 'Error fetching withdrawal history',
        paypalEmailHelp: 'Enter the PayPal email where you want to receive the payment',
        processingTime: 'Processing time: {{days}} business days',
        minimumWithdrawal: 'Minimum withdrawal',
        enterOTPTitle: 'Enter Verification Code',
        otpInstructions: 'Please enter the 6-digit verification code sent to your email.',
        otpCode: 'Verification Code',
        verify: 'Verify',
        enterOTP: 'Please enter the verification code',
        errorVerifyingOTP: 'Error verifying code',
        requestCompleted: 'Withdrawal request completed successfully',
        withdrawalCancelled: 'Withdrawal request has been cancelled due to multiple failed attempts',
      },
      admin: {
        withdrawalManagement: {
          title: 'Withdrawal Management',
          statusFilter: 'Status Filter',
          all: 'All',
          date: 'Date',
          teacher: 'Teacher',
          amount: 'Amount',
          paypalEmail: 'PayPal Email',
          status: 'Status',
          notes: 'Notes',
          actions: 'Actions',
          approve: 'Approve',
          reject: 'Reject',
          approveWithdrawal: 'Approve Withdrawal',
          rejectWithdrawal: 'Reject Withdrawal',
          notesOptional: 'Notes (Optional)',
          approveAndProcess: 'Approve & Process',
          processingInfo: 'This will process the withdrawal via PayPal Payouts API.',
          errorFetching: 'Error fetching withdrawal requests',
          errorProcessing: 'Error processing withdrawal'
        }
      },
      contactUs: {
        title: 'Contact Us',
        type: 'Message Type',
        typeQuestion: 'Question',
        typeProblem: 'Problem',
        typeSuggestion: 'Suggestion',
        typePayment: 'Payment Issue',
        typeOther: 'Other',
        subject: 'Subject',
        message: 'Message',
        send: 'Send Message',
        messageSent: 'Your message has been sent successfully. We will get back to you soon.',
        fillAllFields: 'Please fill all required fields',
        sendError: 'Error sending message. Please try again later.',
        startConversation: 'Start a conversation with us'
      },
      myMessages: {
        title: 'My Messages',
        subject: 'Subject',
        type: 'Type',
        date: 'Date',
        status: 'Status',
        pending: 'Pending',
        answered: 'Answered',
        noMessages: 'No messages found',
        fetchError: 'Error fetching messages',
        yourMessage: 'Your Message',
        adminReply: 'Admin Reply',
        awaitingReply: 'Awaiting reply from admin...'
      },
      booking: {
        title: 'Book a Lesson',
        bookLessonWith: 'Book a Lesson with',
        pricePerLesson: 'Price per lesson',
        instructions: 'Booking Instructions',
        instructionsText: 'Select a time slot from the available hours below. Once you confirm your booking, the amount will be deducted from your wallet balance.',
        selectTimeSlot: 'Select a Time Slot',
        noAvailableSlots: 'No available slots for this day',
        teacher: 'Teacher',
        day: 'Day',
        date: 'Date',
        time: 'Time',
        price: 'Price',
        confirmBooking: 'Confirm Booking',
        confirmAndPay: 'Confirm & Pay',
        bookingSuccessTitle: 'Booking Successful!',
        bookingSuccessMessage: 'Your lesson has been booked successfully. You can view your bookings in the My Bookings section.',
        backToTeacher: 'Back to Teacher',
        currentWeek: 'Current Week',
        weekOf: 'Week of',
        previousWeek: 'Previous Week',
        nextWeek: 'Next Week',
        weekNavigation: 'Week Navigation',
        viewMyBookings: 'View My Bookings',
        bookAgain: 'Book Again',
        bookingFailed: 'Booking failed. Please try again.',
        insufficientBalance: 'Insufficient balance. Please add funds to your wallet.',
        selectDuration: 'Select Lesson Duration',
        duration: 'Duration',
        fullLesson: 'Full Lesson ({{duration}} minutes)',
        halfLesson: 'Half Lesson ({{duration}} minutes)',
        meetingCreated: 'Meeting Created',
        meetingAccessInfo: 'You can access this meeting from your Meetings page when it\'s time for your lesson.',
        viewMyMeetings: 'View My Meetings'
      },
      reviews: {
        title: 'Reviews',
        writeReview: 'Write a Review',
        myReviews: 'My Reviews',
        teacherReviews: 'Teacher Reviews',
        rating: 'Rating',
        comment: 'Comment',
        submit: 'Submit Review',
        update: 'Update Review',
        delete: 'Delete Review',
        confirmDelete: 'Are you sure you want to delete this review?',
        selectTeacher: 'Select a Teacher',
        noTeachers: 'We couldn\'t find any teachers available for review. Please make sure you have completed at least one lesson before writing a review.',
        noCompletedLessons: 'You haven\'t had lessons with any teachers yet. Please book and complete a lesson first.',
        allTeachersReviewed: 'You have already reviewed all teachers you\'ve had lessons with. You can update your existing reviews below.',
        allTeachersReviewedShort: 'You have reviewed all your teachers',
        noReviews: 'No reviews yet',
        yourReview: 'Your Review',
        editReview: 'Edit Review',
        reviewSuccess: 'Review submitted successfully',
        reviewUpdateSuccess: 'Review updated successfully',
        reviewDeleteSuccess: 'Review deleted successfully',
        reviewError: 'Error submitting review',
        reviewRequired: 'Please provide a rating',
        commentPlaceholder: 'Share your experience with this teacher...',
        averageRating: 'Average Rating',
        totalReviews: '{{count}} reviews',
        oneReview: '1 review',
        reviewsBy: 'Reviews by Students',
        reviewBy: 'Review by',
        on: 'on',
        stars: 'stars',
        star: 'star',
        outOf5: 'out of 5',
        selectRating: 'Select Rating',
        reviewsFor: 'Reviews for',
        reviewsWritten: 'Reviews Written',
        reviewsReceived: 'Reviews Received',
        viewAll: 'View All',
        filterBy: 'Filter by',
        sortBy: 'Sort by',
        newest: 'Newest',
        oldest: 'Oldest',
        highestRated: 'Highest Rated',
        lowestRated: 'Lowest Rated',
        noComment: 'No comment provided',
        noReviewsWithFilter: 'No reviews match the selected filter',
        teacherReply: 'Teacher Reply',
        replyToReview: 'Reply to Review',
        editReply: 'Edit Reply',
        deleteReply: 'Delete Reply',
        replyPlaceholder: 'Write your reply to this review...',
        sendReply: 'Send Reply',
        updateReply: 'Update Reply',
        replySuccess: 'Reply sent successfully',
        replyUpdateSuccess: 'Reply updated successfully',
        replyDeleteSuccess: 'Reply deleted successfully',
        replyError: 'Error sending reply',
        confirmDeleteReply: 'Are you sure you want to delete this reply?',
        replyRequired: 'Please write a reply',
        sending: 'Sending...'
      },
      about: {
        title: 'About Us',
        intro: 'Allemnionline is an online educational platform specialized in providing Arabic language teaching services and knowledge related to Arabic culture to learners from around the world, through direct one-on-one lessons delivered by qualified and professional teachers.',
        mission: 'The platform seeks to make learning Arabic accessible and effective, while considering individual differences and special needs of each learner, using the latest educational technological tools.',
        whatWeOffer: 'What We Offer',
        services: {
          privateLessons: 'Private Arabic language lessons for all levels: from beginners to advanced.',
          conversationTraining: 'Training in conversation, listening, reading, and writing in Arabic.',
          culturalElements: 'Introduction to essential elements of Arabic culture that help understand the language in its natural context.',
          digitalPlatform: 'An integrated digital platform that enables direct communication via video, lesson scheduling, and secure electronic payment.',
          targetAudience: 'Our services are directed to children, adults, professionals, and anyone who wants to learn Arabic for academic, personal, or professional purposes.'
        },
        ourMission: 'Our Mission',
        missionText: 'To provide distinguished education in Arabic language and related cultural knowledge, with high quality, through direct teaching and modern technologies, in a manner that respects the diversity of learners and their cultural particularities.',
        contactUs: 'Contact Us',
        contactText: 'For any inquiries or comments, please contact us via email:',
        email: '<EMAIL>'
      },
      app: {
        name: 'Allemnionline',
        copyright: 'Allemnionline 2025',
        tagline: 'Designed with love for Arabic education'
      },
      brand: {
        name: 'Allemnionline'
      },
      common: {
        switchLanguage: 'Switch Language',
        loading: 'Loading...',
        settings: 'Settings',
        profile: 'Profile',
        cancel: 'Cancel',
        back: 'Back',
        continue: 'Continue',
        confirm: 'Confirm',
        update: 'Update',
        success: 'Success',
        error: 'Error',
        save: 'Save',
        saveAndReturn: 'Save & Return',
        saving: 'Saving...',
        email: 'Email Address',
        notProvided: 'Not provided',
        search: 'Search',
        close: 'Close',
        password: 'Password',
        fullName: 'Full Name',
        name: 'Name',
        confirmPassword: 'Confirm Password',
        showMore: 'Show More',
        showLess: 'Show Less',
        gender: 'Gender',
        male: 'Male',
        female: 'Female',
        submit: 'Submit',
        notSet: 'Not Set',
        actions: 'Actions',
        edit: 'Edit',
        delete: 'Delete',
        view: 'View',
        details: 'Details',
        rowsPerPage: 'Rows per page',
        footer: {
          copyright: 'Allemnionline 2025',
          tagline: 'Designed with love for Arabic education'
        },
        currency: 'USD',
        details: 'Details'
      },
      profile: {
        title: 'Profile',
        fullName: 'Full Name',
        edit: 'Edit Profile',
        save: 'Save Changes',
        cancel: 'Cancel',
        success: 'Profile updated successfully',
        error: 'Error updating profile',
        updateSuccess: 'Profile updated successfully',
        passwordUpdateSuccess: 'Password updated successfully',
        teachingInfoUpdateSuccess: 'Teaching information updated successfully!',
        editInfo: 'Edit Profile Information',
        editTeachingInfo: 'Edit Teaching Info',
        saving: 'Saving...',
        errors: {
          update: 'Failed to update profile',
          passwordUpdate: 'Failed to update password',
          passwordMismatch: 'Passwords do not match',
          currentPassword: 'Current password is incorrect',
          fetchError: 'Failed to fetch profile data',
          updateFailed: 'Update failed. Please try again.',
          deleteRequest: 'Failed to send delete request',
          invalidCode: 'Invalid verification code',
          codeRequired: 'Verification code is required',
          cancelDelete: 'Failed to cancel account deletion'
        },
        basicInfo: 'Basic Information',
        teacherInfo: 'Teacher Information',
        editInfo: 'Edit Info',
        changePassword: 'Change Password',
        email: 'Email',
        gender: 'Gender',
        currentPassword: 'Current Password',
        newPassword: 'New Password',
        confirmPassword: 'Confirm Password',
        editPersonalInfo: 'Edit Personal Information',
        togglePasswordVisibility: 'Toggle password visibility',
        uploadPhoto: 'Upload Photo',
        deleteAccount: 'Delete Account',
        deleteAccountTitle: 'Delete Account',
        deleteAccountWarning: 'Are you sure you want to delete your account? This action cannot be undone.',
        deleteAccountNote: 'A verification code will be sent to your email address.',
        sendDeleteCode: 'Send Verification Code',
        verifyDeleteCode: 'Verify Deletion Code',
        deleteCodeSentMessage: 'A verification code has been sent to your email. Please enter it below:',
        deleteCode: 'Verification Code',
        confirmDelete: 'Confirm Deletion',
        deleteCodeSent: 'Verification code sent to your email',
        deletePending: 'Account deletion pending (24 hours)',
        cancelDelete: 'Cancel Deletion',
        deleteCancelled: 'Account deletion cancelled successfully',
        deletePendingAlert: '⚠️ Account Deletion Pending',
        deletePendingMessage: 'Your account is scheduled for deletion. You can cancel this action at any time before the scheduled deletion time.',
        deleteScheduledFor: 'Scheduled deletion time',
        updateStatus: {
          pending: 'Profile update request is under review',
          approved: 'Profile update approved',
          rejected: 'Profile update request rejected',
          requestDate: 'Request Date',
          reviewDate: 'Review Date',
          adminNotes: 'Admin Notes',
          pendingNote: 'Please wait for admin review. Your current data will remain active until the update is approved.'
        },
        teacher: {
          videoUpload: {
            title: 'Upload Introduction Video',
            description: 'Upload a short video introducing yourself to potential students. This video will be shown on your profile.',
            requirements: 'Video Requirements',
            formatRequirement: 'Allowed formats',
            sizeRequirement: 'Maximum size',
            lengthRequirement: 'Recommended length',
            maximum: 'maximum',
            minutes: 'minutes',
            selectVideo: 'Select Video',
            upload: 'Upload Video',
            uploading: 'Uploading...',
            success: 'Video uploaded successfully!',
            videoReady: 'Your video is ready to be included in your application.',
            continue: 'Continue to Application',
            delete: 'Delete Video',
            skipForNow: 'Skip for now',
            invalidVideoFormat: 'Invalid video format. Please select MP4, WebM, or OGG file.',
            videoTooSmall: 'Video size must be at least 1MB.',
            videoTooLarge: 'Video size must not exceed 100MB.',
            noVideoSelected: 'Please select a video file.',
            videoDeleteError: 'Error deleting video. Please try again.'
          },
          uploadVideoNow: 'Upload Video Now',
          videoReady: 'Your video is ready to be included in your application',
          deleteVideo: 'Delete Video',
          availableHoursDescription: 'Select the hours you are available to teach. This will help students find suitable time slots for booking lessons with you.',
          availableHoursApplicationDescription: 'Select the hours you will be available to teach if your application is approved. You can update these hours later from your profile.',
          availableHoursProfileDescription: 'Manage your teaching availability to let students know when you are available for lessons.',
          viewAvailableHours: 'View Available Hours',
          viewAvailableHoursDescription: 'Here you can see all your available teaching hours organized by day.',
          editAvailableHours: 'Edit Hours',
          noHoursForDay: 'No available hours for this day',
          manageAvailableHours: 'Manage Available Hours',
          hoursSavedSuccess: 'Your available hours have been saved successfully!',
          myLessons: 'My Lessons',
          studentName: 'Student Name',
          totalLessons: 'Total Lessons',
          completedLessons: 'Completed Lessons',
          scheduledLessons: 'Scheduled Lessons',
          cancelledLessons: 'Cancelled Lessons',
          noLessonsFound: 'No lessons found',
          errorSavingHours: 'Error saving your available hours. Please try again.',
          errorLoadingHours: 'Error loading your available hours. Please try again.',
          errorParsingHours: 'Error parsing your available hours data. Please try again.',
          saveAndReturn: 'Save & Return',
          timeSlots: 'time slots',
          noAvailableHours: 'No available hours selected. Please select your available hours.',
          selectAll: 'Select All',
          clearAll: 'Clear All',
          timeSlot: 'Time Slot',
          available: 'Available',
          unavailable: 'Unavailable',
          selectAllDays: 'Select this time for all days',
          clearAllDays: 'Clear this time for all days',
          legend: 'Legend',
          cellStatus: 'Cell Status',
          actions: 'Actions',

          changeProfilePicture: 'Change profile picture',
          profile: {
            title: 'Teacher Profile',
            personalInfo: 'Personal Information',
            teachingInfo: 'Teaching Information',
            phone: 'Phone Number',
            country: 'Country',
            residence: 'Place of Residence',
            nativeLanguage: 'Native Language',
            teachingLanguages: {
              title: 'Teaching Languages',
              Arabic: 'Arabic',
              English: 'English',
              French: 'French',
              Spanish: 'Spanish',
              Urdu: 'Urdu',
              Turkish: 'Turkish',
              Indonesian: 'Indonesian',
              Malay: 'Malay',
              Bengali: 'Bengali',
              Hindi: 'Hindi',
              Persian: 'Persian',
              German: 'German',
              Italian: 'Italian',
              Portuguese: 'Portuguese',
              Russian: 'Russian',
              Chinese: 'Chinese',
              Japanese: 'Japanese',
              Korean: 'Korean',
              Thai: 'Thai',
              Vietnamese: 'Vietnamese',
              Swahili: 'Swahili',
              Hausa: 'Hausa',
              Somali: 'Somali',
              select: 'Select teaching languages',
              placeholder: 'Select one or more languages'
            },
            courseTypes: 'Course Types',
            qualifications: 'Qualifications',
            teachingExperience: 'Teaching Experience',
            availableHours: 'Available Hours',
            pricePerLesson: 'Price per Lesson',
            timezone: 'Time Zone',
            paymentMethod: 'Payment Method',
            currency: {
              usd: 'USD',
              eur: 'EUR',
              gbp: 'GBP'
            },
            experience: {
              beginner: 'Beginner (0-2 years)',
              intermediate: 'Intermediate (2-5 years)',
              advanced: 'Advanced (5-10 years)',
              expert: 'Expert (10+ years)'
            }
          }
        },
        genders: {
          male: 'Male',
          female: 'Female'
        }
      },
      nav: {
        home: 'Home',
        dashboard: 'Dashboard',
        teachers: 'Teachers',
        students: 'Students',
        categories: 'Categories',
        languages: 'Languages',
        applications: 'Applications',
        profileUpdates: 'Profile Updates',
        withdrawalManagement: 'Withdrawal Management',
        findTeacher: 'Find Teacher',
        myTeachers: 'My Teachers',
        meetings: 'Meetings',
        chat: 'Chat',
        login: 'Login',
        register: 'Register',
        logout: 'Logout',
        search: 'Search',
        language: 'Language',
        english: 'English',
        arabic: 'Arabic',
        adminDashboard: 'Admin Dashboard',
        teacherDashboard: 'Teacher Dashboard',
        studentDashboard: 'Student Dashboard'
      },
      footer: {
        platformName: 'Allemnionline',
        designedBy: 'Designed with love for Arabic education',
        followUs: 'Follow Us',
        facebook: 'Facebook',
        twitter: 'Twitter',
        instagram: 'Instagram',
        quickLinks: 'Quick Links',
        about: 'About Us',
        contact: 'Contact Us',
        privacy: 'Privacy Policy',
        terms: 'Terms of Service',
        faq: 'FAQ',
        support: 'Support',
        copyright: '© 2025 Allemnionline',
        tagline: 'Designed with love for Arabic education'
      },
      hero: {
        title: 'Arabic in All Languages',
        subtitle: 'A unique educational journey that combines Arabic language and culture',
        startLearning: 'Start Learning',
        becomeTeacher: 'Become a Teacher',
        imageAlt: 'The Holy Quran'
      },
      home: {
        subjects: 'Our Courses',
        subjectsSubtitle: 'Discover a diverse range of Islamic and Arabic subjects with our expert instructors',
        teachers: 'teachers',
        expertTeachers: 'Expert Teachers',
        expertTeachersDesc: 'Learn from certified scholars and native speakers',
        activeStudents: 'Active Students',
        courses: 'Courses',
        coursesDesc: 'Comprehensive courses in Islamic studies and Arabic language',
        studentsDesc: 'Students from around the world',
        whyChooseUs: 'Why Choose Us',
        whyChooseUsSubtitle: 'We offer a unique learning experience combining modern technology with authentic Arabic education',
        meetTeachers: 'Meet Our Teachers',
        meetTeachersSubtitle: 'Learn from qualified and specialized instructors in Islamic and Arabic education',
        features: {
          quality: 'Quality Education',
          qualityDesc: 'Expert instructors specialized in their fields',
          flexible: 'Flexible Learning',
          flexibleDesc: 'Learn anytime, anywhere at your pace',
          interactive: 'Interactive Learning',
          interactiveDesc: 'Interactive lessons and live discussions',
          certified: 'Certified Courses',
          certifiedDesc: 'Get certified in your field of study'
        },
        testimonials: 'What Our Students Say',
        testimonialsSubtitle: 'Hear from our students about their learning experience with our expert teachers',
        reviewFor: 'Review for',
        learnMore: 'Learn More'
      },
      testimonials: {
        student: 'Student',
        reviewFor: 'Review for'
      },
      search: {
        findTeacher: 'Find a Teacher',
        filters: 'Filters',
        subject: 'Subject',
        allSubjects: 'All Subjects',
        language: 'Language',
        allLanguages: 'All Languages',
        priceRange: 'Price Range',
        rating: 'Rating',
        anyRating: 'Any Rating',
        andAbove: '& above',
        noTeachersFound: 'No teachers found',
        yearsOfExperience: '{{years}} years of experience',
        bookLesson: 'Book a Lesson',
        perHour: '/hour',
        subjects: 'Subjects',
        languages: 'Languages',
        searchButton: 'Search',
        clearFilters: 'Clear Filters',
        noTeachersFound: 'No teachers found matching your criteria',
        teachingLanguages: 'Teaching Languages',
        verifiedTeacher: 'Verified Teacher',
        watchIntro: 'Watch Intro',
        watchIntroVideo: 'Watch Intro Video',
        viewProfile: 'View Profile',
        contactAndBook: 'Contact & Book'
      },
      booking: {
        bookLessonWith: 'Book a Lesson with',
        pricePerLesson: 'Price per lesson',
        instructions: 'Booking Instructions',
        instructionsText: 'Select a day and time slot from the calendar below. Once you confirm your booking, the amount will be deducted from your wallet balance.',
        selectTimeSlot: 'Select a Time Slot',
        clickToSelectSlot: 'Click on available time slots to book',
        clickToBook: 'Click to book this slot',
        fullHourAvailable: 'Full hour available',
        halfHourOnly: 'Half hour only',
        fullLesson: 'Full Lesson (50 min)',
        halfLesson: 'Half Lesson (25 min)',
        selectDuration: 'Select Lesson Duration',
        selectBookingOption: 'Select Booking Option',
        bookingType: 'Booking Type',
        regularHalfLesson: 'Regular Half Lesson (25 min)',
        regularFullLesson: 'Regular Full Lesson (50 min)',
        secondHalfOnly: 'Second Half Only (30 min later)',
        fullLessonFromSecondHalf: 'Full Lesson from Second Half (50 min)',
        crossHourLesson: 'Cross-Hour Lesson (30+30 min)',
        regularLesson: 'Regular Lesson',
        duration: 'Duration',
        noAvailableSlots: 'No available slots for this day',
        confirmBooking: 'Confirm Booking',
        teacher: 'Teacher',
        day: 'Day',
        date: 'Date',
        time: 'Time',
        price: 'Price',
        confirmAndPay: 'Confirm & Pay',
        bookingSuccessTitle: 'Booking Successful!',
        bookingSuccessMessage: 'Your lesson has been booked successfully and the amount has been deducted from your wallet. You can view your bookings in the My Bookings section.',
        trialLessonEligible: 'This is your first lesson with this teacher! Trial lesson price: ${{price}}',
        backToTeacher: 'Back to Teacher',
        viewMyBookings: 'View My Bookings',
        bookAgain: 'Book Again',
        bookingFailed: 'Booking failed. Please try again.',
        insufficientBalance: 'Insufficient balance. Please add funds to your wallet before booking.',
        currency: 'USD',
        currentWeek: 'Current Week',
        weekOf: 'Week of',
        previousWeek: 'Previous Week',
        nextWeek: 'Next Week',
        weekNavigation: 'Week Navigation',
        selectTimeRange: 'Select Time Range',
        startTime: 'Start Time',
        endTime: 'End Time',
        lessonDuration: 'Lesson Duration',
        from: 'From',
        to: 'To',
        availableSlot: 'Available Slot',
        selectDuration: 'Select Duration'
      },
      bookings: {
        title: 'My Bookings',
        weeklyTitle: 'This Week\'s Bookings',
        description: 'View and manage your scheduled lessons with teachers.',
        weeklyDescription: 'View your bookings for this week in a calendar format.',
        bookingDetails: 'Booking Details',
        noBookings: 'You have no bookings yet.',
        fetchError: 'Error fetching bookings. Please try again later.',
        teacher: 'Teacher',
        date: 'Date',
        time: 'Time',
        timeRange: 'Time Range',
        duration: 'Duration',
        minutes: 'minutes',
        status: 'Status',
        statusValues: {
          scheduled: 'Scheduled',
          completed: 'Completed',
          cancelled: 'Cancelled',
          issue_reported: 'Issue Reported',
          ongoing: 'Ongoing'
        },
        price: 'Price',
        cancel: 'Cancel',
        confirmCancel: 'Confirm Cancellation',
        cancelWarning: 'Are you sure you want to cancel this booking? The amount will be refunded to your wallet.',
        cancellationReason: 'Cancellation Reason (Optional)',
        cancellationReasonPlaceholder: 'Please provide a reason for cancelling this booking...',
        confirmCancelButton: 'Yes, Cancel Booking',
        cancelling: 'Cancelling...',
        cancelSuccess: 'Booking cancelled successfully and amount refunded to your wallet',
        cancelError: 'Error cancelling booking. Please try again.',
        availableSlot: 'Available Time Slot',
        currentTime: 'Current Time',
        pastSlot: 'Past Time',
        takeBreak: 'Take Break',
        takeBreakTitle: 'Take This Time as Break',
        takeBreakMessage: 'Do you want to take this time as a break?',
        takeBreakNote: 'This time will be hidden from students for this week only',
        breakTakenSuccess: 'Break taken successfully',
        breakTakenError: 'Error taking break',
        break: 'Break',
        clickToTakeBreak: 'Click to take break',
        cancelBreakTitle: 'Cancel Break Time',
        cancelBreakMessage: 'Do you want to cancel this break time?',
        cancelBreakNote: 'This time will become available to students again',
        breakCancelledSuccess: 'Break cancelled successfully',
        breakCancelledError: 'Error cancelling break',
        reschedule: 'Reschedule',
        rescheduleTitle: 'Reschedule Booking',
        rescheduleDescription: 'Select a new time slot for this booking from your available hours.',
        currentBooking: 'Current Booking',
        selectDay: 'Select Day',
        chooseDay: 'Choose a day',
        selectTime: 'Select Time',
        chooseTime: 'Choose a time',
        loadingDays: 'Loading available days...',
        loadingTimes: 'Loading available times...',
        noAvailableDays: 'No available days found for the next 30 days.',
        noAvailableTimes: 'No available times for this day.',
        availableTimes: 'available times',
        rescheduleReason: 'Reschedule Reason (Optional)',
        rescheduleReasonPlaceholder: 'Please provide a reason for rescheduling this booking...',
        rescheduleSuccess: 'Booking rescheduled successfully',
        rescheduleError: 'Error rescheduling booking. Please try again.',
        fetchDaysError: 'Error fetching available days. Please try again.',
        fetchTimesError: 'Error fetching available times. Please try again.',
        currentBooking: 'Current Booking',
        selectNewDate: 'Select New Date',
        selectNewTime: 'Select New Time',
        selectDateFirst: 'Please select a date from the calendar first',
        selectedTime: 'Selected Time',
        confirmReschedule: 'Confirm Reschedule',
        availableSlots: 'slots available',
        backToSelectDay: 'Back to Select Day'
      },
      teacherDetails: {
        backToSearch: 'Back to Search',
        teacherNotFound: 'Teacher not found',
        errorFetching: 'Error fetching teacher details',
        reviews: 'reviews',
        watchIntroVideo: 'Watch Intro Video',
        hideIntroVideo: 'Hide Intro Video',
        bookLesson: 'Book a Lesson',
        teachingSubjects: 'Teaching Subjects',
        languages: 'Languages',
        nativeLanguage: 'Native Language',
        teachingLanguages: 'Teaching Languages',
        qualifications: 'Qualifications',
        yearsOfExperience: '{{years}} years of experience',
        studentReviews: 'Student Reviews',
        noReviews: 'No reviews yet',
        contactInfo: 'Contact Information',
        email: 'Email',
        phone: 'Phone',
        location: 'Location',
        country: 'Country',
        residence: 'City of Residence',
        timezone: 'Timezone',
        experience: 'Experience',
        memberSince: 'Member since',
        availableHours: 'Available Hours',
        paymentInfo: 'Payment Information',
        pricePerLesson: 'Price per Lesson',
        paymentMethod: 'Payment Method',
        cv: 'CV/Resume',
        introVideo: 'Introduction Video',
        courseTypes: 'Course Types'
      },
      chat: {
        conversations: 'Conversations',
        messages: 'Messages',
        typeMessage: 'Type a message...',
        send: 'Send',
        noMessages: 'No messages',
        noChats: 'No conversations',
        startChat: 'Start new conversation',
        loading: 'Loading...',
        today: 'Today',
        yesterday: 'Yesterday',
        online: 'Online',
        offline: 'Offline',
        sent: 'Sent',
        delivered: 'Delivered',
        read: 'Read',
        failed: 'Failed to send',
        retry: 'Retry',
        onlyStudents: 'Only students can start a chat with teachers',
        no_conversations: 'No conversations',
        type_message: 'Type your message here...',
        select_conversation: 'Select a conversation to start',
        last_seen: 'Last seen',
        typing: 'typing...',
        deleteConfirmTitle: 'Delete Conversation',
        deleteConfirmMessage: 'Are you sure you want to delete the conversation with {{name}}?',
        conversationDeleted: 'Conversation deleted successfully',
        conversationDeletedByOther: 'This conversation has been deleted by the other participant',
        deleteConversationError: 'Error deleting conversation',
        messageDeleted: 'Message deleted successfully',
        messageEdited: 'Message edited successfully',
        editError: 'Error editing message',
        deleteError: 'Error deleting message',
        noConversations: 'No conversations found',
         chat: 'Chat'
      },
      auth: {
        login: 'Login',
        logout: 'Logout',
        logoutConfirmTitle: 'Confirm Logout',
        logoutConfirmMessage: 'Are you sure you want to log out?',
        welcomeBack: 'Welcome Back!',
        email: 'Email Address',
        forgotPassword: 'Forgot Password?',
        forgotPasswordInstructions: 'Enter your email address and we\'ll send you a verification code to reset your password.',
        sendResetCode: 'Send Reset Code',
        backToLogin: 'Back to Login',
        resetCodeSent: 'Reset code sent to your email',
        resetRequestFailed: 'Failed to send reset code',
        verifyCode: 'Verify Code',
        verifyCodeInstructions: 'Enter the 6-digit verification code sent to your email:',
        verificationCode: 'Verification Code',
        verifyAndContinue: 'Verify & Continue',
        resendCode: 'Resend Code',
        changeEmail: 'Change Email',
        verifyEmail: 'Verify Email',
        verificationCodeSentTo: 'We sent a verification code to',
        verify: 'Verify',
        verifying: 'Verifying...',
        verificationCodeSent: 'Verification code sent successfully',
        verificationFailed: 'Email verification failed',
        resendFailed: 'Failed to resend verification code',
        resendCodeIn: 'Resend code in',
        sending: 'Sending...',
        invalidCode: 'Please enter a valid 6-digit code',
        fillAllFields: 'Please fill in all fields',
        loginFailed: 'Login failed. Please check your credentials.',
        invalidCredentials: 'Invalid email or password. Please try again.',
        emailNotFound: 'This email address is not registered. Please check your email or sign up.',
        wrongPassword: 'Incorrect password. Please try again.',
        invalidCode: 'Invalid verification code',
        verificationFailed: 'Verification failed',
        resetCodeResent: 'Reset code has been resent to your email',
        resetPassword: 'Reset Password',
        resetPasswordInstructions: 'Create a new password for your account',
        newPassword: 'New Password',
        confirmPassword: 'Confirm Password',
        passwordResetSuccess: 'Password Reset Successful!',
        passwordResetSuccessMessage: 'Your password has been reset successfully.',
        redirectingToLogin: 'Redirecting to login page...',
        resetPasswordFailed: 'Failed to reset password',
        password: 'Password',
        signIn: 'Sign In',
        or: 'OR',
        noAccount: "Don't have an account?",
        signUp: 'Sign Up',
        continueWithGoogle: 'Continue with Google',
        googleSignInError: 'Google sign-in failed. Please try again.',
        signInWithGoogle: 'Continue with Google',
        loginError: 'Login failed. Please check your credentials.',
        accountNotFound: 'Account not found',
        invalidRole: 'Invalid user role',
        chooseAccountType: 'Join Our Community',
        registerDescription: 'Begin your journey in Arabic education. Choose your role and become part of our growing community of learners and educators.',
        registerAsTeacher: 'Teach with Us',
        teacherDescription: 'Share your knowledge and expertise in Islamic studies and Arabic language. Help others learn and grow while building your teaching career.',
        registerAsStudent: 'Learn with Us',
        studentDescription: 'Start your learning journey in Islamic studies and Arabic language with expert teachers from around the world.',
        alreadyHaveAccount: 'Already have an account?',
        getStarted: 'Get Started',
        studentRegistration: 'Student Registration',
        teacherRegistration: 'Teacher Registration',
        joinOurCommunity: 'Join our community of learners',
        joinOurTeachers: 'Share your knowledge with others',
        createAccount: 'Create Account',
        registrationError: 'Registration failed. Please try again.',
        selectGender: 'Please select your gender',
        gender: 'Gender',
        male: 'Male',
        female: 'Female'
      },
      courseTypes: {
        Islamic_Law: 'Islamic Law',
        Fiqh: 'Fiqh',
        Quran: 'Quran',
        Tajweed: 'Tajweed',
        Islamic_History: 'Islamic History',
        Arabic_Grammar: 'Arabic Grammar',
        Arabic_Speaking: 'Arabic Speaking',
        Arabic_Writing: 'Arabic Writing',
        Islamic_Ethics: 'Islamic Ethics',
        Hadith: 'Hadith',
        Aqeedah: 'Islamic Creed'
      },
      languages: {
        Arabic: 'Arabic',
        English: 'English',
        French: 'French',
        Spanish: 'Spanish'
      },
      gender: {
        male: 'Male',
        female: 'Female'
      },
      dashboard: {
        welcome: 'Welcome',
        unauthorized: 'Unauthorized access to dashboard',
        fetchError: 'Error fetching data',
        totalStudents: 'Total Students',
        totalClasses: 'Total Classes',
        averageRating: 'Average Rating',
        totalEarnings: 'Total Earnings',
        categories: 'Teaching Categories',
        recentBookings: 'Recent Bookings',
        recentReviews: 'Recent Reviews',
        learningProgress: 'Learning Progress',
        quickActions: 'Quick Actions',
        recommendedTeachers: 'Recommended Teachers',
        upcomingLessons: 'Upcoming Lessons',
        noUpcomingLessons: 'No upcoming lessons scheduled',
        updateProfile: 'Update Profile',
        findTeacher: 'Find a Teacher',
        browseCourses: 'Browse Courses',
        viewAll: 'View All',
        incompleteProfile: 'Your Profile is Incomplete',
        completeProfileMessage: 'Please complete your profile to access all features and find the perfect teacher for your learning journey.',
        completeProfileToAccess: 'You need to complete your profile to access this page and its features.',
        completeProfile: 'Complete Profile',
        updateProfile: 'Update Profile',
        completeProfileNow: 'Complete Profile Now'
      },
      regions: {
        middle_east: 'Middle East',
        north_africa: 'North Africa',
        sub_saharan_africa: 'Sub-Saharan Africa',
        south_asia: 'South Asia',
        southeast_asia: 'Southeast Asia',
        central_asia: 'Central Asia',
        europe: 'Europe',
        north_america: 'North America',
        south_america: 'South America',
        east_asia: 'East Asia',
        oceania: 'Oceania',
        others: 'others'
      },
      teacher: {
        phoneOptional: 'Optional',
        nativeLanguage: 'Native Language',
        teachingLanguages: {
          title: 'Teaching Languages',
          Arabic: 'Arabic',
          English: 'English',
          French: 'French',
          Spanish: 'Spanish',
          Urdu: 'Urdu',
          Turkish: 'Turkish',
          Indonesian: 'Indonesian',
          Malay: 'Malay',
          Bengali: 'Bengali',
          Hindi: 'Hindi',
          Persian: 'Persian',
          German: 'German',
          Italian: 'Italian',
          Portuguese: 'Portuguese',
          Russian: 'Russian',
          Chinese: 'Chinese',
          Japanese: 'Japanese',
          Korean: 'Korean',
          Thai: 'Thai',
          Vietnamese: 'Vietnamese',
          Swahili: 'Swahili',
          Hausa: 'Hausa',
          Somali: 'Somali',
          select: 'Select teaching languages',
          placeholder: 'Select one or more languages'
        },
        qualifications: 'Qualifications',
        experience: 'Experience',
        subjects: 'Subjects',
        profile: {
          title: 'Teacher Profile',
          personalInfo: 'Personal Information',
          teachingInfo: 'Teaching Information',
          phone: 'Phone Number',
          country: 'Country',
          residence: 'Place of Residence',
          nativeLanguage: 'Native Language',
          teachingLanguages: {
            title: 'Teaching Languages',
            Arabic: 'Arabic',
            English: 'English',
            French: 'French',
            Spanish: 'Spanish',
            Urdu: 'Urdu',
            Turkish: 'Turkish',
            Indonesian: 'Indonesian',
            Malay: 'Malay',
            Bengali: 'Bengali',
            Hindi: 'Hindi',
            Persian: 'Persian',
            German: 'German',
            Italian: 'Italian',
            Portuguese: 'Portuguese',
            Russian: 'Russian',
            Chinese: 'Chinese',
            Japanese: 'Japanese',
            Korean: 'Korean',
            Thai: 'Thai',
            Vietnamese: 'Vietnamese',
            Swahili: 'Swahili',
            Hausa: 'Hausa',
            Somali: 'Somali',
            select: 'Select teaching languages',
            placeholder: 'Select one or more languages'
          },
          courseTypes: 'Course Types',
          qualifications: 'Qualifications',
          teachingExperience: 'Teaching Experience',
          availableHours: 'Available Hours',
          pricePerLesson: 'Price per Lesson',
          timezone: 'Time Zone',
          paymentMethod: 'Payment Method',
          currency: {
            usd: 'USD',
            eur: 'EUR',
            gbp: 'GBP'
          },
          experience: {
            beginner: 'Beginner (0-2 years)',
            intermediate: 'Intermediate (2-5 years)',
            advanced: 'Advanced (5-10 years)',
            expert: 'Expert (10+ years)'
          }
        },
        application: {
          title: 'Teacher Application',
          submit: 'Submit Application',
          success: 'Application submitted successfully',
          error: 'Error submitting application',
          errorFetchingData: 'Error fetching data',
          errorFetchingCategories: 'Error fetching categories',
          statusCardTitle: 'Application Status',
          edit: 'Edit Application',
          editDescription: 'You can edit your application details, but you will need to be approved again by the admin.',
          warningTitle: 'Warning: Application Edit',
          warningMessage: 'Editing your application will send a profile update request that requires admin approval.',
          warningDescription1: 'If you proceed, you will be redirected to the application form where you can edit your details.',
          warningDescription2: 'You will continue teaching with your current data until the admin approves your update request.',
          warningConfirmation: 'Are you sure you want to continue?',
          confirmEdit: 'Yes, Edit Application',
          editApplication: {
            title: 'Edit Application',
            description: 'Update your application details. Changes will be reviewed by admin before being applied.',
            warning: 'Your current data will remain active until the admin approves your changes.'
          },
          changeVideo: 'Change Video',
          status: {
            pending: 'Pending Review',
            approved: 'Approved',
            rejected: 'Rejected'
          },
          statusMessage: {
            pending: 'Your application is being reviewed',
            approved: 'Congratulations! Your application has been approved',
            rejected: 'Unfortunately, your application has been rejected',
            default: 'Application status updated'
          },
          applicationNextSteps: {
            approved: {
              title: 'Next Steps',
              steps: [
                'Please log out from your account',
                'Log in again to activate your full teacher privileges'
              ]
            }
          }
        },
        country: 'Country',
        residence: 'City of Residence',
        nativeLanguage: 'Native Language',
        teachingLanguages: 'Teaching Languages',
        courseTypes: 'Course Types',
        qualifications: 'Qualifications',
        qualificationsPlaceholder: 'Enter your educational qualifications and certifications',
        teachingExperience: 'Years of Teaching Experience',
        introVideo: 'Introduction Video',
        introVideoUrl: 'Introduction Video URL',
        introVideoUrlPlaceholder: 'https://...',
        noIntroVideo: 'No introduction video available',
        videoLinkTab: 'Video Link',
        videoFileTab: 'Upload Video',
        videoLink: 'Link',
        videoUpload: 'Upload',
        videoLinkHelp: 'Enter a YouTube, Vimeo, or other video platform URL',
        videoLinkNote: 'Share a link to your introduction video from a supported platform',
        videoUploadHelp: 'Upload your introduction video directly to our platform',
        videoUploadPlaceholder: 'Upload Video',
        videoSelected: 'Selected video',
        videoUploadError: 'Error uploading video',
        formHasErrors: 'Please correct the errors in the form',
        allowedFormats: 'Allowed formats',
        maxFileSize: 'Maximum file size',
        recommendedPlatforms: 'Recommended platforms',
        cv: 'CV/Resume',
        cvPlaceholder: 'write your CV',
        cvHelperText: 'Write a brief summary of your qualifications, experience, and teaching approach (max 2000 characters)',
        characters: 'characters',
        profilePicture: 'Profile Picture',
        profilePicturePlaceholder: 'Upload your profile picture',
        profilePictureRequired: 'Profile picture is required',
        availableHours: 'Available Teaching Hours',
        availableHoursDescription: 'Select the available times for teaching on each day of the week',
        availableHoursProfileDescription: 'Manage your teaching availability to let students know when you are available for lessons',
        viewAvailableHours: 'View Available Hours',
        viewAvailableHoursDescription: 'Review your current teaching schedule and availability',
        noAvailableHours: 'No available hours have been set yet',
        timeSlot: 'Time Slot',
        timeSlots: 'time slots',
        available: 'Available',
        unavailable: 'Not Available',
        editAvailableHours: 'Edit Available Hours',
        weeklySchedule: 'Weekly Schedule',
        scheduleDescription: 'Your teaching availability throughout the week',
        time: 'Time',
        legend: 'Legend',
        quickStats: 'Quick Statistics',
        totalSlots: 'Total Time Slots',
        hoursPerWeek: 'Hours per Week',
        activeDays: 'Active Days',
        pricePerLesson: 'Price per Lesson',
        pricePerLessonPlaceholder: 'Enter price in USD',
        trialLessonPrice: 'Trial lesson price',
        trialLessonPricePlaceholder: 'Enter trial lesson price in USD',
        trialPriceLessThanRegular: 'Trial lesson price must be less than or equal to regular lesson price',
        timezone: 'Timezone',
        paymentMethod: 'Payment Method',
        phone: 'Phone Number',
        phoneHelp: 'Include country code (e.g., +1, +44, +966)',
        selectedHours: 'hours selected',
        selectAll: 'Select All',
        required: 'Required field',
        formHasErrors: 'Please correct the errors in the form',
        priceRange: 'Price must be between $3 and $100',
        yourEarnings: 'Your earnings after commission:',
        invalidUrl: 'Please enter a valid URL',
        invalidPhone: 'Please enter a valid phone number',
        invalidPrice: 'Please enter a valid price',
        uploadVideoNow: 'Upload Video Now',
        allowedFormats: 'Allowed formats',
        maxFileSize: 'Maximum file size',
        videoFormats: 'MP4, WebM, OGG',
        maxVideoSize: '100MB',
        videoRequired: 'An introduction video is required for your application',
        commitment: 'Teacher Commitment',
        commitmentDescription: 'Please read and agree to the following commitment',
        commitmentProfileDescription: 'View and manage your commitment status and details',
        commitmentRequired: 'You must agree to the commitment to continue',
        commitmentStatus: {
          accepted: 'Commitment accepted',
          pending: 'Commitment pending approval',
          rejected: 'Commitment rejected'
        },
        commitmentTitle: 'Teacher Commitment',
        commitmentAccepted: 'Commitment accepted',
        readCommitment: 'Read and agree to the commitment',
        accept: 'I Agree',
        reject: 'I Decline',
        manageAvailableHours: 'Manage Available Hours',
        weeklyBookings: 'This Week\'s Student Bookings',
        weeklyBookingsDescription: 'View your student bookings for this week in a calendar format.',
        fileTypes: {
          image: 'Supported formats: JPG, PNG (max 5MB)',
          document: 'Supported formats: PDF, DOC, DOCX (max 10MB)'
        },
        submitComplaint: 'Submit Complaint',
        complaintReason: 'Reason for Complaint',
        complaintType1: 'Student attended but commission not transferred',
        complaintType2: 'Student did not attend at all',
        complaintDetails: 'Complaint Details (optional)',
        complaintDetailsPlaceholder: 'Write any additional details if needed...',
        complaintStatus: 'Complaint Status',
        complaintStatusValues: {
          pending: 'Pending',
          resolved: 'Resolved'
        },
      },
      role: {
        admin: 'Admin',
        teacher: 'Teacher',
        student: 'Student'
      },
      validation: {
        required: 'This field is required',
        email: 'Please enter a valid email address',
        password: {
          min: 'Password must be at least {{min}} characters',
          max: 'Password must be at most {{max}} characters',
          match: 'Passwords do not match'
        }
      },
      days: {
        monday: 'Monday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday',
        saturday: 'Saturday',
        sunday: 'Sunday',
        mondayShort: 'Mon',
        tuesdayShort: 'Tue',
        wednesdayShort: 'Wed',
        thursdayShort: 'Thu',
        fridayShort: 'Fri',
        saturdayShort: 'Sat',
        sundayShort: 'Sun'
      },
      hours: {
        hour1: '1:00 AM',
        hour2: '2:00 AM',
        hour3: '3:00 AM',
        hour4: '4:00 AM',
        hour5: '5:00 AM',
        hour6: '6:00 AM',
        hour7: '7:00 AM',
        hour8: '8:00 AM',
        hour9: '9:00 AM',
        hour10: '10:00 AM',
        hour11: '11:00 AM',
        hour12: '12:00 PM',
        hour13: '1:00 PM',
        hour14: '2:00 PM',
        hour15: '3:00 PM',
        hour16: '4:00 PM',
        hour17: '5:00 PM',
        hour18: '6:00 PM',
        hour19: '7:00 PM',
        hour20: '8:00 PM',
        hour21: '9:00 PM',
        hour22: '10:00 PM',
        hour23: '11:00 PM',
        hour24: '12:00 AM'
      },
      student: {
        profile: {
          title: 'Student Profile',
          complete: 'Complete Your Profile',
          languagePreferences: 'Language Preferences',
          personalInfo: 'Personal Information',
          learningPreferences: 'Learning Preferences',
          nativeLanguage: 'Native Language',
          preferredIslamicLanguage: 'Preferred Language for Learning Islam',
          preferredArabicLanguage: 'Preferred Language for Learning Arabic',
          islamLearningLanguage: 'Language for Learning Islam',
          arabicLearningLanguage: 'Language for Learning Arabic',
          age: 'Age',
          country: 'Country',
          timezone: 'Timezone',
          arabicLevel: 'Arabic Proficiency Level',
          privateTutoring: 'I am interested in private tutoring',
          preferGroup: 'NO',
          preferPrivate: 'YES',
          updateSuccess: 'Profile updated successfully',
          editInfo: 'Edit Profile Information',
          levels: {
            beginner: 'Beginner',
            intermediate: 'Intermediate',
            advanced: 'Advanced'
          },
          success: 'Profile updated successfully! Redirecting to dashboard...'
        },
        myTeachers: 'My Teachers',
        noTeachersYet: 'You have no teachers yet.',
        lessonsTaken: 'Lessons Taken'
      },
      admin: {
        dashboard: {
          title: 'Dashboard',
          welcomeMessage: 'Welcome to Admin Dashboard',
          overview: 'Here\'s an overview of your platform statistics and recent activities.',
          totalTeachers: 'Total Teachers',
          pendingApplications: 'Pending Applications',
          totalStudents: 'Total Students',
          totalRevenue: 'Total Revenue',
          totalCourseCategories: 'Total Categories',
          totalLanguages: 'Total Languages',
          totalBookings: 'Total Bookings',
          bookingStats: 'Booking Statistics',
          teachersByLanguage: 'Teachers by Language',
          studentsByCountry: 'Students by Country',
          recentApplications: 'Recent Applications',
          recentStudents: 'Recent Students',
          viewAll: 'View All',
          viewDetails: 'View Details',
          noApplications: 'No recent applications',
          noStudents: 'No recent students',
          noStudentsByCountry: 'No student country data available',
          noTeachersByLanguage: 'No teacher language data available',
          noBookingStats: 'No booking data available',
          completed: 'Completed',
          cancelled: 'Cancelled',
          pending: 'Pending',
          fetchError: 'Error fetching dashboard data'
        },
        meetingSessions: {
          title: 'Meeting Time Reports',
          overview: 'Detailed view of teachers and students join/leave times in meetings',
          totalMeetings: 'Total Meetings',
          totalTime: 'Total Time',
          teacherTime: 'Teacher Time',
          studentTime: 'Student Time',
          filterResults: 'Filter Results',
          userType: 'User Type',
          all: 'All',
          teacher: 'Teacher',
          student: 'Student',
          reset: 'Reset',
          sessions: 'Meeting Sessions',
          user: 'User',
          type: 'Type',
          meeting: 'Meeting',
          joinTime: 'Join Time',
          leaveTime: 'Leave Time',
          duration: 'Duration',
          status: 'Status',
          active: 'Active',
          ended: 'Ended'
        },
        meetingIssues: {
          title: 'Meeting Issues'
        },
        messages: {
          title: 'Messages',
          from: 'From',
          type: 'Type',
          subject: 'Subject',
          date: 'Date',
          status: 'Status',
          pending: 'Pending',
          answered: 'Answered',
          all: 'All Messages',
          view: 'View',
          reply: 'Reply',
          delete: 'Delete',
          search: 'Search messages...',
          noMessages: 'No messages found',
          fetchError: 'Error fetching messages',
          replyTo: 'Reply to',
          originalMessage: 'Original Message',
          yourReply: 'Your Reply',
          sendReply: 'Send Reply',
          replySent: 'Reply sent successfully',
          replyError: 'Error sending reply',
          confirmDelete: 'Confirm Delete',
          deleteWarning: 'Are you sure you want to delete this message? This action cannot be undone.',
          deleteSuccess: 'Message deleted successfully',
          deleteError: 'Error deleting message'
        },
        emailTemplates: {
          title: 'Email Templates',
          description: 'Manage email templates for various system notifications.',
          instructions: 'Edit the templates below to customize the emails sent to users. You can use HTML and variables to personalize the content.',
          approvalTemplate: 'Application Approval Template',
          rejectionTemplate: 'Application Rejection Template',
          save: 'Save Template',
          saveSuccess: 'Template saved successfully',
          saveError: 'Error saving template',
          fetchError: 'Error fetching templates',
          restoreDefault: 'Restore Default',
          preview: 'Preview',
          enterTemplate: 'Enter template content here...',
          availableVariables: 'Available Variables',
          variablesHelp: 'These variables will be replaced with actual values when the email is sent.',
          testEmail: 'Test Email',
          testEmailPlaceholder: 'Enter email address to test',
          sendTest: 'Send Test Email',
          testEmailSuccess: 'Test email sent successfully',
          testEmailError: 'Error sending test email',
          variables: {
            teacherName: 'Teacher\'s full name',
            teacherEmail: 'Teacher\'s email address',
            dashboardUrl: 'URL to the teacher dashboard',
            rejectionReason: 'Reason for application rejection'
          }
        },
        earnings: {
          title: 'Platform Earnings',
          totalCommission: 'Total Commission',
          totalLessons: 'Total Lessons',
          totalRevenue: 'Total Revenue',
          avgCommissionRate: 'Avg Commission Rate',
          startDate: 'Start Date',
          endDate: 'End Date',
          filter: 'Filter',
          clearFilter: 'Clear Filter',
          date: 'Date',
          meeting: 'Meeting',
          teacher: 'Teacher',
          student: 'Student',
          lessonAmount: 'Lesson Amount',
          commissionRate: 'Commission Rate',
          commissionAmount: 'Commission Amount',
          teacherEarnings: 'Teacher Earnings',
          errorFetching: 'Error fetching earnings data'
        },
        passwordManagement: {
          title: 'Password Management',
          description: 'Manage password reset requests and user passwords',
          searchPlaceholder: 'Search by name or email',
          user: 'User',
          email: 'Email',
          role: 'Role',
          requestDate: 'Request Date',
          status: 'Status',
          actions: 'Actions',
          resetPassword: 'Reset Password',
          resetPasswordFor: 'Reset Password for {name}',
          newPassword: 'New Password',
          generatePassword: 'Generate Password',
          resetSuccess: 'Password reset successfully for {email}',
          resetError: 'Error resetting password',
          noRequests: 'No password reset requests found',
          statusPending: 'Pending',
          statusCompleted: 'Completed',
          sendTestEmail: 'Send Test Email',
          testEmailAddress: 'Test Email Address',
          testEmailDescription: 'Send a test password reset email to verify the email template and delivery',
          sendEmail: 'Send Email',
          testEmailSent: 'Test email sent successfully to {email}',
          testEmailError: 'Error sending test email'
        },
        profile: {
          personalInfo: 'Personal Information',
          changePassword: 'Change Password',
          currentPassword: 'Current Password',
          newPassword: 'New Password',
          confirmNewPassword: 'Confirm New Password',
          updateSuccess: 'Profile updated successfully',
          updateError: 'Error updating profile',
          passwordSuccess: 'Password changed successfully',
          passwordError: 'Error changing password',
          imageSuccess: 'Profile image updated successfully',
          imageError: 'Error updating profile image'
        },
        teacherRole: {
          platform_teacher: 'Platform Teacher',
          new_teacher: 'New Teacher'
        },
        teachers: {
          title: 'Teachers',
          searchPlaceholder: 'Search teachers...',
          name: 'Name',
          email: 'Email',
          gender: 'Gender',
          role: 'Role',
          actions: 'Actions',
          viewDetails: 'View Details',
          teacherDetails: 'Teacher Details',
          personalInfo: 'Personal Information',
          deleteConfirm: 'Are you sure you want to delete this teacher?',
          deleteSuccess: 'Teacher deleted successfully',
          deleteError: 'Error deleting teacher',
          fetchError: 'Error fetching teachers'
        },
        students: {
          title: 'Students',
          searchPlaceholder: 'Search students...',
          name: 'Name',
          email: 'Email',
          gender: 'Gender',
          country: 'Country',
          age: 'Age',
          timezone: 'Timezone',
          actions: 'Actions',
          viewDetails: 'View Details',
          studentDetails: 'Student Details',
          personalInfo: 'Personal Information',
          learningPreferences: 'Learning Preferences',
          nativeLanguage: 'Native Language',
          islamLearningLanguage: 'Islam Learning Language',
          arabicLearningLanguage: 'Arabic Learning Language',
          arabicProficiencyLevel: 'Arabic Proficiency Level',
          privateTutoring: 'Private Tutoring',
          profileCompleted: 'Profile Completed',
          deleteConfirm: 'Are you sure you want to delete this student?',
          deleteSuccess: 'Student deleted successfully',
          deleteError: 'Error deleting student',
          fetchError: 'Error fetching students',
          noStudents: 'No students found',
          delete: 'Delete',
          proficiencyLevels: {
            beginner: 'Beginner',
            intermediate: 'Intermediate',
            advanced: 'Advanced'
          }
        },
        categories: {
          title: 'Categories',
          addNew: 'Add New Category',
          editTitle: 'Edit Category',
          addTitle: 'Add Category',
          name: 'Name',
          description: 'Description',
          createdBy: 'Created By',
          updateSuccess: 'Category updated successfully',
          createSuccess: 'Category created successfully',
          deleteSuccess: 'Category deleted successfully',
          deleteConfirm: 'Are you sure you want to delete this category?',
          fetchError: 'Error fetching categories',
          saveError: 'Error saving category',
          deleteError: 'Error deleting category',
          unauthorized: 'Unauthorized access',
          invalidId: 'Invalid category ID'
        },
        languages: {
          title: 'Languages',
          addNew: 'Add New Language',
          editTitle: 'Edit Language',
          addTitle: 'Add Language',
          name: 'Language Name',
          nameRequired: 'Language name is required',
          unauthorized: 'You are not authorized to perform this action',
          fetchError: 'Error fetching languages',
          createSuccess: 'Language created successfully',
          createError: 'Error creating language',
          updateSuccess: 'Language updated successfully',
          updateError: 'Error updating language',
          deleteSuccess: 'Language deleted successfully',
          deleteError: 'Error deleting language',
          deleteConfirm: 'Are you sure you want to delete this language?',
          invalidId: 'Invalid language ID',
          saveError: 'Error saving language'
        },
        applications: {
          searchPlaceholder: 'Search applications...',
          filterByStatus: 'Filter by status',
          name: 'Name',
          email: 'Email',
          phone: 'Phone',
          country: 'Country',
          languages: 'Languages',
          status: 'Status',
          actions: 'Actions',
          statuses: {
            pending: 'Pending',
            approved: 'Approved',
            rejected: 'Rejected'
          },
          title: 'Teacher Applications',
          allStatuses: 'All Statuses',
          viewDetails: 'View Details',
          approve: 'Approve',
          reject: 'Reject',
          applicationDetails: 'Application Details',
          personalInfo: 'Personal Information',
          teachingInfo: 'Teaching Information',
          documents: 'Documents',
          nativeLanguage: 'Native Language',
          teachingLanguages: 'Teaching Languages',
          qualifications: 'Qualifications',
          experience: 'Experience',
          pricePerLesson: 'Price per Lesson',
          viewVideo: 'View Introduction Video',
          viewCV: 'View CV',
          basicInfo: 'Basic Info',
          teachingDetails: 'Teaching Details',
          schedule: 'Schedule',
          location: 'Location',
          applicationDate: 'Application Date',
          courseTypes: 'Course Types',
          pricing: 'Pricing',
          paymentMethod: 'Payment Method',
          introVideo: 'Introduction Video',
          cv: 'CV/Resume',
          availableHours: 'Available Hours',
          yearsOfExperience: '{{years}} years of experience'
        }
      },
      menu: {
        dashboard: 'Dashboard',
        meetings: 'Meetings',
        profile: 'Profile',
        chat: 'Chat',
        platformPolicy: 'Platform Policy'
      },
      meetings: {
        title: 'Meetings',
        myMeetings: 'My Meetings',
        description: 'View and manage your scheduled meetings with teachers',
        noMeetings: 'No meetings scheduled',
        noMeetingsDescription: 'You don\'t have any meetings scheduled yet. Book a lesson to get started!',
        teacher: 'Teacher',
        date: 'Date',
        time: 'Time',
        fetchError: 'Error fetching meetings',
        with: 'with',
        createNew: 'Create New Meeting',
        meetingName: 'Meeting Name',
        meetingDate: 'Meeting Date',
        duration: 'Duration',
        minutes: 'Minutes',
        create: 'Create Meeting',
        start: 'Start Meeting',
        join: 'Join Meeting',
        notStarted: 'Meeting not started yet',
        ended: 'Meeting ended',
        joinError: 'Error joining meeting',
        cancel: 'Cancel Meeting',
        copyLink: 'Copy Room Code',
        linkCopied: 'Room code copied to clipboard',
        cancelSuccess: 'Meeting cancelled successfully',
        cancelError: 'Error cancelling meeting',
        createSuccess: 'Meeting created successfully',
        createError: 'Error creating meeting',
        halfLesson: 'Half Lesson',
        fullLesson: 'Full Lesson',
        consecutiveSlots: 'Consecutive Slots',
        currency: 'USD',
        status: {
          pending: 'Pending',
          ongoing: 'Ongoing',
          completed: 'Completed',
          cancelled: 'Cancelled',
          scheduled: 'Scheduled'
        },
        meetingCompleted: 'Meeting completed successfully',
        errorCompletingMeeting: 'Error completing the meeting',
        participant: 'Participant',
        you: 'You',
        sessionTime: 'Session Time',
        remainingTime: 'Time Remaining',
        timeUp: 'Time Up',
        timeUpTitle: 'Meeting Time Ended',
        timeUpMessage: 'The scheduled meeting time has ended. The meeting will now be closed automatically.',
        timeUpDescription: 'You will be redirected back to your bookings page.',
        backToBookings: 'Back to Bookings',
        active: 'Active',
        stopped: 'Stopped',
        validation: {
          nameRequired: 'Meeting name is required',
          dateRequired: 'Meeting date is required',
          durationRequired: 'Duration is required',
          allFieldsRequired: 'All fields are required'
        },
        student: 'Student',
        dateTime: 'Date & Time',
        amount: 'Amount',
        actions: 'Actions'
      },
      privacy: {
        title: 'Privacy Policy',
        intro: 'This Privacy Policy explains how Allemnionline ("we", "our", or "us") collects, uses, and protects the personal information of users ("you") who visit our website or use our educational services.',
        section1: {
          title: '1. Information We Collect',
          subtitle: 'We may collect the following types of personal information:',
          item1: 'Name, email address, and phone number',
          item2: 'Payment and billing details',
          item3: 'User account information (e.g., language level, lesson history)'
        },
        section2: {
          title: '2. How We Use Your Information',
          subtitle: 'We use your information to:',
          item1: 'Provide and improve our educational services',
          item2: 'Manage scheduling and payments',
          item3: 'Communicate with you regarding your lessons and account',
          item4: 'Ensure platform security and prevent fraud',
          item5: 'Comply with legal and regulatory obligations'
        },
        section3: {
          title: '3. How We Share Your Information',
          subtitle: 'We do not sell your personal information.',
          description: 'We may share it with:',
          item1: 'Payment processors (e.g., Stripe)',
          item2: 'Legal authorities if required by law',
          item3: 'Trusted service providers under confidentiality agreements'
        },
        section4: {
          title: '4. Data Security',
          content: 'We implement appropriate technical and organizational measures to protect your data from unauthorized access, alteration, or disclosure.'
        },
        section5: {
          title: '5. Your Rights',
          subtitle: 'You have the right to:',
          item1: 'Access your personal data',
          item2: 'Request correction or deletion',
          item3: 'Withdraw consent for data processing',
          item4: 'File a complaint with a data protection authority (where applicable)',
          contact: 'To exercise your rights, contact us at: <EMAIL>'
        },
        section6: {
          title: '6. Cookies and Tracking',
          content: 'Our website may use cookies to improve your browsing experience. You can adjust cookie settings in your browser preferences.'
        },
        section7: {
          title: '7. Children\'s Privacy',
          content: 'We do not knowingly collect data from children under 13 without parental consent. If you believe we have collected such data, please contact us immediately.'
        },
        section8: {
          title: '8. Third-Party Links',
          content: 'Our site may contain links to third-party services. We are not responsible for the privacy practices of those websites.'
        },
        section9: {
          title: '9. Changes to This Policy',
          content: 'We may update this policy from time to time. We encourage you to review it periodically. Any changes will be posted on this page with an updated effective date.'
        },
        section10: {
          title: '10. Contact Us',
          content: 'For questions or concerns regarding this policy, please contact:',
          email: '<EMAIL>'
        }
      },
      policies: {
        refund: {
          title: 'Refund Policy',
          section1: {
            title: 'First: Cases where students are granted lesson refunds',
            description: 'Students are entitled to have their lesson refunded to their platform balance for rescheduling in the following cases:',
            items: [
              'If the teacher cancels the lesson or is absent at the scheduled time.',
              'If the lesson cannot be held due to a technical failure on the part of the teacher or platform.'
            ]
          },
          section2: {
            title: 'Second: Cases where lesson refunds or rescheduling are not granted',
            description: 'Students are not entitled to request a lesson refund or rescheduling in the following cases:',
            items: [
              'Student cancels the lesson within less than 12 hours of its scheduled time.',
              'Student is absent from attending the lesson without prior notice.',
              'Loss of connection due to weak internet or malfunction in the student\'s device.',
              'Forgetting login credentials (username or password).',
              'Balance expiration due to not using the platform for more than 180 days.',
              'Student voluntarily deletes their account.',
              'Account suspension due to violation of terms of use.'
            ]
          },
          section3: {
            title: 'Third: Refund policy updates',
            description: 'The platform reserves the right to modify this policy at any time. Continued use of the platform after policy updates constitutes implicit agreement to what is stated therein.'
          },
          contact: {
            title: 'Contact Us',
            email: '📧 <EMAIL>'
          }
        },
        bookingPayment: {
          title: 'Booking and Payment Policy',
          subtitle: 'This policy explains how lessons are scheduled, confirmed, and paid for through the Allemnionline platform.',
          section1: {
            title: '1. Lesson Booking',
            points: [
              'Students can book one-on-one lessons with available tutors directly through the platform.',
              'Lesson times are displayed according to the student\'s local time zone.',
              'Bookings must be made in advance and are subject to tutor availability.',
              'Once the booking and payment are completed, a confirmation email will be sent to both the student and the tutor.'
            ]
          },
          section2: {
            title: '2. Payments',
            points: [
              'All lessons must be paid for in advance to confirm the booking.',
              'Payments are securely processed through trusted payment providers (e.g., Stripe, PayPal, or Wise).',
              'Students are responsible for any transaction fees or commissions charged by their bank or payment provider.',
              'Lesson prices are clearly displayed before completing the payment.'
            ]
          },
          section3: {
            title: '3. Currency and Exchange Rates',
            points: [
              'Payments are made in USD by default.',
              'Prices shown in other currencies are for reference only.',
              'If your payment method uses a different currency, conversion fees and exchange rate differences may apply.',
              'Allemnionline is not responsible for currency fluctuations or additional banking fees.'
            ]
          },
          section4: {
            title: '4. Taxes and Fees',
            points: [
              'Prices may include applicable local taxes (such as VAT or service tax), depending on the student\'s location.',
              'All applicable fees are displayed transparently before payment is finalized.'
            ]
          },
          section5: {
            title: '5. Payment Confirmation and Receipts',
            points: [
              'Once the payment is successfully processed, a digital receipt will be emailed to the student.',
              'A lesson is considered confirmed only after payment is completed.'
            ]
          },
          section6: {
            title: '6. Failed or Delayed Payments',
            points: [
              'If the payment fails or is delayed, the booking will be marked as "pending" and not confirmed.',
              'Students must resolve any payment issues promptly to maintain their booking.'
            ]
          },
          section7: {
            title: '7. Automatic Payments or Subscriptions (if applicable)',
            points: [
              'If a student chooses to activate subscriptions or auto-refill, charges will be automatically deducted from their saved payment method.',
              'Subscriptions can be canceled or modified anytime from the account settings.'
            ]
          },
          section8: {
            title: '8. Support and Contact',
            description: 'For any questions or issues related to payment, please contact our support team:',
            email: '<EMAIL>'
          },
          features: {
            securePayments: 'Secure Payments',
            securePaymentsDesc: 'All transactions are protected with advanced encryption technologies to ensure the security of your financial data.',
            multipleCurrencies: 'Multiple Currencies',
            multipleCurrenciesDesc: 'We support a wide range of currencies and payment methods to make the payment process convenient for you.',
            instantReceipts: 'Instant Receipts',
            instantReceiptsDesc: 'You receive an instant digital receipt after every successful payment transaction.',
            instantConfirmation: 'Instant Confirmation',
            instantConfirmationDesc: 'Bookings are confirmed immediately after successful payment completion.'
          }
        },
        bookingCancellation: {
          title: 'Booking, Cancellation, and Rescheduling Policy',
          subtitle: 'This policy outlines the rules and procedures for scheduling, canceling, and modifying one-on-one lessons by both students and tutors, in order to ensure a professional and effective learning experience.',
          studentPolicy: {
            title: '1. Student Policy',
            booking: {
              title: '1.1 Booking Lessons',
              points: [
                'Students may book any available time slot from the tutor\'s schedule.',
                'Bookings are confirmed immediately upon successful payment through the platform.'
              ]
            },
            cancellation: {
              title: '1.2 Lesson Cancellation',
              points: [
                'Students may cancel a lesson free of charge if the cancellation is made at least 12 hours before the scheduled time.',
                'If the cancellation occurs less than 12 hours before the lesson, the full lesson fee will be charged.',
                'If the student fails to attend the lesson without prior cancellation, the lesson will be considered completed and non-refundable.'
              ]
            },
            rescheduling: {
              title: '1.3 Rescheduling a Lesson',
              points: [
                'Each lesson can be rescheduled only once, and the request must be made at least 12 hours before the original lesson time.',
                'Students may also contact the tutor directly to request rescheduling even less than 12 hours in advance, but the decision will be at the tutor\'s discretion.'
              ]
            },
            lateArrival: {
              title: '1.4 Late Arrival',
              points: [
                'Students have a 15-minute grace period after the scheduled start time.',
                'If the student does not show up within 15 minutes without prior notice, the lesson will be marked as completed and the fee will not be refunded.'
              ]
            }
          },
          tutorPolicy: {
            title: '2. Tutor Policy',
            availability: {
              title: '2.1 Availability',
              points: [
                'Tutors must regularly update their schedules and clearly define their available time slots for lessons.'
              ]
            },
            cancellation: {
              title: '2.2 Lesson Cancellation or Rescheduling',
              points: [
                'Tutors must notify the student as early as possible if they need to cancel or reschedule a lesson.',
                'Repeated cancellations or no-shows by the tutor may lead to temporary account suspension or other administrative actions.'
              ]
            },
            rescheduling: {
              title: '2.3 Modifying Lesson Times',
              points: [
                'Tutors may cancel or modify a lesson time only if the change is made more than 12 hours in advance.',
                'Any rescheduling must be communicated to the student through the platform along with a clear explanation.'
              ]
            },
            lateArrival: {
              title: '2.4 Late Arrival',
              points: [
                'Tutors are allowed a maximum delay of 15 minutes.',
                'If the tutor fails to attend the lesson within that time, the student will be entitled to either a full refund or a free rescheduling.'
              ]
            }
          },
          generalNotes: {
            title: 'General Notes',
            points: [
              'All time calculations are based on the student\'s local time zone.',
              'The platform reserves the right to modify this policy in a way that best serves the educational process. Users will be notified of any official changes.'
            ]
          },
          summary: {
            forStudents: 'For Students',
            forTutors: 'For Tutors',
            freeCancellation: 'Free cancellation 12+ hours before',
            gracePeriod: '15-minute grace period',
            oneReschedule: 'One reschedule per booking',
            cancellationBefore: 'Cancellation 12+ hours before',
            delayAllowance: '15-minute delay allowance',
            immediateNotification: 'Immediate student notification',
            importantNote: 'Important Note: All times are calculated based on the student\'s local time zone'
          }
        }
      },
      contact: {
        title: 'Contact Us',
        subtitle: 'We are happy to hear from you through the following channels for any inquiries related to our educational services, lesson schedules, or technical support:',
        mailingAddress: {
          title: '📍 Mailing Address',
          company: 'Allemnionline',
          address: '30 N Gould St, Ste R, Sheridan, WY 82801'
        },
        email: {
          title: '📧 Official Email',
          address: '<EMAIL>',
          note: 'We welcome all inquiries and aim to respond within 24 hours.'
        },
        phone: {
          title: '☎️ Phone / WhatsApp',
          number: '0019179937166',
          note: 'Available during official working hours, from 9:00 AM to 5:00 PM (Mecca Time).'
        },
        website: {
          title: '🌐 Website',
          url: 'www.allemnionline.com'
        }
      },
      platformPolicies: 'Platform Policies',
      about: {
        title: 'About Us',
        intro: 'Allemnionline is an online educational platform specialized in providing Arabic language teaching services and knowledge related to Arabic culture to learners from around the world, through direct one-on-one lessons delivered by qualified and professional teachers.',
        mission: 'The platform seeks to make learning Arabic accessible and effective, while considering individual differences and special needs of each learner, using the latest educational technological tools.',
        whatWeOffer: 'What We Offer',
        services: {
          privateLessons: 'Private Arabic language lessons for all levels: from beginners to advanced.',
          conversationTraining: 'Training in conversation, listening, reading, and writing in Arabic.',
          culturalElements: 'Introduction to essential elements of Arabic culture that help understand the language in its natural context.',
          digitalPlatform: 'An integrated digital platform that enables direct communication via video, lesson scheduling, and secure electronic payment.',
          targetAudience: 'Our services are directed to children, adults, professionals, and anyone who wants to learn Arabic for academic, personal, or professional purposes.'
        },
        ourMission: 'Our Mission',
        missionText: 'To provide distinguished education in Arabic language and related cultural knowledge, with high quality, through direct teaching and modern technologies, in a manner that respects the diversity of learners and their cultural particularities.',
        contactUs: 'Contact Us',
        contactText: 'For any inquiries or comments, please contact us via email:',
        email: '<EMAIL>'
      }
    }
  },
  ar: {
    translation: {
      appName: 'علّمني أون لاين',
      aboutUs: 'نبذة عنا',
      wallet: {
        title: 'المحفظة',
        balance: 'الرصيد',
        currentBalance: 'الرصيد الحالي',
        transactionHistory: 'سجل المعاملات',
        allTransactions: 'جميع المعاملات',
        noTransactions: 'لم يتم العثور على معاملات',
        errorFetchingTransactions: 'خطأ في جلب المعاملات. يرجى المحاولة مرة أخرى.',
        date: 'التاريخ',
        description: 'الوصف',
        amount: 'المبلغ',
        status: 'الحالة',
        debit: 'خصم',
        credit: 'إضافة',
        pending: 'قيد الانتظار',
        completed: 'مكتمل',
        failed: 'فاشل',
        cancelled: 'ملغي',
        payment: 'دفع',
        lessonWith: 'درس مع {{teacher}}',
        lessonFrom: 'درس من {{student}}',
        student: 'الطالب',
        teacher: 'المعلم',
        rowsPerPage: 'عدد الصفوف في الصفحة',
        addMoney: 'إضافة أموال',
        payWithStripe: 'الدفع بالبطاقة الائتمانية',
        pay: 'دفع',
        depositSuccess: 'تم الإيداع بنجاح! تم تحديث محفظتك.',
        errorProcessingPayment: 'خطأ في معالجة الدفع. يرجى المحاولة مرة أخرى.',
        paypalDeposit: 'إيداع PayPal',
        stripeDeposit: 'إيداع Stripe',
        walletDeposit: 'إيداع محفظة',
        enterOTPTitle: 'أدخل رمز التحقق',
        otpInstructions: 'الرجاء إدخال رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني.',
        otpCode: 'رمز التحقق',
        verify: 'تحقق',
        enterOTP: 'الرجاء إدخال رمز التحقق',
        errorVerifyingOTP: 'خطأ في التحقق من الرمز',
      },
      withdrawal: {
        title: 'سحب الأموال',
        availableBalance: 'الرصيد المتاح',
        requestWithdrawal: 'طلب سحب',
        withdrawalHistory: 'تاريخ السحب',
        noWithdrawals: 'لا توجد طلبات سحب',
        date: 'التاريخ',
        amount: 'المبلغ',
        paypalEmail: 'بريد PayPal الإلكتروني',
        status: 'الحالة',
        actions: 'الإجراءات',
        cancel: 'إلغاء',
        submit: 'إرسال',
        pending: 'قيد الانتظار',
        processing: 'قيد المعالجة',
        completed: 'مكتمل',
        failed: 'فشل',
        cancelled: 'ملغي',
        fillAllFields: 'يرجى ملء جميع الحقول المطلوبة',
        minimumAmount: 'الحد الأدنى للسحب هو ${{amount}}',
        insufficientBalance: 'رصيد غير كافي',
        requestSubmitted: 'تم إرسال طلب السحب بنجاح',
        requestCancelled: 'تم إلغاء طلب السحب بنجاح',
        errorSubmitting: 'خطأ في إرسال طلب السحب',
        errorCancelling: 'خطأ في إلغاء طلب السحب',
        errorFetchingWithdrawals: 'خطأ في جلب تاريخ السحب',
        paypalEmailHelp: 'أدخل بريد PayPal الإلكتروني حيث تريد استلام الدفعة',
        processingTime: 'وقت المعالجة: {{days}} أيام عمل',
        minimumWithdrawal: 'الحد الأدنى للسحب',
        enterOTPTitle: 'أدخل رمز التحقق',
        otpInstructions: 'الرجاء إدخال رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني.',
        otpCode: 'رمز التحقق',
        verify: 'تحقق',
        enterOTP: 'الرجاء إدخال رمز التحقق',
        errorVerifyingOTP: 'خطأ في التحقق من الرمز',
        requestCompleted: 'تم اكتمال طلب السحب بنجاح',
        withdrawalCancelled: 'تم إلغاء طلب السحب بسبب عدة محاولات فاشلة',
        errorCancelling: 'خطأ في إلغاء طلب السحب',
      },
      admin: {
        withdrawalManagement: {
          title: 'إدارة طلبات السحب',
          statusFilter: 'تصفية الحالة',
          all: 'الكل',
          date: 'التاريخ',
          teacher: 'المعلم',
          amount: 'المبلغ',
          paypalEmail: 'بريد PayPal الإلكتروني',
          status: 'الحالة',
          notes: 'الملاحظات',
          actions: 'الإجراءات',
          approve: 'موافقة',
          reject: 'رفض',
          approveWithdrawal: 'الموافقة على السحب',
          rejectWithdrawal: 'رفض السحب',
          notesOptional: 'ملاحظات (اختيارية)',
          approveAndProcess: 'موافقة ومعالجة',
          processingInfo: 'سيتم معالجة السحب عبر PayPal Payouts API.',
          errorFetching: 'خطأ في جلب طلبات السحب',
          errorProcessing: 'خطأ في معالجة السحب'
        }
      },
      contactUs: {
        title: 'تواصل معنا',
        type: 'نوع الرسالة',
        typeQuestion: 'سؤال',
        typeProblem: 'مشكلة',
        typeSuggestion: 'اقتراح',
        typePayment: 'مشكلة في الدفع',
        typeOther: 'أخرى',
        subject: 'الموضوع',
        message: 'الرسالة',
        send: 'إرسال الرسالة',
        messageSent: 'تم إرسال رسالتك بنجاح. سنرد عليك قريبًا.',
        fillAllFields: 'يرجى ملء جميع الحقول المطلوبة',
        sendError: 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى لاحقًا.',
        startConversation: 'ابدأ محادثة معنا'
      },
      myMessages: {
        title: 'رسائلي',
        subject: 'الموضوع',
        type: 'النوع',
        date: 'التاريخ',
        status: 'الحالة',
        pending: 'قيد الانتظار',
        answered: 'تم الرد',
        noMessages: 'لا توجد رسائل',
        fetchError: 'خطأ في جلب الرسائل',
        yourMessage: 'رسالتك',
        adminReply: 'رد الإدارة',
        awaitingReply: 'في انتظار الرد من الإدارة...'
      },
      booking: {
        title: 'حجز درس',
        bookLessonWith: 'حجز درس مع',
        pricePerLesson: 'سعر الدرس',
        instructions: 'تعليمات الحجز',
        instructionsText: 'اختر وقتًا من الأوقات المتاحة أدناه. بمجرد تأكيد الحجز، سيتم خصم المبلغ من رصيد محفظتك.',
        selectTimeSlot: 'اختر وقتًا',
        noAvailableSlots: 'لا توجد أوقات متاحة لهذا اليوم',
        teacher: 'المعلم',
        day: 'اليوم',
        date: 'التاريخ',
        time: 'الوقت',
        price: 'السعر',
        confirmBooking: 'تأكيد الحجز',
        confirmAndPay: 'تأكيد والدفع',
        bookingSuccessTitle: 'تم الحجز بنجاح!',
        bookingSuccessMessage: 'تم حجز درسك بنجاح. يمكنك عرض حجوزاتك في قسم حجوزاتي.',
        backToTeacher: 'العودة إلى المعلم',
        viewMyBookings: 'عرض حجوزاتي',
        bookAgain: 'احجز مرة أخرى',
        bookingFailed: 'فشل الحجز. يرجى المحاولة مرة أخرى.',
        insufficientBalance: 'رصيد غير كافٍ. يرجى إضافة أموال إلى محفظتك.',
        currentWeek: 'الأسبوع الحالي',
        weekOf: 'أسبوع',
        previousWeek: 'الأسبوع السابق',
        nextWeek: 'الأسبوع التالي',
        weekNavigation: 'التنقل بين الأسابيع',
        selectTimeRange: 'اختر المدة الزمنية',
        startTime: 'وقت البداية',
        endTime: 'وقت النهاية',
        lessonDuration: 'مدة الدرس',
        from: 'من',
        to: 'إلى',
        availableSlot: 'خانة متاحة',
        selectDuration: 'اختر المدة',
        selectDuration: 'اختر مدة الدرس',
        duration: 'المدة',
        fullLesson: 'درس كامل ({{duration}} دقيقة)',
        halfLesson: 'نصف درس ({{duration}} دقيقة)',
        meetingCreated: 'تم إنشاء الاجتماع',
        meetingAccessInfo: 'يمكنك الوصول إلى هذا الاجتماع من صفحة الاجتماعات عندما يحين وقت الدرس.',
        viewMyMeetings: 'عرض اجتماعاتي'
      },
      reviews: {
        title: 'التقييمات',
        writeReview: 'كتابة تقييم',
        myReviews: 'تقييماتي',
        teacherReviews: 'تقييمات المعلم',
        rating: 'التقييم',
        comment: 'التعليق',
        submit: 'إرسال التقييم',
        update: 'تحديث التقييم',
        delete: 'حذف التقييم',
        confirmDelete: 'هل أنت متأكد من رغبتك في حذف هذا التقييم؟',
        selectTeacher: 'اختر معلمًا',
        noTeachers: 'لم نتمكن من العثور على معلمين متاحين لكتابة مراجعات لهم. يرجى التأكد من إكمال درس واحد على الأقل قبل كتابة مراجعة.',
        noCompletedLessons: 'لم تأخذ دروسًا مع أي معلمين بعد. يرجى حجز درس وإكماله أولاً.',
        allTeachersReviewed: 'لقد قمت بتقييم جميع المعلمين الذين أخذت دروسًا معهم. يمكنك تحديث تقييماتك الحالية أدناه.',
        allTeachersReviewedShort: 'لقد قمت بتقييم جميع المعلمين',
        noReviews: 'لا توجد تقييمات حتى الآن',
        yourReview: 'تقييمك',
        editReview: 'تعديل التقييم',
        reviewSuccess: 'تم إرسال التقييم بنجاح',
        reviewUpdateSuccess: 'تم تحديث التقييم بنجاح',
        reviewDeleteSuccess: 'تم حذف التقييم بنجاح',
        reviewError: 'خطأ في إرسال التقييم',
        reviewRequired: 'يرجى تقديم تقييم',
        commentPlaceholder: 'شارك تجربتك مع هذا المعلم...',
        averageRating: 'متوسط التقييم',
        totalReviews: '{{count}} تقييمات',
        oneReview: 'تقييم واحد',
        reviewsBy: 'تقييمات الطلاب',
        reviewBy: 'تقييم بواسطة',
        on: 'في',
        stars: 'نجوم',
        star: 'نجمة',
        outOf5: 'من 5',
        selectRating: 'اختر التقييم',
        reviewsFor: 'تقييمات لـ',
        reviewsWritten: 'التقييمات المكتوبة',
        reviewsReceived: 'التقييمات المستلمة',
        viewAll: 'عرض الكل',
        filterBy: 'تصفية حسب',
        sortBy: 'ترتيب حسب',
        newest: 'الأحدث',
        oldest: 'الأقدم',
        highestRated: 'الأعلى تقييمًا',
        lowestRated: 'الأقل تقييمًا',
        noComment: 'لم يتم تقديم تعليق',
        noReviewsWithFilter: 'لا توجد تقييمات تطابق التصفية المحددة',
        teacherReply: 'رد المعلم',
        replyToReview: 'رد على المراجعة',
        editReply: 'تعديل الرد',
        deleteReply: 'حذف الرد',
        replyPlaceholder: 'اكتب ردك على هذه المراجعة...',
        sendReply: 'إرسال الرد',
        updateReply: 'تحديث الرد',
        replySuccess: 'تم إرسال الرد بنجاح',
        replyUpdateSuccess: 'تم تحديث الرد بنجاح',
        replyDeleteSuccess: 'تم حذف الرد بنجاح',
        replyError: 'خطأ في إرسال الرد',
        confirmDeleteReply: 'هل أنت متأكد من حذف هذا الرد؟',
        replyRequired: 'يرجى كتابة رد',
        sending: 'جاري الإرسال...'
      },
      about: {
        title: 'من نحن',
        intro: 'منصة Allemnionline هي منصة تعليمية عبر الإنترنت متخصصة في تقديم خدمات تعليم اللغة العربية والمعرفة ذات الصلة بالثقافة العربية للمتعلمين من جميع أنحاء العالم، من خلال دروس مباشرة (واحد لواحد) يقدمها معلمون مؤهلون ومحترفون.',
        mission: 'تسعى المنصة إلى جعل تعلم اللغة العربية ميسّرًا وفعّالًا، مع مراعاة الفروق الفردية والاحتياجات الخاصة لكل متعلم، وذلك باستخدام أحدث الوسائل التقنية التعليمية.',
        whatWeOffer: 'ماذا نقدم؟',
        services: {
          privateLessons: 'دروس خصوصية في اللغة العربية لجميع المستويات: من المبتدئين إلى المتقدمين.',
          conversationTraining: 'تدريب على المحادثة، الاستماع، القراءة، والكتابة باللغة العربية.',
          culturalElements: 'تعريف بالعناصر الأساسية من الثقافة العربية التي تساعد على فهم اللغة في سياقها الطبيعي.',
          digitalPlatform: 'منصة رقمية متكاملة تتيح التواصل المباشر عبر الفيديو، وجدولة الدروس، والدفع الإلكتروني الآمن.',
          targetAudience: 'خدماتنا موجهة للأطفال، والبالغين، والمهنيين، ولكل من يرغب في تعلم العربية لأغراض أكاديمية أو شخصية أو مهنية.'
        },
        ourMission: 'رسالتنا',
        missionText: 'أن نوفّر تعليمًا متميزًا للغة العربية والمعرفة الثقافية المرتبطة بها، بجودة عالية، ومن خلال التعليم المباشر والتقنيات الحديثة، وبأسلوب يحترم تنوع المتعلمين وخصوصياتهم الثقافية.',
        contactUs: 'للتواصل معنا',
        contactText: 'لأي استفسارات أو ملاحظات، يُرجى التواصل عبر البريد الإلكتروني:',
        email: '<EMAIL>'
      },
      app: {
        name: 'علّمني أون لاين',
        copyright: 'علّمني أون لاين ٢٠٢٥',
        tagline: 'صُمم بحب للتعليم العربي'
      },
      brand: {
        name: 'علّمني أون لاين'
      },
      common: {
        switchLanguage: 'تغيير اللغة',
        loading: 'جاري التحميل...',
        settings: 'الإعدادات',
        profile: 'الملف الشخصي',
        cancel: 'إلغاء',
        back: 'رجوع',
        continue: 'متابعة',
        confirm: 'تأكيد',
        update: 'تحديث',
        success: 'نجاح',
        error: 'خطأ',
        save: 'حفظ',
        saveAndReturn: 'حفظ والعودة',
        saving: 'جاري الحفظ...',
        email: 'البريد الإلكتروني',
        notProvided: 'غير متوفر',
        search: 'بحث',
        close: 'إغلاق',
        password: 'كلمة المرور',
        fullName: 'الاسم الكامل',
        name: 'الاسم',
        confirmPassword: 'تأكيد كلمة المرور',
        showMore: 'عرض المزيد',
        showLess: 'عرض أقل',
        gender: 'الجنس',
        male: 'ذكر',
        female: 'أنثى',
        submit: 'إرسال',
        notSet: 'غير محدد',
        actions: 'الإجراءات',
        edit: 'تعديل',
        delete: 'حذف',
        view: 'عرض',
        details: 'التفاصيل',
        rowsPerPage: 'عدد الصفوف في الصفحة',
        footer: {
          copyright: 'علّمني أون لاين ٢٠٢٥',
          tagline: 'صُمم بحب للتعليم العربي'
        },
        currency: 'دولار',
        details: 'التفاصيل'
      },
      profile: {
        title: 'الملف الشخصي',
        fullName: 'الاسم الكامل',
        edit: 'تعديل الملف الشخصي',
        save: 'حفظ التغييرات',
        cancel: 'إلغاء',
        success: 'تم تحديث الملف الشخصي بنجاح',
        error: 'حدث خطأ أثناء تحديث الملف الشخصي',
        updateSuccess: 'تم تحديث الملف الشخصي بنجاح',
        passwordUpdateSuccess: 'تم تحديث كلمة المرور بنجاح',
        teachingInfoUpdateSuccess: 'تم تحديث معلومات التدريس بنجاح!',
        editTeachingInfo: 'تعديل معلومات التدريس',
        errors: {
          update: 'فشل تحديث الملف الشخصي',
          passwordUpdate: 'فشل تحديث كلمة المرور',
          passwordMismatch: 'كلمات المرور الجديدة غير متطابقة',
          currentPassword: 'كلمة المرور الحالية غير صحيحة',
          fetchError: 'فشل في جلب بيانات الملف الشخصي',
          updateFailed: 'فشل التحديث. يرجى المحاولة مرة أخرى.',
          deleteRequest: 'فشل في إرسال طلب الحذف',
          invalidCode: 'رمز التحقق غير صحيح',
          codeRequired: 'رمز التحقق مطلوب',
          cancelDelete: 'فشل في إلغاء حذف الحساب'
        },
        basicInfo: 'المعلومات الأساسية',
        teacherInfo: 'معلومات المعلم',
        editInfo: 'تعديل المعلومات',
        changePassword: 'تغيير كلمة المرور',
        email: 'البريد الإلكتروني',
        gender: 'الجنس',
        currentPassword: 'كلمة المرور الحالية',
        newPassword: 'كلمة المرور الجديدة',
        confirmPassword: 'تأكيد كلمة المرور',
        editPersonalInfo: 'تعديل المعلومات الشخصية',
        togglePasswordVisibility: 'إظهار/إخفاء كلمة المرور',
        uploadPhoto: 'رفع صورة',
        deleteAccount: 'حذف الحساب',
        deleteAccountTitle: 'حذف الحساب',
        deleteAccountWarning: 'هل أنت متأكد من رغبتك في حذف حسابك؟ هذا الإجراء لا يمكن التراجع عنه.',
        deleteAccountNote: 'سيتم إرسال رمز تحقق إلى عنوان بريدك الإلكتروني.',
        sendDeleteCode: 'إرسال رمز التحقق',
        verifyDeleteCode: 'تحقق من رمز الحذف',
        deleteCodeSentMessage: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني. يرجى إدخاله أدناه:',
        deleteCode: 'رمز التحقق',
        confirmDelete: 'تأكيد الحذف',
        deleteCodeSent: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',
        deletePending: 'حذف الحساب قيد الانتظار (24 ساعة)',
        cancelDelete: 'إلغاء الحذف',
        deleteCancelled: 'تم إلغاء حذف الحساب بنجاح',
        deletePendingAlert: '⚠️ حذف الحساب قيد الانتظار',
        deletePendingMessage: 'حسابك مجدول للحذف. يمكنك إلغاء هذا الإجراء في أي وقت قبل موعد الحذف المحدد.',
        deleteScheduledFor: 'موعد الحذف المجدول',
        updateStatus: {
          pending: 'طلب تعديل البيانات قيد المراجعة',
          approved: 'تم الموافقة على تعديل البيانات',
          rejected: 'تم رفض طلب تعديل البيانات',
          requestDate: 'تاريخ الطلب',
          reviewDate: 'تاريخ المراجعة',
          adminNotes: 'ملاحظات الإدارة',
          pendingNote: 'يرجى انتظار مراجعة الإدارة لطلب التعديل. ستعمل بالبيانات الحالية حتى الموافقة على التعديل.'
        },
        teacher: {
          videoUpload: {
            title: 'رفع فيديو تعريفي',
            description: 'قم برفع فيديو قصير لتقديم نفسك للطلاب المحتملين. سيتم عرض هذا الفيديو على ملفك الشخصي.',
            requirements: 'متطلبات الفيديو',
            formatRequirement: 'الصيغ المسموح بها',
            sizeRequirement: 'الحجم الأقصى',
            lengthRequirement: 'المدة المقترحة',
            maximum: 'كحد أقصى',
            minutes: 'دقائق',
            selectVideo: 'اختر الفيديو',
            upload: 'رفع الفيديو',
            uploading: 'جاري الرفع...',
            success: 'تم رفع الفيديو بنجاح!',
            videoReady: 'فيديو التعريف جاهز للإضافة إلى طلبك.',
            continue: 'متابعة إلى نموذج التقديم',
            delete: 'حذف الفيديو',
            skipForNow: 'تخطي الآن',
            invalidVideoFormat: 'صيغة الفيديو غير صالحة. يرجى اختيار ملف MP4 أو WebM أو OGG.',
            videoTooSmall: 'حجم الفيديو يجب أن يكون على الأقل 1 ميجابايت.',
            videoTooLarge: 'حجم الفيديو يجب ألا يتجاوز 100 ميجابايت.',
            noVideoSelected: 'يرجى اختيار ملف فيديو.',
            videoDeleteError: 'Error deleting video. Please try again.'
          },
          uploadVideoNow: 'رفع الفيديو الآن',
          videoReady: 'فيديو التعريف جاهز للإضافة إلى طلبك',
          deleteVideo: 'حذف الفيديو',
          availableHoursDescription: 'حدد الساعات التي تكون متاحًا فيها للتدريس. سيساعد ذلك الطلاب في العثور على أوقات مناسبة لحجز الدروس معك.',
          availableHoursApplicationDescription: 'حدد الساعات التي ستكون متاحًا فيها للتدريس إذا تمت الموافقة على طلبك. يمكنك تحديث هذه الساعات لاحقًا من ملفك الشخصي.',
          availableHoursProfileDescription: 'إدارة أوقات تواجدك للتدريس لإعلام الطلاب بالأوقات المتاحة للدروس.',
          viewAvailableHours: 'عرض الساعات المتاحة',
          viewAvailableHoursDescription: 'هنا يمكنك رؤية جميع ساعات التدريس المتاحة منظمة حسب اليوم.',
          editAvailableHours: 'تعديل الساعات',
          noHoursForDay: 'لا توجد ساعات متاحة لهذا اليوم',
          manageAvailableHours: 'إدارة الساعات المتاحة',
          hoursSavedSuccess: 'تم حفظ ساعاتك المتاحة بنجاح!',
          myLessons: 'دروسي',
          studentName: 'اسم الطالب',
          totalLessons: 'إجمالي الدروس',
          completedLessons: 'الدروس المكتملة',
          scheduledLessons: 'الدروس المجدولة',
          cancelledLessons: 'الدروس الملغاة',
          noLessonsFound: 'لا توجد دروس',
          errorSavingHours: 'خطأ في حفظ ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',
          errorLoadingHours: 'خطأ في تحميل ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',
          errorParsingHours: 'خطأ في تحليل بيانات ساعاتك المتاحة. يرجى المحاولة مرة أخرى.',
          saveAndReturn: 'حفظ والعودة',
          timeSlots: 'فترة زمنية',
          noAvailableHours: 'لم يتم تحديد ساعات متاحة. يرجى تحديد ساعاتك المتاحة.',
          selectAll: 'تحديد الكل',
          clearAll: 'مسح الكل',
          timeSlot: 'الفترة الزمنية',
          available: 'متاح',
          unavailable: 'غير متاح',
          selectAllDays: 'تحديد هذا الوقت لجميع الأيام',
          clearAllDays: 'مسح هذا الوقت لجميع الأيام',
          legend: 'مفتاح الرموز',
          cellStatus: 'حالة الخلية',
          actions: 'الإجراءات',

          changeProfilePicture: 'تغيير الصورة الشخصية',
          profile: {
            title: 'ملف المعلم',
            personalInfo: 'المعلومات الشخصية',
            teachingInfo: 'معلومات التدريس',
            phone: 'رقم الهاتف',
            country: 'الدولة',
            residence: 'مكان الإقامة',
            nativeLanguage: 'اللغة الأم',
            teachingLanguages: {
              title: 'لغات التدريس',
              Arabic: 'العربية',
              English: 'الإنجليزية',
              French: 'الفرنسية',
              Spanish: 'الإسبانية',
              Urdu: 'الأوردية',
              Turkish: 'التركية',
              Indonesian: 'الإندونيسية',
              Malay: 'الماليزية',
              Bengali: 'البنغالية',
              Hindi: 'الهندية',
              Persian: 'الفارسية',
              German: 'الألمانية',
              Italian: 'الإيطالية',
              Portuguese: 'البرتغالية',
              Russian: 'الروسية',
              Chinese: 'الصينية',
              Japanese: 'اليابانية',
              Korean: 'الكورية',
              Thai: 'التايلاندية',
              Vietnamese: 'الفيتنامية',
              Swahili: 'السواحيلية',
              Hausa: 'الهوسا',
              Somali: 'الصومالية',
              select: 'اختر لغات التدريس',
              placeholder: 'اختر لغة واحدة أو أكثر'
            },
            courseTypes: 'أنواع الدورات',
            qualifications: 'المؤهلات',
            teachingExperience: 'الخبرة في التدريس',
            availableHours: 'الساعات المتاحة',
            pricePerLesson: 'السعر لكل درس',
            timezone: 'المنطقة الزمنية',
            paymentMethod: 'طريقة الدفع',
            currency: {
              usd: 'دولار أمريكي',
              eur: 'يورو',
              gbp: 'جنيه إسترليني'
            },
            experience: {
              beginner: 'مبتدئ (0-2 سنوات)',
              intermediate: 'متوسط (2-5 سنوات)',
              advanced: 'متقدم (5-10 سنوات)',
              expert: 'خبير (أكثر من 10 سنوات)'
            }
          }
        },
        genders: {
          male: 'ذكر',
          female: 'أنثى'
        }
      },
      nav: {
        home: 'الرئيسية',
        dashboard: 'لوحة التحكم',
        teachers: 'المعلمون',
        students: 'الطلاب',
        categories: 'التصنيفات',
        languages: 'اللغات',
        applications: 'الطلبات',
        profileUpdates: 'تعديلات البيانات',
        withdrawalManagement: 'إدارة طلبات السحب',
        findTeacher: 'ابحث عن معلم',
        myTeachers: 'أساتذتي',
        meetings: 'الاجتماعات',
        chat: 'المحادثات',
        login: 'تسجيل الدخول',
        register: 'إنشاء حساب',
        logout: 'تسجيل الخروج',
        search: 'بحث',
        language: 'اللغة',
        english: 'الإنجليزية',
        arabic: 'العربية',
        adminDashboard: 'لوحة تحكم المدير',
        teacherDashboard: 'لوحة تحكم المعلم',
        studentDashboard: 'لوحة تحكم الطالب'
      },
      footer: {
        platformName: 'علّمني أون لاين',
        designedBy: 'صُمم بحب للتعليم العربي',
        followUs: 'تابعنا',
        facebook: 'فيسبوك',
        twitter: 'تويتر',
        instagram: 'انستغرام',
        quickLinks: 'روابط سريعة',
        about: 'من نحن',
        contact: 'اتصل بنا',
        privacy: 'سياسة الخصوصية',
        terms: 'شروط الخدمة',
        faq: 'الأسئلة الشائعة',
        support: 'الدعم الفني',
        copyright: '© ٢٠٢٥ علّمني أون لاين',
        tagline: 'صُمم بحب للتعليم العربي'
      },
      hero: {
        title: 'العربية بجميع اللغات',
        subtitle: 'رحلة تعليمية فريدة تجمع بين اللغة والثقافة العربية',
        startLearning: 'ابدأ التعلم',
        becomeTeacher: 'كن مدرساً',
        imageAlt: 'القرآن الكريم'
      },
      home: {
        subjects: 'موادنا الدراسية',
        subjectsSubtitle: 'اكتشف مجموعة متنوعة من المواد الإسلامية والعربية مع نخبة من المعلمين المتخصصين',
        teachers: 'معلم',
        expertTeachers: 'معلمون متخصصون',
        expertTeachersDesc: 'تعلم على يد علماء معتمدين ومتحدثين أصليين',
        activeStudents: 'طلاب نشطون',
        courses: 'دورات',
        coursesDesc: 'دورات شاملة في الدراسات الإسلامية واللغة العربية',
        studentsDesc: 'طلاب من جميع أنحاء العالم',
        whyChooseUs: 'لماذا تختارنا',
        whyChooseUsSubtitle: 'نقدم تجربة تعليمية فريدة تجمع بين التكنولوجيا الحديثة والتعليم الإسلامي الأصيل',
        meetTeachers: 'تعرف على معلمينا',
        meetTeachersSubtitle: 'نخبة من المعلمين المؤهلين والمتخصصين في تعليم الإسلام واللغة العربية',
        features: {
          quality: 'تعليم عالي الجودة',
          qualityDesc: 'معلمون متخصصون وخبراء في مجالاتهم',
          flexible: 'مرونة في التعلم',
          flexibleDesc: 'تعلم في أي وقت ومن أي مكان',
          interactive: 'تعلم تفاعلي',
          interactiveDesc: 'دروس تفاعلية ومناقشات مباشرة',
          certified: 'شهادات معتمدة',
          certifiedDesc: 'احصل على شهادات معتمدة في مجال دراستك'
        },
        testimonials: 'ماذا يقول طلابنا',
        testimonialsSubtitle: 'استمع إلى طلابنا حول تجربتهم التعليمية مع معلمينا المتخصصين',
        reviewFor: 'تقييم لـ',
        learnMore: 'اكتشف المزيد'
      },
      testimonials: {
        student: 'طالب',
        reviewFor: 'تقييم لـ'
      },
      search: {
        findTeacher: 'ابحث عن معلم',
        filters: 'التصفية',
        subject: 'المادة',
        allSubjects: 'جميع المواد',
        language: 'اللغة',
        allLanguages: 'جميع اللغات',
        priceRange: 'نطاق السعر',
        rating: 'التقييم',
        anyRating: 'أي تقييم',
        andAbove: 'وما فوق',
        noTeachersFound: 'لم يتم العثور على معلمين',
        yearsOfExperience: '{{years}} سنوات من الخبرة',
        bookLesson: 'احجز درساً',
        perHour: '/ساعة',
        subjects: 'المواد',
        languages: 'اللغات',
        searchButton: 'بحث',
        clearFilters: 'مسح التصفية',
        noTeachersFound: 'لم يتم العثور على معلمين مطابقين لمعاييرك',
        teachingLanguages: 'لغات التدريس',
        verifiedTeacher: 'معلم موثق',
        watchIntro: 'مشاهدة المقدمة',
        watchIntroVideo: 'مشاهدة فيديو التعريف',
        viewProfile: 'عرض الملف الشخصي',
        contactAndBook: 'تواصل واحجز'
      },
      booking: {
        bookLessonWith: 'احجز درساً مع',
        pricePerLesson: 'سعر الدرس',
        instructions: 'تعليمات الحجز',
        instructionsText: 'اختر يوماً وموعداً من التقويم أدناه. بعد تأكيد الحجز، سيتم خصم المبلغ من رصيد محفظتك.',
        selectTimeSlot: 'اختر موعداً',
        clickToSelectSlot: 'اضغط على المواعيد المتاحة للحجز',
        clickToBook: 'اضغط لحجز هذا الموعد',
        fullHour: 'ساعة كاملة',
        fullHourAvailable: 'ساعة كاملة متاحة',
        halfHourOnly: 'نصف ساعة فقط',
        fullLesson: 'درس كامل (50 دقيقة)',
        halfLesson: 'نصف درس (25 دقيقة)',
        consecutiveSlots: 'فترات متتالية',
        selectDuration: 'اختر مدة الدرس',
        selectBookingOption: 'اختر نوع الحجز',
        bookingType: 'نوع الحجز',
        regularHalfLesson: 'نصف درس عادي (25 دقيقة)',
        regularFullLesson: 'درس كامل عادي (50 دقيقة)',
        secondHalfOnly: 'النصف الثاني فقط (بعد 30 دقيقة)',
        fullLessonFromSecondHalf: 'درس كامل من النصف الثاني (50 دقيقة)',
        crossHourLesson: 'درس عبر الساعات (30+30 دقيقة)',
        regularLesson: 'درس عادي',
        duration: 'المدة',
        noAvailableSlots: 'لا توجد مواعيد متاحة لهذا اليوم',
        confirmBooking: 'تأكيد الحجز',
        teacher: 'المعلم',
        day: 'اليوم',
        date: 'التاريخ',
        time: 'الوقت',
        price: 'السعر',
        confirmAndPay: 'تأكيد والدفع',
        bookingSuccessTitle: 'تم الحجز بنجاح!',
        bookingSuccessMessage: 'تم حجز درسك بنجاح وتم خصم المبلغ من محفظتك. يمكنك عرض حجوزاتك في قسم حجوزاتي.',
        backToTeacher: 'العودة إلى المعلم',
        viewMyBookings: 'عرض حجوزاتي',
        bookAgain: 'احجز مرة أخرى',
        bookingFailed: 'فشل الحجز. يرجى المحاولة مرة أخرى.',
        insufficientBalance: 'رصيدك غير كافٍ. يرجى إضافة رصيد إلى محفظتك قبل الحجز.',
        currency: 'دولار',
        currentWeek: 'الأسبوع الحالي',
        weekOf: 'أسبوع',
        previousWeek: 'الأسبوع السابق',
        nextWeek: 'الأسبوع التالي',
        weekNavigation: 'التنقل بين الأسابيع',
        selectTimeRange: 'اختر المدة الزمنية',
        startTime: 'وقت البداية',
        endTime: 'وقت النهاية',
        lessonDuration: 'مدة الدرس',
        from: 'من',
        to: 'إلى',
        availableSlot: 'خانة متاحة',
        selectDuration: 'اختر المدة'
      },
      bookings: {
        title: 'حجوزاتي',
        weeklyTitle: 'حجوزات هذا الأسبوع',
        description: 'عرض وإدارة دروسك المجدولة مع المعلمين.',
        weeklyDescription: 'عرض حجوزاتك لهذا الأسبوع في شكل تقويم.',
        bookingDetails: 'تفاصيل الحجز',
        noBookings: 'ليس لديك حجوزات حتى الآن.',
        fetchError: 'خطأ في جلب الحجوزات. يرجى المحاولة مرة أخرى.',
        teacher: 'المعلم',
        date: 'التاريخ',
        time: 'الوقت',
        timeRange: 'نطاق الوقت',
        duration: 'المدة',
        minutes: 'دقيقة',
        status: 'الحالة',
        statusValues: {
          scheduled: 'مجدول',
          completed: 'مكتمل',
          cancelled: 'ملغي',
          issue_reported: 'تم الإبلاغ عن مشكلة',
          ongoing: 'جاري'
        },
        price: 'السعر',
        cancel: 'إلغاء',
        confirmCancel: 'تأكيد الإلغاء',
        cancelWarning: 'هل أنت متأكد من رغبتك في إلغاء هذا الحجز؟ سيتم إعادة المبلغ إلى محفظتك.',
        cancellationReason: 'سبب الإلغاء (اختياري)',
        cancellationReasonPlaceholder: 'يرجى كتابة سبب إلغاء هذا الحجز...',
        confirmCancelButton: 'نعم، إلغاء الحجز',
        cancelling: 'جاري الإلغاء...',
        cancelSuccess: 'تم إلغاء الحجز بنجاح وتمت إعادة المبلغ إلى محفظتك',
        cancelError: 'خطأ في إلغاء الحجز. يرجى المحاولة مرة أخرى.',
        availableSlot: 'وقت متاح',
        currentTime: 'الوقت الحالي',
        pastSlot: 'وقت فائت',
        takeBreak: 'أخذ راحة',
        takeBreakTitle: 'أخذ هذا الوقت راحة',
        takeBreakMessage: 'هل تريد أخذ هذا الوقت راحة؟',
        takeBreakNote: 'سيتم إخفاء هذا الوقت من الطلاب لهذا الأسبوع فقط',
        breakTakenSuccess: 'تم أخذ الراحة بنجاح',
        breakTakenError: 'خطأ في أخذ الراحة',
        break: 'راحة',
        clickToTakeBreak: 'اضغط لأخذ راحة',
        cancelBreakTitle: 'إلغاء وقت الراحة',
        cancelBreakMessage: 'هل تريد إلغاء وقت الراحة هذا؟',
        cancelBreakNote: 'سيصبح هذا الوقت متاحاً للطلاب مرة أخرى',
        breakCancelledSuccess: 'تم إلغاء الراحة بنجاح',
        breakCancelledError: 'خطأ في إلغاء الراحة',
        reschedule: 'إعادة جدولة',
        rescheduleTitle: 'إعادة جدولة الحجز',
        rescheduleDescription: 'اختر موعداً جديداً لهذا الحجز من أوقاتك المتاحة.',
        currentBooking: 'الحجز الحالي',
        selectDay: 'اختر اليوم',
        chooseDay: 'اختر يوماً',
        selectTime: 'اختر الوقت',
        chooseTime: 'اختر وقتاً',
        loadingDays: 'جاري تحميل الأيام المتاحة...',
        loadingTimes: 'جاري تحميل الأوقات المتاحة...',
        noAvailableDays: 'لا توجد أيام متاحة خلال الـ 30 يوماً القادمة.',
        noAvailableTimes: 'لا توجد أوقات متاحة في هذا اليوم.',
        availableTimes: 'أوقات متاحة',
        rescheduleReason: 'سبب إعادة الجدولة (اختياري)',
        rescheduleReasonPlaceholder: 'يرجى كتابة سبب إعادة جدولة هذا الحجز...',
        rescheduleSuccess: 'تم إعادة جدولة الحجز بنجاح',
        rescheduleError: 'خطأ في إعادة جدولة الحجز. يرجى المحاولة مرة أخرى.',
        fetchDaysError: 'خطأ في جلب الأيام المتاحة. يرجى المحاولة مرة أخرى.',
        fetchTimesError: 'خطأ في جلب الأوقات المتاحة. يرجى المحاولة مرة أخرى.',
        currentBooking: 'الحجز الحالي',
        selectNewDate: 'اختر التاريخ الجديد',
        selectNewTime: 'اختر الوقت الجديد',
        selectDateFirst: 'اختر تاريخاً من التقويم أولاً',
        selectedTime: 'الوقت المختار',
        confirmReschedule: 'تأكيد إعادة الجدولة',
        availableSlots: 'فترة متاحة',
        backToSelectDay: 'العودة لاختيار اليوم'
      },
      teacherDetails: {
        backToSearch: 'العودة إلى البحث',
        teacherNotFound: 'لم يتم العثور على المعلم',
        errorFetching: 'خطأ في جلب تفاصيل المعلم',
        reviews: 'تقييمات',
        watchIntroVideo: 'مشاهدة الفيديو التعريفي',
        hideIntroVideo: 'إخفاء الفيديو التعريفي',
        bookLesson: 'حجز درس',
        teachingSubjects: 'المواد التعليمية',
        languages: 'اللغات',
        nativeLanguage: 'اللغة الأم',
        teachingLanguages: 'لغات التدريس',
        qualifications: 'المؤهلات',
        yearsOfExperience: '{{years}} سنوات من الخبرة',
        studentReviews: 'تقييمات الطلاب',
        noReviews: 'لا توجد تقييمات حتى الآن',
        contactInfo: 'معلومات الاتصال',
        email: 'البريد الإلكتروني',
        phone: 'رقم الهاتف',
        location: 'الموقع',
        country: 'الدولة',
        residence: 'مدينة الإقامة',
        timezone: 'المنطقة الزمنية',
        experience: 'الخبرة',
        memberSince: 'عضو منذ',
        availableHours: 'الساعات المتاحة',
        paymentInfo: 'معلومات الدفع',
        pricePerLesson: 'سعر الدرس',
        paymentMethod: 'طريقة الدفع',
        cv: 'السيرة الذاتية',
        introVideo: 'الفيديو التعريفي',
        courseTypes: 'أنواع الدورات'
      },
      chat: {
        conversations: 'المحادثات',
        messages: 'الرسائل',
        typeMessage: 'اكتب رسالة...',
        send: 'إرسال',
        noMessages: 'لا توجد رسائل',
        noChats: 'لا توجد محادثات',
        startChat: 'ابدأ محادثة جديدة',
        loading: 'جاري التحميل...',
        today: 'اليوم',
        yesterday: 'أمس',
        online: 'متصل',
        offline: 'غير متصل',
        sent: 'تم الإرسال',
        delivered: 'تم التوصيل',
        read: 'تمت القراءة',
        failed: 'فشل الإرسال',
        retry: 'إعادة المحاولة',
        onlyStudents: 'يمكن للطلاب فقط بدء محادثة مع المعلمين',
        no_conversations: 'لا توجد محادثات',
        type_message: 'اكتب رسالتك هنا...',
        select_conversation: 'اختر محادثة للبدء',
        last_seen: 'آخر ظهور',
        typing: 'يكتب...',
        deleteConfirmTitle: 'حذف المحادثة',
        deleteConfirmMessage: 'هل أنت متأكد من رغبتك في حذف المحادثة مع {{name}}؟',
        conversationDeleted: 'تم حذف المحادثة بنجاح',
        conversationDeletedByOther: 'تم حذف هذه المحادثة من قبل المشارك الآخر',
        deleteConversationError: 'حدث خطأ أثناء حذف المحادثة',
        messageDeleted: 'تم حذف الرسالة بنجاح',
        messageEdited: 'تم تعديل الرسالة بنجاح',
        editError: 'حدث خطأ أثناء تعديل الرسالة',
        deleteError: 'حدث خطأ أثناء حذف الرسالة',
        noConversations: 'لا توجد محادثات',
         chat: 'دردشة'
      },
      auth: {
        login: 'تسجيل الدخول',
        logout: 'تسجيل الخروج',
        logoutConfirmTitle: 'تأكيد تسجيل الخروج',
        logoutConfirmMessage: 'هل أنت متأكد أنك تريد تسجيل الخروج؟',
        welcomeBack: 'مرحباً بعودتك!',
        email: 'البريد الإلكتروني',
        password: 'كلمة المرور',
        signIn: 'تسجيل الدخول',
        or: 'أو',
        noAccount: 'ليس لديك حساب؟',
        forgotPassword: 'نسيت كلمة المرور؟',
        forgotPasswordInstructions: 'أدخل بريدك الإلكتروني وسنرسل لك رمز التحقق لإعادة تعيين كلمة المرور.',
        sendResetCode: 'إرسال رمز التحقق',
        backToLogin: 'العودة إلى تسجيل الدخول',
        resetCodeSent: 'تم إرسال رمز التحقق إلى بريدك الإلكتروني',
        resetRequestFailed: 'فشل إرسال رمز التحقق',
        verifyCode: 'التحقق من الرمز',
        verifyCodeInstructions: 'أدخل رمز التحقق المكون من 6 أرقام المرسل إلى بريدك الإلكتروني:',
        verifyEmail: 'تحقق من البريد الإلكتروني',
        verificationCodeSentTo: 'أرسلنا رمز التحقق إلى',
        verify: 'تحقق',
        verifying: 'جاري التحقق...',
        verificationCodeSent: 'تم إرسال رمز التحقق بنجاح',
        verificationFailed: 'فشل التحقق من البريد الإلكتروني',
        resendFailed: 'فشل إعادة إرسال رمز التحقق',
        resendCodeIn: 'إعادة إرسال الرمز خلال',
        sending: 'جاري الإرسال...',
        invalidCode: 'يرجى إدخال رمز صحيح مكون من 6 أرقام',
        fillAllFields: 'يرجى ملء جميع الحقول',
        loginFailed: 'فشل تسجيل الدخول. يرجى التحقق من بياناتك.',
        invalidCredentials: 'البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.',
        emailNotFound: 'هذا البريد الإلكتروني غير مسجل. يرجى التحقق من البريد الإلكتروني أو إنشاء حساب جديد.',
        wrongPassword: 'كلمة المرور غير صحيحة. يرجى المحاولة مرة أخرى.',
        verificationCode: 'رمز التحقق',
        verifyAndContinue: 'التحقق والمتابعة',
        resendCode: 'إعادة إرسال الرمز',
        changeEmail: 'تغيير البريد الإلكتروني',
        invalidCode: 'رمز التحقق غير صالح',
        verificationFailed: 'فشل التحقق',
        resetCodeResent: 'تم إعادة إرسال رمز التحقق إلى بريدك الإلكتروني',
        resetPassword: 'إعادة تعيين كلمة المرور',
        resetPasswordInstructions: 'أنشئ كلمة مرور جديدة لحسابك',
        newPassword: 'كلمة المرور الجديدة',
        confirmPassword: 'تأكيد كلمة المرور',
        passwordResetSuccess: 'تم إعادة تعيين كلمة المرور بنجاح!',
        passwordResetSuccessMessage: 'تم إعادة تعيين كلمة المرور الخاصة بك بنجاح.',
        redirectingToLogin: 'جاري إعادة التوجيه إلى صفحة تسجيل الدخول...',
        resetPasswordFailed: 'فشل إعادة تعيين كلمة المرور',
        signUp: 'إنشاء حساب',
        continueWithGoogle: 'المتابعة باستخدام جوجل',
        googleSignInError: 'فشل تسجيل الدخول بجوجل. يرجى المحاولة مرة أخرى.',
        signInWithGoogle: 'المتابعة باستخدام جوجل',
        loginError: 'فشل تسجيل الدخول. يرجى التحقق من بيانات الاعتماد الخاصة بك.',
        accountNotFound: 'الحساب غير موجود',
        invalidRole: 'دور المستخدم غير صالح',
        chooseAccountType: 'انضم إلى مجتمعنا',
        registerDescription: 'ابدأ رحلتك في التعليم الإسلامي. اختر دورك وكن جزءاً من مجتمعنا المتنامي من المتعلمين والمعلمين.',
        registerAsTeacher: 'علّم معنا',
        teacherDescription: 'شارك معرفتك وخبرتك في الدراسات الإسلامية واللغة العربية. ساعد الآخرين على التعلم والنمو مع تطوير مسيرتك في التعليم.',
        registerAsStudent: 'تعلّم معنا',
        studentDescription: 'ابدأ رحلة تعلمك في الدراسات الإسلامية واللغة العربية مع معلمين خبراء من جميع أنحاء العالم.',
        alreadyHaveAccount: 'لديك حساب بالفعل؟',
        getStarted: 'ابدأ الآن',
        studentRegistration: 'تسجيل طالب جديد',
        teacherRegistration: 'تسجيل معلم جديد',
        joinOurCommunity: 'انضم إلى مجتمع المتعلمين',
        joinOurTeachers: 'شارك معرفتك مع الآخرين',
        createAccount: 'إنشاء حساب',
        registrationError: 'فشل التسجيل. يرجى المحاولة مرة أخرى.',
        selectGender: 'الرجاء اختيار الجنس',
        gender: 'الجنس',
        male: 'ذكر',
        female: 'أنثى'
      },
      courseTypes: {
        Islamic_Law: 'الشريعة الإسلامية',
        Fiqh: 'الفقه',
        Quran: 'القرآن الكريم',
        Tajweed: 'التجويد',
        Islamic_History: 'التاريخ الإسلامي',
        Arabic_Grammar: 'قواعد اللغة العربية',
        Arabic_Speaking: 'المحادثة العربية',
        Arabic_Writing: 'الكتابة العربية',
        Islamic_Ethics: 'الأخلاق الإسلامية',
        Hadith: 'الحديث الشريف',
        Aqeedah: 'العقيدة'
      },
      languages: {
        Arabic: 'العربية',
        English: 'الإنجليزية',
        French: 'الفرنسية',
        Spanish: 'الإسبانية'
      },
      gender: {
        male: 'ذكر',
        female: 'أنثى'
      },
      dashboard: {
        welcome: 'مرحباً',
        unauthorized: 'غير مصرح لك بالوصول إلى لوحة التحكم',
        fetchError: 'حدث خطأ أثناء جلب البيانات',
        totalStudents: 'إجمالي الطلاب',
        totalClasses: 'إجمالي الدروس',
        averageRating: 'متوسط التقييم',
        totalEarnings: 'إجمالي الأرباح',
        categories: 'فئات التدريس',
        recentBookings: 'آخر الحجوزات',
        recentReviews: 'آخر التقييمات',
        learningProgress: 'تقدم التعلم',
        quickActions: 'إجراءات سريعة',
        recommendedTeachers: 'معلمون موصى بهم',
        upcomingLessons: 'الدروس القادمة',
        noUpcomingLessons: 'لا توجد دروس قادمة',
        updateProfile: 'تحديث الملف الشخصي',
        findTeacher: 'ابحث عن معلم',
        browseCourses: 'تصفح الدورات',
        viewAll: 'عرض الكل',
        incompleteProfile: 'ملفك الشخصي غير مكتمل',
        completeProfileMessage: 'يرجى إكمال ملفك الشخصي للوصول إلى جميع الميزات والعثور على المعلم المناسب لرحلتك التعليمية.',
        completeProfileToAccess: 'يجب عليك إكمال ملفك الشخصي للوصول إلى هذه الصفحة وميزاتها.',
        completeProfile: 'إكمال الملف الشخصي',
        updateProfile: 'تحديث الملف الشخصي',
        completeProfileNow: 'أكمل ملفك الشخصي الآن'
      },
      regions: {
        middle_east: 'الشرق الأوسط',
        north_africa: 'شمال أفريقيا',
        sub_saharan_africa: 'أفريقيا جنوب الصحراء',
        south_asia: 'جنوب آسيا',
        southeast_asia: 'جنوب شرق آسيا',
        central_asia: 'آسيا الوسطى',
        europe: 'أوروبا',
        north_america: 'أمريكا الشمالية',
        south_america: 'أمريكا الجنوبية',
        east_asia: 'شرق آسيا',
        oceania: 'أوقيانوسيا',
        others: 'أخرى'
      },
      teacher: {
        phoneOptional: 'اختياري',
        nativeLanguage: 'اللغة الأم',
        teachingLanguages: {
          title: 'لغات التدريس',
          Arabic: 'العربية',
          English: 'الإنجليزية',
          French: 'الفرنسية',
          Spanish: 'الإسبانية',
          Urdu: 'الأوردية',
          Turkish: 'التركية',
          Indonesian: 'الإندونيسية',
          Malay: 'الماليزية',
          Bengali: 'البنغالية',
          Hindi: 'الهندية',
          Persian: 'الفارسية',
          German: 'الألمانية',
          Italian: 'الإيطالية',
          Portuguese: 'البرتغالية',
          Russian: 'الروسية',
          Chinese: 'الصينية',
          Japanese: 'اليابانية',
          Korean: 'الكورية',
          Thai: 'التايلاندية',
          Vietnamese: 'الفيتنامية',
          Swahili: 'السواحيلية',
          Hausa: 'الهوسا',
          Somali: 'الصومالية',
          select: 'اختر لغات التدريس',
          placeholder: 'اختر لغة واحدة أو أكثر'
        },
        qualifications: 'المؤهلات',
        experience: 'الخبرة',
        subjects: 'المواد',
        profile: {
          title: 'ملف المعلم',
          personalInfo: 'المعلومات الشخصية',
          teachingInfo: 'معلومات التدريس',
          phone: 'رقم الهاتف',
          country: 'الدولة',
          residence: 'مكان الإقامة',
          nativeLanguage: 'اللغة الأم',
          teachingLanguages: {
            title: 'لغات التدريس',
            Arabic: 'العربية',
            English: 'الإنجليزية',
            French: 'الفرنسية',
            Spanish: 'الإسبانية',
            Urdu: 'الأوردية',
            Turkish: 'التركية',
            Indonesian: 'الإندونيسية',
            Malay: 'الماليزية',
            Bengali: 'البنغالية',
            Hindi: 'الهندية',
            Persian: 'الفارسية',
            German: 'الألمانية',
            Italian: 'الإيطالية',
            Portuguese: 'البرتغالية',
            Russian: 'الروسية',
            Chinese: 'الصينية',
            Japanese: 'اليابانية',
            Korean: 'الكورية',
            Thai: 'التايلاندية',
            Vietnamese: 'الفيتنامية',
            Swahili: 'السواحيلية',
            Hausa: 'الهوسا',
            Somali: 'الصومالية',
            select: 'اختر لغات التدريس',
            placeholder: 'اختر لغة واحدة أو أكثر'
          },
          courseTypes: 'أنواع الدورات',
          qualifications: 'المؤهلات',
          teachingExperience: 'الخبرة في التدريس',
          availableHours: 'الساعات المتاحة',
          pricePerLesson: 'السعر لكل درس',
          timezone: 'المنطقة الزمنية',
          paymentMethod: 'طريقة الدفع',
          currency: {
            usd: 'دولار أمريكي',
            eur: 'يورو',
            gbp: 'جنيه إسترليني'
          },
          experience: {
            beginner: 'مبتدئ (0-2 سنوات)',
            intermediate: 'متوسط (2-5 سنوات)',
            advanced: 'متقدم (5-10 سنوات)',
            expert: 'خبير (أكثر من 10 سنوات)'
          }
        },
        application: {
          title: 'طلب التقديم للتدريس',
          submit: 'تقديم الطلب',
          success: 'تم تقديم الطلب بنجاح',
          error: 'حدث خطأ أثناء تقديم الطلب',
          errorFetchingData: 'حدث خطأ أثناء جلب البيانات',
          errorFetchingCategories: 'حدث خطأ أثناء جلب التصنيفات',
          statusCardTitle: 'حالة طلب التقديم',
          edit: 'تعديل الطلب',
          editDescription: 'يمكنك تعديل بيانات طلبك، ولكن ستحتاج إلى موافقة المشرف مرة أخرى.',
          warningTitle: 'تحذير: تعديل الطلب',
          warningMessage: 'تعديل بياناتك سيرسل طلب تحديث يتطلب موافقة الإدارة.',
          warningDescription1: 'إذا تابعت، سيتم توجيهك إلى نموذج التعديل حيث يمكنك تحديث بياناتك.',
          warningDescription2: 'ستستمر في التدريس بالبيانات الحالية حتى توافق الإدارة على طلب التحديث.',
          warningConfirmation: 'هل أنت متأكد من أنك تريد المتابعة؟',
          confirmEdit: 'نعم، تعديل الطلب',
          editApplication: {
            title: 'تعديل الطلب',
            description: 'تحديث بيانات طلبك. ستتم مراجعة التغييرات من قبل الإدارة قبل تطبيقها.',
            warning: 'ستبقى بياناتك الحالية نشطة حتى توافق الإدارة على التغييرات.'
          },
          changeVideo: 'تغيير الفيديو',
          status: {
            pending: 'قيد المراجعة',
            approved: 'تم القبول',
            rejected: 'تم الرفض'
          },
          statusMessage: {
            pending: 'طلبك قيد المراجعة',
            approved: 'تهانينا! تم قبول طلبك كمدرس',
            rejected: 'عذراً، تم رفض طلبك',
            default: 'تم تحديث حالة الطلب'
          },
          applicationNextSteps: {
            approved: {
              title: 'الخطوات التالية',
              steps: [
                'الرجاء تسجيل الخروج من حسابك',
                'تسجيل الدخول مرة أخرى لتفعيل صلاحياتك الكاملة كمدرس'
              ]
            }
          }
        },
        country: 'الدولة',
        residence: 'مدينة الإقامة',
        nativeLanguage: 'اللغة الأم',
        teachingLanguages: 'لغات التدريس',
        courseTypes: 'أنواع الدورات',
        qualifications: 'المؤهلات',
        qualificationsPlaceholder: 'أدخل مؤهلاتك التعليمية والشهادات',
        teachingExperience: 'سنوات الخبرة في التدريس',
        introVideo: 'فيديو التعريف',
        introVideoUrl: 'رابط فيديو التعريف',
        introVideoUrlPlaceholder: 'https://...',
        noIntroVideo: 'لا يوجد فيديو تعريفي متاح',
        videoLinkTab: 'رابط الفيديو',
        videoFileTab: 'رفع فيديو',
        videoLink: 'رابط',
        videoUpload: 'رفع',
        videoLinkHelp: 'أدخل رابط من يوتيوب أو منصة فيديو أخرى',
        videoLinkNote: 'شارك رابط فيديو التعريف الخاص بك من منصة مدعومة',
        videoUploadHelp: 'قم برفع فيديو التعريف الخاص بك مباشرة إلى منصتنا',
        videoUploadPlaceholder: 'رفع فيديو',
        videoSelected: 'الفيديو المحدد',
        videoUploadError: 'خطأ في رفع الفيديو',
        allowedFormats: 'الصيغ المسموحة',
        maxFileSize: 'الحجم الأقصى للملف',
        recommendedPlatforms: 'المنصات الموصى بها',
        cv: 'السيرة الذاتية',
        cvPlaceholder: 'اكتب سيرتك الذاتية',
        cvHelperText: 'اكتب ملخصًا موجزًا لمؤهلاتك وخبراتك ونهجك في التدريس (الحد الأقصى 2000 حرف)',
        characters: 'حرف',
        profilePicture: 'الصورة الشخصية',
        profilePicturePlaceholder: 'ارفع صورتك الشخصية',
        profilePictureRequired: 'الصورة الشخصية مطلوبة',
        availableHours: 'ساعات التدريس المتاحة',
        availableHoursDescription: 'حدد الأوقات المتاحة للتدريس في كل يوم من أيام الأسبوع',
        availableHoursProfileDescription: 'إدارة أوقات تواجدك للتدريس لإعلام الطلاب بالأوقات المتاحة للدروس',
        viewAvailableHours: 'عرض الساعات المتاحة',
        viewAvailableHoursDescription: 'مراجعة جدولك الحالي للتدريس والأوقات المتاحة',
        noAvailableHours: 'لم يتم تحديد ساعات متاحة بعد',
        timeSlot: 'الفترة الزمنية',
        timeSlots: 'فترات زمنية',
        available: 'متاح',
        unavailable: 'غير متاح',
        editAvailableHours: 'تعديل الساعات المتاحة',
        weeklySchedule: 'الجدول الأسبوعي',
        scheduleDescription: 'أوقات تواجدك للتدريس خلال الأسبوع',
        time: 'الوقت',
        legend: 'مفتاح الرموز',
        quickStats: 'إحصائيات سريعة',
        totalSlots: 'إجمالي الفترات الزمنية',
        hoursPerWeek: 'ساعات في الأسبوع',
        activeDays: 'الأيام النشطة',
        pricePerLesson: 'السعر لكل درس',
        pricePerLessonPlaceholder: 'أدخل السعر بالدولار الأمريكي',
        trialLessonPrice: 'سعر الدرس التجريبي',
        trialLessonPricePlaceholder: 'أدخل سعر الدرس التجريبي بالدولار الأمريكي',
        trialPriceLessThanRegular: 'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي',
        timezone: 'المنطقة الزمنية',
        paymentMethod: 'طريقة الدفع',
        phone: 'رقم الهاتف',
        phoneHelp: 'متضمناً رمز الدولة (مثال: 966+, 44+, 1+)',
        commitment: 'التعهد',
        commitmentDescription: 'يرجى قراءة والموافقة على التعهد التالي',
        commitmentProfileDescription: 'عرض وإدارة حالة وتفاصيل التعهد الخاص بك',
        commitmentRequired: 'يجب الموافقة على التعهد للمتابعة',
        commitmentStatus: {
          accepted: 'تم قبول التعهد',
          pending: 'في انتظار الموافقة على التعهد',
          rejected: 'تم رفض التعهد'
        },
        formHasErrors: 'يرجى تصحيح الأخطاء في النموذج قبل المتابعة',
        selectedHours: 'ساعات مختارة',
        selectAll: 'اختيار الكل',
        required: 'هذا الحقل مطلوب',
        formHasErrors: 'يرجى تصحيح الأخطاء في النموذج',
        priceRange: 'السعر يجب أن يكون بين $3 و $100',
        yourEarnings: 'ما ستحصل عليه بعد خصم العمولة:',
        invalidUrl: 'الرجاء إدخال رابط صحيح',
        invalidPhone: 'الرجاء إدخال رقم هاتف صحيح',
        invalidPrice: 'الرجاء إدخال سعر صحيح',
        uploadVideoNow: 'رفع الفيديو الآن',
        videoReady: 'الفيديو جاهز',
        deleteVideo: 'حذف الفيديو',
        manageAvailableHours: 'إدارة الساعات المتاحة',
        weeklyBookings: 'حجوزات الطلاب لهذا الأسبوع',
        weeklyBookingsDescription: 'عرض حجوزات طلابك لهذا الأسبوع في شكل تقويم.',

        commitmentTitle: 'تعهد المعلمين',
        commitmentAccepted: 'تم قبول التعهد',
        readCommitment: 'قراءة التعهد والموافقة عليه',
        accept: 'أوافق',
        reject: 'أرفض',
        allowedFormats: 'الصيغ المسموحة',
        maxFileSize: 'الحد الأقصى لحجم الملف',
        videoFormats: 'MP4, WebM, OGG',
        maxVideoSize: '100 ميجابايت',
        videoRequired: 'فيديو تعريفي مطلوب لطلب التقديم الخاص بك',
        fileTypes: {
          image: 'الصيغ المدعومة: JPG, PNG (الحد الأقصى 5 ميجابايت)',
          document: 'الصيغ المدعومة: PDF, DOC, DOCX (الحد الأقصى 10 ميجابايت)'
        },
        submitComplaint: 'تقديم شكوى',
        complaintReason: 'سبب الشكوى',
        complaintType1: 'الطالب حضر لكن العمولة لم تُحول',
        complaintType2: 'الطالب لم يحضر نهائياً',
        complaintDetails: 'تفاصيل الشكوى (اختياري)',
        complaintDetailsPlaceholder: 'اكتب أي تفاصيل إضافية إذا لزم الأمر...',
        complaintStatus: 'حالة الشكوى',
        complaintStatusValues: {
          pending: 'في الانتظار',
          resolved: 'تم الحل'
        }
      },
      role: {
        admin: 'مدير',
        teacher: 'معلم',
        student: 'طالب'
      },
      validation: {
        required: 'هذا الحقل مطلوب',
        email: 'يرجى إدخال بريد إلكتروني صحيح',
        password: {
          min: 'يجب أن تكون كلمة المرور {{min}} أحرف على الأقل',
          max: 'يجب أن تكون كلمة المرور {{max}} حرفاً على الأكثر',
          match: 'كلمات المرور غير متطابقة'
        }
      },
      days: {
        monday: 'الإثنين',
        tuesday: 'الثلاثاء',
        wednesday: 'الأربعاء',
        thursday: 'الخميس',
        friday: 'الجمعة',
        saturday: 'السبت',
        sunday: 'الأحد',
        mondayShort: 'اثنين',
        tuesdayShort: 'ثلاثاء',
        wednesdayShort: 'أربعاء',
        thursdayShort: 'خميس',
        fridayShort: 'جمعة',
        saturdayShort: 'سبت',
        sundayShort: 'أحد'
      },
      hours: {
        hour1: '1:00 ص',
        hour2: '2:00 ص',
        hour3: '3:00 ص',
        hour4: '4:00 ص',
        hour5: '5:00 ص',
        hour6: '6:00 ص',
        hour7: '7:00 ص',
        hour8: '8:00 ص',
        hour9: '9:00 ص',
        hour10: '10:00 ص',
        hour11: '11:00 ص',
        hour12: '12:00 م',
        hour13: '1:00 م',
        hour14: '2:00 م',
        hour15: '3:00 م',
        hour16: '4:00 م',
        hour17: '5:00 م',
        hour18: '6:00 م',
        hour19: '7:00 م',
        hour20: '8:00 م',
        hour21: '9:00 م',
        hour22: '10:00 م',
        hour23: '11:00 م',
        hour24: '12:00 ص'
      },
      student: {
        profile: {
          title: 'الملف الشخصي للطالب',
          complete: 'أكمل ملفك الشخصي',
          languagePreferences: 'تفضيلات اللغة',
          personalInfo: 'المعلومات الشخصية',
          learningPreferences: 'تفضيلات التعلم',
          nativeLanguage: 'اللغة الأم',
          preferredIslamicLanguage: 'اللغة المفضلة لتعلم الإسلام',
          preferredArabicLanguage: 'اللغة المفضلة لتعلم العربية',
          islamLearningLanguage: 'لغة تعلم الإسلام',
          arabicLearningLanguage: 'لغة تعلم العربية',
          age: 'العمر',
          country: 'البلد',
          timezone: 'المنطقة الزمنية',
          arabicLevel: 'مستوى اللغة العربية',
          privateTutoring: 'أنا مهتم بالدروس الخصوصية',
          preferGroup: 'لا',
          preferPrivate: 'نعم',
          updateSuccess: 'تم تحديث الملف الشخصي بنجاح',
          editInfo: 'تعديل معلومات الملف الشخصي',
          levels: {
            beginner: 'مبتدئ',
            intermediate: 'متوسط',
            advanced: 'متقدم'
          },
          success: 'تم تحديث الملف الشخصي بنجاح! جاري التوجيه إلى لوحة التحكم...'
        },
        myTeachers: 'أساتذتي',
        noTeachersYet: 'لم تقم بحجز أي درس بعد.',
        lessonsTaken: 'عدد الدروس'
      },
      admin: {
        profile: {
          personalInfo: 'المعلومات الشخصية',
          changePassword: 'تغيير كلمة المرور',
          currentPassword: 'كلمة المرور الحالية',
          newPassword: 'كلمة المرور الجديدة',
          confirmNewPassword: 'تأكيد كلمة المرور الجديدة',
          updateSuccess: 'تم تحديث الملف الشخصي بنجاح',
          updateError: 'خطأ في تحديث الملف الشخصي',
          passwordSuccess: 'تم تغيير كلمة المرور بنجاح',
          passwordError: 'خطأ في تغيير كلمة المرور',
          imageSuccess: 'تم تحديث صورة الملف الشخصي بنجاح',
          imageError: 'خطأ في تحديث صورة الملف الشخصي'
        },
        teacherRole: {
          platform_teacher: 'معلم معتمد',
          new_teacher: 'معلم جديد'
        },
        teachers: {
          title: 'المعلمون',
          searchPlaceholder: 'البحث عن معلمين...',
          name: 'الاسم',
          email: 'البريد الإلكتروني',
          gender: 'الجنس',
          role: 'الدور',
          actions: 'الإجراءات',
          viewDetails: 'عرض التفاصيل',
          teacherDetails: 'تفاصيل المعلم',
          personalInfo: 'المعلومات الشخصية',
          deleteConfirm: 'هل أنت متأكد من حذف هذا المعلم؟',
          deleteSuccess: 'تم حذف المعلم بنجاح',
          deleteError: 'خطأ في حذف المعلم',
          fetchError: 'خطأ في جلب المعلمين'
        },
        students: {
          title: 'الطلاب',
          searchPlaceholder: 'البحث عن طلاب...',
          name: 'الاسم',
          email: 'البريد الإلكتروني',
          gender: 'الجنس',
          country: 'الدولة',
          age: 'العمر',
          timezone: 'المنطقة الزمنية',
          actions: 'الإجراءات',
          viewDetails: 'عرض التفاصيل',
          studentDetails: 'تفاصيل الطالب',
          personalInfo: 'المعلومات الشخصية',
          learningPreferences: 'تفضيلات التعلم',
          nativeLanguage: 'اللغة الأم',
          islamLearningLanguage: 'لغة تعلم الإسلام',
          arabicLearningLanguage: 'لغة تعلم العربية',
          arabicProficiencyLevel: 'مستوى إتقان اللغة العربية',
          privateTutoring: 'التدريس الخصوصي',
          profileCompleted: 'اكتمال الملف الشخصي',
          deleteConfirm: 'هل أنت متأكد من حذف هذا الطالب؟',
          deleteSuccess: 'تم حذف الطالب بنجاح',
          deleteError: 'خطأ في حذف الطالب',
          fetchError: 'خطأ في جلب الطلاب',
          noStudents: 'لم يتم العثور على طلاب',
          delete: 'حذف',
          proficiencyLevels: {
            beginner: 'مبتدئ',
            intermediate: 'متوسط',
            advanced: 'متقدم'
          }
        },
        categories: {
          title: 'التصنيفات',
          addNew: 'إضافة تصنيف جديد',
          editTitle: 'تعديل التصنيف',
          addTitle: 'إضافة تصنيف',
          name: 'الاسم',
          description: 'الوصف',
          createdBy: 'تم الإنشاء بواسطة',
          updateSuccess: 'تم تحديث التصنيف بنجاح',
          createSuccess: 'تم إنشاء التصنيف بنجاح',
          deleteSuccess: 'تم حذف التصنيف بنجاح',
          deleteConfirm: 'هل أنت متأكد من حذف هذا التصنيف؟',
          fetchError: 'خطأ في جلب التصنيفات',
          saveError: 'خطأ في حفظ التصنيف',
          deleteError: 'خطأ في حذف التصنيف',
          unauthorized: 'غير مصرح به',
          invalidId: 'معرف التصنيف غير صالح'
        },
        languages: {
          title: 'اللغات',
          addNew: 'إضافة لغة جديدة',
          editTitle: 'تعديل اللغة',
          addTitle: 'إضافة لغة',
          name: 'اسم اللغة',
          nameRequired: 'اسم اللغة مطلوب',
          unauthorized: 'غير مصرح لك بتنفيذ هذا الإجراء',
          fetchError: 'خطأ في جلب اللغات',
          createSuccess: 'تم إنشاء اللغة بنجاح',
          createError: 'خطأ في إنشاء اللغة',
          updateSuccess: 'تم تحديث اللغة بنجاح',
          updateError: 'خطأ في تحديث اللغة',
          deleteSuccess: 'تم حذف اللغة بنجاح',
          deleteError: 'خطأ في حذف اللغة',
          deleteConfirm: 'هل أنت متأكد أنك تريد حذف هذه اللغة؟',
          invalidId: 'معرف اللغة غير صالح',
          saveError: 'خطأ في حفظ اللغة'
        },
        applications: {
          searchPlaceholder: 'البحث في الطلبات...',
          filterByStatus: 'تصفية حسب الحالة',
          name: 'الاسم',
          email: 'البريد الإلكتروني',
          phone: 'الهاتف',
          country: 'الدولة',
          languages: 'اللغات',
          status: 'الحالة',
          actions: 'الإجراءات',
          statuses: {
            pending: 'قيد المراجعة',
            approved: 'مقبول',
            rejected: 'مرفوض'
          },
          title: 'طلبات المعلمين',
          allStatuses: 'جميع الحالات',
          viewDetails: 'عرض التفاصيل',
          approve: 'موافقة',
          reject: 'رفض',
          applicationDetails: 'تفاصيل الطلب',
          personalInfo: 'المعلومات الشخصية',
          teachingInfo: 'معلومات التدريس',
          documents: 'المستندات',
          nativeLanguage: 'اللغة الأم',
          teachingLanguages: 'لغات التدريس',
          qualifications: 'المؤهلات',
          experience: 'الخبرة',
          pricePerLesson: 'سعر الدرس',
          viewVideo: 'عرض الفيديو التعريفي',
          viewCV: 'عرض السيرة الذاتية',
          basicInfo: 'المعلومات الأساسية',
          teachingDetails: 'تفاصيل التدريس',
          schedule: 'الجدول الزمني',
          location: 'الموقع',
          applicationDate: 'تاريخ التقديم',
          courseTypes: 'أنواع الدورات',
          pricing: 'التسعير',
          paymentMethod: 'طريقة الدفع',
          introVideo: 'الفيديو التعريفي',
          cv: 'السيرة الذاتية',
          availableHours: 'الساعات المتاحة',
          yearsOfExperience: '{{years}} سنوات من الخبرة'
        }
      },
      menu: {
        dashboard: 'لوحة التحكم',
        meetings: 'الاجتماعات',
        profile: 'الملف الشخصي',
        chat: 'المحادثات',
        platformPolicy: 'سياسة المنصة'
      },
      meetings: {
        title: 'الاجتماعات',
        myMeetings: 'اجتماعاتي',
        description: 'عرض وإدارة اجتماعاتك المجدولة مع المعلمين',
        noMeetings: 'لا توجد اجتماعات مجدولة',
        noMeetingsDescription: 'ليس لديك أي اجتماعات مجدولة حتى الآن. احجز درسًا للبدء!',
        teacher: 'المعلم',
        date: 'التاريخ',
        time: 'الوقت',
        fetchError: 'خطأ في جلب الاجتماعات',
        with: 'مع',
        createNew: 'إنشاء اجتماع جديد',
        meetingName: 'اسم الاجتماع',
        meetingDate: 'تاريخ الاجتماع',
        duration: 'المدة',
        minutes: 'دقيقة',
        create: 'إنشاء الاجتماع',
        start: 'بدء الاجتماع',
        join: 'انضمام للاجتماع',
        notStarted: 'لم يبدأ الاجتماع بعد',
        ended: 'انتهى الاجتماع',
        joinError: 'خطأ في الانضمام للاجتماع',
        cancel: 'إلغاء الاجتماع',
        copyLink: 'نسخ رمز الغرفة',
        linkCopied: 'تم نسخ رمز الغرفة',
        cancelSuccess: 'تم إلغاء الاجتماع بنجاح',
        cancelError: 'حدث خطأ أثناء إلغاء الاجتماع',
        createSuccess: 'تم إنشاء الاجتماع بنجاح',
        createError: 'حدث خطأ أثناء إنشاء الاجتماع',
        halfLesson: 'نصف حصة',
        fullLesson: 'حصة كاملة',
        currency: 'دولار',
        status: {
          pending: 'قيد الانتظار',
          ongoing: 'جاري',
          completed: 'مكتمل',
          cancelled: 'ملغي',
          scheduled: 'مجدول'
        },
        meetingCompleted: 'تم إنهاء الاجتماع بنجاح',
        errorCompletingMeeting: 'حدث خطأ أثناء إنهاء الاجتماع',
        participant: 'مشارك',
        you: 'أنت',
        sessionTime: 'وقت الجلسة',
        remainingTime: 'الوقت المتبقي',
        timeUp: 'انتهى الوقت',
        timeUpTitle: 'انتهى وقت الاجتماع',
        timeUpMessage: 'انتهى الوقت المحدد للاجتماع. سيتم إغلاق الاجتماع تلقائياً الآن.',
        timeUpDescription: 'سيتم توجيهك إلى صفحة الحجوزات.',
        backToBookings: 'العودة للحجوزات',
        active: 'نشط',
        stopped: 'متوقف',
        validation: {
          nameRequired: 'اسم الاجتماع مطلوب',
          dateRequired: 'تاريخ الاجتماع مطلوب',
          durationRequired: 'مدة الاجتماع مطلوبة',
          allFieldsRequired: 'جميع الحقول مطلوبة'
        },
        student: 'الطالب',
        dateTime: 'التاريخ والوقت',
        amount: 'المبلغ',
        actions: 'الإجراءات'
      },
      privacy: {
        title: 'سياسة الخصوصية',
        intro: 'توضح سياسة الخصوصية هذه كيف تقوم منصة Allemnionline ("نحن" أو "المنصة") بجمع المعلومات الشخصية لمستخدميها ("أنت") الذين يزورون موقعنا الإلكتروني أو يستخدمون خدماتنا التعليمية، وكيفية استخدام هذه المعلومات وحمايتها.',
        section1: {
          title: '1. المعلومات التي نجمعها',
          subtitle: 'قد نقوم بجمع المعلومات التالية:',
          item1: 'الاسم، وعنوان البريد الإلكتروني، ورقم الهاتف',
          item2: 'بيانات الدفع والفوترة',
          item3: 'معلومات الحساب (مثل المستوى اللغوي، وسجل الدروس)'
        },
        section2: {
          title: '2. كيفية استخدام المعلومات',
          subtitle: 'نستخدم معلوماتك من أجل:',
          item1: 'تقديم خدماتنا التعليمية وتحسينها',
          item2: 'إدارة جدولة الدروس وعمليات الدفع',
          item3: 'التواصل معك بخصوص دروسك وحسابك',
          item4: 'ضمان أمان المنصة ومنع الاحتيال',
          item5: 'الامتثال للالتزامات القانونية والتنظيمية'
        },
        section3: {
          title: '3. مشاركة المعلومات',
          subtitle: 'نحن لا نبيع معلوماتك الشخصية لأي طرف ثالث.',
          description: 'وقد نشاركها مع:',
          item1: 'مزودي خدمات الدفع (مثل Stripe)',
          item2: 'الجهات القانونية إذا طُلب منا ذلك بموجب القانون',
          item3: 'مزودي الخدمات الموثوقين المرتبطين باتفاقيات سرية'
        },
        section4: {
          title: '4. أمن البيانات',
          content: 'نطبق إجراءات تقنية وتنظيمية مناسبة لحماية بياناتك من الوصول أو التغيير أو الكشف غير المصرح به.'
        },
        section5: {
          title: '5. حقوقك',
          subtitle: 'يحق لك:',
          item1: 'الوصول إلى بياناتك الشخصية',
          item2: 'طلب تصحيحها أو حذفها',
          item3: 'سحب موافقتك على معالجتها',
          item4: 'تقديم شكوى لجهة حماية البيانات (إن وُجدت)',
          contact: 'لممارسة حقوقك، يُرجى التواصل معنا على البريد التالي: <EMAIL>'
        },
        section6: {
          title: '6. ملفات تعريف الارتباط (Cookies)',
          content: 'قد يستخدم موقعنا ملفات تعريف الارتباط لتحسين تجربة التصفح. يمكنك تعديل إعدادات المتصفح للتحكم في ملفات تعريف الارتباط.'
        },
        section7: {
          title: '7. خصوصية الأطفال',
          content: 'لا نقوم بجمع بيانات من الأطفال دون سن 13 عامًا بدون موافقة الوالدين. إذا كنت تعتقد أننا جمعنا بيانات من طفل دون إذن، يُرجى التواصل معنا فورًا.'
        },
        section8: {
          title: '8. الروابط الخارجية',
          content: 'قد يحتوي موقعنا على روابط إلى مواقع خارجية. لسنا مسؤولين عن ممارسات الخصوصية الخاصة بتلك المواقع.'
        },
        section9: {
          title: '9. التعديلات على هذه السياسة',
          content: 'قد نقوم بتحديث هذه السياسة من وقت لآخر. ونشجعك على مراجعتها بشكل دوري. سيتم نشر أي تغييرات على هذه الصفحة مع تاريخ نفاذ محدث.'
        },
        section10: {
          title: '10. للتواصل معنا',
          content: 'إذا كانت لديك أي أسئلة أو استفسارات بخصوص هذه السياسة، يُرجى التواصل عبر:',
          email: '<EMAIL>'
        }
      },
      policies: {
        refund: {
          title: 'سياسة الاسترداد',
          section1: {
            title: 'أولًا: حالات يُمنح فيها الطالب استرداد الدرس',
            description: 'يحق للطالب استرداد الدرس إلى رصيده داخل المنصة لإعادة جدولته، في الحالات التالية:',
            items: [
              'إذا ألغى المعلم الدرس أو تغيب عنه في الموعد المحدد.',
              'إذا تعذر عقد الدرس بسبب خلل تقني من طرف المعلم أو المنصة.'
            ]
          },
          section2: {
            title: 'ثانيًا: حالات لا يُمنح فيها استرداد الدرس أو إعادة الجدولة',
            description: 'لا يحق للطالب طلب استرداد الدرس أو إعادة جدولته في الحالات الآتية:',
            items: [
              'إلغاء الطالب للدرس خلال أقل من 12 ساعة من موعده.',
              'تغيّب الطالب عن حضور الدرس دون إشعار مسبق.',
              'فقدان الاتصال بسبب ضعف الإنترنت أو عطل في جهاز الطالب.',
              'نسيان بيانات الدخول (اسم المستخدم أو كلمة المرور).',
              'انتهاء صلاحية الرصيد نتيجة عدم استخدام المنصة لمدة تتجاوز 180 يومًا.',
              'حذف الطالب لحسابه بشكل اختياري.',
              'إيقاف الحساب نتيجة مخالفة شروط الاستخدام.'
            ]
          },
          section3: {
            title: 'ثالثًا: تحديث سياسة الاسترداد',
            description: 'تحتفظ المنصة بحق تعديل هذه السياسة في أي وقت. ويُعد استمرار استخدام المنصة بعد تحديث السياسة موافقة ضمنية على ما ورد فيها.'
          },
          contact: {
            title: 'للتواصل معنا',
            email: '📧 <EMAIL>'
          }
        },
        bookingPayment: {
          title: 'سياسة الحجز والدفع',
          subtitle: 'توضح هذه السياسة كيفية جدولة الدروس وتأكيدها وتسديد رسومها عبر منصة Allemnionline.',
          section1: {
            title: '1. حجز الدروس',
            points: [
              'يمكن للطلاب حجز دروس فردية (واحد لواحد) مع المعلمين المتاحين مباشرة عبر المنصة.',
              'يتم عرض أوقات الدروس حسب التوقيت المحلي للطالب.',
              'يجب أن يتم الحجز مسبقًا ويخضع لتوفر المعلم.',
              'بعد إتمام الحجز والدفع، يتم إرسال رسالة تأكيد تلقائيًا إلى الطالب والمعلم.'
            ]
          },
          section2: {
            title: '2. الدفع',
            points: [
              'يجب دفع رسوم الدرس مسبقًا لتأكيد الحجز.',
              'تتم عمليات الدفع بأمان عبر مزودي خدمات الدفع المعتمدين (مثل Stripe أو PayPal أو Wise).',
              'يتحمل الطالب أي رسوم تحويل أو عمولات تفرضها بنوكه أو مزودو الدفع.',
              'تُعرض أسعار الدروس بوضوح قبل إتمام الدفع.'
            ]
          },
          section3: {
            title: '3. العملة وأسعار الصرف',
            points: [
              'تتم عمليات الدفع افتراضيًا بالدولار الأمريكي (USD).',
              'تُعرض الأسعار بعملات أخرى لأغراض الإرشاد فقط.',
              'إذا كانت وسيلة الدفع الخاصة بك تستخدم عملة مختلفة، فقد تُطبق رسوم تحويل عملة أو فروقات في سعر الصرف.',
              'لا تتحمل Allemnionline أي مسؤولية عن فروقات الأسعار الناتجة عن تقلبات العملات أو الرسوم البنكية.'
            ]
          },
          section4: {
            title: '4. الضرائب والرسوم',
            points: [
              'قد تشمل الأسعار ضرائب محلية (مثل ضريبة القيمة المضافة أو ضريبة الخدمات) حسب موقع المستخدم.',
              'يتم عرض جميع الرسوم المطبقة بشكل شفاف قبل إتمام الدفع.'
            ]
          },
          section5: {
            title: '5. تأكيد الدفع والإيصالات',
            points: [
              'بعد إتمام الدفع بنجاح، يتم إرسال إيصال إلكتروني إلى الطالب عبر البريد الإلكتروني.',
              'يتم تأكيد الحجز فقط بعد معالجة الدفع بنجاح.'
            ]
          },
          section6: {
            title: '6. فشل أو تأخير الدفع',
            points: [
              'في حال فشل الدفع أو تأخره، يُعتبر الحجز "معلّقًا" ولا يُعد مؤكدًا.',
              'يجب على الطالب معالجة المشكلة فورًا لضمان الحفاظ على الحجز.'
            ]
          },
          section7: {
            title: '7. الدفع التلقائي أو الاشتراكات (إن وجدت)',
            points: [
              'إذا اختار الطالب الاشتراك أو إعادة التعبئة التلقائية، سيتم خصم الرسوم تلقائيًا من وسيلة الدفع المحفوظة.',
              'يمكن إلغاء الاشتراك أو تعديله في أي وقت من خلال إعدادات الحساب.'
            ]
          },
          section8: {
            title: '8. الدعم الفني والتواصل',
            description: 'للاستفسارات أو المشكلات المتعلقة بالدفع، يرجى التواصل مع فريق الدعم عبر:',
            email: '<EMAIL>'
          },
          features: {
            securePayments: 'الدفع الآمن',
            securePaymentsDesc: 'جميع المعاملات محمية بتقنيات التشفير المتقدمة لضمان أمان بياناتك المالية.',
            multipleCurrencies: 'عملات متعددة',
            multipleCurrenciesDesc: 'ندعم مجموعة واسعة من العملات ووسائل الدفع لتسهيل عملية الدفع عليك.',
            instantReceipts: 'إيصالات فورية',
            instantReceiptsDesc: 'تحصل على إيصال إلكتروني فوري بعد كل عملية دفع ناجحة.',
            instantConfirmation: 'تأكيد فوري',
            instantConfirmationDesc: 'يتم تأكيد الحجز فوراً بعد إتمام الدفع بنجاح.'
          }
        },
        bookingCancellation: {
          title: 'سياسة الحجز والإلغاء وإعادة الجدولة',
          subtitle: 'توضح هذه السياسة الضوابط والإجراءات المتعلقة بحجز الدروس الفردية (واحد لواحد)، وإلغائها أو تعديلها، من قبل كل من الطالب والمدرس، وذلك بهدف ضمان الانضباط وتحقيق أفضل تجربة تعليمية.',
          studentPolicy: {
            title: 'أولًا: سياسة الطالب',
            booking: {
              title: '1. حجز الدروس',
              points: [
                'يمكن للطالب حجز أي وقت متاح في جدول المعلم.',
                'يتم تأكيد الحجز فور إتمام الدفع عبر المنصة.'
              ]
            },
            cancellation: {
              title: '2. إلغاء الدرس',
              points: [
                'يمكن للطالب إلغاء الدرس دون أي خصم بشرط أن يتم الإلغاء قبل 12 ساعة على الأقل من موعد الدرس.',
                'في حال الإلغاء خلال أقل من 12 ساعة، يتم خصم قيمة الدرس كاملة.',
                'إذا تغيب الطالب عن حضور الدرس دون إلغاء مسبق، يُعتبر الدرس مكتملًا ولا يحق له استرداد أي مبلغ.'
              ]
            },
            rescheduling: {
              title: '3. تعديل موعد الدرس',
              points: [
                'يحق للطالب تعديل موعد الدرس مرة واحدة فقط لكل حجز، بشرط أن يتم التعديل قبل 12 ساعة على الأقل من الموعد الأصلي.',
                'يمكن للطالب التواصل المباشر مع المعلم وطلب إعادة الجدولة بشكل ودي حتى قبل أقل من 12 ساعة، ويعود القرار في هذه الحالة إلى المعلم.'
              ]
            },
            lateArrival: {
              title: '4. التأخر في الحضور',
              points: [
                'يُمنح الطالب فترة سماح مدتها 15 دقيقة بعد وقت بدء الدرس.',
                'إذا لم يحضر الطالب خلال هذه الفترة ودون إشعار مسبق، يُعتبر الدرس لاغيًا ويُحسب كاملًا.'
              ]
            }
          },
          tutorPolicy: {
            title: 'ثانيًا: سياسة المعلم',
            availability: {
              title: '1. إتاحة المواعيد للحجز',
              points: [
                'يجب على المعلم تحديث جدوله بانتظام، وتحديد أوقات الدروس المتاحة بدقة وشفافية.'
              ]
            },
            cancellation: {
              title: '2. إلغاء أو إعادة جدولة الدروس',
              points: [
                'يلتزم المعلم بإشعار الطالب فورًا في حال قرر إلغاء الدرس أو إعادة جدولته.',
                'في حال تكرار إلغاء الدروس من طرف المعلم أو تغيب دون إشعار، تحتفظ المنصة بحق تعليق الحساب مؤقتًا أو اتخاذ ما يلزم من الإجراءات الإدارية.'
              ]
            },
            rescheduling: {
              title: '3. تعديل مواعيد الدروس',
              points: [
                'يحق للمعلم إلغاء الدرس أو تعديله بشرط أن يتم ذلك قبل أكثر من 12 ساعة من الموعد المحدد.',
                'يمكنه إعادة جدولة الدرس بشرط تقديم سبب واضح عبر المنصة وإبلاغ الطالب بذلك.'
              ]
            },
            lateArrival: {
              title: '4. التأخر في الحضور',
              points: [
                'يُسمح للمعلم بفترة تأخير لا تتجاوز 15 دقيقة.',
                'إذا لم يحضر المعلم بعد انقضاء هذه المدة، يُمنح الطالب خيار إعادة جدولة الدرس أو استرداد كامل المبلغ.'
              ]
            }
          },
          generalNotes: {
            title: 'ملاحظات عامة',
            points: [
              'يتم احتساب جميع المدد الزمنية حسب توقيت الطالب المحلي.',
              'تحتفظ المنصة بحق تعديل هذه السياسة بما يحقق مصلحة العملية التعليمية، وسيتم إشعار جميع المستخدمين بأي تحديثات رسمية.'
            ]
          },
          summary: {
            forStudents: 'للطالب',
            forTutors: 'للمعلم',
            freeCancellation: 'إلغاء مجاني قبل 12 ساعة',
            gracePeriod: 'فترة سماح 15 دقيقة',
            oneReschedule: 'تعديل مرة واحدة لكل حجز',
            cancellationBefore: 'إلغاء قبل 12+ ساعة',
            delayAllowance: 'فترة تأخير 15 دقيقة',
            immediateNotification: 'إشعار فوري للطالب',
            importantNote: 'ملاحظة مهمة: جميع الأوقات تُحسب حسب التوقيت المحلي للطالب'
          }
        }
      },
      platformPolicies: 'سياسات المنصة',
      about: {
        title: 'من نحن',
        intro: 'منصة Allemnionline هي منصة تعليمية عبر الإنترنت متخصصة في تقديم خدمات تعليم اللغة العربية والمعرفة ذات الصلة بالثقافة العربية للمتعلمين من جميع أنحاء العالم، من خلال دروس مباشرة (واحد لواحد) يقدمها معلمون مؤهلون ومحترفون.',
        mission: 'تسعى المنصة إلى جعل تعلم اللغة العربية ميسّرًا وفعّالًا، مع مراعاة الفروق الفردية والاحتياجات الخاصة لكل متعلم، وذلك باستخدام أحدث الوسائل التقنية التعليمية.',
        whatWeOffer: 'ماذا نقدم؟',
        services: {
          privateLessons: 'دروس خصوصية في اللغة العربية لجميع المستويات: من المبتدئين إلى المتقدمين.',
          conversationTraining: 'تدريب على المحادثة، الاستماع، القراءة، والكتابة باللغة العربية.',
          culturalElements: 'تعريف بالعناصر الأساسية من الثقافة العربية التي تساعد على فهم اللغة في سياقها الطبيعي.',
          digitalPlatform: 'منصة رقمية متكاملة تتيح التواصل المباشر عبر الفيديو، وجدولة الدروس، والدفع الإلكتروني الآمن.',
          targetAudience: 'خدماتنا موجهة للأطفال، والبالغين، والمهنيين، ولكل من يرغب في تعلم العربية لأغراض أكاديمية أو شخصية أو مهنية.'
        },
        ourMission: 'رسالتنا',
        missionText: 'أن نوفّر تعليمًا متميزًا للغة العربية والمعرفة الثقافية المرتبطة بها، بجودة عالية، ومن خلال التعليم المباشر والتقنيات الحديثة، وبأسلوب يحترم تنوع المتعلمين وخصوصياتهم الثقافية.',
        contactUs: 'للتواصل معنا',
        contactText: 'لأي استفسارات أو ملاحظات، يُرجى التواصل عبر البريد الإلكتروني:',
        email: '<EMAIL>'
      }
    }
  }
};

// Define the complete merged resources
const mergedResources = {
  en: {
    translation: {
      ...resources.en.translation,
      privacyPolicy: {
        title: 'Privacy Policy & Terms of Service',
        intro: 'Welcome to TeachMeIslam platform. Before registering, please read and agree to our privacy policy and terms of service, which include details about commission rates, booking and cancellation policies.',
        bookingPolicy: {
          title: 'Booking and Cancellation Policy',
        },
        studentPolicy: {
          title: 'Student Policy',
          booking: {
            title: 'Booking Lessons',
            description: 'Students can book a lesson at any available time in the teacher\'s schedule. Booking is confirmed immediately upon payment through the platform.'
          },
          cancellation: {
            title: 'Cancelling Lessons',
            description: 'Students can cancel a lesson without penalty at least 12 hours before the scheduled time. If cancelled less than 12 hours before, the full lesson fee will be charged. If a student is absent without cancellation, the lesson is considered completed and no refund will be issued.'
          },
          rescheduling: {
            title: 'Rescheduling Lessons',
            description: 'Lessons can be rescheduled once per lesson, provided the change is made at least 12 hours before the original scheduled time.'
          },
          attendance: {
            title: 'Attendance',
            description: 'If a student is more than 15 minutes late without notice, they will be considered absent and the lesson will be counted as completed.'
          }
        },
        teacherPolicy: {
          title: 'Teacher Policy',
          availability: {
            title: 'Availability',
            description: 'Teachers must regularly update their schedule and accurately indicate available times.'
          },
          cancellation: {
            title: 'Cancelling or Rescheduling',
            description: 'Teachers must notify students when cancelling or rescheduling any lesson as soon as possible. Repeated cancellations or absences without notice may result in temporary account suspension or appropriate administrative action.'
          },
          rescheduling: {
            title: 'Rescheduling Lessons',
            description: 'Teachers may suggest rescheduling with prior agreement from the student through the platform.'
          },
          attendance: {
            title: 'Attendance',
            description: 'Teachers are allowed a maximum delay of 15 minutes. If a teacher does not attend after 15 minutes, the student has the right to reschedule the lesson or receive a full refund.'
          }
        },
        generalNotes: {
          title: 'General Notes',
          timeZone: 'All time periods are calculated according to the student\'s time zone.',
          policyChanges: 'The platform reserves the right to modify these policies to benefit the educational process, with notification to users of any changes.'
        },
        commissionPolicy: {
          title: 'Commission Policy',
          description: 'The platform applies the following commission rates on lesson fees:',
          rates: {
            3: 'For $3 lessons: 33.3% commission',
            4: 'For $4 lessons: 25% commission',
            5: 'For $5 lessons: 20% commission',
            6: 'For $6 lessons: 16.7% commission',
            7: 'For $7 and above lessons: 15% commission'
          },
          explanation: 'The current commission rates have been set to be fair and motivating, and may be subject to periodic review to achieve a balance between service quality and operating costs, with teachers being notified of any changes in advance.'
        },

        agreement: {
          checkbox: 'I have read and agree to the Privacy Policy and Terms of Service',
          required: 'You must agree to the Privacy Policy and Terms of Service to register'
        }
      },
      admin: {
        ...resources.en.translation.admin
      },
      teacher: {
        ...resources.en.translation.teacher,
        myLessons: 'My Lessons',
        studentName: 'Student Name',
        totalLessons: 'Total Lessons',
        noLessonsFound: 'No lessons found',
        submitComplaint: 'Submit Complaint',
        complaintReason: 'Reason for Complaint',
        complaintType1: 'Student attended but commission not transferred',
        complaintType2: 'Student did not attend at all',
        complaintDetails: 'Complaint Details (optional)',
        complaintDetailsPlaceholder: 'Write any additional details if needed...',
        complaintStatus: 'Complaint Status',
        complaintStatusValues: {
          pending: 'Pending',
          resolved: 'Resolved'
        },
        videoUpload: {
          title: 'Upload Introduction Video',
          description: 'Upload a short video introducing yourself to potential students. This video will be shown on your profile.',
          requirements: 'Video Requirements',
          formatRequirement: 'Allowed formats',
          sizeRequirement: 'Maximum size',
          lengthRequirement: 'Recommended length',
          minutes: 'minutes',
          selectVideo: 'Select Video',
          upload: 'Upload Video',
          uploading: 'Uploading...',
          success: 'Video uploaded successfully!',
          videoReady: 'Your video is ready. You can now continue to the application form or upload a different video.',
          continue: 'Continue to Application',
          delete: 'Delete Video',
          skipForNow: 'Skip for now',
        },
        editVideoUpload: {
          title: 'Edit Introduction Video',
          description: 'You can change your introduction video here. The new video will replace the current one.',
          requirements: 'Video Requirements',
          formatRequirement: 'Format',
          sizeRequirement: 'Size',
          lengthRequirement: 'Duration',
          minutes: 'minutes',
          selectVideo: 'Select New Video',
          upload: 'Upload Video',
          uploading: 'Uploading...',
          success: 'Video uploaded successfully!',
          videoReady: 'Video is ready and saved',
          saveAndReturn: 'Save and Return',
          delete: 'Delete Video',
          invalidVideoFormat: 'Invalid video format. Please select MP4, WebM, or OGG file.',
          videoTooSmall: 'Video size must be at least 1MB.',
          videoTooLarge: 'Video size must not exceed 100MB.',
          noVideoSelected: 'Please select a video file.',
          videoDeleteError: 'Error deleting video. Please try again.'
        },
        commitment: 'Teacher Commitment',
        commitmentDescription: 'Please read and agree to the following commitment',
        commitmentProfileDescription: 'View and manage your commitment status and details',
        commitmentRequired: 'You must agree to the commitment to continue',
        commitmentStatus: {
          accepted: 'Commitment accepted',
          pending: 'Commitment pending approval',
          rejected: 'Commitment rejected'
        },
        commitmentTitle: 'Teacher Commitment',
        commitmentAccepted: 'Commitment accepted',
        readCommitment: 'Read and agree to the commitment',
        accept: 'I Agree',
        reject: 'I Decline',
        yourEarnings: 'Your earnings after commission:',

        commitmentText: {
          intro: 'I, the teacher applying to teach through the "Allemnionline in All Languages" platform, hereby acknowledge and pledge the following:',
          point1: 'To uphold honesty, integrity, and sincerity in fulfilling my educational mission, and to adhere to noble Islamic ethics in my dealings with students and the administration.',
          point2: 'To ensure that the teaching of Islam and Arabic is in accordance with the methodology of Ahl al-Sunnah wa al-Jama\'ah, and to respect the consensus of recognized scholars.',
          point3: 'To adhere to the methodology of the righteous predecessors (Salaf) in creed, methodology, and conduct, and not to deviate from what is found in the Quran and Sunnah as understood by the Companions (may Allah be pleased with them) and those who followed them in righteousness.',
          point4: 'To refrain from praising or promoting any deviant, extremist groups or ideas, or those with partisan or political orientations that contradict the methodology of the Salaf.',
          point5: 'Not to exploit the platform to spread ideas or orientations that contradict its religious and scholarly identity, and to limit teaching to what is approved and prescribed by the platform administration.',
          point6: 'To cooperate with the platform administration with complete transparency and respect, and to update data, work schedules, and respond to students in a timely manner.',
          conclusion: 'I acknowledge that violating what is stated in this commitment authorizes the platform administration to take appropriate measures as it deems fit, including suspending the account or terminating cooperation.'
        }
      }
    }
  },
  ar: {
    translation: {
      ...resources.ar.translation,
      privacyPolicy: {
        title: 'سياسة الخصوصية وشروط الخدمة',
        intro: 'مرحبًا بك في منصة TeachMeIslam. قبل التسجيل، يرجى قراءة والموافقة على سياسة الخصوصية وشروط الخدمة، والتي تتضمن تفاصيل حول معدلات العمولة وسياسات الحجز والإلغاء.',
        bookingPolicy: {
          title: 'سياسة الحجز والإلغاء',
        },
        studentPolicy: {
          title: 'سياسة الطالب',
          booking: {
            title: 'حجز الدروس',
            description: 'يمكن للطالب حجز درس في أي وقت متاح في جدول المدرس. يتم تأكيد الحجز فور إتمام الدفع عبر المنصة.'
          },
          cancellation: {
            title: 'إلغاء الدروس',
            description: 'يمكن للطالب إلغاء الدرس دون غرامة قبل 12 ساعة على الأقل من موعد الدرس. إذا تم الإلغاء خلال أقل من 12 ساعة، تُخصم قيمة الدرس كاملة. إذا تغيب الطالب دون إلغاء، يُعتبر الدرس مكتملًا ولا يحق له استرداد أي مبلغ.'
          },
          rescheduling: {
            title: 'تعديل موعد الدرس',
            description: 'يُسمح بتعديل موعد الدرس مرة واحدة فقط لكل درس، بشرط أن يكون ذلك قبل 12 ساعة من الموعد الأصلي.'
          },
          attendance: {
            title: 'التأخير في الحضور',
            description: 'إذا تأخر الطالب لأكثر من 15 دقيقة دون إشعار، يُعتبر غائبًا ويُحسب الدرس.'
          }
        },
        teacherPolicy: {
          title: 'سياسة المدرس',
          availability: {
            title: 'توفر المدرس للحجز',
            description: 'يلتزم المدرس بتحديث جدول مواعيده بشكل منتظم، وتحديد الأوقات المتاحة بدقة.'
          },
          cancellation: {
            title: 'إلغاء الدرس أو إعادة الجدولة',
            description: 'يجب على المدرس إشعار الطالب عند إلغاء أو إعادة جدولة أي درس، وفي أقرب وقت ممكن. في حال تكرر إلغاء الدروس أو تغيب المدرس دون إشعار، قد يتم تعليق الحساب مؤقتًا أو اتخاذ إجراءات إدارية مناسبة.'
          },
          rescheduling: {
            title: 'تعديل موعد الدرس',
            description: 'يمكن للمدرس اقتراح تعديل الموعد باتفاق مسبق مع الطالب عبر المنصة.'
          },
          attendance: {
            title: 'التأخير في الحضور',
            description: 'يُمنح المدرس مهلة تأخير تصل إلى 15 دقيقة كحد أقصى. إذا لم يحضر المدرس بعد مرور 15 دقيقة، يُمنح الطالب حق إعادة جدولة الدرس أو استرداد قيمته كاملة.'
          }
        },
        generalNotes: {
          title: 'ملاحظات عامة',
          timeZone: 'يتم احتساب كل المدد الزمنية بحسب توقيت الطالب.',
          policyChanges: 'تحتفظ المنصة بحقها في تعديل هذه السياسات بما يحقق مصلحة العملية التعليمية، مع إشعار المستخدمين بأي تغييرات.'
        },
        commissionPolicy: {
          title: 'سياسة العمولة',
          description: 'تطبق المنصة معدلات العمولة التالية على رسوم الدروس:',
          rates: {
            3: 'للدروس بقيمة 3 دولار: عمولة 33.3%',
            4: 'للدروس بقيمة 4 دولار: عمولة 25%',
            5: 'للدروس بقيمة 5 دولار: عمولة 20%',
            6: 'للدروس بقيمة 6 دولار: عمولة 16.7%',
            7: 'للدروس بقيمة 7 دولار وما فوق: عمولة 15%'
          },
          explanation: 'نسبة العمولة الحالية تم تحديدها لتكون عادلة ومحفزة، وقد تخضع للمراجعة الدورية بما يحقق التوازن بين جودة الخدمة وتكاليف التشغيل، على أن يُبلغ المدرسون بأي تعديل مسبقًا.'
        },

        agreement: {
          checkbox: 'لقد قرأت وأوافق على سياسة الخصوصية وشروط الخدمة',
          required: 'يجب أن توافق على سياسة الخصوصية وشروط الخدمة للتسجيل'
        }
      },
      teacher: {
        ...resources.ar.translation.teacher,
        myLessons: 'دروسي',
        studentName: 'اسم الطالب',
        totalLessons: 'إجمالي الدروس',
        noLessonsFound: 'لا توجد دروس',
        submitComplaint: 'تقديم شكوى',
        complaintReason: 'سبب الشكوى',
        complaintType1: 'الطالب حضر لكن العمولة لم تُحول',
        complaintType2: 'الطالب لم يحضر نهائياً',
        complaintDetails: 'تفاصيل الشكوى (اختياري)',
        complaintDetailsPlaceholder: 'اكتب أي تفاصيل إضافية إذا لزم الأمر...',
        complaintStatus: 'حالة الشكوى',
        complaintStatusValues: {
          pending: 'في الانتظار',
          resolved: 'تم الحل'
        },
        videoUpload: {
          title: 'رفع فيديو تعريفي',
          description: 'قم برفع فيديو قصير لتقديم نفسك للطلاب المحتملين. سيتم عرض هذا الفيديو على ملفك الشخصي.',
          requirements: 'متطلبات الفيديو',
          formatRequirement: 'الصيغ المسموح بها',
          sizeRequirement: 'الحجم الأقصى',
          lengthRequirement: 'المدة المقترحة',
          minutes: 'دقائق',
          selectVideo: 'اختر الفيديو',
          upload: 'رفع الفيديو',
          uploading: 'جاري الرفع...',
          success: 'تم رفع الفيديو بنجاح!',
          videoReady: 'فيديو التعريف جاهز. يمكنك الآن المتابعة إلى نموذج التقديم أو رفع فيديو مختلف.',
          continue: 'متابعة إلى نموذج التقديم',
          delete: 'حذف الفيديو',
          skipForNow: 'تخطي الآن',
        },
        editVideoUpload: {
          title: 'تعديل الفيديو التعريفي',
          description: 'يمكنك تغيير الفيديو التعريفي الخاص بك هنا. الفيديو الجديد سيحل محل الفيديو الحالي.',
          requirements: 'متطلبات الفيديو',
          formatRequirement: 'الصيغة',
          sizeRequirement: 'الحجم',
          lengthRequirement: 'المدة',
          minutes: 'دقائق',
          selectVideo: 'اختر فيديو جديد',
          upload: 'رفع الفيديو',
          uploading: 'جاري الرفع...',
          success: 'تم رفع الفيديو بنجاح!',
          videoReady: 'الفيديو جاهز ومحفوظ',
          saveAndReturn: 'حفظ والعودة',
          delete: 'حذف الفيديو',
          invalidVideoFormat: 'صيغة الفيديو غير صالحة. يرجى اختيار ملف MP4 أو WebM أو OGG.',
          videoTooSmall: 'حجم الفيديو يجب أن يكون على الأقل 1 ميجابايت.',
          videoTooLarge: 'حجم الفيديو يجب ألا يتجاوز 100 ميجابايت.',
          noVideoSelected: 'يرجى اختيار ملف فيديو.',
          videoDeleteError: 'خطأ في حذف الفيديو. يرجى المحاولة مرة أخرى.'
        },
        commitment: 'التعهد',
        commitmentDescription: 'يرجى قراءة والموافقة على التعهد التالي',
        commitmentProfileDescription: 'عرض وإدارة حالة وتفاصيل التعهد الخاص بك',
        commitmentRequired: 'يجب الموافقة على التعهد للمتابعة',
        commitmentStatus: {
          accepted: 'تم قبول التعهد',
          pending: 'في انتظار الموافقة على التعهد',
          rejected: 'تم رفض التعهد'
        },
        commitmentTitle: 'تعهد المعلمين',
        commitmentAccepted: 'تم قبول التعهد',
        readCommitment: 'قراءة التعهد والموافقة عليه',
        accept: 'أوافق',
        reject: 'أرفض',
        yourEarnings: 'ما ستحصل عليه بعد خصم العمولة:',

        commitmentText: {
          intro: 'أنا المدرّس/ـة المتقدّم/ـة للتدريس عبر منصة "علّمني أون لاين بجميع اللغات"، أُقرّ وأتعهد بما يلي:',
          point1: 'التحلّي بالصدق والأمانة والإخلاص في أداء رسالتي التعليمية، والالتزام بالأخلاق الإسلامية الفاضلة في تعاملي مع الطلاب والإدارة.',
          point2: 'الحرص على تعليم الإسلام والعربية وفق منهج أهل السنة والجماعة، ومراعاة ما اتفقت عليه كلمة أهل العلم المعتبرين.',
          point3: 'الالتزام بمذهب السلف الصالح في العقيدة والمنهج والسلوك، وعدم الخروج عن ما جاء في الكتاب والسنة بفهم الصحابة رضي الله عنهم ومن تبعهم بإحسان.',
          point4: 'الامتناع عن الإشادة أو الترويج لأي جماعات أو أفكار منحرفة أو متطرفة أو ذات طابع حزبي أو سياسي مخالف لمنهج السلف.',
          point5: 'عدم استغلال المنصة في نشر أفكار أو توجهات مخالفة لهويتها الشرعية والعلمية، والاقتصار في التدريس على ما هو معتمد ومقرّر من قبل إدارة المنصة.',
          point6: 'التعاون مع إدارة المنصة بكل شفافية واحترام، وتحديث البيانات وتوقيت العمل والرد على الطلاب في الوقت المحدد.',
          conclusion: 'وأقرّ بأن مخالفة ما ورد في هذا التعهّد يخول إدارة المنصة اتخاذ ما تراه مناسبًا من إجراءات، بما في ذلك تعليق الحساب أو إنهاء التعاون.'
        }
      },
      admin: {
        ...resources.ar.translation.admin,
        withdrawalManagement: {
          title: 'إدارة طلبات السحب',
          statusFilter: 'تصفية الحالة',
          all: 'الكل',
          date: 'التاريخ',
          teacher: 'المعلم',
          amount: 'المبلغ',
          paypalEmail: 'بريد PayPal الإلكتروني',
          status: 'الحالة',
          notes: 'الملاحظات',
          actions: 'الإجراءات',
          approve: 'موافقة',
          reject: 'رفض',
          approveWithdrawal: 'الموافقة على السحب',
          rejectWithdrawal: 'رفض السحب',
          notesOptional: 'ملاحظات (اختيارية)',
          approveAndProcess: 'موافقة ومعالجة',
          processingInfo: 'سيتم معالجة السحب عبر PayPal Payouts API.',
          errorFetching: 'خطأ في جلب طلبات السحب',
          errorProcessing: 'خطأ في معالجة السحب'
        },
        earnings: {
          title: 'أرباح المنصة',
          totalCommission: 'إجمالي العمولة',
          totalLessons: 'إجمالي الدروس',
          totalRevenue: 'إجمالي الإيرادات',
          avgCommissionRate: 'متوسط معدل العمولة',
          startDate: 'تاريخ البداية',
          endDate: 'تاريخ النهاية',
          filter: 'تصفية',
          clearFilter: 'مسح التصفية',
          date: 'التاريخ',
          meeting: 'الاجتماع',
          teacher: 'المعلم',
          student: 'الطالب',
          lessonAmount: 'مبلغ الدرس',
          commissionRate: 'معدل العمولة',
          commissionAmount: 'مبلغ العمولة',
          teacherEarnings: 'أرباح المعلم',
          errorFetching: 'خطأ في جلب بيانات الأرباح'
        },
        dashboard: {
          title: 'لوحة التحكم',
          welcomeMessage: 'مرحبًا بك في لوحة تحكم المدير',
          overview: 'إليك نظرة عامة على إحصائيات منصتك والأنشطة الحديثة.',
          totalTeachers: 'إجمالي المعلمين',
          pendingApplications: 'الطلبات المعلقة',
          totalStudents: 'إجمالي الطلاب',
          totalRevenue: 'إجمالي الإيرادات',
          totalCourseCategories: 'إجمالي الفئات',
          totalLanguages: 'إجمالي اللغات',
          totalBookings: 'إجمالي الحجوزات',
          bookingStats: 'إحصائيات الحجز',
          teachersByLanguage: 'المعلمون حسب اللغة',
          studentsByCountry: 'الطلاب حسب البلد',
          recentApplications: 'الطلبات الحديثة',
          recentStudents: 'الطلاب الجدد',
          viewAll: 'عرض الكل',
          viewDetails: 'عرض التفاصيل',
          noApplications: 'لا توجد طلبات حديثة',
          noStudents: 'لا يوجد طلاب جدد',
          noStudentsByCountry: 'لا توجد بيانات طلاب حسب البلد',
          noTeachersByLanguage: 'لا توجد بيانات معلمين حسب اللغة',
          noBookingStats: 'لا توجد بيانات حجز',
          completed: 'مكتمل',
          cancelled: 'ملغي',
          pending: 'قيد الانتظار',
          fetchError: 'خطأ في جلب بيانات لوحة التحكم'
        },
        meetingSessions: {
          title: 'تقارير وقت الاجتماعات',
          overview: 'عرض تفصيلي لأوقات انضمام وخروج المعلمين والطلاب في الاجتماعات',
          totalMeetings: 'إجمالي الاجتماعات',
          totalTime: 'إجمالي الوقت',
          teacherTime: 'وقت المعلمين',
          studentTime: 'وقت الطلاب',
          filterResults: 'فلترة النتائج',
          userType: 'نوع المستخدم',
          all: 'الكل',
          teacher: 'معلم',
          student: 'طالب',
          reset: 'إعادة تعيين',
          sessions: 'جلسات الاجتماعات',
          user: 'المستخدم',
          type: 'النوع',
          meeting: 'الاجتماع',
          joinTime: 'وقت الانضمام',
          leaveTime: 'وقت الخروج',
          duration: 'المدة',
          status: 'الحالة',
          active: 'نشط',
          ended: 'منتهي'
        },
        meetingIssues: {
          title: 'مشاكل الاجتماعات'
        },
        messages: {
          title: 'الرسائل',
          from: 'من',
          type: 'النوع',
          subject: 'الموضوع',
          date: 'التاريخ',
          status: 'الحالة',
          pending: 'قيد الانتظار',
          answered: 'تم الرد',
          all: 'جميع الرسائل',
          view: 'عرض',
          reply: 'رد',
          delete: 'حذف',
          search: 'البحث في الرسائل...',
          noMessages: 'لا توجد رسائل',
          fetchError: 'خطأ في جلب الرسائل',
          replyTo: 'رد على',
          originalMessage: 'الرسالة الأصلية',
          yourReply: 'ردك',
          sendReply: 'إرسال الرد',
          replySent: 'تم إرسال الرد بنجاح',
          replyError: 'خطأ في إرسال الرد',
          confirmDelete: 'تأكيد الحذف',
          deleteWarning: 'هل أنت متأكد من رغبتك في حذف هذه الرسالة؟ لا يمكن التراجع عن هذا الإجراء.',
          deleteSuccess: 'تم حذف الرسالة بنجاح',
          deleteError: 'خطأ في حذف الرسالة'
        },
        emailTemplates: {
          title: 'قوالب البريد الإلكتروني',
          description: 'إدارة قوالب البريد الإلكتروني لإشعارات النظام المختلفة.',
          instructions: 'قم بتحرير القوالب أدناه لتخصيص رسائل البريد الإلكتروني المرسلة للمستخدمين. يمكنك استخدام HTML والمتغيرات لتخصيص المحتوى.',
          approvalTemplate: 'قالب الموافقة على الطلب',
          rejectionTemplate: 'قالب رفض الطلب',
          save: 'حفظ القالب',
          saveSuccess: 'تم حفظ القالب بنجاح',
          saveError: 'خطأ في حفظ القالب',
          fetchError: 'خطأ في جلب القوالب',
          restoreDefault: 'استعادة الافتراضي',
          preview: 'معاينة',
          enterTemplate: 'أدخل محتوى القالب هنا...',
          availableVariables: 'المتغيرات المتاحة',
          variablesHelp: 'سيتم استبدال هذه المتغيرات بالقيم الفعلية عند إرسال البريد الإلكتروني.',
          testEmail: 'بريد إلكتروني تجريبي',
          testEmailPlaceholder: 'أدخل عنوان البريد الإلكتروني للاختبار',
          sendTest: 'إرسال بريد تجريبي',
          testEmailSuccess: 'تم إرسال البريد التجريبي بنجاح',
          testEmailError: 'خطأ في إرسال البريد التجريبي',
          variables: {
            teacherName: 'الاسم الكامل للمعلم',
            teacherEmail: 'عنوان البريد الإلكتروني للمعلم',
            dashboardUrl: 'رابط لوحة تحكم المعلم',
            rejectionReason: 'سبب رفض الطلب'
          }
        },
        earnings: {
          title: 'أرباح المنصة',
          totalCommission: 'إجمالي العمولة',
          totalLessons: 'إجمالي الدروس',
          totalRevenue: 'إجمالي الإيرادات',
          avgCommissionRate: 'متوسط معدل العمولة',
          startDate: 'تاريخ البداية',
          endDate: 'تاريخ النهاية',
          filter: 'تصفية',
          clearFilter: 'مسح التصفية',
          date: 'التاريخ',
          meeting: 'الاجتماع',
          teacher: 'المعلم',
          student: 'الطالب',
          lessonAmount: 'مبلغ الدرس',
          commissionRate: 'معدل العمولة',
          commissionAmount: 'مبلغ العمولة',
          teacherEarnings: 'أرباح المعلم',
          errorFetching: 'خطأ في جلب بيانات الأرباح'
        },
        passwordManagement: {
          title: 'إدارة كلمات المرور',
          description: 'إدارة طلبات إعادة تعيين كلمة المرور وكلمات مرور المستخدمين',
          searchPlaceholder: 'البحث بالاسم أو البريد الإلكتروني',
          user: 'المستخدم',
          email: 'البريد الإلكتروني',
          role: 'الدور',
          requestDate: 'تاريخ الطلب',
          status: 'الحالة',
          actions: 'الإجراءات',
          resetPassword: 'إعادة تعيين كلمة المرور',
          resetPasswordFor: 'إعادة تعيين كلمة المرور لـ {name}',
          newPassword: 'كلمة المرور الجديدة',
          generatePassword: 'توليد كلمة مرور',
          resetSuccess: 'تم إعادة تعيين كلمة المرور بنجاح لـ {email}',
          resetError: 'خطأ في إعادة تعيين كلمة المرور',
          noRequests: 'لا توجد طلبات إعادة تعيين كلمة المرور',
          statusPending: 'قيد الانتظار',
          statusCompleted: 'مكتمل',
          sendTestEmail: 'إرسال بريد إلكتروني تجريبي',
          testEmailAddress: 'عنوان البريد الإلكتروني التجريبي',
          testEmailDescription: 'إرسال بريد إلكتروني تجريبي لإعادة تعيين كلمة المرور للتحقق من قالب البريد الإلكتروني والتسليم',
          sendEmail: 'إرسال بريد إلكتروني',
          testEmailSent: 'تم إرسال البريد الإلكتروني التجريبي بنجاح إلى {email}',
          testEmailError: 'خطأ في إرسال البريد الإلكتروني التجريبي'
        }
      }
    }
  }
};

i18n
  .use(initReactI18next)
  .init({
    resources: mergedResources,
    lng: localStorage.getItem('language') || 'en',
    fallbackLng: 'en',
    debug: false,
    interpolation: {
      escapeValue: false
    },
    react: {
      useSuspense: false
    },
    // Force reload translations
    load: 'languageOnly',
    cleanCode: true
  });

export default i18n;
