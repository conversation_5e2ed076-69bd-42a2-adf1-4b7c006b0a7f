{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{useLocation}from'react-router-dom';import{Container,Typography,Box,Fade,Card,CardContent,Divider,List,ListItem,ListItemText,ListItemIcon,useTheme,alpha,Alert,Grid,Stack,Paper,Chip}from'@mui/material';import{Email as EmailIcon,Schedule as ScheduleIcon,CheckCircle as CheckCircleIcon,Cancel as CancelIcon,Payment as PaymentIcon,CurrencyExchange as CurrencyExchangeIcon,Receipt as ReceiptIcon,Warning as WarningIcon,Security as SecurityIcon,AccessTime as AccessTimeIcon,CreditCard as CreditCardIcon,Info as InfoIcon,Timer as TimerIcon,Notifications as NotificationsIcon,Person as PersonIcon,School as SchoolIcon}from'@mui/icons-material';import termsConditions from'../i18n/translations/termsConditions';import i18n from'../i18n/i18n';// Merge the Terms & Conditions keys into existing translation bundles\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";i18n.addResourceBundle('en','translation',termsConditions.en,true,true);i18n.addResourceBundle('ar','translation',termsConditions.ar,true,true);const PlatformPolicy=()=>{const{t,i18n}=useTranslation();const theme=useTheme();const location=useLocation();const isRTL=i18n.language==='ar';// Get the section from URL hash\nconst section=location.hash.replace('#','')||'terms';const SectionCard=_ref=>{let{title,children,bg,icon}=_ref;return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{mb:4,borderRadius:3,...(isRTL?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:bg||alpha(theme.palette.primary.main,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{children:[title&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[icon&&/*#__PURE__*/_jsx(Box,{sx:{mr:isRTL?0:1,ml:isRTL?1:0,color:theme.palette.primary.main},children:icon}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:title})]}),children]})});};/**\n   * Safely fetch a translation; returns empty string if key is missing.\n   */const safeT=key=>{const val=t(key);return val&&!val.includes(key)?val:'';};const renderSubSection=subSection=>{if(!subSection)return null;return/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[subSection.title&&/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:subSection.title}),subSection.description&&/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:subSection.description}),subSection.points&&/*#__PURE__*/_jsx(List,{children:subSection.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]},subSection.title);};const renderPrivacySection=(title,description,bullets)=>{return/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:title}),description&&/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:description}),bullets.length>0&&/*#__PURE__*/_jsx(List,{children:bullets.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{textAlign:isRTL?'right':'left',fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]});};// Terms and Conditions Section\nif(section==='terms'){return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,pt:4,pb:8},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:800,children:/*#__PURE__*/_jsxs(Paper,{elevation:0,sx:{p:{xs:3,md:6},borderRadius:3,background:'transparent',direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",align:\"center\",sx:{mb:4,fontWeight:800,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:t('termsConditions.title')}),/*#__PURE__*/_jsx(Stack,{spacing:4,children:t('termsConditions.content').split('\\n\\n').filter(paragraph=>paragraph.trim()).map((paragraph,index)=>/*#__PURE__*/_jsx(Card,{elevation:3,sx:{borderRadius:3,...(isRTL?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:alpha(theme.palette.primary.main,0.02),transition:'all 0.3s ease','&:hover':{transform:'translateY(-2px)',boxShadow:theme.shadows[6]}},children:/*#__PURE__*/_jsx(CardContent,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left',fontSize:'1.1rem'},children:paragraph.trim()})})},index))})]})})})});}// Booking Cancellation Policy Section\nif(section==='booking-cancellation'){const policy=t('policies.bookingCancellation',{returnObjects:true});return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,pt:4,pb:8},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:800,children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",align:\"center\",sx:{mb:4,fontWeight:800,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",align:\"center\",sx:{mb:6,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.text.secondary,maxWidth:'800px',mx:'auto'},children:policy.description}),/*#__PURE__*/_jsxs(SectionCard,{title:policy.studentCancellation.title,icon:/*#__PURE__*/_jsx(PersonIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.studentCancellation.description}),/*#__PURE__*/_jsx(List,{children:policy.studentCancellation.rules.map((rule,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(AccessTimeIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:rule,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]}),/*#__PURE__*/_jsxs(SectionCard,{title:policy.teacherCancellation.title,icon:/*#__PURE__*/_jsx(SchoolIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.teacherCancellation.description}),/*#__PURE__*/_jsx(List,{children:policy.teacherCancellation.rules.map((rule,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:rule,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]}),/*#__PURE__*/_jsxs(SectionCard,{title:policy.emergencySituations.title,icon:/*#__PURE__*/_jsx(WarningIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.emergencySituations.description}),/*#__PURE__*/_jsx(List,{children:policy.emergencySituations.examples.map((example,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"warning\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:example,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]}),/*#__PURE__*/_jsxs(SectionCard,{title:policy.notificationRequirements.title,icon:/*#__PURE__*/_jsx(NotificationsIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.notificationRequirements.description}),/*#__PURE__*/_jsx(List,{children:policy.notificationRequirements.methods.map((method,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(TimerIcon,{color:\"info\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:method,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]}),/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(EmailIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.contact.title}),/*#__PURE__*/_jsx(Typography,{children:policy.contact.email})]})]})})})});}// Booking Payment Policy Section\nif(section==='payment'){const policy=t('policies.bookingPayment',{returnObjects:true});const renderSection=section=>/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(PaymentIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))}),section.subSections&&section.subSections.map((subSection,idx)=>renderSubSection(subSection)),section.description&&/*#__PURE__*/_jsx(Typography,{sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.description})]})});return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,pt:4,pb:8},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:800,children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",mb:4,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.text.secondary,textAlign:isRTL?'right':'left'},children:policy.description}),policy.sections&&policy.sections.map((section,idx)=>/*#__PURE__*/_jsx(\"div\",{children:renderSection(section)},idx)),policy.features&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Divider,{sx:{my:3}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,mb:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(SecurityIcon,{}),severity:\"success\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.features.securePayments}),/*#__PURE__*/_jsx(Typography,{children:policy.features.securePaymentsDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(CurrencyExchangeIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.features.multipleCurrencies}),/*#__PURE__*/_jsx(Typography,{children:policy.features.multipleCurrenciesDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(ReceiptIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.features.instantReceipts}),/*#__PURE__*/_jsx(Typography,{children:policy.features.instantReceiptsDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(CheckCircleIcon,{}),severity:\"success\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.features.instantConfirmation}),/*#__PURE__*/_jsx(Typography,{children:policy.features.instantConfirmationDesc})]})})]})]}),/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(EmailIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.contact.title}),/*#__PURE__*/_jsx(Typography,{children:policy.contact.email})]})]})})})});}// Refund Policy Section\nif(section==='refund'){const refund=t('policies.refund',{returnObjects:true});return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,pt:4,pb:8},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:800,children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",align:\"center\",sx:{mb:4,fontWeight:800,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:refund.title}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",align:\"center\",sx:{mb:6,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.text.secondary,maxWidth:'800px',mx:'auto'},children:refund.description}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{title:refund.section1.title,icon:/*#__PURE__*/_jsx(CheckCircleIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refund.section1.description}),/*#__PURE__*/_jsx(List,{children:refund.section1.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif'}})]},idx))})]}),/*#__PURE__*/_jsxs(SectionCard,{title:refund.section2.title,icon:/*#__PURE__*/_jsx(CancelIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refund.section2.description}),/*#__PURE__*/_jsx(List,{children:refund.section2.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(CancelIcon,{color:\"error\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif'}})]},idx))})]}),refund.sections&&refund.sections.map((section,idx)=>/*#__PURE__*/_jsxs(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:section.description}),section.items&&/*#__PURE__*/_jsx(List,{children:section.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif'}})]},idx))})]},idx)),/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(EmailIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:refund.contact.title}),/*#__PURE__*/_jsx(Typography,{children:refund.contact.email})]})]})})})});}// Privacy Policy Section\nif(section==='privacy'){return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,pt:4,pb:8},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:800,children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",align:\"center\",sx:{mb:4,fontWeight:800,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:safeT('policies.privacy.title')}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",align:\"center\",sx:{mb:6,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.text.secondary,maxWidth:'800px',mx:'auto'},children:safeT('policies.privacy.description')}),renderPrivacySection(safeT('policies.privacy.informationCollection.title'),safeT('policies.privacy.informationCollection.description'),t('policies.privacy.informationCollection.items',{returnObjects:true})||[]),renderPrivacySection(safeT('policies.privacy.informationUse.title'),safeT('policies.privacy.informationUse.description'),t('policies.privacy.informationUse.items',{returnObjects:true})||[]),renderPrivacySection(safeT('policies.privacy.informationSharing.title'),safeT('policies.privacy.informationSharing.description'),t('policies.privacy.informationSharing.items',{returnObjects:true})||[]),renderPrivacySection(safeT('policies.privacy.dataSecurity.title'),safeT('policies.privacy.dataSecurity.description'),t('policies.privacy.dataSecurity.items',{returnObjects:true})||[]),renderPrivacySection(safeT('policies.privacy.userRights.title'),safeT('policies.privacy.userRights.description'),t('policies.privacy.userRights.items',{returnObjects:true})||[]),renderPrivacySection(safeT('policies.privacy.cookies.title'),safeT('policies.privacy.cookies.description'),t('policies.privacy.cookies.items',{returnObjects:true})||[]),renderPrivacySection(safeT('policies.privacy.thirdPartyServices.title'),safeT('policies.privacy.thirdPartyServices.description'),t('policies.privacy.thirdPartyServices.items',{returnObjects:true})||[]),renderPrivacySection(safeT('policies.privacy.policyUpdates.title'),safeT('policies.privacy.policyUpdates.description'),t('policies.privacy.policyUpdates.items',{returnObjects:true})||[]),/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(EmailIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:safeT('policies.privacy.contact.title')}),/*#__PURE__*/_jsx(Typography,{children:safeT('policies.privacy.contact.email')})]})]})})})});}return null;};export default PlatformPolicy;", "map": {"version": 3, "names": ["React", "useTranslation", "useLocation", "Container", "Typography", "Box", "Fade", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "List", "ListItem", "ListItemText", "ListItemIcon", "useTheme", "alpha", "<PERSON><PERSON>", "Grid", "<PERSON><PERSON>", "Paper", "Chip", "Email", "EmailIcon", "Schedule", "ScheduleIcon", "CheckCircle", "CheckCircleIcon", "Cancel", "CancelIcon", "Payment", "PaymentIcon", "CurrencyExchange", "CurrencyExchangeIcon", "Receipt", "ReceiptIcon", "Warning", "WarningIcon", "Security", "SecurityIcon", "AccessTime", "AccessTimeIcon", "CreditCard", "CreditCardIcon", "Info", "InfoIcon", "Timer", "TimerIcon", "Notifications", "NotificationsIcon", "Person", "PersonIcon", "School", "SchoolIcon", "termsConditions", "i18n", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "addResourceBundle", "en", "ar", "PlatformPolicy", "t", "theme", "location", "isRTL", "language", "section", "hash", "replace", "SectionCard", "_ref", "title", "children", "bg", "icon", "elevation", "sx", "mb", "borderRadius", "borderRight", "palette", "primary", "main", "borderLeft", "backgroundColor", "display", "alignItems", "mr", "ml", "color", "variant", "fontWeight", "fontFamily", "safeT", "key", "val", "includes", "renderSubSection", "subSection", "description", "textAlign", "points", "map", "item", "idx", "pl", "pr", "flexDirection", "min<PERSON><PERSON><PERSON>", "mx", "renderPrivacySection", "bullets", "length", "minHeight", "background", "pt", "pb", "max<PERSON><PERSON><PERSON>", "in", "timeout", "p", "xs", "md", "direction", "align", "spacing", "split", "filter", "paragraph", "trim", "index", "transition", "transform", "boxShadow", "shadows", "lineHeight", "fontSize", "policy", "returnObjects", "text", "secondary", "studentCancellation", "rules", "rule", "teacherCancellation", "emergencySituations", "examples", "example", "notificationRequirements", "methods", "method", "severity", "contact", "email", "renderSection", "subSections", "sections", "features", "my", "container", "securePayments", "securePaymentsDesc", "multipleCurrencies", "multipleCurrenciesDesc", "instantReceipts", "instantReceiptsDesc", "instantConfirmation", "instantConfirmationDesc", "refund", "section1", "items", "section2"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PlatformPolicy.js"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useLocation } from 'react-router-dom';\nimport {\n  Container,\n  Typography,\n  Box,\n  Fade,\n  Card,\n  CardContent,\n  Divider,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  useTheme,\n  alpha,\n  Alert,\n  Grid,\n  Stack,\n  Paper,\n  Chip,\n} from '@mui/material';\nimport {\n  Email as EmailIcon,\n  Schedule as ScheduleIcon,\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  Payment as PaymentIcon,\n  CurrencyExchange as CurrencyExchangeIcon,\n  Receipt as ReceiptIcon,\n  Warning as WarningIcon,\n  Security as SecurityIcon,\n  AccessTime as AccessTimeIcon,\n  CreditCard as CreditCardIcon,\n  Info as InfoIcon,\n  Timer as TimerIcon,\n  Notifications as NotificationsIcon,\n  Person as PersonIcon,\n  School as SchoolIcon,\n} from '@mui/icons-material';\nimport termsConditions from '../i18n/translations/termsConditions';\nimport i18n from '../i18n/i18n';\n\n// Merge the Terms & Conditions keys into existing translation bundles\ni18n.addResourceBundle('en', 'translation', termsConditions.en, true, true);\ni18n.addResourceBundle('ar', 'translation', termsConditions.ar, true, true);\n\nconst PlatformPolicy = () => {\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const location = useLocation();\n  const isRTL = i18n.language === 'ar';\n\n  // Get the section from URL hash\n  const section = location.hash.replace('#', '') || 'terms';\n\n  const SectionCard = ({ title, children, bg, icon }) => (\n    <Card\n      elevation={2}\n      sx={{\n        mb: 4,\n        borderRadius: 3,\n        ...(isRTL\n          ? { borderRight: `6px solid ${theme.palette.primary.main}` }\n          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\n        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),\n      }}\n    >\n      <CardContent>\n        {title && (\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n            {icon && (\n              <Box sx={{ mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0, color: theme.palette.primary.main }}>\n                {icon}\n              </Box>\n            )}\n            <Typography\n              variant=\"h5\"\n              fontWeight={600}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                color: theme.palette.primary.main,\n              }}\n            >\n              {title}\n            </Typography>\n          </Box>\n        )}\n        {children}\n      </CardContent>\n    </Card>\n  );\n\n  /**\n   * Safely fetch a translation; returns empty string if key is missing.\n   */\n  const safeT = (key) => {\n    const val = t(key);\n    return val && !val.includes(key) ? val : '';\n  };\n\n  const renderSubSection = (subSection) => {\n    if (!subSection) return null;\n\n    return (\n      <Box key={subSection.title} sx={{ mb: 3 }}>\n        {subSection.title && (\n          <Typography\n            variant=\"h6\"\n            fontWeight={600}\n            mb={2}\n            sx={{\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n              color: theme.palette.primary.main,\n            }}\n          >\n            {subSection.title}\n          </Typography>\n        )}\n        {subSection.description && (\n          <Typography\n            mb={2}\n            sx={{\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n              textAlign: isRTL ? 'right' : 'left'\n            }}\n          >\n            {subSection.description}\n          </Typography>\n        )}\n        {subSection.points && (\n          <List>\n            {subSection.points.map((item, idx) => (\n              <ListItem key={idx} sx={{\n                pl: isRTL ? 0 : 2,\n                pr: isRTL ? 2 : 0,\n                flexDirection: 'row',\n                textAlign: isRTL ? 'right' : 'left',\n              }}>\n                <ListItemIcon sx={{\n                  minWidth: 'auto',\n                  mx: isRTL ? 0 : 1,\n                  ml: isRTL ? 1 : 0,\n                }}>\n                  <InfoIcon color=\"success\" />\n                </ListItemIcon>\n                <ListItemText \n                  primary={item}\n                  sx={{ \n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  }}\n                />\n              </ListItem>\n            ))}\n          </List>\n        )}\n      </Box>\n    );\n  };\n\n  const renderPrivacySection = (title, description, bullets) => {\n    return (\n      <SectionCard>\n        <Typography\n          variant=\"h6\"\n          fontWeight={600}\n          mb={2}\n          sx={{\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n            color: theme.palette.primary.main,\n          }}\n        >\n          {title}\n        </Typography>\n        {description && (\n          <Typography\n            mb={2}\n            sx={{\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n              textAlign: isRTL ? 'right' : 'left'\n            }}\n          >\n            {description}\n          </Typography>\n        )}\n        {bullets.length > 0 && (\n          <List>\n            {bullets.map((item, idx) => (\n              <ListItem\n                key={idx}\n                sx={{\n                  pl: isRTL ? 0 : 2,\n                  pr: isRTL ? 2 : 0,\n                  flexDirection: 'row',\n                  textAlign: isRTL ? 'right' : 'left',\n                }}\n              >\n                <ListItemIcon\n                  sx={{\n                    minWidth: 'auto',\n                    mx: isRTL ? 0 : 1,\n                    ml: isRTL ? 1 : 0,\n                  }}\n                >\n                  <InfoIcon color=\"primary\" />\n                </ListItemIcon>\n                <ListItemText\n                  primary={item}\n                  sx={{ \n                    textAlign: isRTL ? 'right' : 'left',\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  }}\n                />\n              </ListItem>\n            ))}\n          </List>\n        )}\n      </SectionCard>\n    );\n  };\n\n  // Terms and Conditions Section\n  if (section === 'terms') {\n    return (\n      <Box\n        sx={{\n          minHeight: '100vh',\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\n          pt: 4,\n          pb: 8,\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Fade in timeout={800}>\n            <Paper\n              elevation={0}\n              sx={{\n                p: { xs: 3, md: 6 },\n                borderRadius: 3,\n                background: 'transparent',\n                direction: isRTL ? 'rtl' : 'ltr',\n              }}\n            >\n              <Typography\n                variant=\"h3\"\n                align=\"center\"\n                sx={{\n                  mb: 4,\n                  fontWeight: 800,\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.primary.main,\n                }}\n              >\n                {t('termsConditions.title')}\n              </Typography>\n              {/* Render paragraphs in individual cards */}\n              <Stack spacing={4}>\n                {t('termsConditions.content')\n                  .split('\\n\\n')\n                  .filter(paragraph => paragraph.trim())\n                  .map((paragraph, index) => (\n                    <Card\n                      key={index}\n                      elevation={3}\n                      sx={{\n                        borderRadius: 3,\n                        ...(isRTL\n                          ? { borderRight: `6px solid ${theme.palette.primary.main}` }\n                          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\n                        backgroundColor: alpha(theme.palette.primary.main, 0.02),\n                        transition: 'all 0.3s ease',\n                        '&:hover': {\n                          transform: 'translateY(-2px)',\n                          boxShadow: theme.shadows[6],\n                        },\n                      }}\n                    >\n                      <CardContent>\n                        <Typography\n                          variant=\"body1\"\n                          sx={{\n                            lineHeight: 1.8,\n                            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                            textAlign: isRTL ? 'right' : 'left',\n                            fontSize: '1.1rem',\n                          }}\n                        >\n                          {paragraph.trim()}\n                        </Typography>\n                      </CardContent>\n                    </Card>\n                  ))}\n              </Stack>\n            </Paper>\n          </Fade>\n        </Container>\n      </Box>\n    );\n  }\n\n  // Booking Cancellation Policy Section\n  if (section === 'booking-cancellation') {\n    const policy = t('policies.bookingCancellation', { returnObjects: true });\n\n    return (\n      <Box\n        sx={{\n          minHeight: '100vh',\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\n          pt: 4,\n          pb: 8,\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Fade in timeout={800}>\n            <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n              <Typography\n                variant=\"h3\"\n                align=\"center\"\n                sx={{\n                  mb: 4,\n                  fontWeight: 800,\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.primary.main,\n                }}\n              >\n                {policy.title}\n              </Typography>\n\n              <Typography\n                variant=\"h6\"\n                align=\"center\"\n                sx={{\n                  mb: 6,\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.text.secondary,\n                  maxWidth: '800px',\n                  mx: 'auto',\n                }}\n              >\n                {policy.description}\n              </Typography>\n\n              {/* Student Cancellation Section */}\n              <SectionCard title={policy.studentCancellation.title} icon={<PersonIcon />}>\n                <Typography\n                  mb={2}\n                  sx={{\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                    textAlign: isRTL ? 'right' : 'left'\n                  }}\n                >\n                  {policy.studentCancellation.description}\n                </Typography>\n                <List>\n                  {policy.studentCancellation.rules.map((rule, idx) => (\n                    <ListItem key={idx} sx={{\n                      pl: isRTL ? 0 : 2,\n                      pr: isRTL ? 2 : 0,\n                      flexDirection: 'row',\n                      textAlign: isRTL ? 'right' : 'left',\n                    }}>\n                      <ListItemIcon sx={{\n                        minWidth: 'auto',\n                        mx: isRTL ? 0 : 1,\n                        ml: isRTL ? 1 : 0,\n                      }}>\n                        <AccessTimeIcon color=\"primary\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={rule}\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </SectionCard>\n\n              {/* Teacher Cancellation Section */}\n              <SectionCard title={policy.teacherCancellation.title} icon={<SchoolIcon />}>\n                <Typography\n                  mb={2}\n                  sx={{\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                    textAlign: isRTL ? 'right' : 'left'\n                  }}\n                >\n                  {policy.teacherCancellation.description}\n                </Typography>\n                <List>\n                  {policy.teacherCancellation.rules.map((rule, idx) => (\n                    <ListItem key={idx} sx={{\n                      pl: isRTL ? 0 : 2,\n                      pr: isRTL ? 2 : 0,\n                      flexDirection: 'row',\n                      textAlign: isRTL ? 'right' : 'left',\n                    }}>\n                      <ListItemIcon sx={{\n                        minWidth: 'auto',\n                        mx: isRTL ? 0 : 1,\n                        ml: isRTL ? 1 : 0,\n                      }}>\n                        <CheckCircleIcon color=\"success\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={rule}\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </SectionCard>\n\n              {/* Emergency Situations */}\n              <SectionCard title={policy.emergencySituations.title} icon={<WarningIcon />}>\n                <Typography\n                  mb={2}\n                  sx={{\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                    textAlign: isRTL ? 'right' : 'left'\n                  }}\n                >\n                  {policy.emergencySituations.description}\n                </Typography>\n                <List>\n                  {policy.emergencySituations.examples.map((example, idx) => (\n                    <ListItem key={idx} sx={{\n                      pl: isRTL ? 0 : 2,\n                      pr: isRTL ? 2 : 0,\n                      flexDirection: 'row',\n                      textAlign: isRTL ? 'right' : 'left',\n                    }}>\n                      <ListItemIcon sx={{\n                        minWidth: 'auto',\n                        mx: isRTL ? 0 : 1,\n                        ml: isRTL ? 1 : 0,\n                      }}>\n                        <InfoIcon color=\"warning\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={example}\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </SectionCard>\n\n              {/* Notification Requirements */}\n              <SectionCard title={policy.notificationRequirements.title} icon={<NotificationsIcon />}>\n                <Typography\n                  mb={2}\n                  sx={{\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                    textAlign: isRTL ? 'right' : 'left'\n                  }}\n                >\n                  {policy.notificationRequirements.description}\n                </Typography>\n                <List>\n                  {policy.notificationRequirements.methods.map((method, idx) => (\n                    <ListItem key={idx} sx={{\n                      pl: isRTL ? 0 : 2,\n                      pr: isRTL ? 2 : 0,\n                      flexDirection: 'row',\n                      textAlign: isRTL ? 'right' : 'left',\n                    }}>\n                      <ListItemIcon sx={{\n                        minWidth: 'auto',\n                        mx: isRTL ? 0 : 1,\n                        ml: isRTL ? 1 : 0,\n                      }}>\n                        <TimerIcon color=\"info\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={method}\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </SectionCard>\n\n              {/* Contact Information */}\n              <Alert icon={<EmailIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                <Typography fontWeight={600}>{policy.contact.title}</Typography>\n                <Typography>{policy.contact.email}</Typography>\n              </Alert>\n            </Box>\n          </Fade>\n        </Container>\n      </Box>\n    );\n  }\n\n  // Booking Payment Policy Section\n  if (section === 'payment') {\n    const policy = t('policies.bookingPayment', { returnObjects: true });\n\n    const renderSection = (section) => (\n      <SectionCard title={section.title} icon={<PaymentIcon />}>\n        <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n          {section.points && (\n            <List>\n              {section.points.map((item, idx) => (\n                <ListItem key={idx} sx={{\n                  pl: isRTL ? 0 : 2,\n                  pr: isRTL ? 2 : 0,\n                  flexDirection: 'row',\n                  textAlign: isRTL ? 'right' : 'left',\n                }}>\n                  <ListItemIcon sx={{\n                    minWidth: 'auto',\n                    mx: isRTL ? 0 : 1,\n                    ml: isRTL ? 1 : 0,\n                  }}>\n                    <InfoIcon color=\"success\" />\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item}\n                    sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\n                  />\n                </ListItem>\n              ))}\n            </List>\n          )}\n          {section.subSections && section.subSections.map((subSection, idx) => renderSubSection(subSection))}\n          {section.description && (\n            <Typography\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {section.description}\n            </Typography>\n          )}\n        </Box>\n      </SectionCard>\n    );\n\n    return (\n      <Box\n        sx={{\n          minHeight: '100vh',\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\n          pt: 4,\n          pb: 8,\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Fade in timeout={800}>\n            <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\n              <Typography\n                variant=\"h4\"\n                fontWeight={700}\n                mb={2}\n                sx={{\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                  color: theme.palette.primary.main,\n                  textAlign: isRTL ? 'right' : 'left'\n                }}\n              >\n                {policy.title}\n              </Typography>\n              <Typography\n                variant=\"h6\"\n                mb={4}\n                sx={{\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                  color: theme.palette.text.secondary,\n                  textAlign: isRTL ? 'right' : 'left'\n                }}\n              >\n                {policy.description}\n              </Typography>\n\n              {/* Render all sections */}\n              {policy.sections && policy.sections.map((section, idx) => (\n                <div key={idx}>\n                  {renderSection(section)}\n                </div>\n              ))}\n\n              {/* Features Section */}\n              {policy.features && (\n                <>\n                  <Divider sx={{ my: 3 }} />\n                  <Grid container spacing={2} mb={3}>\n                    <Grid item xs={12} md={6}>\n                      <Alert icon={<SecurityIcon />} severity=\"success\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                        <Typography fontWeight={600}>{policy.features.securePayments}</Typography>\n                        <Typography>{policy.features.securePaymentsDesc}</Typography>\n                      </Alert>\n                    </Grid>\n                    <Grid item xs={12} md={6}>\n                      <Alert icon={<CurrencyExchangeIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                        <Typography fontWeight={600}>{policy.features.multipleCurrencies}</Typography>\n                        <Typography>{policy.features.multipleCurrenciesDesc}</Typography>\n                      </Alert>\n                    </Grid>\n                    <Grid item xs={12} md={6}>\n                      <Alert icon={<ReceiptIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                        <Typography fontWeight={600}>{policy.features.instantReceipts}</Typography>\n                        <Typography>{policy.features.instantReceiptsDesc}</Typography>\n                      </Alert>\n                    </Grid>\n                    <Grid item xs={12} md={6}>\n                      <Alert icon={<CheckCircleIcon />} severity=\"success\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                        <Typography fontWeight={600}>{policy.features.instantConfirmation}</Typography>\n                        <Typography>{policy.features.instantConfirmationDesc}</Typography>\n                      </Alert>\n                    </Grid>\n                  </Grid>\n                </>\n              )}\n\n              {/* Contact Information */}\n              <Alert icon={<EmailIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                <Typography fontWeight={600}>{policy.contact.title}</Typography>\n                <Typography>{policy.contact.email}</Typography>\n              </Alert>\n            </Box>\n          </Fade>\n        </Container>\n      </Box>\n    );\n  }\n\n  // Refund Policy Section\n  if (section === 'refund') {\n    const refund = t('policies.refund', { returnObjects: true });\n\n    return (\n      <Box\n        sx={{\n          minHeight: '100vh',\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\n          pt: 4,\n          pb: 8,\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Fade in timeout={800}>\n            <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n              <Typography\n                variant=\"h3\"\n                align=\"center\"\n                sx={{\n                  mb: 4,\n                  fontWeight: 800,\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.primary.main,\n                }}\n              >\n                {refund.title}\n              </Typography>\n\n              <Typography\n                variant=\"h6\"\n                align=\"center\"\n                sx={{\n                  mb: 6,\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.text.secondary,\n                  maxWidth: '800px',\n                  mx: 'auto',\n                }}\n              >\n                {refund.description}\n              </Typography>\n\n              <Divider sx={{ mb: 3 }} />\n\n              {/* القسم الأول */}\n              <SectionCard title={refund.section1.title} icon={<CheckCircleIcon />}>\n                <Typography\n                  mb={2}\n                  sx={{\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                    textAlign: isRTL ? 'right' : 'left'\n                  }}\n                >\n                  {refund.section1.description}\n                </Typography>\n                <List>\n                  {refund.section1.items.map((item, idx) => (\n                    <ListItem key={idx} sx={{\n                      pl: isRTL ? 0 : 2,\n                      pr: isRTL ? 2 : 0,\n                      flexDirection: 'row',\n                      textAlign: isRTL ? 'right' : 'left',\n                    }}>\n                      <ListItemIcon sx={{\n                        minWidth: 'auto',\n                        mx: isRTL ? 0 : 1,\n                        ml: isRTL ? 1 : 0,\n                      }}>\n                        <CheckCircleIcon color=\"success\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={item}\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </SectionCard>\n\n              {/* القسم الثاني */}\n              <SectionCard title={refund.section2.title} icon={<CancelIcon />}>\n                <Typography\n                  mb={2}\n                  sx={{\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                    textAlign: isRTL ? 'right' : 'left'\n                  }}\n                >\n                  {refund.section2.description}\n                </Typography>\n                <List>\n                  {refund.section2.items.map((item, idx) => (\n                    <ListItem key={idx} sx={{\n                      pl: isRTL ? 0 : 2,\n                      pr: isRTL ? 2 : 0,\n                      flexDirection: 'row',\n                      textAlign: isRTL ? 'right' : 'left',\n                    }}>\n                      <ListItemIcon sx={{\n                        minWidth: 'auto',\n                        mx: isRTL ? 0 : 1,\n                        ml: isRTL ? 1 : 0,\n                      }}>\n                        <CancelIcon color=\"error\" />\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={item}\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n                      />\n                    </ListItem>\n                  ))}\n                </List>\n              </SectionCard>\n\n              {/* باقي الأقسام */}\n              {refund.sections && refund.sections.map((section, idx) => (\n                <SectionCard key={idx} title={section.title} icon={<InfoIcon />}>\n                  <Typography\n                    mb={2}\n                    sx={{\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                      textAlign: isRTL ? 'right' : 'left'\n                    }}\n                  >\n                    {section.description}\n                  </Typography>\n                  {section.items && (\n                    <List>\n                      {section.items.map((item, idx) => (\n                        <ListItem key={idx} sx={{\n                          pl: isRTL ? 0 : 2,\n                          pr: isRTL ? 2 : 0,\n                          flexDirection: 'row',\n                          textAlign: isRTL ? 'right' : 'left',\n                        }}>\n                          <ListItemIcon sx={{\n                            minWidth: 'auto',\n                            mx: isRTL ? 0 : 1,\n                            ml: isRTL ? 1 : 0,\n                          }}>\n                            <InfoIcon color=\"success\" />\n                          </ListItemIcon>\n                          <ListItemText\n                            primary={item}\n                            sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n                  )}\n                </SectionCard>\n              ))}\n\n              {/* Contact Information */}\n              <Alert icon={<EmailIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                <Typography fontWeight={600}>{refund.contact.title}</Typography>\n                <Typography>{refund.contact.email}</Typography>\n              </Alert>\n            </Box>\n          </Fade>\n        </Container>\n      </Box>\n    );\n  }\n\n  // Privacy Policy Section\n  if (section === 'privacy') {\n    return (\n      <Box\n        sx={{\n          minHeight: '100vh',\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\n          pt: 4,\n          pb: 8,\n        }}\n      >\n        <Container maxWidth=\"lg\">\n          <Fade in timeout={800}>\n            <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n              <Typography\n                variant=\"h3\"\n                align=\"center\"\n                sx={{\n                  mb: 4,\n                  fontWeight: 800,\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.primary.main,\n                }}\n              >\n                {safeT('policies.privacy.title')}\n              </Typography>\n\n              <Typography\n                variant=\"h6\"\n                align=\"center\"\n                sx={{\n                  mb: 6,\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                  color: theme.palette.text.secondary,\n                  maxWidth: '800px',\n                  mx: 'auto',\n                }}\n              >\n                {safeT('policies.privacy.description')}\n              </Typography>\n\n              {/* Information Collection */}\n              {renderPrivacySection(\n                safeT('policies.privacy.informationCollection.title'),\n                safeT('policies.privacy.informationCollection.description'),\n                t('policies.privacy.informationCollection.items', { returnObjects: true }) || []\n              )}\n\n              {/* Information Use */}\n              {renderPrivacySection(\n                safeT('policies.privacy.informationUse.title'),\n                safeT('policies.privacy.informationUse.description'),\n                t('policies.privacy.informationUse.items', { returnObjects: true }) || []\n              )}\n\n              {/* Information Sharing */}\n              {renderPrivacySection(\n                safeT('policies.privacy.informationSharing.title'),\n                safeT('policies.privacy.informationSharing.description'),\n                t('policies.privacy.informationSharing.items', { returnObjects: true }) || []\n              )}\n\n              {/* Data Security */}\n              {renderPrivacySection(\n                safeT('policies.privacy.dataSecurity.title'),\n                safeT('policies.privacy.dataSecurity.description'),\n                t('policies.privacy.dataSecurity.items', { returnObjects: true }) || []\n              )}\n\n              {/* User Rights */}\n              {renderPrivacySection(\n                safeT('policies.privacy.userRights.title'),\n                safeT('policies.privacy.userRights.description'),\n                t('policies.privacy.userRights.items', { returnObjects: true }) || []\n              )}\n\n              {/* Cookies */}\n              {renderPrivacySection(\n                safeT('policies.privacy.cookies.title'),\n                safeT('policies.privacy.cookies.description'),\n                t('policies.privacy.cookies.items', { returnObjects: true }) || []\n              )}\n\n              {/* Third Party Services */}\n              {renderPrivacySection(\n                safeT('policies.privacy.thirdPartyServices.title'),\n                safeT('policies.privacy.thirdPartyServices.description'),\n                t('policies.privacy.thirdPartyServices.items', { returnObjects: true }) || []\n              )}\n\n              {/* Policy Updates */}\n              {renderPrivacySection(\n                safeT('policies.privacy.policyUpdates.title'),\n                safeT('policies.privacy.policyUpdates.description'),\n                t('policies.privacy.policyUpdates.items', { returnObjects: true }) || []\n              )}\n\n              {/* Contact Information */}\n              <Alert icon={<EmailIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                <Typography fontWeight={600}>{safeT('policies.privacy.contact.title')}</Typography>\n                <Typography>{safeT('policies.privacy.contact.email')}</Typography>\n              </Alert>\n            </Box>\n          </Fade>\n        </Container>\n      </Box>\n    );\n  }\n\n  return null;\n};\n\nexport default PlatformPolicy;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,IAAI,CACJC,IAAI,CACJC,WAAW,CACXC,OAAO,CACPC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,QAAQ,CACRC,KAAK,CACLC,KAAK,CACLC,IAAI,CACJC,KAAK,CACLC,KAAK,CACLC,IAAI,KACC,eAAe,CACtB,OACEC,KAAK,GAAI,CAAAC,SAAS,CAClBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,gBAAgB,GAAI,CAAAC,oBAAoB,CACxCC,OAAO,GAAI,CAAAC,WAAW,CACtBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,KAAK,GAAI,CAAAC,SAAS,CAClBC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,KACf,qBAAqB,CAC5B,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAClE,MAAO,CAAAC,IAAI,KAAM,cAAc,CAE/B;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACAN,IAAI,CAACO,iBAAiB,CAAC,IAAI,CAAE,aAAa,CAAER,eAAe,CAACS,EAAE,CAAE,IAAI,CAAE,IAAI,CAAC,CAC3ER,IAAI,CAACO,iBAAiB,CAAC,IAAI,CAAE,aAAa,CAAER,eAAe,CAACU,EAAE,CAAE,IAAI,CAAE,IAAI,CAAC,CAE3E,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAEX,IAAK,CAAC,CAAGrD,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAiE,KAAK,CAAGpD,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAqD,QAAQ,CAAGjE,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAkE,KAAK,CAAGd,IAAI,CAACe,QAAQ,GAAK,IAAI,CAEpC;AACA,KAAM,CAAAC,OAAO,CAAGH,QAAQ,CAACI,IAAI,CAACC,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,EAAI,OAAO,CAEzD,KAAM,CAAAC,WAAW,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,EAAE,CAAEC,IAAK,CAAC,CAAAJ,IAAA,oBAChDlB,IAAA,CAACjD,IAAI,EACHwE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACf,IAAId,KAAK,CACL,CAAEe,WAAW,CAAE,aAAajB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAarB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEX,EAAE,EAAI9D,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAC/D,CAAE,CAAAV,QAAA,cAEFlB,KAAA,CAAClD,WAAW,EAAAoE,QAAA,EACTD,KAAK,eACJjB,KAAA,CAACrD,GAAG,EAAC2E,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAET,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,EACvDE,IAAI,eACHtB,IAAA,CAACnD,GAAG,EAAC2E,EAAE,CAAE,CAAEW,EAAE,CAAEvB,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAV,QAAA,CAClFE,IAAI,CACF,CACN,cACDtB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBf,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAV,QAAA,CAEDD,KAAK,CACI,CAAC,EACV,CACN,CACAC,QAAQ,EACE,CAAC,CACV,CAAC,EACR,CAED;AACF;AACA,KACE,KAAM,CAAAqB,KAAK,CAAIC,GAAG,EAAK,CACrB,KAAM,CAAAC,GAAG,CAAGlC,CAAC,CAACiC,GAAG,CAAC,CAClB,MAAO,CAAAC,GAAG,EAAI,CAACA,GAAG,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAGC,GAAG,CAAG,EAAE,CAC7C,CAAC,CAED,KAAM,CAAAE,gBAAgB,CAAIC,UAAU,EAAK,CACvC,GAAI,CAACA,UAAU,CAAE,MAAO,KAAI,CAE5B,mBACE5C,KAAA,CAACrD,GAAG,EAAwB2E,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,EACvC0B,UAAU,CAAC3B,KAAK,eACfnB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBd,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAV,QAAA,CAED0B,UAAU,CAAC3B,KAAK,CACP,CACb,CACA2B,UAAU,CAACC,WAAW,eACrB/C,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAED0B,UAAU,CAACC,WAAW,CACb,CACb,CACAD,UAAU,CAACG,MAAM,eAChBjD,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACF0B,UAAU,CAACG,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC/BlD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAACZ,QAAQ,EAACiD,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAEsB,IAAK,CACd3B,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAC9C,CAAE,CACH,CAAC,GAlBWwC,GAmBL,CACX,CAAC,CACE,CACP,GAlDON,UAAU,CAAC3B,KAmDhB,CAAC,CAEV,CAAC,CAED,KAAM,CAAAuC,oBAAoB,CAAGA,CAACvC,KAAK,CAAE4B,WAAW,CAAEY,OAAO,GAAK,CAC5D,mBACEzD,KAAA,CAACe,WAAW,EAAAG,QAAA,eACVpB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBd,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAV,QAAA,CAEDD,KAAK,CACI,CAAC,CACZ4B,WAAW,eACV/C,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAED2B,WAAW,CACF,CACb,CACAY,OAAO,CAACC,MAAM,CAAG,CAAC,eACjB5D,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFuC,OAAO,CAACT,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACrBlD,KAAA,CAAC/C,QAAQ,EAEPqE,EAAE,CAAE,CACF6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eAEFpB,IAAA,CAAC3C,YAAY,EACXmE,EAAE,CAAE,CACFgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cAEFpB,IAAA,CAACZ,QAAQ,EAACiD,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAEsB,IAAK,CACd3B,EAAE,CAAE,CACFwB,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC4B,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAC9C,CAAE,CACH,CAAC,GAvBGwC,GAwBG,CACX,CAAC,CACE,CACP,EACU,CAAC,CAElB,CAAC,CAED;AACA,GAAItC,OAAO,GAAK,OAAO,CAAE,CACvB,mBACEd,IAAA,CAACnD,GAAG,EACF2E,EAAE,CAAE,CACFqC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBvG,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAKvE,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHiC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAA5C,QAAA,cAEFpB,IAAA,CAACrD,SAAS,EAACsH,QAAQ,CAAC,IAAI,CAAA7C,QAAA,cACtBpB,IAAA,CAAClD,IAAI,EAACoH,EAAE,MAACC,OAAO,CAAE,GAAI,CAAA/C,QAAA,cACpBlB,KAAA,CAACvC,KAAK,EACJ4D,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACF4C,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnB5C,YAAY,CAAE,CAAC,CACfoC,UAAU,CAAE,aAAa,CACzBS,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAQ,QAAA,eAEFpB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZkC,KAAK,CAAC,QAAQ,CACdhD,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLc,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAV,QAAA,CAEDX,CAAC,CAAC,uBAAuB,CAAC,CACjB,CAAC,cAEbT,IAAA,CAACtC,KAAK,EAAC+G,OAAO,CAAE,CAAE,CAAArD,QAAA,CACfX,CAAC,CAAC,yBAAyB,CAAC,CAC1BiE,KAAK,CAAC,MAAM,CAAC,CACbC,MAAM,CAACC,SAAS,EAAIA,SAAS,CAACC,IAAI,CAAC,CAAC,CAAC,CACrC3B,GAAG,CAAC,CAAC0B,SAAS,CAAEE,KAAK,gBACpB9E,IAAA,CAACjD,IAAI,EAEHwE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFE,YAAY,CAAE,CAAC,CACf,IAAId,KAAK,CACL,CAAEe,WAAW,CAAE,aAAajB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAarB,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEzE,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACxDiD,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTC,SAAS,CAAE,kBAAkB,CAC7BC,SAAS,CAAEvE,KAAK,CAACwE,OAAO,CAAC,CAAC,CAC5B,CACF,CAAE,CAAA9D,QAAA,cAEFpB,IAAA,CAAChD,WAAW,EAAAoE,QAAA,cACVpB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,OAAO,CACfd,EAAE,CAAE,CACF2D,UAAU,CAAE,GAAG,CACf3C,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCwE,QAAQ,CAAE,QACZ,CAAE,CAAAhE,QAAA,CAEDwD,SAAS,CAACC,IAAI,CAAC,CAAC,CACP,CAAC,CACF,CAAC,EA3BTC,KA4BD,CACP,CAAC,CACC,CAAC,EACH,CAAC,CACJ,CAAC,CACE,CAAC,CACT,CAAC,CAEV,CAEA;AACA,GAAIhE,OAAO,GAAK,sBAAsB,CAAE,CACtC,KAAM,CAAAuE,MAAM,CAAG5E,CAAC,CAAC,8BAA8B,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,CAEzE,mBACEtF,IAAA,CAACnD,GAAG,EACF2E,EAAE,CAAE,CACFqC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBvG,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAKvE,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHiC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAA5C,QAAA,cAEFpB,IAAA,CAACrD,SAAS,EAACsH,QAAQ,CAAC,IAAI,CAAA7C,QAAA,cACtBpB,IAAA,CAAClD,IAAI,EAACoH,EAAE,MAACC,OAAO,CAAE,GAAI,CAAA/C,QAAA,cACpBlB,KAAA,CAACrD,GAAG,EAAC2E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eAC5CpB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZkC,KAAK,CAAC,QAAQ,CACdhD,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLc,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAV,QAAA,CAEDiE,MAAM,CAAClE,KAAK,CACH,CAAC,cAEbnB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZkC,KAAK,CAAC,QAAQ,CACdhD,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLe,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAAC2D,IAAI,CAACC,SAAS,CACnCvB,QAAQ,CAAE,OAAO,CACjBR,EAAE,CAAE,MACN,CAAE,CAAArC,QAAA,CAEDiE,MAAM,CAACtC,WAAW,CACT,CAAC,cAGb7C,KAAA,CAACe,WAAW,EAACE,KAAK,CAAEkE,MAAM,CAACI,mBAAmB,CAACtE,KAAM,CAACG,IAAI,cAAEtB,IAAA,CAACN,UAAU,GAAE,CAAE,CAAA0B,QAAA,eACzEpB,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDiE,MAAM,CAACI,mBAAmB,CAAC1C,WAAW,CAC7B,CAAC,cACb/C,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFiE,MAAM,CAACI,mBAAmB,CAACC,KAAK,CAACxC,GAAG,CAAC,CAACyC,IAAI,CAAEvC,GAAG,gBAC9ClD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAAChB,cAAc,EAACqD,KAAK,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAE8D,IAAK,CACdnE,EAAE,CAAE,CAAEgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWwC,GAiBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdlD,KAAA,CAACe,WAAW,EAACE,KAAK,CAAEkE,MAAM,CAACO,mBAAmB,CAACzE,KAAM,CAACG,IAAI,cAAEtB,IAAA,CAACJ,UAAU,GAAE,CAAE,CAAAwB,QAAA,eACzEpB,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDiE,MAAM,CAACO,mBAAmB,CAAC7C,WAAW,CAC7B,CAAC,cACb/C,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFiE,MAAM,CAACO,mBAAmB,CAACF,KAAK,CAACxC,GAAG,CAAC,CAACyC,IAAI,CAAEvC,GAAG,gBAC9ClD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAAC9B,eAAe,EAACmE,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAE8D,IAAK,CACdnE,EAAE,CAAE,CAAEgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWwC,GAiBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdlD,KAAA,CAACe,WAAW,EAACE,KAAK,CAAEkE,MAAM,CAACQ,mBAAmB,CAAC1E,KAAM,CAACG,IAAI,cAAEtB,IAAA,CAACpB,WAAW,GAAE,CAAE,CAAAwC,QAAA,eAC1EpB,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDiE,MAAM,CAACQ,mBAAmB,CAAC9C,WAAW,CAC7B,CAAC,cACb/C,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFiE,MAAM,CAACQ,mBAAmB,CAACC,QAAQ,CAAC5C,GAAG,CAAC,CAAC6C,OAAO,CAAE3C,GAAG,gBACpDlD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAACZ,QAAQ,EAACiD,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAEkE,OAAQ,CACjBvE,EAAE,CAAE,CAAEgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWwC,GAiBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdlD,KAAA,CAACe,WAAW,EAACE,KAAK,CAAEkE,MAAM,CAACW,wBAAwB,CAAC7E,KAAM,CAACG,IAAI,cAAEtB,IAAA,CAACR,iBAAiB,GAAE,CAAE,CAAA4B,QAAA,eACrFpB,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDiE,MAAM,CAACW,wBAAwB,CAACjD,WAAW,CAClC,CAAC,cACb/C,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFiE,MAAM,CAACW,wBAAwB,CAACC,OAAO,CAAC/C,GAAG,CAAC,CAACgD,MAAM,CAAE9C,GAAG,gBACvDlD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAACV,SAAS,EAAC+C,KAAK,CAAC,MAAM,CAAE,CAAC,CACd,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAEqE,MAAO,CAChB1E,EAAE,CAAE,CAAEgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWwC,GAiBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdlD,KAAA,CAAC1C,KAAK,EAAC8D,IAAI,cAAEtB,IAAA,CAAClC,SAAS,GAAE,CAAE,CAACqI,QAAQ,CAAC,MAAM,CAAC3E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eACnFpB,IAAA,CAACpD,UAAU,EAAC2F,UAAU,CAAE,GAAI,CAAAnB,QAAA,CAAEiE,MAAM,CAACe,OAAO,CAACjF,KAAK,CAAa,CAAC,cAChEnB,IAAA,CAACpD,UAAU,EAAAwE,QAAA,CAAEiE,MAAM,CAACe,OAAO,CAACC,KAAK,CAAa,CAAC,EAC1C,CAAC,EACL,CAAC,CACF,CAAC,CACE,CAAC,CACT,CAAC,CAEV,CAEA;AACA,GAAIvF,OAAO,GAAK,SAAS,CAAE,CACzB,KAAM,CAAAuE,MAAM,CAAG5E,CAAC,CAAC,yBAAyB,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,CAEpE,KAAM,CAAAgB,aAAa,CAAIxF,OAAO,eAC5Bd,IAAA,CAACiB,WAAW,EAACE,KAAK,CAAEL,OAAO,CAACK,KAAM,CAACG,IAAI,cAAEtB,IAAA,CAAC1B,WAAW,GAAE,CAAE,CAAA8C,QAAA,cACvDlB,KAAA,CAACrD,GAAG,EAAC2E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,EAC3CN,OAAO,CAACmC,MAAM,eACbjD,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFN,OAAO,CAACmC,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC5BlD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAACZ,QAAQ,EAACiD,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAEsB,IAAK,CACd3B,EAAE,CAAE,CAAEgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWwC,GAiBL,CACX,CAAC,CACE,CACP,CACAtC,OAAO,CAACyF,WAAW,EAAIzF,OAAO,CAACyF,WAAW,CAACrD,GAAG,CAAC,CAACJ,UAAU,CAAEM,GAAG,GAAKP,gBAAgB,CAACC,UAAU,CAAC,CAAC,CACjGhC,OAAO,CAACiC,WAAW,eAClB/C,IAAA,CAACpD,UAAU,EACT4E,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDN,OAAO,CAACiC,WAAW,CACV,CACb,EACE,CAAC,CACK,CACd,CAED,mBACE/C,IAAA,CAACnD,GAAG,EACF2E,EAAE,CAAE,CACFqC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBvG,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAKvE,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHiC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAA5C,QAAA,cAEFpB,IAAA,CAACrD,SAAS,EAACsH,QAAQ,CAAC,IAAI,CAAA7C,QAAA,cACtBpB,IAAA,CAAClD,IAAI,EAACoH,EAAE,MAACC,OAAO,CAAE,GAAI,CAAA/C,QAAA,cACpBlB,KAAA,CAACrD,GAAG,EAAC2E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAQ,QAAA,eACjFpB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBd,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCkB,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDiE,MAAM,CAAClE,KAAK,CACH,CAAC,cACbnB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZb,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAAC2D,IAAI,CAACC,SAAS,CACnCxC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDiE,MAAM,CAACtC,WAAW,CACT,CAAC,CAGZsC,MAAM,CAACmB,QAAQ,EAAInB,MAAM,CAACmB,QAAQ,CAACtD,GAAG,CAAC,CAACpC,OAAO,CAAEsC,GAAG,gBACnDpD,IAAA,QAAAoB,QAAA,CACGkF,aAAa,CAACxF,OAAO,CAAC,EADfsC,GAEL,CACN,CAAC,CAGDiC,MAAM,CAACoB,QAAQ,eACdvG,KAAA,CAAAE,SAAA,EAAAgB,QAAA,eACEpB,IAAA,CAAC/C,OAAO,EAACuE,EAAE,CAAE,CAAEkF,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1BxG,KAAA,CAACzC,IAAI,EAACkJ,SAAS,MAAClC,OAAO,CAAE,CAAE,CAAChD,EAAE,CAAE,CAAE,CAAAL,QAAA,eAChCpB,IAAA,CAACvC,IAAI,EAAC0F,IAAI,MAACkB,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlD,QAAA,cACvBlB,KAAA,CAAC1C,KAAK,EAAC8D,IAAI,cAAEtB,IAAA,CAAClB,YAAY,GAAE,CAAE,CAACqH,QAAQ,CAAC,SAAS,CAAC3E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eACzFpB,IAAA,CAACpD,UAAU,EAAC2F,UAAU,CAAE,GAAI,CAAAnB,QAAA,CAAEiE,MAAM,CAACoB,QAAQ,CAACG,cAAc,CAAa,CAAC,cAC1E5G,IAAA,CAACpD,UAAU,EAAAwE,QAAA,CAAEiE,MAAM,CAACoB,QAAQ,CAACI,kBAAkB,CAAa,CAAC,EACxD,CAAC,CACJ,CAAC,cACP7G,IAAA,CAACvC,IAAI,EAAC0F,IAAI,MAACkB,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlD,QAAA,cACvBlB,KAAA,CAAC1C,KAAK,EAAC8D,IAAI,cAAEtB,IAAA,CAACxB,oBAAoB,GAAE,CAAE,CAAC2H,QAAQ,CAAC,MAAM,CAAC3E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eAC9FpB,IAAA,CAACpD,UAAU,EAAC2F,UAAU,CAAE,GAAI,CAAAnB,QAAA,CAAEiE,MAAM,CAACoB,QAAQ,CAACK,kBAAkB,CAAa,CAAC,cAC9E9G,IAAA,CAACpD,UAAU,EAAAwE,QAAA,CAAEiE,MAAM,CAACoB,QAAQ,CAACM,sBAAsB,CAAa,CAAC,EAC5D,CAAC,CACJ,CAAC,cACP/G,IAAA,CAACvC,IAAI,EAAC0F,IAAI,MAACkB,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlD,QAAA,cACvBlB,KAAA,CAAC1C,KAAK,EAAC8D,IAAI,cAAEtB,IAAA,CAACtB,WAAW,GAAE,CAAE,CAACyH,QAAQ,CAAC,MAAM,CAAC3E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eACrFpB,IAAA,CAACpD,UAAU,EAAC2F,UAAU,CAAE,GAAI,CAAAnB,QAAA,CAAEiE,MAAM,CAACoB,QAAQ,CAACO,eAAe,CAAa,CAAC,cAC3EhH,IAAA,CAACpD,UAAU,EAAAwE,QAAA,CAAEiE,MAAM,CAACoB,QAAQ,CAACQ,mBAAmB,CAAa,CAAC,EACzD,CAAC,CACJ,CAAC,cACPjH,IAAA,CAACvC,IAAI,EAAC0F,IAAI,MAACkB,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlD,QAAA,cACvBlB,KAAA,CAAC1C,KAAK,EAAC8D,IAAI,cAAEtB,IAAA,CAAC9B,eAAe,GAAE,CAAE,CAACiI,QAAQ,CAAC,SAAS,CAAC3E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eAC5FpB,IAAA,CAACpD,UAAU,EAAC2F,UAAU,CAAE,GAAI,CAAAnB,QAAA,CAAEiE,MAAM,CAACoB,QAAQ,CAACS,mBAAmB,CAAa,CAAC,cAC/ElH,IAAA,CAACpD,UAAU,EAAAwE,QAAA,CAAEiE,MAAM,CAACoB,QAAQ,CAACU,uBAAuB,CAAa,CAAC,EAC7D,CAAC,CACJ,CAAC,EACH,CAAC,EACP,CACH,cAGDjH,KAAA,CAAC1C,KAAK,EAAC8D,IAAI,cAAEtB,IAAA,CAAClC,SAAS,GAAE,CAAE,CAACqI,QAAQ,CAAC,MAAM,CAAC3E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eACnFpB,IAAA,CAACpD,UAAU,EAAC2F,UAAU,CAAE,GAAI,CAAAnB,QAAA,CAAEiE,MAAM,CAACe,OAAO,CAACjF,KAAK,CAAa,CAAC,cAChEnB,IAAA,CAACpD,UAAU,EAAAwE,QAAA,CAAEiE,MAAM,CAACe,OAAO,CAACC,KAAK,CAAa,CAAC,EAC1C,CAAC,EACL,CAAC,CACF,CAAC,CACE,CAAC,CACT,CAAC,CAEV,CAEA;AACA,GAAIvF,OAAO,GAAK,QAAQ,CAAE,CACxB,KAAM,CAAAsG,MAAM,CAAG3G,CAAC,CAAC,iBAAiB,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,CAE5D,mBACEtF,IAAA,CAACnD,GAAG,EACF2E,EAAE,CAAE,CACFqC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBvG,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAKvE,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHiC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAA5C,QAAA,cAEFpB,IAAA,CAACrD,SAAS,EAACsH,QAAQ,CAAC,IAAI,CAAA7C,QAAA,cACtBpB,IAAA,CAAClD,IAAI,EAACoH,EAAE,MAACC,OAAO,CAAE,GAAI,CAAA/C,QAAA,cACpBlB,KAAA,CAACrD,GAAG,EAAC2E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eAC5CpB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZkC,KAAK,CAAC,QAAQ,CACdhD,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLc,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAV,QAAA,CAEDgG,MAAM,CAACjG,KAAK,CACH,CAAC,cAEbnB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZkC,KAAK,CAAC,QAAQ,CACdhD,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLe,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAAC2D,IAAI,CAACC,SAAS,CACnCvB,QAAQ,CAAE,OAAO,CACjBR,EAAE,CAAE,MACN,CAAE,CAAArC,QAAA,CAEDgG,MAAM,CAACrE,WAAW,CACT,CAAC,cAEb/C,IAAA,CAAC/C,OAAO,EAACuE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BvB,KAAA,CAACe,WAAW,EAACE,KAAK,CAAEiG,MAAM,CAACC,QAAQ,CAAClG,KAAM,CAACG,IAAI,cAAEtB,IAAA,CAAC9B,eAAe,GAAE,CAAE,CAAAkD,QAAA,eACnEpB,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDgG,MAAM,CAACC,QAAQ,CAACtE,WAAW,CAClB,CAAC,cACb/C,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFgG,MAAM,CAACC,QAAQ,CAACC,KAAK,CAACpE,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACnClD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAAC9B,eAAe,EAACmE,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAEsB,IAAK,CACd3B,EAAE,CAAE,CAAEgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAC1E,CAAC,GAhBWwC,GAiBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdlD,KAAA,CAACe,WAAW,EAACE,KAAK,CAAEiG,MAAM,CAACG,QAAQ,CAACpG,KAAM,CAACG,IAAI,cAAEtB,IAAA,CAAC5B,UAAU,GAAE,CAAE,CAAAgD,QAAA,eAC9DpB,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDgG,MAAM,CAACG,QAAQ,CAACxE,WAAW,CAClB,CAAC,cACb/C,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFgG,MAAM,CAACG,QAAQ,CAACD,KAAK,CAACpE,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACnClD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAAC5B,UAAU,EAACiE,KAAK,CAAC,OAAO,CAAE,CAAC,CAChB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAEsB,IAAK,CACd3B,EAAE,CAAE,CAAEgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAC1E,CAAC,GAhBWwC,GAiBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,CAGbgE,MAAM,CAACZ,QAAQ,EAAIY,MAAM,CAACZ,QAAQ,CAACtD,GAAG,CAAC,CAACpC,OAAO,CAAEsC,GAAG,gBACnDlD,KAAA,CAACe,WAAW,EAAWE,KAAK,CAAEL,OAAO,CAACK,KAAM,CAACG,IAAI,cAAEtB,IAAA,CAACZ,QAAQ,GAAE,CAAE,CAAAgC,QAAA,eAC9DpB,IAAA,CAACpD,UAAU,EACT6E,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEoC,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,CAEDN,OAAO,CAACiC,WAAW,CACV,CAAC,CACZjC,OAAO,CAACwG,KAAK,eACZtH,IAAA,CAAC9C,IAAI,EAAAkE,QAAA,CACFN,OAAO,CAACwG,KAAK,CAACpE,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC3BlD,KAAA,CAAC/C,QAAQ,EAAWqE,EAAE,CAAE,CACtB6B,EAAE,CAAEzC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB0C,EAAE,CAAE1C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2C,aAAa,CAAE,KAAK,CACpBP,SAAS,CAAEpC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAQ,QAAA,eACApB,IAAA,CAAC3C,YAAY,EAACmE,EAAE,CAAE,CAChBgC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwB,EAAE,CAAExB,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAQ,QAAA,cACApB,IAAA,CAACZ,QAAQ,EAACiD,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfrC,IAAA,CAAC5C,YAAY,EACXyE,OAAO,CAAEsB,IAAK,CACd3B,EAAE,CAAE,CAAEgB,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAC1E,CAAC,GAhBWwC,GAiBL,CACX,CAAC,CACE,CACP,GAjCeA,GAkCL,CACd,CAAC,cAGFlD,KAAA,CAAC1C,KAAK,EAAC8D,IAAI,cAAEtB,IAAA,CAAClC,SAAS,GAAE,CAAE,CAACqI,QAAQ,CAAC,MAAM,CAAC3E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eACnFpB,IAAA,CAACpD,UAAU,EAAC2F,UAAU,CAAE,GAAI,CAAAnB,QAAA,CAAEgG,MAAM,CAAChB,OAAO,CAACjF,KAAK,CAAa,CAAC,cAChEnB,IAAA,CAACpD,UAAU,EAAAwE,QAAA,CAAEgG,MAAM,CAAChB,OAAO,CAACC,KAAK,CAAa,CAAC,EAC1C,CAAC,EACL,CAAC,CACF,CAAC,CACE,CAAC,CACT,CAAC,CAEV,CAEA;AACA,GAAIvF,OAAO,GAAK,SAAS,CAAE,CACzB,mBACEd,IAAA,CAACnD,GAAG,EACF2E,EAAE,CAAE,CACFqC,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBvG,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAKvE,KAAK,CAACmD,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHiC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAA5C,QAAA,cAEFpB,IAAA,CAACrD,SAAS,EAACsH,QAAQ,CAAC,IAAI,CAAA7C,QAAA,cACtBpB,IAAA,CAAClD,IAAI,EAACoH,EAAE,MAACC,OAAO,CAAE,GAAI,CAAA/C,QAAA,cACpBlB,KAAA,CAACrD,GAAG,EAAC2E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eAC5CpB,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZkC,KAAK,CAAC,QAAQ,CACdhD,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLc,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAV,QAAA,CAEDqB,KAAK,CAAC,wBAAwB,CAAC,CACtB,CAAC,cAEbzC,IAAA,CAACpD,UAAU,EACT0F,OAAO,CAAC,IAAI,CACZkC,KAAK,CAAC,QAAQ,CACdhD,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLe,UAAU,CAAE5B,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDyB,KAAK,CAAE3B,KAAK,CAACkB,OAAO,CAAC2D,IAAI,CAACC,SAAS,CACnCvB,QAAQ,CAAE,OAAO,CACjBR,EAAE,CAAE,MACN,CAAE,CAAArC,QAAA,CAEDqB,KAAK,CAAC,8BAA8B,CAAC,CAC5B,CAAC,CAGZiB,oBAAoB,CACnBjB,KAAK,CAAC,8CAA8C,CAAC,CACrDA,KAAK,CAAC,oDAAoD,CAAC,CAC3DhC,CAAC,CAAC,8CAA8C,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,EAAI,EAChF,CAAC,CAGA5B,oBAAoB,CACnBjB,KAAK,CAAC,uCAAuC,CAAC,CAC9CA,KAAK,CAAC,6CAA6C,CAAC,CACpDhC,CAAC,CAAC,uCAAuC,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,EAAI,EACzE,CAAC,CAGA5B,oBAAoB,CACnBjB,KAAK,CAAC,2CAA2C,CAAC,CAClDA,KAAK,CAAC,iDAAiD,CAAC,CACxDhC,CAAC,CAAC,2CAA2C,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,EAAI,EAC7E,CAAC,CAGA5B,oBAAoB,CACnBjB,KAAK,CAAC,qCAAqC,CAAC,CAC5CA,KAAK,CAAC,2CAA2C,CAAC,CAClDhC,CAAC,CAAC,qCAAqC,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,EAAI,EACvE,CAAC,CAGA5B,oBAAoB,CACnBjB,KAAK,CAAC,mCAAmC,CAAC,CAC1CA,KAAK,CAAC,yCAAyC,CAAC,CAChDhC,CAAC,CAAC,mCAAmC,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,EAAI,EACrE,CAAC,CAGA5B,oBAAoB,CACnBjB,KAAK,CAAC,gCAAgC,CAAC,CACvCA,KAAK,CAAC,sCAAsC,CAAC,CAC7ChC,CAAC,CAAC,gCAAgC,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,EAAI,EAClE,CAAC,CAGA5B,oBAAoB,CACnBjB,KAAK,CAAC,2CAA2C,CAAC,CAClDA,KAAK,CAAC,iDAAiD,CAAC,CACxDhC,CAAC,CAAC,2CAA2C,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,EAAI,EAC7E,CAAC,CAGA5B,oBAAoB,CACnBjB,KAAK,CAAC,sCAAsC,CAAC,CAC7CA,KAAK,CAAC,4CAA4C,CAAC,CACnDhC,CAAC,CAAC,sCAAsC,CAAE,CAAE6E,aAAa,CAAE,IAAK,CAAC,CAAC,EAAI,EACxE,CAAC,cAGDpF,KAAA,CAAC1C,KAAK,EAAC8D,IAAI,cAAEtB,IAAA,CAAClC,SAAS,GAAE,CAAE,CAACqI,QAAQ,CAAC,MAAM,CAAC3E,EAAE,CAAE,CAAE+C,SAAS,CAAE3D,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAQ,QAAA,eACnFpB,IAAA,CAACpD,UAAU,EAAC2F,UAAU,CAAE,GAAI,CAAAnB,QAAA,CAAEqB,KAAK,CAAC,gCAAgC,CAAC,CAAa,CAAC,cACnFzC,IAAA,CAACpD,UAAU,EAAAwE,QAAA,CAAEqB,KAAK,CAAC,gCAAgC,CAAC,CAAa,CAAC,EAC7D,CAAC,EACL,CAAC,CACF,CAAC,CACE,CAAC,CACT,CAAC,CAEV,CAEA,MAAO,KAAI,CACb,CAAC,CAED,cAAe,CAAAjC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}