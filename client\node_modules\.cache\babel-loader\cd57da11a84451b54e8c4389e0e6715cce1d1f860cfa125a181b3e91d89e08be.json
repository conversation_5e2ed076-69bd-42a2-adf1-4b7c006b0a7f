{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useLocation,useNavigate}from'react-router-dom';import{Container,Typography,Box,Paper,Tabs,Tab,List,ListItem,ListItemIcon,ListItemText,Alert,Divider,Grid}from'@mui/material';import{Info as InfoIcon,CheckCircle as CheckCircleIcon,Cancel as CancelIcon,Security as SecurityIcon,CurrencyExchange as CurrencyExchangeIcon,Receipt as ReceiptIcon,Payment as PaymentIcon}from'@mui/icons-material';import{useTranslation}from'react-i18next';import{useTheme}from'@mui/material/styles';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PlatformPolicy=()=>{var _terms$sections,_bookingPolicy$sectio,_paymentPolicy$sectio,_refundPolicy$additio,_privacyPolicy$sectio;const{t,i18n}=useTranslation();const theme=useTheme();const location=useLocation();const navigate=useNavigate();const isRTL=i18n.language==='ar';const[activeTab,setActiveTab]=useState(0);// تحديد التبويب النشط بناءً على الـ hash في الـ URL\nuseEffect(()=>{const hash=location.hash.replace('#','');switch(hash){case'terms':setActiveTab(0);break;case'booking-cancellation':setActiveTab(1);break;case'payment':setActiveTab(2);break;case'refund':setActiveTab(3);break;case'privacy':setActiveTab(4);break;default:setActiveTab(0);}},[location.hash]);const handleTabChange=(event,newValue)=>{setActiveTab(newValue);const tabs=['terms','booking-cancellation','payment','refund','privacy'];navigate(`/platform-policy#${tabs[newValue]}`);};// بيانات الصفحات\nconst terms=t('termsAndConditions',{returnObjects:true});const bookingPolicy=t('bookingCancellationPolicy',{returnObjects:true});const paymentPolicy=t('bookingPaymentPolicy',{returnObjects:true});const refundPolicy=t('refundPolicy',{returnObjects:true});const privacyPolicy=t('privacyPolicy',{returnObjects:true});// مكون لعرض القسم\nconst SectionCard=_ref=>{let{title,icon,children}=_ref;return/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:3,mb:3,borderRadius:2,direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:2,children:[icon,/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,ml:isRTL?0:1,mr:isRTL?1:0,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main},children:title})]}),children]});};return/*#__PURE__*/_jsxs(Container,{maxWidth:\"lg\",sx:{py:4,direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:700,mb:4,textAlign:\"center\",sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main},children:t('platformPolicies')}),/*#__PURE__*/_jsx(Paper,{elevation:1,sx:{mb:4},children:/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{'& .MuiTab-root':{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',fontWeight:600,minHeight:64}},children:[/*#__PURE__*/_jsx(Tab,{label:terms.title}),/*#__PURE__*/_jsx(Tab,{label:bookingPolicy.title}),/*#__PURE__*/_jsx(Tab,{label:paymentPolicy.title}),/*#__PURE__*/_jsx(Tab,{label:refundPolicy.title}),/*#__PURE__*/_jsx(Tab,{label:privacyPolicy.title})]})}),/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[activeTab===0&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:terms.title}),/*#__PURE__*/_jsx(Typography,{mb:4,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:terms.description}),(_terms$sections=terms.sections)===null||_terms$sections===void 0?void 0:_terms$sections.map((section,index)=>/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.description&&/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:section.description}),section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]})},index))]}),activeTab===1&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:bookingPolicy.title}),/*#__PURE__*/_jsx(Typography,{mb:4,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:bookingPolicy.description}),(_bookingPolicy$sectio=bookingPolicy.sections)===null||_bookingPolicy$sectio===void 0?void 0:_bookingPolicy$sectio.map((section,index)=>/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.description&&/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:section.description}),section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]})},index))]}),activeTab===2&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:paymentPolicy.title}),/*#__PURE__*/_jsx(Typography,{mb:4,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:paymentPolicy.description}),(_paymentPolicy$sectio=paymentPolicy.sections)===null||_paymentPolicy$sectio===void 0?void 0:_paymentPolicy$sectio.map((section,index)=>/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(PaymentIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.description&&/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:section.description}),section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]})},index)),paymentPolicy.features&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Divider,{sx:{my:3}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,mb:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(SecurityIcon,{}),severity:\"success\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:paymentPolicy.features.securePayments}),/*#__PURE__*/_jsx(Typography,{children:paymentPolicy.features.securePaymentsDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(CurrencyExchangeIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:paymentPolicy.features.multipleCurrencies}),/*#__PURE__*/_jsx(Typography,{children:paymentPolicy.features.multipleCurrenciesDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(ReceiptIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:paymentPolicy.features.instantReceipts}),/*#__PURE__*/_jsx(Typography,{children:paymentPolicy.features.instantReceiptsDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(CheckCircleIcon,{}),severity:\"success\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:paymentPolicy.features.instantConfirmation}),/*#__PURE__*/_jsx(Typography,{children:paymentPolicy.features.instantConfirmationDesc})]})})]})]}),paymentPolicy.contact&&/*#__PURE__*/_jsxs(Alert,{severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:paymentPolicy.contact.title}),/*#__PURE__*/_jsx(Typography,{children:paymentPolicy.contact.email})]})]}),activeTab===3&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:refundPolicy.title}),/*#__PURE__*/_jsx(Typography,{mb:4,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refundPolicy.description}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{title:refundPolicy.section1.title,icon:/*#__PURE__*/_jsx(CheckCircleIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refundPolicy.section1.description}),/*#__PURE__*/_jsx(List,{children:refundPolicy.section1.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'}})]},idx))})]}),/*#__PURE__*/_jsxs(SectionCard,{title:refundPolicy.section2.title,icon:/*#__PURE__*/_jsx(CancelIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refundPolicy.section2.description}),/*#__PURE__*/_jsx(List,{children:refundPolicy.section2.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(CancelIcon,{color:\"error\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'}})]},idx))})]}),(_refundPolicy$additio=refundPolicy.additionalSections)===null||_refundPolicy$additio===void 0?void 0:_refundPolicy$additio.map((section,index)=>/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.description&&/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:section.description}),section.items&&/*#__PURE__*/_jsx(List,{children:section.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'}})]},idx))})]})},index)),refundPolicy.contact&&/*#__PURE__*/_jsxs(Alert,{severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:refundPolicy.contact.title}),/*#__PURE__*/_jsx(Typography,{children:refundPolicy.contact.email})]})]}),activeTab===4&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:privacyPolicy.title}),/*#__PURE__*/_jsx(Typography,{mb:4,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:privacyPolicy.description}),(_privacyPolicy$sectio=privacyPolicy.sections)===null||_privacyPolicy$sectio===void 0?void 0:_privacyPolicy$sectio.map((section,index)=>{const{title,description,bullets=[]}=section;return/*#__PURE__*/_jsx(SectionCard,{title:title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[description&&/*#__PURE__*/_jsx(Typography,{mb:bullets.length>0?2:0,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:description}),bullets.length>0&&/*#__PURE__*/_jsx(List,{children:bullets.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{textAlign:isRTL?'right':'left',fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]})},index);})]})]})]});};export default PlatformPolicy;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "useNavigate", "Container", "Typography", "Box", "Paper", "Tabs", "Tab", "List", "ListItem", "ListItemIcon", "ListItemText", "<PERSON><PERSON>", "Divider", "Grid", "Info", "InfoIcon", "CheckCircle", "CheckCircleIcon", "Cancel", "CancelIcon", "Security", "SecurityIcon", "CurrencyExchange", "CurrencyExchangeIcon", "Receipt", "ReceiptIcon", "Payment", "PaymentIcon", "useTranslation", "useTheme", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PlatformPolicy", "_terms$sections", "_bookingPolicy$sectio", "_paymentPolicy$sectio", "_refundPolicy$additio", "_privacyPolicy$sectio", "t", "i18n", "theme", "location", "navigate", "isRTL", "language", "activeTab", "setActiveTab", "hash", "replace", "handleTabChange", "event", "newValue", "tabs", "terms", "returnObjects", "bookingPolicy", "paymentPolicy", "refundPolicy", "privacyPolicy", "SectionCard", "_ref", "title", "icon", "children", "elevation", "sx", "p", "mb", "borderRadius", "direction", "display", "alignItems", "variant", "fontWeight", "ml", "mr", "fontFamily", "color", "palette", "primary", "main", "max<PERSON><PERSON><PERSON>", "py", "textAlign", "value", "onChange", "scrollButtons", "minHeight", "label", "description", "sections", "map", "section", "index", "points", "item", "idx", "pl", "pr", "flexDirection", "min<PERSON><PERSON><PERSON>", "mx", "features", "my", "container", "spacing", "xs", "md", "severity", "securePayments", "securePaymentsDesc", "multipleCurrencies", "multipleCurrenciesDesc", "instantReceipts", "instantReceiptsDesc", "instantConfirmation", "instantConfirmationDesc", "contact", "email", "section1", "items", "section2", "additionalSections", "bullets", "length"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PlatformPolicy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport {\n  Container,\n  Typography,\n  Box,\n  Paper,\n  Tabs,\n  Tab,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Alert,\n  Divider,\n  Grid\n} from '@mui/material';\nimport {\n  Info as InfoIcon,\n  CheckCircle as CheckCircleIcon,\n  Cancel as CancelIcon,\n  Security as SecurityIcon,\n  CurrencyExchange as CurrencyExchangeIcon,\n  Receipt as ReceiptIcon,\n  Payment as PaymentIcon\n} from '@mui/icons-material';\nimport { useTranslation } from 'react-i18next';\nimport { useTheme } from '@mui/material/styles';\n\nconst PlatformPolicy = () => {\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const isRTL = i18n.language === 'ar';\n  \n  const [activeTab, setActiveTab] = useState(0);\n\n  // تحديد التبويب النشط بناءً على الـ hash في الـ URL\n  useEffect(() => {\n    const hash = location.hash.replace('#', '');\n    switch (hash) {\n      case 'terms':\n        setActiveTab(0);\n        break;\n      case 'booking-cancellation':\n        setActiveTab(1);\n        break;\n      case 'payment':\n        setActiveTab(2);\n        break;\n      case 'refund':\n        setActiveTab(3);\n        break;\n      case 'privacy':\n        setActiveTab(4);\n        break;\n      default:\n        setActiveTab(0);\n    }\n  }, [location.hash]);\n\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    const tabs = ['terms', 'booking-cancellation', 'payment', 'refund', 'privacy'];\n    navigate(`/platform-policy#${tabs[newValue]}`);\n  };\n\n  // بيانات الصفحات\n  const terms = t('termsAndConditions', { returnObjects: true });\n  const bookingPolicy = t('bookingCancellationPolicy', { returnObjects: true });\n  const paymentPolicy = t('bookingPaymentPolicy', { returnObjects: true });\n  const refundPolicy = t('refundPolicy', { returnObjects: true });\n  const privacyPolicy = t('privacyPolicy', { returnObjects: true });\n\n  // مكون لعرض القسم\n  const SectionCard = ({ title, icon, children }) => (\n    <Paper \n      elevation={2} \n      sx={{ \n        p: 3, \n        mb: 3, \n        borderRadius: 2,\n        direction: isRTL ? 'rtl' : 'ltr'\n      }}\n    >\n      <Box display=\"flex\" alignItems=\"center\" mb={2}>\n        {icon}\n        <Typography \n          variant=\"h6\" \n          fontWeight={600} \n          ml={isRTL ? 0 : 1}\n          mr={isRTL ? 1 : 0}\n          sx={{ \n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n            color: theme.palette.primary.main\n          }}\n        >\n          {title}\n        </Typography>\n      </Box>\n      {children}\n    </Paper>\n  );\n\n  return (\n    <Container maxWidth=\"lg\" sx={{ py: 4, direction: isRTL ? 'rtl' : 'ltr' }}>\n      {/* العنوان الرئيسي */}\n      <Typography\n        variant=\"h3\"\n        fontWeight={700}\n        mb={4}\n        textAlign=\"center\"\n        sx={{\n          fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n          color: theme.palette.primary.main\n        }}\n      >\n        {t('platformPolicies')}\n      </Typography>\n\n      {/* شريط التنقل */}\n      <Paper elevation={1} sx={{ mb: 4 }}>\n        <Tabs\n          value={activeTab}\n          onChange={handleTabChange}\n          variant=\"scrollable\"\n          scrollButtons=\"auto\"\n          sx={{\n            '& .MuiTab-root': {\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n              fontWeight: 600,\n              minHeight: 64,\n            }\n          }}\n        >\n          <Tab label={terms.title} />\n          <Tab label={bookingPolicy.title} />\n          <Tab label={paymentPolicy.title} />\n          <Tab label={refundPolicy.title} />\n          <Tab label={privacyPolicy.title} />\n        </Tabs>\n      </Paper>\n\n      {/* محتوى التبويبات */}\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n        {/* الشروط والأحكام */}\n        {activeTab === 0 && (\n          <Box>\n            <Typography\n              variant=\"h4\"\n              fontWeight={700}\n              mb={2}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                color: theme.palette.primary.main,\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {terms.title}\n            </Typography>\n            <Typography\n              mb={4}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {terms.description}\n            </Typography>\n            {terms.sections?.map((section, index) => (\n              <SectionCard key={index} title={section.title} icon={<InfoIcon />}>\n                <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                  {section.description && (\n                    <Typography \n                      mb={2}\n                      sx={{\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                        textAlign: isRTL ? 'right' : 'left'\n                      }}\n                    >\n                      {section.description}\n                    </Typography>\n                  )}\n                  {section.points && (\n                    <List>\n                      {section.points.map((item, idx) => (\n                        <ListItem key={idx} sx={{\n                          pl: isRTL ? 0 : 2,\n                          pr: isRTL ? 2 : 0,\n                          flexDirection: 'row',\n                          textAlign: isRTL ? 'right' : 'left',\n                        }}>\n                          <ListItemIcon sx={{\n                            minWidth: 'auto',\n                            mx: isRTL ? 0 : 1,\n                            ml: isRTL ? 1 : 0,\n                          }}>\n                            <InfoIcon color=\"success\" />\n                          </ListItemIcon>\n                          <ListItemText \n                            primary={item}\n                            sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n                  )}\n                </Box>\n              </SectionCard>\n            ))}\n          </Box>\n        )}\n\n        {/* سياسة الحجز والإلغاء */}\n        {activeTab === 1 && (\n          <Box>\n            <Typography\n              variant=\"h4\"\n              fontWeight={700}\n              mb={2}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                color: theme.palette.primary.main,\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {bookingPolicy.title}\n            </Typography>\n            <Typography\n              mb={4}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {bookingPolicy.description}\n            </Typography>\n            {bookingPolicy.sections?.map((section, index) => (\n              <SectionCard key={index} title={section.title} icon={<InfoIcon />}>\n                <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                  {section.description && (\n                    <Typography\n                      mb={2}\n                      sx={{\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                        textAlign: isRTL ? 'right' : 'left'\n                      }}\n                    >\n                      {section.description}\n                    </Typography>\n                  )}\n                  {section.points && (\n                    <List>\n                      {section.points.map((item, idx) => (\n                        <ListItem key={idx} sx={{\n                          pl: isRTL ? 0 : 2,\n                          pr: isRTL ? 2 : 0,\n                          flexDirection: 'row',\n                          textAlign: isRTL ? 'right' : 'left',\n                        }}>\n                          <ListItemIcon sx={{\n                            minWidth: 'auto',\n                            mx: isRTL ? 0 : 1,\n                            ml: isRTL ? 1 : 0,\n                          }}>\n                            <InfoIcon color=\"success\" />\n                          </ListItemIcon>\n                          <ListItemText\n                            primary={item}\n                            sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n                  )}\n                </Box>\n              </SectionCard>\n            ))}\n          </Box>\n        )}\n\n        {/* سياسة الدفع */}\n        {activeTab === 2 && (\n          <Box>\n            <Typography\n              variant=\"h4\"\n              fontWeight={700}\n              mb={2}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                color: theme.palette.primary.main,\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {paymentPolicy.title}\n            </Typography>\n            <Typography\n              mb={4}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {paymentPolicy.description}\n            </Typography>\n\n            {/* الأقسام الرئيسية */}\n            {paymentPolicy.sections?.map((section, index) => (\n              <SectionCard key={index} title={section.title} icon={<PaymentIcon />}>\n                <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                  {section.description && (\n                    <Typography\n                      mb={2}\n                      sx={{\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                        textAlign: isRTL ? 'right' : 'left'\n                      }}\n                    >\n                      {section.description}\n                    </Typography>\n                  )}\n                  {section.points && (\n                    <List>\n                      {section.points.map((item, idx) => (\n                        <ListItem key={idx} sx={{\n                          pl: isRTL ? 0 : 2,\n                          pr: isRTL ? 2 : 0,\n                          flexDirection: 'row',\n                          textAlign: isRTL ? 'right' : 'left',\n                        }}>\n                          <ListItemIcon sx={{\n                            minWidth: 'auto',\n                            mx: isRTL ? 0 : 1,\n                            ml: isRTL ? 1 : 0,\n                          }}>\n                            <InfoIcon color=\"success\" />\n                          </ListItemIcon>\n                          <ListItemText\n                            primary={item}\n                            sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n                  )}\n                </Box>\n              </SectionCard>\n            ))}\n\n            {/* الميزات */}\n            {paymentPolicy.features && (\n              <>\n                <Divider sx={{ my: 3 }} />\n                <Grid container spacing={2} mb={3}>\n                  <Grid item xs={12} md={6}>\n                    <Alert icon={<SecurityIcon />} severity=\"success\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                      <Typography fontWeight={600}>{paymentPolicy.features.securePayments}</Typography>\n                      <Typography>{paymentPolicy.features.securePaymentsDesc}</Typography>\n                    </Alert>\n                  </Grid>\n                  <Grid item xs={12} md={6}>\n                    <Alert icon={<CurrencyExchangeIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                      <Typography fontWeight={600}>{paymentPolicy.features.multipleCurrencies}</Typography>\n                      <Typography>{paymentPolicy.features.multipleCurrenciesDesc}</Typography>\n                    </Alert>\n                  </Grid>\n                  <Grid item xs={12} md={6}>\n                    <Alert icon={<ReceiptIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                      <Typography fontWeight={600}>{paymentPolicy.features.instantReceipts}</Typography>\n                      <Typography>{paymentPolicy.features.instantReceiptsDesc}</Typography>\n                    </Alert>\n                  </Grid>\n                  <Grid item xs={12} md={6}>\n                    <Alert icon={<CheckCircleIcon />} severity=\"success\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                      <Typography fontWeight={600}>{paymentPolicy.features.instantConfirmation}</Typography>\n                      <Typography>{paymentPolicy.features.instantConfirmationDesc}</Typography>\n                    </Alert>\n                  </Grid>\n                </Grid>\n              </>\n            )}\n\n            {/* معلومات التواصل */}\n            {paymentPolicy.contact && (\n              <Alert severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                <Typography fontWeight={600}>{paymentPolicy.contact.title}</Typography>\n                <Typography>{paymentPolicy.contact.email}</Typography>\n              </Alert>\n            )}\n          </Box>\n        )}\n\n        {/* سياسة الاسترداد */}\n        {activeTab === 3 && (\n          <Box>\n            <Typography\n              variant=\"h4\"\n              fontWeight={700}\n              mb={2}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                color: theme.palette.primary.main,\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {refundPolicy.title}\n            </Typography>\n            <Typography\n              mb={4}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {refundPolicy.description}\n            </Typography>\n\n            <Divider sx={{ mb: 3 }} />\n\n            {/* القسم الأول */}\n            <SectionCard title={refundPolicy.section1.title} icon={<CheckCircleIcon />}>\n              <Typography\n                mb={2}\n                sx={{\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                  textAlign: isRTL ? 'right' : 'left'\n                }}\n              >\n                {refundPolicy.section1.description}\n              </Typography>\n              <List>\n                {refundPolicy.section1.items.map((item, idx) => (\n                  <ListItem key={idx} sx={{\n                    pl: isRTL ? 0 : 2,\n                    pr: isRTL ? 2 : 0,\n                    flexDirection: 'row',\n                    textAlign: isRTL ? 'right' : 'left',\n                  }}>\n                    <ListItemIcon sx={{\n                      minWidth: 'auto',\n                      mx: isRTL ? 0 : 1,\n                      ml: isRTL ? 1 : 0,\n                    }}>\n                      <CheckCircleIcon color=\"success\" />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={item}\n                      sx={{\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                        textAlign: isRTL ? 'right' : 'left'\n                      }}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </SectionCard>\n\n            {/* القسم الثاني */}\n            <SectionCard title={refundPolicy.section2.title} icon={<CancelIcon />}>\n              <Typography\n                mb={2}\n                sx={{\n                  fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                  textAlign: isRTL ? 'right' : 'left'\n                }}\n              >\n                {refundPolicy.section2.description}\n              </Typography>\n              <List>\n                {refundPolicy.section2.items.map((item, idx) => (\n                  <ListItem key={idx} sx={{\n                    pl: isRTL ? 0 : 2,\n                    pr: isRTL ? 2 : 0,\n                    flexDirection: 'row',\n                    textAlign: isRTL ? 'right' : 'left',\n                  }}>\n                    <ListItemIcon sx={{\n                      minWidth: 'auto',\n                      mx: isRTL ? 0 : 1,\n                      ml: isRTL ? 1 : 0,\n                    }}>\n                      <CancelIcon color=\"error\" />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary={item}\n                      sx={{\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                        textAlign: isRTL ? 'right' : 'left'\n                      }}\n                    />\n                  </ListItem>\n                ))}\n              </List>\n            </SectionCard>\n\n            {/* الأقسام الإضافية */}\n            {refundPolicy.additionalSections?.map((section, index) => (\n              <SectionCard key={index} title={section.title} icon={<InfoIcon />}>\n                <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                  {section.description && (\n                    <Typography\n                      mb={2}\n                      sx={{\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                        textAlign: isRTL ? 'right' : 'left'\n                      }}\n                    >\n                      {section.description}\n                    </Typography>\n                  )}\n                  {section.items && (\n                    <List>\n                      {section.items.map((item, idx) => (\n                        <ListItem key={idx} sx={{\n                          pl: isRTL ? 0 : 2,\n                          pr: isRTL ? 2 : 0,\n                          flexDirection: 'row',\n                          textAlign: isRTL ? 'right' : 'left',\n                        }}>\n                          <ListItemIcon sx={{\n                            minWidth: 'auto',\n                            mx: isRTL ? 0 : 1,\n                            ml: isRTL ? 1 : 0,\n                          }}>\n                            <InfoIcon color=\"success\" />\n                          </ListItemIcon>\n                          <ListItemText\n                            primary={item}\n                            sx={{\n                              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\n                              textAlign: isRTL ? 'right' : 'left'\n                            }}\n                          />\n                        </ListItem>\n                      ))}\n                    </List>\n                  )}\n                </Box>\n              </SectionCard>\n            ))}\n\n            {/* معلومات التواصل */}\n            {refundPolicy.contact && (\n              <Alert severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                <Typography fontWeight={600}>{refundPolicy.contact.title}</Typography>\n                <Typography>{refundPolicy.contact.email}</Typography>\n              </Alert>\n            )}\n          </Box>\n        )}\n\n        {/* سياسة الخصوصية */}\n        {activeTab === 4 && (\n          <Box>\n            <Typography\n              variant=\"h4\"\n              fontWeight={700}\n              mb={2}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                color: theme.palette.primary.main,\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {privacyPolicy.title}\n            </Typography>\n            <Typography\n              mb={4}\n              sx={{\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                textAlign: isRTL ? 'right' : 'left'\n              }}\n            >\n              {privacyPolicy.description}\n            </Typography>\n\n            {/* الأقسام */}\n            {privacyPolicy.sections?.map((section, index) => {\n              const { title, description, bullets = [] } = section;\n\n              return (\n                <SectionCard key={index} title={title} icon={<InfoIcon />}>\n                  <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\n                    {description && (\n                      <Typography\n                        mb={bullets.length > 0 ? 2 : 0}\n                        sx={{\n                          fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n                          textAlign: isRTL ? 'right' : 'left'\n                        }}\n                      >\n                        {description}\n                      </Typography>\n                    )}\n                    {bullets.length > 0 && (\n                      <List>\n                        {bullets.map((item, idx) => (\n                          <ListItem\n                            key={idx}\n                            sx={{\n                              pl: isRTL ? 0 : 2,\n                              pr: isRTL ? 2 : 0,\n                              flexDirection: 'row',\n                              textAlign: isRTL ? 'right' : 'left',\n                            }}\n                          >\n                            <ListItemIcon\n                              sx={{\n                                minWidth: 'auto',\n                                mx: isRTL ? 0 : 1,\n                                ml: isRTL ? 1 : 0,\n                              }}\n                            >\n                              <InfoIcon color=\"primary\" />\n                            </ListItemIcon>\n                            <ListItemText\n                              primary={item}\n                              sx={{\n                                textAlign: isRTL ? 'right' : 'left',\n                                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit'\n                              }}\n                            />\n                          </ListItem>\n                        ))}\n                      </List>\n                    )}\n                  </Box>\n                </SectionCard>\n              );\n            })}\n          </Box>\n        )}\n      </Box>\n    </Container>\n  );\n};\n\nexport default PlatformPolicy;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,KAAK,CACLC,IAAI,CACJC,GAAG,CACHC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,KAAK,CACLC,OAAO,CACPC,IAAI,KACC,eAAe,CACtB,OACEC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,gBAAgB,GAAI,CAAAC,oBAAoB,CACxCC,OAAO,GAAI,CAAAC,WAAW,CACtBC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,QAAQ,KAAQ,sBAAsB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEhD,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,KAAAC,eAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAC3B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGf,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAgB,KAAK,CAAGf,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAgB,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+C,QAAQ,CAAG9C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA+C,KAAK,CAAGJ,IAAI,CAACK,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGrD,QAAQ,CAAC,CAAC,CAAC,CAE7C;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAqD,IAAI,CAAGN,QAAQ,CAACM,IAAI,CAACC,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CAC3C,OAAQD,IAAI,EACV,IAAK,OAAO,CACVD,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,sBAAsB,CACzBA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,SAAS,CACZA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,QAAQ,CACXA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,IAAK,SAAS,CACZA,YAAY,CAAC,CAAC,CAAC,CACf,MACF,QACEA,YAAY,CAAC,CAAC,CAAC,CACnB,CACF,CAAC,CAAE,CAACL,QAAQ,CAACM,IAAI,CAAC,CAAC,CAEnB,KAAM,CAAAE,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC3CL,YAAY,CAACK,QAAQ,CAAC,CACtB,KAAM,CAAAC,IAAI,CAAG,CAAC,OAAO,CAAE,sBAAsB,CAAE,SAAS,CAAE,QAAQ,CAAE,SAAS,CAAC,CAC9EV,QAAQ,CAAC,oBAAoBU,IAAI,CAACD,QAAQ,CAAC,EAAE,CAAC,CAChD,CAAC,CAED;AACA,KAAM,CAAAE,KAAK,CAAGf,CAAC,CAAC,oBAAoB,CAAE,CAAEgB,aAAa,CAAE,IAAK,CAAC,CAAC,CAC9D,KAAM,CAAAC,aAAa,CAAGjB,CAAC,CAAC,2BAA2B,CAAE,CAAEgB,aAAa,CAAE,IAAK,CAAC,CAAC,CAC7E,KAAM,CAAAE,aAAa,CAAGlB,CAAC,CAAC,sBAAsB,CAAE,CAAEgB,aAAa,CAAE,IAAK,CAAC,CAAC,CACxE,KAAM,CAAAG,YAAY,CAAGnB,CAAC,CAAC,cAAc,CAAE,CAAEgB,aAAa,CAAE,IAAK,CAAC,CAAC,CAC/D,KAAM,CAAAI,aAAa,CAAGpB,CAAC,CAAC,eAAe,CAAE,CAAEgB,aAAa,CAAE,IAAK,CAAC,CAAC,CAEjE;AACA,KAAM,CAAAK,WAAW,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,IAAI,CAAEC,QAAS,CAAC,CAAAH,IAAA,oBAC5C/B,KAAA,CAAC7B,KAAK,EACJgE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAoB,QAAA,eAEFlC,KAAA,CAAC9B,GAAG,EAACuE,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACJ,EAAE,CAAE,CAAE,CAAAJ,QAAA,EAC3CD,IAAI,cACLnC,IAAA,CAAC7B,UAAU,EACT0E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBC,EAAE,CAAE/B,KAAK,CAAG,CAAC,CAAG,CAAE,CAClBgC,EAAE,CAAEhC,KAAK,CAAG,CAAC,CAAG,CAAE,CAClBsB,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEkC,KAAK,CAAErC,KAAK,CAACsC,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAjB,QAAA,CAEDF,KAAK,CACI,CAAC,EACV,CAAC,CACLE,QAAQ,EACJ,CAAC,EACT,CAED,mBACElC,KAAA,CAAChC,SAAS,EAACoF,QAAQ,CAAC,IAAI,CAAChB,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAC,CAAEb,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,eAEvEpC,IAAA,CAAC7B,UAAU,EACT0E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBN,EAAE,CAAE,CAAE,CACNgB,SAAS,CAAC,QAAQ,CAClBlB,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEkC,KAAK,CAAErC,KAAK,CAACsC,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAjB,QAAA,CAEDzB,CAAC,CAAC,kBAAkB,CAAC,CACZ,CAAC,cAGbX,IAAA,CAAC3B,KAAK,EAACgE,SAAS,CAAE,CAAE,CAACC,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cACjClC,KAAA,CAAC5B,IAAI,EACHmF,KAAK,CAAEvC,SAAU,CACjBwC,QAAQ,CAAEpC,eAAgB,CAC1BuB,OAAO,CAAC,YAAY,CACpBc,aAAa,CAAC,MAAM,CACpBrB,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChBW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChE8B,UAAU,CAAE,GAAG,CACfc,SAAS,CAAE,EACb,CACF,CAAE,CAAAxB,QAAA,eAEFpC,IAAA,CAACzB,GAAG,EAACsF,KAAK,CAAEnC,KAAK,CAACQ,KAAM,CAAE,CAAC,cAC3BlC,IAAA,CAACzB,GAAG,EAACsF,KAAK,CAAEjC,aAAa,CAACM,KAAM,CAAE,CAAC,cACnClC,IAAA,CAACzB,GAAG,EAACsF,KAAK,CAAEhC,aAAa,CAACK,KAAM,CAAE,CAAC,cACnClC,IAAA,CAACzB,GAAG,EAACsF,KAAK,CAAE/B,YAAY,CAACI,KAAM,CAAE,CAAC,cAClClC,IAAA,CAACzB,GAAG,EAACsF,KAAK,CAAE9B,aAAa,CAACG,KAAM,CAAE,CAAC,EAC/B,CAAC,CACF,CAAC,cAGRhC,KAAA,CAAC9B,GAAG,EAACkE,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,EAE3ClB,SAAS,GAAK,CAAC,eACdhB,KAAA,CAAC9B,GAAG,EAAAgE,QAAA,eACFpC,IAAA,CAAC7B,UAAU,EACT0E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBN,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEkC,KAAK,CAAErC,KAAK,CAACsC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCG,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDV,KAAK,CAACQ,KAAK,CACF,CAAC,cACblC,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDV,KAAK,CAACoC,WAAW,CACR,CAAC,EAAAxD,eAAA,CACZoB,KAAK,CAACqC,QAAQ,UAAAzD,eAAA,iBAAdA,eAAA,CAAgB0D,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAClClE,IAAA,CAACgC,WAAW,EAAaE,KAAK,CAAE+B,OAAO,CAAC/B,KAAM,CAACC,IAAI,cAAEnC,IAAA,CAAChB,QAAQ,GAAE,CAAE,CAAAoD,QAAA,cAChElC,KAAA,CAAC9B,GAAG,EAACkE,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,EAC3C6B,OAAO,CAACH,WAAW,eAClB9D,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAED6B,OAAO,CAACH,WAAW,CACV,CACb,CACAG,OAAO,CAACE,MAAM,eACbnE,IAAA,CAACxB,IAAI,EAAA4D,QAAA,CACF6B,OAAO,CAACE,MAAM,CAACH,GAAG,CAAC,CAACI,IAAI,CAAEC,GAAG,gBAC5BnE,KAAA,CAACzB,QAAQ,EAAW6D,EAAE,CAAE,CACtBgC,EAAE,CAAEtD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuD,EAAE,CAAEvD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,aAAa,CAAE,KAAK,CACpBhB,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,eACApC,IAAA,CAACtB,YAAY,EAAC4D,EAAE,CAAE,CAChBmC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE1D,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+B,EAAE,CAAE/B,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAoB,QAAA,cACApC,IAAA,CAAChB,QAAQ,EAACkE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACflD,IAAA,CAACrB,YAAY,EACXyE,OAAO,CAAEgB,IAAK,CACd9B,EAAE,CAAE,CAAEW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWqD,GAiBL,CACX,CAAC,CACE,CACP,EACE,CAAC,EArCUH,KAsCL,CACd,CAAC,EACC,CACN,CAGAhD,SAAS,GAAK,CAAC,eACdhB,KAAA,CAAC9B,GAAG,EAAAgE,QAAA,eACFpC,IAAA,CAAC7B,UAAU,EACT0E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBN,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEkC,KAAK,CAAErC,KAAK,CAACsC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCG,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDR,aAAa,CAACM,KAAK,CACV,CAAC,cACblC,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDR,aAAa,CAACkC,WAAW,CAChB,CAAC,EAAAvD,qBAAA,CACZqB,aAAa,CAACmC,QAAQ,UAAAxD,qBAAA,iBAAtBA,qBAAA,CAAwByD,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC1ClE,IAAA,CAACgC,WAAW,EAAaE,KAAK,CAAE+B,OAAO,CAAC/B,KAAM,CAACC,IAAI,cAAEnC,IAAA,CAAChB,QAAQ,GAAE,CAAE,CAAAoD,QAAA,cAChElC,KAAA,CAAC9B,GAAG,EAACkE,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,EAC3C6B,OAAO,CAACH,WAAW,eAClB9D,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAED6B,OAAO,CAACH,WAAW,CACV,CACb,CACAG,OAAO,CAACE,MAAM,eACbnE,IAAA,CAACxB,IAAI,EAAA4D,QAAA,CACF6B,OAAO,CAACE,MAAM,CAACH,GAAG,CAAC,CAACI,IAAI,CAAEC,GAAG,gBAC5BnE,KAAA,CAACzB,QAAQ,EAAW6D,EAAE,CAAE,CACtBgC,EAAE,CAAEtD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuD,EAAE,CAAEvD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,aAAa,CAAE,KAAK,CACpBhB,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,eACApC,IAAA,CAACtB,YAAY,EAAC4D,EAAE,CAAE,CAChBmC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE1D,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+B,EAAE,CAAE/B,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAoB,QAAA,cACApC,IAAA,CAAChB,QAAQ,EAACkE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACflD,IAAA,CAACrB,YAAY,EACXyE,OAAO,CAAEgB,IAAK,CACd9B,EAAE,CAAE,CAAEW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWqD,GAiBL,CACX,CAAC,CACE,CACP,EACE,CAAC,EArCUH,KAsCL,CACd,CAAC,EACC,CACN,CAGAhD,SAAS,GAAK,CAAC,eACdhB,KAAA,CAAC9B,GAAG,EAAAgE,QAAA,eACFpC,IAAA,CAAC7B,UAAU,EACT0E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBN,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEkC,KAAK,CAAErC,KAAK,CAACsC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCG,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDP,aAAa,CAACK,KAAK,CACV,CAAC,cACblC,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDP,aAAa,CAACiC,WAAW,CAChB,CAAC,EAAAtD,qBAAA,CAGZqB,aAAa,CAACkC,QAAQ,UAAAvD,qBAAA,iBAAtBA,qBAAA,CAAwBwD,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC1ClE,IAAA,CAACgC,WAAW,EAAaE,KAAK,CAAE+B,OAAO,CAAC/B,KAAM,CAACC,IAAI,cAAEnC,IAAA,CAACJ,WAAW,GAAE,CAAE,CAAAwC,QAAA,cACnElC,KAAA,CAAC9B,GAAG,EAACkE,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,EAC3C6B,OAAO,CAACH,WAAW,eAClB9D,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAED6B,OAAO,CAACH,WAAW,CACV,CACb,CACAG,OAAO,CAACE,MAAM,eACbnE,IAAA,CAACxB,IAAI,EAAA4D,QAAA,CACF6B,OAAO,CAACE,MAAM,CAACH,GAAG,CAAC,CAACI,IAAI,CAAEC,GAAG,gBAC5BnE,KAAA,CAACzB,QAAQ,EAAW6D,EAAE,CAAE,CACtBgC,EAAE,CAAEtD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuD,EAAE,CAAEvD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,aAAa,CAAE,KAAK,CACpBhB,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,eACApC,IAAA,CAACtB,YAAY,EAAC4D,EAAE,CAAE,CAChBmC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE1D,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+B,EAAE,CAAE/B,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAoB,QAAA,cACApC,IAAA,CAAChB,QAAQ,EAACkE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACflD,IAAA,CAACrB,YAAY,EACXyE,OAAO,CAAEgB,IAAK,CACd9B,EAAE,CAAE,CAAEW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWqD,GAiBL,CACX,CAAC,CACE,CACP,EACE,CAAC,EArCUH,KAsCL,CACd,CAAC,CAGDrC,aAAa,CAAC8C,QAAQ,eACrBzE,KAAA,CAAAE,SAAA,EAAAgC,QAAA,eACEpC,IAAA,CAACnB,OAAO,EAACyD,EAAE,CAAE,CAAEsC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1B1E,KAAA,CAACpB,IAAI,EAAC+F,SAAS,MAACC,OAAO,CAAE,CAAE,CAACtC,EAAE,CAAE,CAAE,CAAAJ,QAAA,eAChCpC,IAAA,CAAClB,IAAI,EAACsF,IAAI,MAACW,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5C,QAAA,cACvBlC,KAAA,CAACtB,KAAK,EAACuD,IAAI,cAAEnC,IAAA,CAACV,YAAY,GAAE,CAAE,CAAC2F,QAAQ,CAAC,SAAS,CAAC3C,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,eACzFpC,IAAA,CAAC7B,UAAU,EAAC2E,UAAU,CAAE,GAAI,CAAAV,QAAA,CAAEP,aAAa,CAAC8C,QAAQ,CAACO,cAAc,CAAa,CAAC,cACjFlF,IAAA,CAAC7B,UAAU,EAAAiE,QAAA,CAAEP,aAAa,CAAC8C,QAAQ,CAACQ,kBAAkB,CAAa,CAAC,EAC/D,CAAC,CACJ,CAAC,cACPnF,IAAA,CAAClB,IAAI,EAACsF,IAAI,MAACW,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5C,QAAA,cACvBlC,KAAA,CAACtB,KAAK,EAACuD,IAAI,cAAEnC,IAAA,CAACR,oBAAoB,GAAE,CAAE,CAACyF,QAAQ,CAAC,MAAM,CAAC3C,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,eAC9FpC,IAAA,CAAC7B,UAAU,EAAC2E,UAAU,CAAE,GAAI,CAAAV,QAAA,CAAEP,aAAa,CAAC8C,QAAQ,CAACS,kBAAkB,CAAa,CAAC,cACrFpF,IAAA,CAAC7B,UAAU,EAAAiE,QAAA,CAAEP,aAAa,CAAC8C,QAAQ,CAACU,sBAAsB,CAAa,CAAC,EACnE,CAAC,CACJ,CAAC,cACPrF,IAAA,CAAClB,IAAI,EAACsF,IAAI,MAACW,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5C,QAAA,cACvBlC,KAAA,CAACtB,KAAK,EAACuD,IAAI,cAAEnC,IAAA,CAACN,WAAW,GAAE,CAAE,CAACuF,QAAQ,CAAC,MAAM,CAAC3C,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,eACrFpC,IAAA,CAAC7B,UAAU,EAAC2E,UAAU,CAAE,GAAI,CAAAV,QAAA,CAAEP,aAAa,CAAC8C,QAAQ,CAACW,eAAe,CAAa,CAAC,cAClFtF,IAAA,CAAC7B,UAAU,EAAAiE,QAAA,CAAEP,aAAa,CAAC8C,QAAQ,CAACY,mBAAmB,CAAa,CAAC,EAChE,CAAC,CACJ,CAAC,cACPvF,IAAA,CAAClB,IAAI,EAACsF,IAAI,MAACW,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA5C,QAAA,cACvBlC,KAAA,CAACtB,KAAK,EAACuD,IAAI,cAAEnC,IAAA,CAACd,eAAe,GAAE,CAAE,CAAC+F,QAAQ,CAAC,SAAS,CAAC3C,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,eAC5FpC,IAAA,CAAC7B,UAAU,EAAC2E,UAAU,CAAE,GAAI,CAAAV,QAAA,CAAEP,aAAa,CAAC8C,QAAQ,CAACa,mBAAmB,CAAa,CAAC,cACtFxF,IAAA,CAAC7B,UAAU,EAAAiE,QAAA,CAAEP,aAAa,CAAC8C,QAAQ,CAACc,uBAAuB,CAAa,CAAC,EACpE,CAAC,CACJ,CAAC,EACH,CAAC,EACP,CACH,CAGA5D,aAAa,CAAC6D,OAAO,eACpBxF,KAAA,CAACtB,KAAK,EAACqG,QAAQ,CAAC,MAAM,CAAC3C,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,eAC9DpC,IAAA,CAAC7B,UAAU,EAAC2E,UAAU,CAAE,GAAI,CAAAV,QAAA,CAAEP,aAAa,CAAC6D,OAAO,CAACxD,KAAK,CAAa,CAAC,cACvElC,IAAA,CAAC7B,UAAU,EAAAiE,QAAA,CAAEP,aAAa,CAAC6D,OAAO,CAACC,KAAK,CAAa,CAAC,EACjD,CACR,EACE,CACN,CAGAzE,SAAS,GAAK,CAAC,eACdhB,KAAA,CAAC9B,GAAG,EAAAgE,QAAA,eACFpC,IAAA,CAAC7B,UAAU,EACT0E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBN,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEkC,KAAK,CAAErC,KAAK,CAACsC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCG,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDN,YAAY,CAACI,KAAK,CACT,CAAC,cACblC,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDN,YAAY,CAACgC,WAAW,CACf,CAAC,cAEb9D,IAAA,CAACnB,OAAO,EAACyD,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BtC,KAAA,CAAC8B,WAAW,EAACE,KAAK,CAAEJ,YAAY,CAAC8D,QAAQ,CAAC1D,KAAM,CAACC,IAAI,cAAEnC,IAAA,CAACd,eAAe,GAAE,CAAE,CAAAkD,QAAA,eACzEpC,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDN,YAAY,CAAC8D,QAAQ,CAAC9B,WAAW,CACxB,CAAC,cACb9D,IAAA,CAACxB,IAAI,EAAA4D,QAAA,CACFN,YAAY,CAAC8D,QAAQ,CAACC,KAAK,CAAC7B,GAAG,CAAC,CAACI,IAAI,CAAEC,GAAG,gBACzCnE,KAAA,CAACzB,QAAQ,EAAW6D,EAAE,CAAE,CACtBgC,EAAE,CAAEtD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuD,EAAE,CAAEvD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,aAAa,CAAE,KAAK,CACpBhB,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,eACApC,IAAA,CAACtB,YAAY,EAAC4D,EAAE,CAAE,CAChBmC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE1D,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+B,EAAE,CAAE/B,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAoB,QAAA,cACApC,IAAA,CAACd,eAAe,EAACgE,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACflD,IAAA,CAACrB,YAAY,EACXyE,OAAO,CAAEgB,IAAK,CACd9B,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CACH,CAAC,GAnBWqD,GAoBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGdnE,KAAA,CAAC8B,WAAW,EAACE,KAAK,CAAEJ,YAAY,CAACgE,QAAQ,CAAC5D,KAAM,CAACC,IAAI,cAAEnC,IAAA,CAACZ,UAAU,GAAE,CAAE,CAAAgD,QAAA,eACpEpC,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDN,YAAY,CAACgE,QAAQ,CAAChC,WAAW,CACxB,CAAC,cACb9D,IAAA,CAACxB,IAAI,EAAA4D,QAAA,CACFN,YAAY,CAACgE,QAAQ,CAACD,KAAK,CAAC7B,GAAG,CAAC,CAACI,IAAI,CAAEC,GAAG,gBACzCnE,KAAA,CAACzB,QAAQ,EAAW6D,EAAE,CAAE,CACtBgC,EAAE,CAAEtD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuD,EAAE,CAAEvD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,aAAa,CAAE,KAAK,CACpBhB,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,eACApC,IAAA,CAACtB,YAAY,EAAC4D,EAAE,CAAE,CAChBmC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE1D,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+B,EAAE,CAAE/B,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAoB,QAAA,cACApC,IAAA,CAACZ,UAAU,EAAC8D,KAAK,CAAC,OAAO,CAAE,CAAC,CAChB,CAAC,cACflD,IAAA,CAACrB,YAAY,EACXyE,OAAO,CAAEgB,IAAK,CACd9B,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CACH,CAAC,GAnBWqD,GAoBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,EAAA5D,qBAAA,CAGbqB,YAAY,CAACiE,kBAAkB,UAAAtF,qBAAA,iBAA/BA,qBAAA,CAAiCuD,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBACnDlE,IAAA,CAACgC,WAAW,EAAaE,KAAK,CAAE+B,OAAO,CAAC/B,KAAM,CAACC,IAAI,cAAEnC,IAAA,CAAChB,QAAQ,GAAE,CAAE,CAAAoD,QAAA,cAChElC,KAAA,CAAC9B,GAAG,EAACkE,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,EAC3C6B,OAAO,CAACH,WAAW,eAClB9D,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAED6B,OAAO,CAACH,WAAW,CACV,CACb,CACAG,OAAO,CAAC4B,KAAK,eACZ7F,IAAA,CAACxB,IAAI,EAAA4D,QAAA,CACF6B,OAAO,CAAC4B,KAAK,CAAC7B,GAAG,CAAC,CAACI,IAAI,CAAEC,GAAG,gBAC3BnE,KAAA,CAACzB,QAAQ,EAAW6D,EAAE,CAAE,CACtBgC,EAAE,CAAEtD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuD,EAAE,CAAEvD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,aAAa,CAAE,KAAK,CACpBhB,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,eACApC,IAAA,CAACtB,YAAY,EAAC4D,EAAE,CAAE,CAChBmC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE1D,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+B,EAAE,CAAE/B,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAoB,QAAA,cACApC,IAAA,CAAChB,QAAQ,EAACkE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACflD,IAAA,CAACrB,YAAY,EACXyE,OAAO,CAAEgB,IAAK,CACd9B,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CACH,CAAC,GAnBWqD,GAoBL,CACX,CAAC,CACE,CACP,EACE,CAAC,EAxCUH,KAyCL,CACd,CAAC,CAGDpC,YAAY,CAAC4D,OAAO,eACnBxF,KAAA,CAACtB,KAAK,EAACqG,QAAQ,CAAC,MAAM,CAAC3C,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,eAC9DpC,IAAA,CAAC7B,UAAU,EAAC2E,UAAU,CAAE,GAAI,CAAAV,QAAA,CAAEN,YAAY,CAAC4D,OAAO,CAACxD,KAAK,CAAa,CAAC,cACtElC,IAAA,CAAC7B,UAAU,EAAAiE,QAAA,CAAEN,YAAY,CAAC4D,OAAO,CAACC,KAAK,CAAa,CAAC,EAChD,CACR,EACE,CACN,CAGAzE,SAAS,GAAK,CAAC,eACdhB,KAAA,CAAC9B,GAAG,EAAAgE,QAAA,eACFpC,IAAA,CAAC7B,UAAU,EACT0E,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBN,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEkC,KAAK,CAAErC,KAAK,CAACsC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCG,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDL,aAAa,CAACG,KAAK,CACV,CAAC,cACblC,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAE,CAAE,CACNF,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAEDL,aAAa,CAAC+B,WAAW,CAChB,CAAC,EAAApD,qBAAA,CAGZqB,aAAa,CAACgC,QAAQ,UAAArD,qBAAA,iBAAtBA,qBAAA,CAAwBsD,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,GAAK,CAC/C,KAAM,CAAEhC,KAAK,CAAE4B,WAAW,CAAEkC,OAAO,CAAG,EAAG,CAAC,CAAG/B,OAAO,CAEpD,mBACEjE,IAAA,CAACgC,WAAW,EAAaE,KAAK,CAAEA,KAAM,CAACC,IAAI,cAAEnC,IAAA,CAAChB,QAAQ,GAAE,CAAE,CAAAoD,QAAA,cACxDlC,KAAA,CAAC9B,GAAG,EAACkE,EAAE,CAAE,CAAEI,SAAS,CAAE1B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAoB,QAAA,EAC3C0B,WAAW,eACV9D,IAAA,CAAC7B,UAAU,EACTqE,EAAE,CAAEwD,OAAO,CAACC,MAAM,CAAG,CAAC,CAAG,CAAC,CAAG,CAAE,CAC/B3D,EAAE,CAAE,CACFW,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,CAED0B,WAAW,CACF,CACb,CACAkC,OAAO,CAACC,MAAM,CAAG,CAAC,eACjBjG,IAAA,CAACxB,IAAI,EAAA4D,QAAA,CACF4D,OAAO,CAAChC,GAAG,CAAC,CAACI,IAAI,CAAEC,GAAG,gBACrBnE,KAAA,CAACzB,QAAQ,EAEP6D,EAAE,CAAE,CACFgC,EAAE,CAAEtD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuD,EAAE,CAAEvD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,aAAa,CAAE,KAAK,CACpBhB,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAoB,QAAA,eAEFpC,IAAA,CAACtB,YAAY,EACX4D,EAAE,CAAE,CACFmC,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAE1D,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB+B,EAAE,CAAE/B,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAoB,QAAA,cAEFpC,IAAA,CAAChB,QAAQ,EAACkE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACflD,IAAA,CAACrB,YAAY,EACXyE,OAAO,CAAEgB,IAAK,CACd9B,EAAE,CAAE,CACFkB,SAAS,CAAExC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCiC,UAAU,CAAEjC,KAAK,CAAG,qBAAqB,CAAG,SAC9C,CAAE,CACH,CAAC,GAvBGqD,GAwBG,CACX,CAAC,CACE,CACP,EACE,CAAC,EA7CUH,KA8CL,CAAC,CAElB,CAAC,CAAC,EACC,CACN,EACE,CAAC,EACG,CAAC,CAEhB,CAAC,CAED,cAAe,CAAA7D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}