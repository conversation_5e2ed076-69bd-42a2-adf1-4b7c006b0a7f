import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import ResizeObserverFix from './utils/ResizeObserverFix';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, CircularProgress } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { SocketProvider } from './contexts/SocketContext';
import { UnreadMessagesProvider } from './contexts/UnreadMessagesContext';
import { Toaster } from 'react-hot-toast';
import { setupResizeObserverPolyfill } from './utils/resizeObserver';
import Header from './components/layout/Header';
import Footer from './components/Footer';
import AuthenticatedFooter from './components/AuthenticatedFooter';
import './i18n/i18n';

// Import pages here
import Home from './pages/Home';
import AboutUs from './pages/AboutUs';
import PlatformPolicy from './pages/PlatformPolicy';
import PendingDeletion from './pages/PendingDeletion';
import TermsAndConditions from './pages/TermsAndConditions';
import PrivacyPolicy from './pages/PrivacyPolicy';
import RefundPolicy from './pages/RefundPolicy';
import BookingPaymentPolicy from './pages/BookingPaymentPolicy';
import BookingCancellationPolicy from './pages/BookingCancellationPolicy';
import Login from './pages/auth/Login';
import RegisterChoice from './pages/auth/RegisterChoice';
import StudentRegister from './pages/auth/StudentRegister';
import TeacherRegister from './pages/auth/TeacherRegister';
import ForgotPassword from './pages/auth/ForgotPassword';
import VerifyResetCode from './pages/auth/VerifyResetCode';
import ResetPassword from './pages/auth/ResetPassword';
import VerifyEmail from './pages/auth/VerifyEmail';
import AdminDashboard from './pages/admin/Dashboard';
import AdminProfile from './pages/admin/Profile';
import TeacherApplications from './pages/admin/TeacherApplications';
import Teachers from './pages/admin/Teachers';
import Students from './pages/admin/Students';
import Categories from './pages/admin/Categories';
import Languages from './pages/admin/Languages';
import ProfileUpdates from './pages/admin/ProfileUpdates';
import MeetingSessions from './pages/admin/MeetingSessions';
import TeacherDashboard from './pages/teacher/Dashboard';
import TeacherApplication from './pages/teacher/Application';
import EditApplication from './pages/teacher/EditApplication';
import EditVideoUpload from './pages/teacher/EditVideoUpload';
import TeacherVideoUpload from './pages/teacher/VideoUpload';
import TeacherApplicationAvailableHours from './pages/teacher/AvailableHours';
import TeacherProfileAvailableHours from './pages/teacher/TeacherAvailableHours';
import TeacherViewAvailableHours from './pages/teacher/ViewAvailableHours';
import TeacherProfile from './pages/teacher/Profile';
import TeacherChat from './pages/teacher/Chat';
import TeacherMeetings from './pages/teacher/Meetings';
import TeacherBookings from './pages/teacher/Bookings';
import MyLessons from './pages/teacher/MyLessons';
import StudentDashboard from './pages/student/Dashboard';
import StudentProfile from './pages/student/Profile';
import CompleteProfile from './pages/student/CompleteProfile';
import StudentChat from './pages/student/Chat';
import StudentFindTeacher from './pages/student/FindTeacher';
import StudentTeacherProfile from './pages/student/TeacherProfile';
import StudentChatEmbed from './pages/student/ChatEmbed';
import FindTeacher from './pages/FindTeacher';
import TeacherDetails from './pages/TeacherDetails';
import JoinMeeting from './pages/student/JoinMeeting';
import StudentMeetings from './pages/student/Meetings';
import AdminWallet from './pages/admin/Wallet';
import TeacherWallet from './pages/teacher/Wallet';
import StudentWallet from './pages/student/Wallet';
import StudentContactUs from './pages/student/ContactUs';
import TeacherContactUs from './pages/teacher/ContactUs';
import AdminMessages from './pages/admin/Messages';
import StudentMyMessages from './pages/student/MyMessages';
import TeacherMyMessages from './pages/teacher/MyMessages';
import BookingPage from './pages/student/BookingPage';
import Bookings from './pages/student/Bookings';
import MyTeachers from './pages/student/MyTeachers';
import WriteReview from './pages/student/WriteReview';
import TeacherReviews from './pages/teacher/Reviews';
import TeacherWithdrawal from './pages/teacher/Withdrawal';
import AdminWithdrawalManagement from './pages/admin/WithdrawalManagement';
import AdminEarnings from './pages/admin/AdminEarnings';
import AdminMeetingIssues from './pages/admin/MeetingIssues';
import ContactUs from './pages/ContactUs';

// Define roles
const ROLES = {
  ADMIN: 'admin',
  PLATFORM_TEACHER: 'platform_teacher',
  NEW_TEACHER: 'new_teacher',
  STUDENT: 'student'
};

// Protected Route Component
const ProtectedRoute = ({ children, allowedRoles }) => {
  const { currentUser, isAuthenticated, loading } = useAuth();
  const location = useLocation();

  // If still loading, show loading spinner
  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  // If not authenticated or no user, redirect to login
  if (!isAuthenticated || !currentUser) {
    return <Navigate to="/login" replace state={{ from: location }} />;
  }

  // If user account is pending deletion, redirect to pending deletion page
  // unless they're already on that page
  if (currentUser.status === 'pending_deletion' && location.pathname !== '/pending-deletion') {
    return <Navigate to="/pending-deletion" replace />;
  }

  // Check if user has required role
  if (!allowedRoles.includes(currentUser.role)) {
    // Special case: if a new teacher tries to access platform teacher routes
    if (currentUser.role === ROLES.NEW_TEACHER && location.pathname === '/teacher/dashboard') {
      return <Navigate to="/teacher/application" replace />;
    }

    // Special case: if a platform teacher tries to access new teacher routes
    if (currentUser.role === ROLES.PLATFORM_TEACHER && location.pathname === '/teacher/application') {
      return <Navigate to="/teacher/dashboard" replace />;
    }

    // Default redirects based on role
    switch (currentUser.role) {
      case ROLES.ADMIN:
        return <Navigate to="/admin/dashboard" replace />;
      case ROLES.PLATFORM_TEACHER:
        return <Navigate to="/teacher/dashboard" replace />;
      case ROLES.NEW_TEACHER:
        return <Navigate to="/teacher/application" replace />;
      case ROLES.STUDENT:
        return <Navigate to="/student/dashboard" replace />;
      default:
        return <Navigate to="/login" replace />;
    }
  }

  // All checks passed, render the protected component
  return children;
};

// Public Route Component
const PublicRoute = ({ children }) => {
  const { isAuthenticated, currentUser } = useAuth();

  // If authenticated, redirect to appropriate dashboard
  if (isAuthenticated && currentUser) {
    // If user account is pending deletion, redirect to pending deletion page
    if (currentUser.status === 'pending_deletion') {
      return <Navigate to="/pending-deletion" replace />;
    }

    switch (currentUser.role) {
      case ROLES.ADMIN:
        return <Navigate to="/admin/dashboard" replace />;
      case ROLES.PLATFORM_TEACHER:
        return <Navigate to="/teacher/dashboard" replace />;
      case ROLES.NEW_TEACHER:
        return <Navigate to="/teacher/application" replace />;
      case ROLES.STUDENT:
        return <Navigate to="/student/dashboard" replace />;
      default:
        return <Navigate to="/" replace />;
    }
  }

  return children;
};

// Component to determine which Footer should be shown
const ConditionalFooter = () => {
  const location = useLocation();
  const { isAuthenticated, currentUser } = useAuth();

  // Check if user is on protected routes (when user is logged in)
  const isProtectedRoute = isAuthenticated && currentUser && (
    location.pathname.startsWith('/admin/') ||
    location.pathname.startsWith('/teacher/') ||
    location.pathname.startsWith('/student/') ||
    location.pathname.startsWith('/join-meeting/') ||
    location.pathname === '/platform-policy'
  );

  // Show authenticated footer on protected routes
  if (isProtectedRoute) {
    return <AuthenticatedFooter />;
  }

  // Show public footer on public routes
  return <Footer />;
};

function App() {
  const { i18n } = useTranslation();
  const direction = i18n.language === 'ar' ? 'rtl' : 'ltr';

  useEffect(() => {
    setupResizeObserverPolyfill();
  }, []);

  const theme = createTheme({
    direction,
    palette: {
      primary: {
        main: '#0C4B33', // Deep Islamic green
      },
      secondary: {
        main: '#C3A343', // Islamic gold
      },
    },
    typography: {
      fontFamily: direction === 'rtl' ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',
    },
  });

  return (
    <Router>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <ResizeObserverFix />
        <AuthProvider>
          <SocketProvider>
            <UnreadMessagesProvider>
              <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
              <Header />
              <Box component="main" sx={{ flexGrow: 1, py: 3 }}>
                <Routes>
                  {/* Public Routes */}
                  <Route path="/" element={<PublicRoute><Home /></PublicRoute>} />
                  <Route path="/login" element={<PublicRoute><Login /></PublicRoute>} />
                  <Route path="/register" element={<PublicRoute><RegisterChoice /></PublicRoute>} />
                  <Route path="/register/student" element={<PublicRoute><StudentRegister /></PublicRoute>} />
                  <Route path="/register/teacher" element={<PublicRoute><TeacherRegister /></PublicRoute>} />
                  <Route path="/verify-email" element={<PublicRoute><VerifyEmail /></PublicRoute>} />
                  <Route path="/forgot-password" element={<PublicRoute><ForgotPassword /></PublicRoute>} />
                  <Route path="/verify-reset-code" element={<PublicRoute><VerifyResetCode /></PublicRoute>} />
                  <Route path="/reset-password" element={<PublicRoute><ResetPassword /></PublicRoute>} />
                  <Route path="/about-us" element={<PublicRoute><AboutUs /></PublicRoute>} />
                  {/* Pending Deletion Page - accessible to users with pending_deletion status */}
                  <Route path="/pending-deletion" element={<PendingDeletion />} />
                  {/* Policy Pages */}
                  <Route path="/terms-and-conditions" element={<PublicRoute><TermsAndConditions /></PublicRoute>} />
                  <Route path="/privacy-policy" element={<PublicRoute><PrivacyPolicy /></PublicRoute>} />
                  <Route path="/booking-cancellation-policy" element={<PublicRoute><BookingCancellationPolicy /></PublicRoute>} />
                  <Route path="/booking-payment-policy" element={<PublicRoute><BookingPaymentPolicy /></PublicRoute>} />
                  <Route path="/refund-policy" element={<PublicRoute><RefundPolicy /></PublicRoute>} />
                  <Route path="/find-teacher" element={<PublicRoute><FindTeacher /></PublicRoute>} />
                  <Route path="/teacher/:id" element={<PublicRoute><TeacherDetails /></PublicRoute>} />

                  {/* Admin Routes */}
                  <Route path="/admin/dashboard" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <AdminDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/profile" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <AdminProfile />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/teachers" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <Teachers />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/students" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <Students />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/applications" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <TeacherApplications />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/categories" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <Categories />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/languages" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <Languages />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/wallet" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <AdminWallet />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/messages" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <AdminMessages />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/profile-updates" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <ProfileUpdates />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/meeting-sessions" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <MeetingSessions />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/withdrawals" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <AdminWithdrawalManagement />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/meeting-issues" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <AdminMeetingIssues />
                    </ProtectedRoute>
                  } />
                  <Route path="/admin/earnings" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>
                      <AdminEarnings />
                    </ProtectedRoute>
                  } />

                  {/* Teacher Routes */}
                  <Route path="/teacher/dashboard" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <TeacherDashboard />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/meetings" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <TeacherMeetings />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/bookings" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <TeacherBookings />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/application" element={
                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER]}>
                      <TeacherApplication />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/edit-application" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <EditApplication />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/edit-video" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <EditVideoUpload />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/upload-video" element={
                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER, ROLES.PLATFORM_TEACHER]}>
                      <TeacherVideoUpload />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/available-hours" element={
                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER]}>
                      <TeacherApplicationAvailableHours />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/manage-hours" element={
                    <ProtectedRoute allowedRoles={[ROLES.TEACHER, ROLES.PLATFORM_TEACHER]}>
                      <TeacherProfileAvailableHours />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/view-hours" element={
                    <ProtectedRoute allowedRoles={[ROLES.TEACHER, ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>
                      <TeacherViewAvailableHours />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/profile" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>
                      <TeacherProfile />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/chat" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <TeacherChat />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/wallet" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>
                      <TeacherWallet />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/withdrawal" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <TeacherWithdrawal />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/contact-us" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>
                      <TeacherContactUs />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/my-messages" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>
                      <TeacherMyMessages />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/reviews" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <TeacherReviews />
                    </ProtectedRoute>
                  } />
                  <Route path="/teacher/my-lessons" element={
                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>
                      <MyLessons />
                    </ProtectedRoute>
                  } />

                  {/* Student Routes */}
                  <Route
                    path="/student/dashboard"
                    element={
                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                        <StudentDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/student/meetings"
                    element={
                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                        <StudentMeetings />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/student/profile"
                    element={
                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                        <StudentProfile />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/student/complete-profile" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <CompleteProfile />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/chat" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <StudentChat />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/find-teacher" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <StudentFindTeacher />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/teacher/:id" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <StudentTeacherProfile />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/book/:id" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <BookingPage />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/my-teachers" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <MyTeachers />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/bookings" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <Bookings />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/chat-embed" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <StudentChatEmbed />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/wallet" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <StudentWallet />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/contact-us" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <StudentContactUs />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/my-messages" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <StudentMyMessages />
                    </ProtectedRoute>
                  } />
                  <Route path="/student/write-review" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>
                      <WriteReview />
                    </ProtectedRoute>
                  } />
                  <Route path="/join-meeting/:roomName" element={
                    <ProtectedRoute allowedRoles={[ROLES.STUDENT, ROLES.PLATFORM_TEACHER]}>
                      <JoinMeeting />
                    </ProtectedRoute>
                  } />

                  {/* Common Routes */}
                  <Route path="/find-teacher" element={<FindTeacher />} />
                  <Route path="/teacher/:id" element={<TeacherDetails />} />
                  <Route path="/about-us" element={<AboutUs />} />
                  <Route path="/contact-us" element={<ContactUs />} />
                  {/* Pending Deletion Page - accessible to users with pending_deletion status */}
                  <Route path="/pending-deletion" element={<PendingDeletion />} />
                  {/* Unified Platform Policy - accessible to all authenticated users */}
                  <Route path="/platform-policy" element={
                    <ProtectedRoute allowedRoles={[ROLES.ADMIN, ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER, ROLES.STUDENT]}>
                      <PlatformPolicy />
                    </ProtectedRoute>
                  } />

                  {/* Policy Pages - accessible to authenticated users */}
                  <Route path="/terms-and-conditions" element={<TermsAndConditions />} />
                  <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                  <Route path="/booking-cancellation-policy" element={<BookingCancellationPolicy />} />
                  <Route path="/booking-payment-policy" element={<BookingPaymentPolicy />} />
                  <Route path="/refund-policy" element={<RefundPolicy />} />

                  {/* Catch all route */}
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </Box>
              <ConditionalFooter />
              </Box>
            </UnreadMessagesProvider>
          </SocketProvider>
        </AuthProvider>
      </ThemeProvider>
      <Toaster
        position="top-center"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#333',
            color: '#fff',
          },
        }}
      />
    </Router>
  );
}

export default App;
