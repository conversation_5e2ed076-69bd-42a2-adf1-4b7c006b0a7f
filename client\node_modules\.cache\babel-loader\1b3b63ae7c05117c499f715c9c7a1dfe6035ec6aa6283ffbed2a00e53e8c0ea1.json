{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useLocation,useNavigate}from'react-router-dom';import{Container,Typography,Paper,Box,Divider,useTheme,Fade,List,ListItem,ListItemText,ListItemIcon,Tabs,Tab,Card,CardContent,alpha,Stack,Alert,Grid}from'@mui/material';import{Security as SecurityIcon,Schedule as ScheduleIcon,Person as PersonIcon,School as SchoolIcon,Info as InfoIcon,Payment as PaymentIcon,Refresh as RefreshIcon,Description as DescriptionIcon,Email as EmailIcon,CheckCircle as CheckCircleIcon,Cancel as CancelIcon,CurrencyExchange as CurrencyExchangeIcon,Receipt as ReceiptIcon}from'@mui/icons-material';import Layout from'../components/Layout';import termsConditions from'../i18n/translations/termsConditions';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const PlatformPolicy=()=>{const{t,i18n}=useTranslation();const theme=useTheme();const location=useLocation();const navigate=useNavigate();const[isRTL,setIsRTL]=useState(i18n.language==='ar');const[activeTab,setActiveTab]=useState(0);useEffect(()=>{setIsRTL(i18n.language==='ar');document.dir=i18n.language==='ar'?'rtl':'ltr';document.documentElement.dir=i18n.language==='ar'?'rtl':'ltr';document.body.dir=i18n.language==='ar'?'rtl':'ltr';// Handle URL-based navigation to specific sections\nconst hash=location.hash;if(hash==='#terms')setActiveTab(0);else if(hash==='#privacy')setActiveTab(1);else if(hash==='#booking')setActiveTab(2);else if(hash==='#payment')setActiveTab(3);else if(hash==='#refund')setActiveTab(4);},[i18n.language,location.hash]);const handleTabChange=(event,newValue)=>{setActiveTab(newValue);const tabs=['#terms','#privacy','#booking','#payment','#refund'];navigate(`/platform-policy${tabs[newValue]}`,{replace:true});};const tabsConfig=[{label:i18n.language==='ar'?'الشروط والأحكام':'Terms & Conditions',icon:/*#__PURE__*/_jsx(DescriptionIcon,{})},{label:i18n.language==='ar'?'سياسة الخصوصية':'Privacy Policy',icon:/*#__PURE__*/_jsx(SecurityIcon,{})},{label:i18n.language==='ar'?'سياسة الحجز والإلغاء':'Booking & Cancellation',icon:/*#__PURE__*/_jsx(ScheduleIcon,{})},{label:i18n.language==='ar'?'سياسة الدفع':'Payment Policy',icon:/*#__PURE__*/_jsx(PaymentIcon,{})},{label:i18n.language==='ar'?'سياسة الاسترداد':'Refund Policy',icon:/*#__PURE__*/_jsx(RefreshIcon,{})}];// Helper component for section cards\nconst SectionCard=_ref=>{let{title,children,icon,bg}=_ref;return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{mb:4,borderRadius:3,direction:isRTL?'rtl':'ltr',...(isRTL?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:bg||alpha(theme.palette.primary.main,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2,flexDirection:isRTL?'row-reverse':'row',direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[icon&&/*#__PURE__*/_jsx(Box,{sx:{mr:isRTL?0:2,ml:isRTL?2:0,color:theme.palette.primary.main},children:icon}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:title})]}),/*#__PURE__*/_jsx(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:children})]})});};// Helper function to render subsections\nconst renderSubSection=subSection=>{if(!subSection||typeof subSection!=='object')return null;return/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:theme.palette.primary.main,mb:1,fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:subSection.title}),subSection.points&&/*#__PURE__*/_jsx(List,{children:subSection.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]});};// Tab panel component\nconst TabPanel=_ref2=>{let{children,value,index}=_ref2;return/*#__PURE__*/_jsx(\"div\",{hidden:value!==index,children:value===index&&/*#__PURE__*/_jsx(Box,{sx:{py:3,direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:children})});};// Render Terms and Conditions\nconst renderTermsAndConditions=()=>{const termsContent=i18n.language==='ar'?termsConditions.ar.termsConditions:termsConditions.en.termsConditions;if(!termsContent||typeof termsContent!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:3,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:termsContent.title||(isRTL?'الشروط والأحكام':'Terms and Conditions')}),/*#__PURE__*/_jsx(Stack,{spacing:3,children:termsContent.content&&termsContent.content.split(/\\n\\s*\\n/).filter(section=>section.trim()!=='').map((section,idx)=>{const lines=section.split('\\n');const title=lines[0];const body=lines.slice(1).join('\\n');return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{borderRadius:3,direction:isRTL?'rtl':'ltr',...(isRTL?{borderRight:`4px solid ${theme.palette.primary.main}`}:{borderLeft:`4px solid ${theme.palette.primary.main}`}),backgroundColor:idx%2===0?alpha(theme.palette.primary.main,0.02):alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:title}),body&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',whiteSpace:'pre-line',textAlign:isRTL?'right':'left'},children:body})]})},idx);})})]});};// Render Privacy Policy\nconst renderPrivacyPolicy=()=>{const privacy=t('privacy',{returnObjects:true});if(!privacy||typeof privacy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}/**\r\n     * Safely fetch a translation; returns empty string if key is missing.\r\n     */const safeT=key=>{const val=t(key);return val&&!val.includes(key)?val:'';};const renderStandardSection=num=>{const subtitle=safeT(`privacy.section${num}.subtitle`);const content=safeT(`privacy.section${num}.content`);const description=safeT(`privacy.section${num}.description`);const contact=safeT(`privacy.section${num}.contact`);const email=safeT(`privacy.section${num}.email`);const bullets=[1,2,3,4,5].map(i=>safeT(`privacy.section${num}.item${i}`)).filter(item=>item);return/*#__PURE__*/_jsxs(SectionCard,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t(`privacy.section${num}.title`)}),subtitle&&/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:subtitle}),content&&/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:content}),description&&/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:description}),bullets.length>0&&/*#__PURE__*/_jsx(List,{children:bullets.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{textAlign:isRTL?'right':'left',fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))}),contact&&/*#__PURE__*/_jsx(Typography,{mb:1,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:contact}),email&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left',color:theme.palette.primary.main,fontWeight:600},children:email})]},num);};return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif'},children:t('privacy.title')}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",mb:3,children:t('privacy.intro')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),[1,2,3,4,5,6,7,8,9].map(num=>renderStandardSection(num))]});};// Render Booking & Cancellation Policy\nconst renderBookingPolicy=()=>{const policy=t('policies.bookingCancellation',{returnObjects:true});if(!policy||typeof policy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",sx:{mb:3,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsx(SectionCard,{title:policy.studentPolicy.title,icon:/*#__PURE__*/_jsx(PersonIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[renderSubSection(policy.studentPolicy.booking),renderSubSection(policy.studentPolicy.cancellation),renderSubSection(policy.studentPolicy.rescheduling),renderSubSection(policy.studentPolicy.lateArrival)]})}),/*#__PURE__*/_jsx(SectionCard,{title:policy.tutorPolicy.title,icon:/*#__PURE__*/_jsx(SchoolIcon,{}),bg:alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[renderSubSection(policy.tutorPolicy.availability),renderSubSection(policy.tutorPolicy.cancellation),renderSubSection(policy.tutorPolicy.rescheduling),renderSubSection(policy.tutorPolicy.lateArrival)]})}),/*#__PURE__*/_jsx(SectionCard,{title:policy.generalNotes.title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:policy.generalNotes.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5,textAlign:isRTL?'right':'left',pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"info\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})})]});};// Render Payment Policy\nconst renderPaymentPolicy=()=>{const policy=t('policies.bookingPayment',{returnObjects:true});if(!policy||typeof policy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}const renderSection=section=>/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(PaymentIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))}),section.description&&/*#__PURE__*/_jsx(Typography,{sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.description})]})});return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",sx:{mb:3,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),renderSection(policy.section1),renderSection(policy.section2),renderSection(policy.section3),renderSection(policy.section4),renderSection(policy.section5),renderSection(policy.section6),renderSection(policy.section7),renderSection(policy.section8),policy.features&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Divider,{sx:{my:3}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,mb:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(SecurityIcon,{}),severity:\"success\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.features.securePayments}),/*#__PURE__*/_jsx(Typography,{children:policy.features.securePaymentsDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(CurrencyExchangeIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.features.multipleCurrencies}),/*#__PURE__*/_jsx(Typography,{children:policy.features.multipleCurrenciesDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(ReceiptIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.features.instantReceipts}),/*#__PURE__*/_jsx(Typography,{children:policy.features.instantReceiptsDesc})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Alert,{icon:/*#__PURE__*/_jsx(CheckCircleIcon,{}),severity:\"success\",sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{fontWeight:600,children:policy.features.instantConfirmation}),/*#__PURE__*/_jsx(Typography,{children:policy.features.instantConfirmationDesc})]})})]})]}),policy.section8&&policy.section8.email&&/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:1,children:policy.section8.title||policy.section8.description}),/*#__PURE__*/_jsx(Typography,{mb:1,children:policy.section8.description}),/*#__PURE__*/_jsx(Alert,{icon:/*#__PURE__*/_jsx(EmailIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr'},children:policy.section8.email})]})]});};// Render Refund Policy\nconst renderRefundPolicy=()=>{const refund=t('policies.refund',{returnObjects:true});if(!refund||typeof refund!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:refund.title}),/*#__PURE__*/_jsx(Divider,{sx:{mb:3}}),/*#__PURE__*/_jsxs(SectionCard,{title:refund.section1.title,icon:/*#__PURE__*/_jsx(CheckCircleIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refund.section1.description}),/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:refund.section1.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'}})]},idx))})]}),/*#__PURE__*/_jsxs(SectionCard,{title:refund.section2.title,icon:/*#__PURE__*/_jsx(CancelIcon,{}),children:[/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refund.section2.description}),/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:refund.section2.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(CancelIcon,{color:\"error\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'}})]},idx))})]}),/*#__PURE__*/_jsx(SectionCard,{title:refund.section3.title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsx(Typography,{sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refund.section3.description})}),[4,5,6,7,8].map(num=>{const section=refund[`section${num}`];if(!section)return null;return/*#__PURE__*/_jsx(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(RefreshIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.description&&/*#__PURE__*/_jsx(Typography,{mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:section.description}),section.items&&/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:section.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,flexDirection:'row',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mx:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"success\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'}})]},idx))})]})},num);}),/*#__PURE__*/_jsx(Divider,{sx:{my:3}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',textAlign:isRTL?'right':'left'},children:refund.contact.title}),/*#__PURE__*/_jsx(Alert,{icon:/*#__PURE__*/_jsx(EmailIcon,{}),severity:\"info\",sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:refund.contact.email})]})]});};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Box,{sx:{py:4,minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",sx:{direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:1000,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:2,md:4},mb:4,borderRadius:3,direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left','& *':{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},'& .MuiTypography-root':{textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr'},'& .MuiList-root':{direction:isRTL?'rtl':'ltr'},'& .MuiListItem-root':{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:4,textAlign:'center',direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",component:\"h1\",sx:{mb:2,fontWeight:800,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,direction:isRTL?'rtl':'ltr',textAlign:'center'},children:isRTL?'سياسات المنصة':'Platform Policies'}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',direction:isRTL?'rtl':'ltr',textAlign:'center'},children:isRTL?'جميع السياسات والشروط الخاصة بمنصة علّمني أون لاين في مكان واحد':'All Allemnionline platform policies and terms in one place'})]}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider',mb:3,direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{'& .MuiTab-root':{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',minHeight:64,textAlign:isRTL?'right':'left'},'& .MuiTabs-flexContainer':{direction:isRTL?'rtl':'ltr'}},children:tabsConfig.map((tab,index)=>/*#__PURE__*/_jsx(Tab,{icon:tab.icon,label:tab.label,iconPosition:\"start\",sx:{flexDirection:isRTL?'row-reverse':'row',textAlign:isRTL?'right':'left','& .MuiTab-iconWrapper':{mr:isRTL?0:1,ml:isRTL?1:0}}},index))})}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:0,children:renderTermsAndConditions()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:1,children:renderPrivacyPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:2,children:renderBookingPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:3,children:renderPaymentPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:4,children:renderRefundPolicy()})]})})})})});};export default PlatformPolicy;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useLocation", "useNavigate", "Container", "Typography", "Paper", "Box", "Divider", "useTheme", "Fade", "List", "ListItem", "ListItemText", "ListItemIcon", "Tabs", "Tab", "Card", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "<PERSON><PERSON>", "<PERSON><PERSON>", "Grid", "Security", "SecurityIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Refresh", "RefreshIcon", "Description", "DescriptionIcon", "Email", "EmailIcon", "CheckCircle", "CheckCircleIcon", "Cancel", "CancelIcon", "CurrencyExchange", "CurrencyExchangeIcon", "Receipt", "ReceiptIcon", "Layout", "termsConditions", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "PlatformPolicy", "t", "i18n", "theme", "location", "navigate", "isRTL", "setIsRTL", "language", "activeTab", "setActiveTab", "document", "dir", "documentElement", "body", "hash", "handleTabChange", "event", "newValue", "tabs", "replace", "tabsConfig", "label", "icon", "SectionCard", "_ref", "title", "children", "bg", "elevation", "sx", "mb", "borderRadius", "direction", "borderRight", "palette", "primary", "main", "borderLeft", "backgroundColor", "display", "alignItems", "flexDirection", "textAlign", "mr", "ml", "color", "variant", "fontWeight", "fontFamily", "renderSubSection", "subSection", "points", "map", "item", "idx", "pl", "pr", "min<PERSON><PERSON><PERSON>", "mx", "TabPanel", "_ref2", "value", "index", "hidden", "py", "renderTermsAndConditions", "termsContent", "ar", "en", "spacing", "content", "split", "filter", "section", "trim", "lines", "slice", "join", "secondary", "light", "lineHeight", "whiteSpace", "renderPrivacyPolicy", "privacy", "returnObjects", "safeT", "key", "val", "includes", "renderStandardSection", "num", "subtitle", "description", "contact", "email", "bullets", "i", "length", "renderBookingPolicy", "policy", "studentPolicy", "booking", "cancellation", "rescheduling", "lateArrival", "tutorPolicy", "availability", "generalNotes", "renderPaymentPolicy", "renderSection", "section1", "section2", "section3", "section4", "section5", "section6", "section7", "section8", "features", "my", "container", "xs", "md", "severity", "securePayments", "securePaymentsDesc", "multipleCurrencies", "multipleCurrenciesDesc", "instantReceipts", "instantReceiptsDesc", "instantConfirmation", "instantConfirmationDesc", "renderRefundPolicy", "refund", "items", "minHeight", "background", "max<PERSON><PERSON><PERSON>", "in", "timeout", "p", "component", "borderBottom", "borderColor", "onChange", "scrollButtons", "tab", "iconPosition"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PlatformPolicy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Paper,\r\n  Box,\r\n  Divider,\r\n  useTheme,\r\n  Fade,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Tabs,\r\n  Tab,\r\n  Card,\r\n  CardContent,\r\n  alpha,\r\n  Stack,\r\n  Alert,\r\n  Grid,\r\n} from '@mui/material';\r\nimport {\r\n  Security as SecurityIcon,\r\n  Schedule as ScheduleIcon,\r\n  Person as PersonIcon,\r\n  School as SchoolIcon,\r\n  Info as InfoIcon,\r\n  Payment as PaymentIcon,\r\n  Refresh as RefreshIcon,\r\n  Description as DescriptionIcon,\r\n  Email as EmailIcon,\r\n  CheckCircle as CheckCircleIcon,\r\n  Cancel as CancelIcon,\r\n  CurrencyExchange as CurrencyExchangeIcon,\r\n  Receipt as ReceiptIcon,\r\n} from '@mui/icons-material';\r\nimport Layout from '../components/Layout';\r\nimport termsConditions from '../i18n/translations/termsConditions';\r\n\r\nconst PlatformPolicy = () => {\r\n  const { t, i18n } = useTranslation();\r\n  const theme = useTheme();\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const [isRTL, setIsRTL] = useState(i18n.language === 'ar');\r\n  const [activeTab, setActiveTab] = useState(0);\r\n\r\n  useEffect(() => {\r\n    setIsRTL(i18n.language === 'ar');\r\n    document.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n    document.body.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n\r\n    // Handle URL-based navigation to specific sections\r\n    const hash = location.hash;\r\n    if (hash === '#terms') setActiveTab(0);\r\n    else if (hash === '#privacy') setActiveTab(1);\r\n    else if (hash === '#booking') setActiveTab(2);\r\n    else if (hash === '#payment') setActiveTab(3);\r\n    else if (hash === '#refund') setActiveTab(4);\r\n  }, [i18n.language, location.hash]);\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    const tabs = ['#terms', '#privacy', '#booking', '#payment', '#refund'];\r\n    navigate(`/platform-policy${tabs[newValue]}`, { replace: true });\r\n  };\r\n\r\n  const tabsConfig = [\r\n    { label: i18n.language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions', icon: <DescriptionIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy', icon: <SecurityIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation', icon: <ScheduleIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الدفع' : 'Payment Policy', icon: <PaymentIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الاسترداد' : 'Refund Policy', icon: <RefreshIcon /> },\r\n  ];\r\n\r\n  // Helper component for section cards\r\n  const SectionCard = ({ title, children, icon, bg }) => (\r\n    <Card\r\n      elevation={2}\r\n      sx={{\r\n        mb: 4,\r\n        borderRadius: 3,\r\n        direction: isRTL ? 'rtl' : 'ltr',\r\n        ...(isRTL\r\n          ? { borderRight: `6px solid ${theme.palette.primary.main}` }\r\n          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\r\n        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),\r\n      }}\r\n    >\r\n      <CardContent sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n        <Box sx={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          mb: 2,\r\n          flexDirection: isRTL ? 'row-reverse' : 'row',\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n          textAlign: isRTL ? 'right' : 'left'\r\n        }}>\r\n          {icon && (\r\n            <Box sx={{ mr: isRTL ? 0 : 2, ml: isRTL ? 2 : 0, color: theme.palette.primary.main }}>\r\n              {icon}\r\n            </Box>\r\n          )}\r\n          <Typography\r\n            variant=\"h5\"\r\n            fontWeight={600}\r\n            color={theme.palette.primary.main}\r\n            sx={{\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n              direction: isRTL ? 'rtl' : 'ltr',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {title}\r\n          </Typography>\r\n        </Box>\r\n        <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n          {children}\r\n        </Box>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n\r\n  // Helper function to render subsections\r\n  const renderSubSection = (subSection) => {\r\n    if (!subSection || typeof subSection !== 'object') return null;\r\n\r\n    return (\r\n      <Box sx={{ mb: 3 }}>\r\n        <Typography\r\n          variant=\"h6\"\r\n          sx={{\r\n            fontWeight: 600,\r\n            color: theme.palette.primary.main,\r\n            mb: 1,\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {subSection.title}\r\n        </Typography>\r\n        {subSection.points && (\r\n          <List>\r\n            {subSection.points.map((item, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                pl: isRTL ? 0 : 2,\r\n                pr: isRTL ? 2 : 0,\r\n                flexDirection: 'row',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mx: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                }}>\r\n                  <InfoIcon color=\"success\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        )}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Tab panel component\r\n  const TabPanel = ({ children, value, index }) => (\r\n    <div hidden={value !== index}>\r\n      {value === index && (\r\n        <Box sx={{\r\n          py: 3,\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n          textAlign: isRTL ? 'right' : 'left'\r\n        }}>\r\n          {children}\r\n        </Box>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  // Render Terms and Conditions\r\n  const renderTermsAndConditions = () => {\r\n    const termsContent = i18n.language === 'ar' ? termsConditions.ar.termsConditions : termsConditions.en.termsConditions;\r\n\r\n    if (!termsContent || typeof termsContent !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={3}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {termsContent.title || (isRTL ? 'الشروط والأحكام' : 'Terms and Conditions')}\r\n        </Typography>\r\n        <Stack spacing={3}>\r\n          {termsContent.content && termsContent.content\r\n            .split(/\\n\\s*\\n/)\r\n            .filter((section) => section.trim() !== '')\r\n            .map((section, idx) => {\r\n              const lines = section.split('\\n');\r\n              const title = lines[0];\r\n              const body = lines.slice(1).join('\\n');\r\n              return (\r\n                <Card\r\n                  key={idx}\r\n                  elevation={2}\r\n                  sx={{\r\n                    borderRadius: 3,\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    ...(isRTL\r\n                      ? { borderRight: `4px solid ${theme.palette.primary.main}` }\r\n                      : { borderLeft: `4px solid ${theme.palette.primary.main}` }),\r\n                    backgroundColor: idx % 2 === 0\r\n                      ? alpha(theme.palette.primary.main, 0.02)\r\n                      : alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02),\r\n                  }}\r\n                >\r\n                  <CardContent>\r\n                    <Typography\r\n                      variant=\"h6\"\r\n                      fontWeight={600}\r\n                      mb={2}\r\n                      sx={{\r\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                        color: theme.palette.primary.main,\r\n                        textAlign: isRTL ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {title}\r\n                    </Typography>\r\n                    {body && (\r\n                      <Typography\r\n                        variant=\"body1\"\r\n                        sx={{\r\n                          lineHeight: 1.8,\r\n                          fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                          whiteSpace: 'pre-line',\r\n                          textAlign: isRTL ? 'right' : 'left'\r\n                        }}\r\n                      >\r\n                        {body}\r\n                      </Typography>\r\n                    )}\r\n                  </CardContent>\r\n                </Card>\r\n              );\r\n            })}\r\n        </Stack>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Privacy Policy\r\n  const renderPrivacyPolicy = () => {\r\n    const privacy = t('privacy', { returnObjects: true });\r\n\r\n    if (!privacy || typeof privacy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    /**\r\n     * Safely fetch a translation; returns empty string if key is missing.\r\n     */\r\n    const safeT = (key) => {\r\n      const val = t(key);\r\n      return val && !val.includes(key) ? val : '';\r\n    };\r\n\r\n    const renderStandardSection = (num) => {\r\n      const subtitle = safeT(`privacy.section${num}.subtitle`);\r\n      const content = safeT(`privacy.section${num}.content`);\r\n      const description = safeT(`privacy.section${num}.description`);\r\n      const contact = safeT(`privacy.section${num}.contact`);\r\n      const email = safeT(`privacy.section${num}.email`);\r\n\r\n      const bullets = [1, 2, 3, 4, 5]\r\n        .map((i) => safeT(`privacy.section${num}.item${i}`))\r\n        .filter((item) => item);\r\n\r\n      return (\r\n        <SectionCard key={num}>\r\n          <Typography\r\n            variant=\"h5\"\r\n            fontWeight={600}\r\n            color={theme.palette.primary.main}\r\n            mb={2}\r\n            sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\r\n          >\r\n            {t(`privacy.section${num}.title`)}\r\n          </Typography>\r\n          {subtitle && (\r\n            <Typography\r\n              mb={1}\r\n              sx={{\r\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                textAlign: isRTL ? 'right' : 'left'\r\n              }}\r\n            >\r\n              {subtitle}\r\n            </Typography>\r\n          )}\r\n          {content && (\r\n            <Typography\r\n              mb={1}\r\n              sx={{\r\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                textAlign: isRTL ? 'right' : 'left'\r\n              }}\r\n            >\r\n              {content}\r\n            </Typography>\r\n          )}\r\n          {description && (\r\n            <Typography\r\n              mb={1}\r\n              sx={{\r\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                textAlign: isRTL ? 'right' : 'left'\r\n              }}\r\n            >\r\n              {description}\r\n            </Typography>\r\n          )}\r\n          {bullets.length > 0 && (\r\n            <List>\r\n              {bullets.map((item, idx) => (\r\n                <ListItem\r\n                  key={idx}\r\n                  sx={{\r\n                    pl: isRTL ? 0 : 2,\r\n                    pr: isRTL ? 2 : 0,\r\n                    flexDirection: 'row',\r\n                    textAlign: isRTL ? 'right' : 'left',\r\n                  }}\r\n                >\r\n                  <ListItemIcon\r\n                    sx={{\r\n                      minWidth: 'auto',\r\n                      mx: isRTL ? 0 : 1,\r\n                      ml: isRTL ? 1 : 0,\r\n                    }}\r\n                  >\r\n                    <InfoIcon color=\"primary\" />\r\n                  </ListItemIcon>\r\n                  <ListItemText\r\n                    primary={item}\r\n                    sx={{\r\n                      textAlign: isRTL ? 'right' : 'left',\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit'\r\n                    }}\r\n                  />\r\n                </ListItem>\r\n              ))}\r\n            </List>\r\n          )}\r\n          {contact && (\r\n            <Typography\r\n              mb={1}\r\n              sx={{\r\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                textAlign: isRTL ? 'right' : 'left'\r\n              }}\r\n            >\r\n              {contact}\r\n            </Typography>\r\n          )}\r\n          {email && (\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n                color: theme.palette.primary.main,\r\n                fontWeight: 600\r\n              }}\r\n            >\r\n              {email}\r\n            </Typography>\r\n          )}\r\n        </SectionCard>\r\n      );\r\n    };\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        {/* Title & Intro */}\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\r\n        >\r\n          {t('privacy.title')}\r\n        </Typography>\r\n        <Typography variant=\"subtitle1\" color=\"text.secondary\" mb={3}>\r\n          {t('privacy.intro')}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {/* Sections 1-9 */}\r\n        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => renderStandardSection(num))}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Booking & Cancellation Policy\r\n  const renderBookingPolicy = () => {\r\n    const policy = t('policies.bookingCancellation', { returnObjects: true });\r\n\r\n    if (!policy || typeof policy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.title}\r\n        </Typography>\r\n        <Typography\r\n          variant=\"subtitle1\"\r\n          color=\"text.secondary\"\r\n          sx={{\r\n            mb: 3,\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.subtitle}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {/* Student Policy */}\r\n        <SectionCard title={policy.studentPolicy.title} icon={<PersonIcon />}>\r\n          <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {renderSubSection(policy.studentPolicy.booking)}\r\n            {renderSubSection(policy.studentPolicy.cancellation)}\r\n            {renderSubSection(policy.studentPolicy.rescheduling)}\r\n            {renderSubSection(policy.studentPolicy.lateArrival)}\r\n          </Box>\r\n        </SectionCard>\r\n\r\n        {/* Tutor Policy */}\r\n        <SectionCard\r\n          title={policy.tutorPolicy.title}\r\n          icon={<SchoolIcon />}\r\n          bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}\r\n        >\r\n          <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {renderSubSection(policy.tutorPolicy.availability)}\r\n            {renderSubSection(policy.tutorPolicy.cancellation)}\r\n            {renderSubSection(policy.tutorPolicy.rescheduling)}\r\n            {renderSubSection(policy.tutorPolicy.lateArrival)}\r\n          </Box>\r\n        </SectionCard>\r\n\r\n        {/* General Notes */}\r\n        <SectionCard title={policy.generalNotes.title} icon={<InfoIcon />}>\r\n          <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {policy.generalNotes.points.map((item, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                py: 0.5,\r\n                textAlign: isRTL ? 'right' : 'left',\r\n                pl: isRTL ? 0 : 2,\r\n                pr: isRTL ? 2 : 0,\r\n                flexDirection: 'row',\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mx: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                }}>\r\n                  <InfoIcon color=\"info\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </SectionCard>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Payment Policy\r\n  const renderPaymentPolicy = () => {\r\n    const policy = t('policies.bookingPayment', { returnObjects: true });\r\n\r\n    if (!policy || typeof policy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    const renderSection = (section) => (\r\n      <SectionCard title={section.title} icon={<PaymentIcon />}>\r\n        <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n          {section.points && (\r\n            <List>\r\n              {section.points.map((item, idx) => (\r\n                <ListItem key={idx} sx={{\r\n                  pl: isRTL ? 0 : 2,\r\n                  pr: isRTL ? 2 : 0,\r\n                  flexDirection: 'row',\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                }}>\r\n                  <ListItemIcon sx={{\r\n                    minWidth: 'auto',\r\n                    mx: isRTL ? 0 : 1,\r\n                    ml: isRTL ? 1 : 0,\r\n                  }}>\r\n                    <InfoIcon color=\"success\" />\r\n                  </ListItemIcon>\r\n                  <ListItemText\r\n                    primary={item}\r\n                    sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                  />\r\n                </ListItem>\r\n              ))}\r\n            </List>\r\n          )}\r\n          {section.description && (\r\n            <Typography\r\n              sx={{\r\n                fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                textAlign: isRTL ? 'right' : 'left'\r\n              }}\r\n            >\r\n              {section.description}\r\n            </Typography>\r\n          )}\r\n        </Box>\r\n      </SectionCard>\r\n    );\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.title}\r\n        </Typography>\r\n        <Typography\r\n          variant=\"subtitle1\"\r\n          color=\"text.secondary\"\r\n          sx={{\r\n            mb: 3,\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.subtitle}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {renderSection(policy.section1)}\r\n        {renderSection(policy.section2)}\r\n        {renderSection(policy.section3)}\r\n        {renderSection(policy.section4)}\r\n        {renderSection(policy.section5)}\r\n        {renderSection(policy.section6)}\r\n        {renderSection(policy.section7)}\r\n        {renderSection(policy.section8)}\r\n\r\n        {/* Features */}\r\n        {policy.features && (\r\n          <>\r\n            <Divider sx={{ my: 3 }} />\r\n            <Grid container spacing={2} mb={3}>\r\n              <Grid item xs={12} md={6}>\r\n                <Alert icon={<SecurityIcon />} severity=\"success\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                  <Typography fontWeight={600}>{policy.features.securePayments}</Typography>\r\n                  <Typography>{policy.features.securePaymentsDesc}</Typography>\r\n                </Alert>\r\n              </Grid>\r\n              <Grid item xs={12} md={6}>\r\n                <Alert icon={<CurrencyExchangeIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                  <Typography fontWeight={600}>{policy.features.multipleCurrencies}</Typography>\r\n                  <Typography>{policy.features.multipleCurrenciesDesc}</Typography>\r\n                </Alert>\r\n              </Grid>\r\n              <Grid item xs={12} md={6}>\r\n                <Alert icon={<ReceiptIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                  <Typography fontWeight={600}>{policy.features.instantReceipts}</Typography>\r\n                  <Typography>{policy.features.instantReceiptsDesc}</Typography>\r\n                </Alert>\r\n              </Grid>\r\n              <Grid item xs={12} md={6}>\r\n                <Alert icon={<CheckCircleIcon />} severity=\"success\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                  <Typography fontWeight={600}>{policy.features.instantConfirmation}</Typography>\r\n                  <Typography>{policy.features.instantConfirmationDesc}</Typography>\r\n                </Alert>\r\n              </Grid>\r\n            </Grid>\r\n          </>\r\n        )}\r\n\r\n        {/* Contact */}\r\n        {policy.section8 && policy.section8.email && (\r\n          <Box>\r\n            <Typography variant=\"h6\" fontWeight={600} mb={1}>\r\n              {policy.section8.title || policy.section8.description}\r\n            </Typography>\r\n            <Typography mb={1}>{policy.section8.description}</Typography>\r\n            <Alert icon={<EmailIcon />} severity=\"info\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n              {policy.section8.email}\r\n            </Alert>\r\n          </Box>\r\n        )}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Refund Policy\r\n  const renderRefundPolicy = () => {\r\n    const refund = t('policies.refund', { returnObjects: true });\r\n\r\n    if (!refund || typeof refund !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {refund.title}\r\n        </Typography>\r\n        <Divider sx={{ mb: 3 }} />\r\n\r\n        {/* القسم الأول */}\r\n        <SectionCard title={refund.section1.title} icon={<CheckCircleIcon />}>\r\n          <Typography\r\n            mb={2}\r\n            sx={{\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {refund.section1.description}\r\n          </Typography>\r\n          <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {refund.section1.items.map((item, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                pl: isRTL ? 0 : 2,\r\n                pr: isRTL ? 2 : 0,\r\n                flexDirection: 'row',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mx: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                }}>\r\n                  <CheckCircleIcon color=\"success\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    textAlign: isRTL ? 'right' : 'left'\r\n                  }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </SectionCard>\r\n\r\n        {/* القسم الثاني */}\r\n        <SectionCard title={refund.section2.title} icon={<CancelIcon />}>\r\n          <Typography\r\n            mb={2}\r\n            sx={{\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {refund.section2.description}\r\n          </Typography>\r\n          <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n            {refund.section2.items.map((item, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                pl: isRTL ? 0 : 2,\r\n                pr: isRTL ? 2 : 0,\r\n                flexDirection: 'row',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mx: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                }}>\r\n                  <CancelIcon color=\"error\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    textAlign: isRTL ? 'right' : 'left'\r\n                  }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </SectionCard>\r\n\r\n        {/* القسم الثالث */}\r\n        <SectionCard title={refund.section3.title} icon={<InfoIcon />}>\r\n          <Typography\r\n            sx={{\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {refund.section3.description}\r\n          </Typography>\r\n        </SectionCard>\r\n\r\n        {/* باقي الأقسام */}\r\n        {[4, 5, 6, 7, 8].map((num) => {\r\n          const section = refund[`section${num}`];\r\n          if (!section) return null;\r\n\r\n          return (\r\n            <SectionCard key={num} title={section.title} icon={<RefreshIcon />}>\r\n              <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                {section.description && (\r\n                  <Typography\r\n                    mb={2}\r\n                    sx={{\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n                      textAlign: isRTL ? 'right' : 'left'\r\n                    }}\r\n                  >\r\n                    {section.description}\r\n                  </Typography>\r\n                )}\r\n                {section.items && (\r\n                  <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                    {section.items.map((item, idx) => (\r\n                      <ListItem key={idx} sx={{\r\n                        pl: isRTL ? 0 : 2,\r\n                        pr: isRTL ? 2 : 0,\r\n                        flexDirection: 'row',\r\n                        textAlign: isRTL ? 'right' : 'left',\r\n                      }}>\r\n                        <ListItemIcon sx={{\r\n                          minWidth: 'auto',\r\n                          mx: isRTL ? 0 : 1,\r\n                          ml: isRTL ? 1 : 0,\r\n                        }}>\r\n                          <InfoIcon color=\"success\" />\r\n                        </ListItemIcon>\r\n                        <ListItemText\r\n                          primary={item}\r\n                          sx={{\r\n                            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                            textAlign: isRTL ? 'right' : 'left'\r\n                          }}\r\n                        />\r\n                      </ListItem>\r\n                    ))}\r\n                  </List>\r\n                )}\r\n              </Box>\r\n            </SectionCard>\r\n          );\r\n        })}\r\n\r\n        {/* معلومات التواصل */}\r\n        <Divider sx={{ my: 3 }} />\r\n        <Box>\r\n          <Typography\r\n            variant=\"h6\"\r\n            fontWeight={600}\r\n            mb={2}\r\n            sx={{\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {refund.contact.title}\r\n          </Typography>\r\n          <Alert\r\n            icon={<EmailIcon />}\r\n            severity=\"info\"\r\n            sx={{\r\n              direction: isRTL ? 'rtl' : 'ltr',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {refund.contact.email}\r\n          </Alert>\r\n        </Box>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Layout>\r\n      <Box\r\n        sx={{\r\n          py: 4,\r\n          minHeight: '100vh',\r\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n        }}\r\n      >\r\n        <Container maxWidth=\"lg\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n          <Fade in timeout={1000}>\r\n            <Paper\r\n              elevation={3}\r\n              sx={{\r\n                p: { xs: 2, md: 4 },\r\n                mb: 4,\r\n                borderRadius: 3,\r\n                direction: isRTL ? 'rtl' : 'ltr',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n                '& *': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                },\r\n                '& .MuiTypography-root': {\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                },\r\n                '& .MuiList-root': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                },\r\n                '& .MuiListItem-root': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                },\r\n              }}\r\n            >\r\n              {/* Header */}\r\n              <Box sx={{\r\n                mb: 4,\r\n                textAlign: 'center',\r\n                direction: isRTL ? 'rtl' : 'ltr'\r\n              }}>\r\n                <Typography\r\n                  variant=\"h3\"\r\n                  component=\"h1\"\r\n                  sx={{\r\n                    mb: 2,\r\n                    fontWeight: 800,\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    color: theme.palette.primary.main,\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    textAlign: 'center',\r\n                  }}\r\n                >\r\n                  {isRTL ? 'سياسات المنصة' : 'Platform Policies'}\r\n                </Typography>\r\n                <Typography\r\n                  variant=\"subtitle1\"\r\n                  color=\"text.secondary\"\r\n                  sx={{\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    textAlign: 'center',\r\n                  }}\r\n                >\r\n                  {isRTL\r\n                    ? 'جميع السياسات والشروط الخاصة بمنصة علّمني أون لاين في مكان واحد'\r\n                    : 'All Allemnionline platform policies and terms in one place'\r\n                  }\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Tabs */}\r\n              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3, direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                <Tabs\r\n                  value={activeTab}\r\n                  onChange={handleTabChange}\r\n                  variant=\"scrollable\"\r\n                  scrollButtons=\"auto\"\r\n                  sx={{\r\n                    '& .MuiTab-root': {\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                      minHeight: 64,\r\n                      textAlign: isRTL ? 'right' : 'left',\r\n                    },\r\n                    '& .MuiTabs-flexContainer': {\r\n                      direction: isRTL ? 'rtl' : 'ltr',\r\n                    }\r\n                  }}\r\n                >\r\n                  {tabsConfig.map((tab, index) => (\r\n                    <Tab\r\n                      key={index}\r\n                      icon={tab.icon}\r\n                      label={tab.label}\r\n                      iconPosition=\"start\"\r\n                      sx={{\r\n                        flexDirection: isRTL ? 'row-reverse' : 'row',\r\n                        textAlign: isRTL ? 'right' : 'left',\r\n                        '& .MuiTab-iconWrapper': {\r\n                          mr: isRTL ? 0 : 1,\r\n                          ml: isRTL ? 1 : 0,\r\n                        }\r\n                      }}\r\n                    />\r\n                  ))}\r\n                </Tabs>\r\n              </Box>\r\n\r\n              {/* Tab Panels */}\r\n              <TabPanel value={activeTab} index={0}>\r\n                {renderTermsAndConditions()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={1}>\r\n                {renderPrivacyPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={2}>\r\n                {renderBookingPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={3}>\r\n                {renderPaymentPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={4}>\r\n                {renderRefundPolicy()}\r\n              </TabPanel>\r\n\r\n            </Paper>\r\n          </Fade>\r\n        </Container>\r\n      </Box>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport default PlatformPolicy;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,SAAS,CACTC,UAAU,CACVC,KAAK,CACLC,GAAG,CACHC,OAAO,CACPC,QAAQ,CACRC,IAAI,CACJC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,IAAI,CACJC,GAAG,CACHC,IAAI,CACJC,WAAW,CACXC,KAAK,CACLC,KAAK,CACLC,KAAK,CACLC,IAAI,KACC,eAAe,CACtB,OACEC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,KAAK,GAAI,CAAAC,SAAS,CAClBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,MAAM,GAAI,CAAAC,UAAU,CACpBC,gBAAgB,GAAI,CAAAC,oBAAoB,CACxCC,OAAO,GAAI,CAAAC,WAAW,KACjB,qBAAqB,CAC5B,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnE,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG1D,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA2D,KAAK,CAAGnD,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAoD,QAAQ,CAAG3D,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA4D,QAAQ,CAAG3D,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC4D,KAAK,CAAEC,QAAQ,CAAC,CAAGjE,QAAQ,CAAC4D,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAC1D,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGpE,QAAQ,CAAC,CAAC,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACdgE,QAAQ,CAACL,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAChCG,QAAQ,CAACC,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CACrDG,QAAQ,CAACE,eAAe,CAACD,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CACrEG,QAAQ,CAACG,IAAI,CAACF,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CAE1D;AACA,KAAM,CAAAO,IAAI,CAAGX,QAAQ,CAACW,IAAI,CAC1B,GAAIA,IAAI,GAAK,QAAQ,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IAClC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,SAAS,CAAEL,YAAY,CAAC,CAAC,CAAC,CAC9C,CAAC,CAAE,CAACR,IAAI,CAACM,QAAQ,CAAEJ,QAAQ,CAACW,IAAI,CAAC,CAAC,CAElC,KAAM,CAAAC,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC3CR,YAAY,CAACQ,QAAQ,CAAC,CACtB,KAAM,CAAAC,IAAI,CAAG,CAAC,QAAQ,CAAE,UAAU,CAAE,UAAU,CAAE,UAAU,CAAE,SAAS,CAAC,CACtEd,QAAQ,CAAC,mBAAmBc,IAAI,CAACD,QAAQ,CAAC,EAAE,CAAE,CAAEE,OAAO,CAAE,IAAK,CAAC,CAAC,CAClE,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEC,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,iBAAiB,CAAG,oBAAoB,CAAEe,IAAI,cAAE5B,IAAA,CAACd,eAAe,GAAE,CAAE,CAAC,CACvG,CAAEyC,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,gBAAgB,CAAG,gBAAgB,CAAEe,IAAI,cAAE5B,IAAA,CAAC5B,YAAY,GAAE,CAAE,CAAC,CAC/F,CAAEuD,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,sBAAsB,CAAG,wBAAwB,CAAEe,IAAI,cAAE5B,IAAA,CAAC1B,YAAY,GAAE,CAAE,CAAC,CAC7G,CAAEqD,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,aAAa,CAAG,gBAAgB,CAAEe,IAAI,cAAE5B,IAAA,CAAClB,WAAW,GAAE,CAAE,CAAC,CAC3F,CAAE6C,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,iBAAiB,CAAG,eAAe,CAAEe,IAAI,cAAE5B,IAAA,CAAChB,WAAW,GAAE,CAAE,CAAC,CAC/F,CAED;AACA,KAAM,CAAA6C,WAAW,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,QAAQ,CAAEJ,IAAI,CAAEK,EAAG,CAAC,CAAAH,IAAA,oBAChD9B,IAAA,CAACnC,IAAI,EACHqE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChC,IAAIA,KAAK,CACL,CAAE4B,WAAW,CAAE,aAAa/B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAanC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEX,EAAE,EAAIlE,KAAK,CAACyC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAC/D,CAAE,CAAAV,QAAA,cAEF9B,KAAA,CAACpC,WAAW,EAACqE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,eACpD9B,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CACPU,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBV,EAAE,CAAE,CAAC,CACLW,aAAa,CAAEpC,KAAK,CAAG,aAAa,CAAG,KAAK,CAC5C2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,EACCJ,IAAI,eACH5B,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CAAEc,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAV,QAAA,CAClFJ,IAAI,CACF,CACN,cACD5B,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCP,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChE2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDD,KAAK,CACI,CAAC,EACV,CAAC,cACN/B,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,CAChFA,QAAQ,CACN,CAAC,EACK,CAAC,CACV,CAAC,EACR,CAED;AACA,KAAM,CAAAuB,gBAAgB,CAAIC,UAAU,EAAK,CACvC,GAAI,CAACA,UAAU,EAAI,MAAO,CAAAA,UAAU,GAAK,QAAQ,CAAE,MAAO,KAAI,CAE9D,mBACEtD,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjBhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZjB,EAAE,CAAE,CACFkB,UAAU,CAAE,GAAG,CACfF,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCN,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwB,UAAU,CAACzB,KAAK,CACP,CAAC,CACZyB,UAAU,CAACC,MAAM,eAChBzD,IAAA,CAACzC,IAAI,EAAAyE,QAAA,CACFwB,UAAU,CAACC,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC/B1D,KAAA,CAAC1C,QAAQ,EAAW2E,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACAhC,IAAA,CAACtC,YAAY,EAACyE,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACAhC,IAAA,CAACpB,QAAQ,EAACuE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfnD,IAAA,CAACvC,YAAY,EACXgF,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWiD,GAiBL,CACX,CAAC,CACE,CACP,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAK,QAAQ,CAAGC,KAAA,MAAC,CAAElC,QAAQ,CAAEmC,KAAK,CAAEC,KAAM,CAAC,CAAAF,KAAA,oBAC1ClE,IAAA,QAAKqE,MAAM,CAAEF,KAAK,GAAKC,KAAM,CAAApC,QAAA,CAC1BmC,KAAK,GAAKC,KAAK,eACdpE,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CACPmC,EAAE,CAAE,CAAC,CACLhC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CACCA,QAAQ,CACN,CACN,CACE,CAAC,EACP,CAED;AACA,KAAM,CAAAuC,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAC,YAAY,CAAGjE,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAGf,eAAe,CAAC2E,EAAE,CAAC3E,eAAe,CAAGA,eAAe,CAAC4E,EAAE,CAAC5E,eAAe,CAErH,GAAI,CAAC0E,YAAY,EAAI,MAAO,CAAAA,YAAY,GAAK,QAAQ,CAAE,CACrD,mBACExE,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtChC,IAAA,CAAC/C,UAAU,EAACmG,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACET,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjFhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwC,YAAY,CAACzC,KAAK,GAAKpB,KAAK,CAAG,iBAAiB,CAAG,sBAAsB,CAAC,CACjE,CAAC,cACbX,IAAA,CAAChC,KAAK,EAAC2G,OAAO,CAAE,CAAE,CAAA3C,QAAA,CACfwC,YAAY,CAACI,OAAO,EAAIJ,YAAY,CAACI,OAAO,CAC1CC,KAAK,CAAC,SAAS,CAAC,CAChBC,MAAM,CAAEC,OAAO,EAAKA,OAAO,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC1CtB,GAAG,CAAC,CAACqB,OAAO,CAAEnB,GAAG,GAAK,CACrB,KAAM,CAAAqB,KAAK,CAAGF,OAAO,CAACF,KAAK,CAAC,IAAI,CAAC,CACjC,KAAM,CAAA9C,KAAK,CAAGkD,KAAK,CAAC,CAAC,CAAC,CACtB,KAAM,CAAA9D,IAAI,CAAG8D,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACtC,mBACEnF,IAAA,CAACnC,IAAI,EAEHqE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFE,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChC,IAAIA,KAAK,CACL,CAAE4B,WAAW,CAAE,aAAa/B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAanC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEgB,GAAG,CAAG,CAAC,GAAK,CAAC,CAC1B7F,KAAK,CAACyC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvC3E,KAAK,CAACyC,KAAK,CAACgC,OAAO,CAAC4C,SAAS,CAAC1C,IAAI,EAAIlC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAAC4C,KAAK,CAAE,IAAI,CAC7E,CAAE,CAAArD,QAAA,cAEF9B,KAAA,CAACpC,WAAW,EAAAkE,QAAA,eACVhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDD,KAAK,CACI,CAAC,CACZZ,IAAI,eACHnB,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFmD,UAAU,CAAE,GAAG,CACfhC,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD4E,UAAU,CAAE,UAAU,CACtBvC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDb,IAAI,CACK,CACb,EACU,CAAC,EAvCTyC,GAwCD,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACL,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA4B,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,OAAO,CAAGnF,CAAC,CAAC,SAAS,CAAE,CAAEoF,aAAa,CAAE,IAAK,CAAC,CAAC,CAErD,GAAI,CAACD,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,CAC3C,mBACEzF,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtChC,IAAA,CAAC/C,UAAU,EAACmG,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA;AACJ;AACA,OACI,KAAM,CAAAgF,KAAK,CAAIC,GAAG,EAAK,CACrB,KAAM,CAAAC,GAAG,CAAGvF,CAAC,CAACsF,GAAG,CAAC,CAClB,MAAO,CAAAC,GAAG,EAAI,CAACA,GAAG,CAACC,QAAQ,CAACF,GAAG,CAAC,CAAGC,GAAG,CAAG,EAAE,CAC7C,CAAC,CAED,KAAM,CAAAE,qBAAqB,CAAIC,GAAG,EAAK,CACrC,KAAM,CAAAC,QAAQ,CAAGN,KAAK,CAAC,kBAAkBK,GAAG,WAAW,CAAC,CACxD,KAAM,CAAApB,OAAO,CAAGe,KAAK,CAAC,kBAAkBK,GAAG,UAAU,CAAC,CACtD,KAAM,CAAAE,WAAW,CAAGP,KAAK,CAAC,kBAAkBK,GAAG,cAAc,CAAC,CAC9D,KAAM,CAAAG,OAAO,CAAGR,KAAK,CAAC,kBAAkBK,GAAG,UAAU,CAAC,CACtD,KAAM,CAAAI,KAAK,CAAGT,KAAK,CAAC,kBAAkBK,GAAG,QAAQ,CAAC,CAElD,KAAM,CAAAK,OAAO,CAAG,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAC5B3C,GAAG,CAAE4C,CAAC,EAAKX,KAAK,CAAC,kBAAkBK,GAAG,QAAQM,CAAC,EAAE,CAAC,CAAC,CACnDxB,MAAM,CAAEnB,IAAI,EAAKA,IAAI,CAAC,CAEzB,mBACEzD,KAAA,CAAC2B,WAAW,EAAAG,QAAA,eACVhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCN,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAqB,QAAA,CAExE1B,CAAC,CAAC,kBAAkB0F,GAAG,QAAQ,CAAC,CACvB,CAAC,CACZC,QAAQ,eACPjG,IAAA,CAAC/C,UAAU,EACTmF,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDiE,QAAQ,CACC,CACb,CACArB,OAAO,eACN5E,IAAA,CAAC/C,UAAU,EACTmF,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED4C,OAAO,CACE,CACb,CACAsB,WAAW,eACVlG,IAAA,CAAC/C,UAAU,EACTmF,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDkE,WAAW,CACF,CACb,CACAG,OAAO,CAACE,MAAM,CAAG,CAAC,eACjBvG,IAAA,CAACzC,IAAI,EAAAyE,QAAA,CACFqE,OAAO,CAAC3C,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACrB1D,KAAA,CAAC1C,QAAQ,EAEP2E,EAAE,CAAE,CACF0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eAEFhC,IAAA,CAACtC,YAAY,EACXyE,EAAE,CAAE,CACF4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cAEFhC,IAAA,CAACpB,QAAQ,EAACuE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfnD,IAAA,CAACvC,YAAY,EACXgF,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CACFa,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2C,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAC9C,CAAE,CACH,CAAC,GAvBGiD,GAwBG,CACX,CAAC,CACE,CACP,CACAuC,OAAO,eACNnG,IAAA,CAAC/C,UAAU,EACTmF,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDmE,OAAO,CACE,CACb,CACAC,KAAK,eACJpG,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCW,UAAU,CAAE,GACd,CAAE,CAAArB,QAAA,CAEDoE,KAAK,CACI,CACb,GAlGeJ,GAmGL,CAAC,CAElB,CAAC,CAED,mBACE9F,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eAEjFhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAqB,QAAA,CAExE1B,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbN,IAAA,CAAC/C,UAAU,EAACmG,OAAO,CAAC,WAAW,CAACD,KAAK,CAAC,gBAAgB,CAACf,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC1D1B,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cACbN,IAAA,CAAC5C,OAAO,EAAC+E,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACsB,GAAG,CAAEsC,GAAG,EAAKD,qBAAqB,CAACC,GAAG,CAAC,CAAC,EAClE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAQ,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,MAAM,CAAGnG,CAAC,CAAC,8BAA8B,CAAE,CAAEoF,aAAa,CAAE,IAAK,CAAC,CAAC,CAEzE,GAAI,CAACe,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACEzG,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtChC,IAAA,CAAC/C,UAAU,EAACmG,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACET,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjFhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDyE,MAAM,CAAC1E,KAAK,CACH,CAAC,cACb/B,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,gBAAgB,CACtBhB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDyE,MAAM,CAACR,QAAQ,CACN,CAAC,cACbjG,IAAA,CAAC5C,OAAO,EAAC+E,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BpC,IAAA,CAAC6B,WAAW,EAACE,KAAK,CAAE0E,MAAM,CAACC,aAAa,CAAC3E,KAAM,CAACH,IAAI,cAAE5B,IAAA,CAACxB,UAAU,GAAE,CAAE,CAAAwD,QAAA,cACnE9B,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3CuB,gBAAgB,CAACkD,MAAM,CAACC,aAAa,CAACC,OAAO,CAAC,CAC9CpD,gBAAgB,CAACkD,MAAM,CAACC,aAAa,CAACE,YAAY,CAAC,CACnDrD,gBAAgB,CAACkD,MAAM,CAACC,aAAa,CAACG,YAAY,CAAC,CACnDtD,gBAAgB,CAACkD,MAAM,CAACC,aAAa,CAACI,WAAW,CAAC,EAChD,CAAC,CACK,CAAC,cAGd9G,IAAA,CAAC6B,WAAW,EACVE,KAAK,CAAE0E,MAAM,CAACM,WAAW,CAAChF,KAAM,CAChCH,IAAI,cAAE5B,IAAA,CAACtB,UAAU,GAAE,CAAE,CACrBuD,EAAE,CAAElE,KAAK,CAACyC,KAAK,CAACgC,OAAO,CAAC4C,SAAS,CAAC1C,IAAI,EAAIlC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAAC4C,KAAK,CAAE,IAAI,CAAE,CAAArD,QAAA,cAE7E9B,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3CuB,gBAAgB,CAACkD,MAAM,CAACM,WAAW,CAACC,YAAY,CAAC,CACjDzD,gBAAgB,CAACkD,MAAM,CAACM,WAAW,CAACH,YAAY,CAAC,CACjDrD,gBAAgB,CAACkD,MAAM,CAACM,WAAW,CAACF,YAAY,CAAC,CACjDtD,gBAAgB,CAACkD,MAAM,CAACM,WAAW,CAACD,WAAW,CAAC,EAC9C,CAAC,CACK,CAAC,cAGd9G,IAAA,CAAC6B,WAAW,EAACE,KAAK,CAAE0E,MAAM,CAACQ,YAAY,CAAClF,KAAM,CAACH,IAAI,cAAE5B,IAAA,CAACpB,QAAQ,GAAE,CAAE,CAAAoD,QAAA,cAChEhC,IAAA,CAACzC,IAAI,EAAC4E,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5CyE,MAAM,CAACQ,YAAY,CAACxD,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACxC1D,KAAA,CAAC1C,QAAQ,EAAW2E,EAAE,CAAE,CACtBmC,EAAE,CAAE,GAAG,CACPtB,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnCkD,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KACjB,CAAE,CAAAf,QAAA,eACAhC,IAAA,CAACtC,YAAY,EAACyE,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACAhC,IAAA,CAACpB,QAAQ,EAACuE,KAAK,CAAC,MAAM,CAAE,CAAC,CACb,CAAC,cACfnD,IAAA,CAACvC,YAAY,EACXgF,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAjBWiD,GAkBL,CACX,CAAC,CACE,CAAC,CACI,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAsD,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAT,MAAM,CAAGnG,CAAC,CAAC,yBAAyB,CAAE,CAAEoF,aAAa,CAAE,IAAK,CAAC,CAAC,CAEpE,GAAI,CAACe,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACEzG,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtChC,IAAA,CAAC/C,UAAU,EAACmG,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,KAAM,CAAAwG,aAAa,CAAIpC,OAAO,eAC5B/E,IAAA,CAAC6B,WAAW,EAACE,KAAK,CAAEgD,OAAO,CAAChD,KAAM,CAACH,IAAI,cAAE5B,IAAA,CAAClB,WAAW,GAAE,CAAE,CAAAkD,QAAA,cACvD9B,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3C+C,OAAO,CAACtB,MAAM,eACbzD,IAAA,CAACzC,IAAI,EAAAyE,QAAA,CACF+C,OAAO,CAACtB,MAAM,CAACC,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC5B1D,KAAA,CAAC1C,QAAQ,EAAW2E,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACAhC,IAAA,CAACtC,YAAY,EAACyE,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACAhC,IAAA,CAACpB,QAAQ,EAACuE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfnD,IAAA,CAACvC,YAAY,EACXgF,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAhBWiD,GAiBL,CACX,CAAC,CACE,CACP,CACAmB,OAAO,CAACmB,WAAW,eAClBlG,IAAA,CAAC/C,UAAU,EACTkF,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED+C,OAAO,CAACmB,WAAW,CACV,CACb,EACE,CAAC,CACK,CACd,CAED,mBACEhG,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjFhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDyE,MAAM,CAAC1E,KAAK,CACH,CAAC,cACb/B,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,gBAAgB,CACtBhB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDyE,MAAM,CAACR,QAAQ,CACN,CAAC,cACbjG,IAAA,CAAC5C,OAAO,EAAC+E,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAEzB+E,aAAa,CAACV,MAAM,CAACW,QAAQ,CAAC,CAC9BD,aAAa,CAACV,MAAM,CAACY,QAAQ,CAAC,CAC9BF,aAAa,CAACV,MAAM,CAACa,QAAQ,CAAC,CAC9BH,aAAa,CAACV,MAAM,CAACc,QAAQ,CAAC,CAC9BJ,aAAa,CAACV,MAAM,CAACe,QAAQ,CAAC,CAC9BL,aAAa,CAACV,MAAM,CAACgB,QAAQ,CAAC,CAC9BN,aAAa,CAACV,MAAM,CAACiB,QAAQ,CAAC,CAC9BP,aAAa,CAACV,MAAM,CAACkB,QAAQ,CAAC,CAG9BlB,MAAM,CAACmB,QAAQ,eACd1H,KAAA,CAAAE,SAAA,EAAA4B,QAAA,eACEhC,IAAA,CAAC5C,OAAO,EAAC+E,EAAE,CAAE,CAAE0F,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1B3H,KAAA,CAAChC,IAAI,EAAC4J,SAAS,MAACnD,OAAO,CAAE,CAAE,CAACvC,EAAE,CAAE,CAAE,CAAAJ,QAAA,eAChChC,IAAA,CAAC9B,IAAI,EAACyF,IAAI,MAACoE,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAhG,QAAA,cACvB9B,KAAA,CAACjC,KAAK,EAAC2D,IAAI,cAAE5B,IAAA,CAAC5B,YAAY,GAAE,CAAE,CAAC6J,QAAQ,CAAC,SAAS,CAAC9F,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,eACzFhC,IAAA,CAAC/C,UAAU,EAACoG,UAAU,CAAE,GAAI,CAAArB,QAAA,CAAEyE,MAAM,CAACmB,QAAQ,CAACM,cAAc,CAAa,CAAC,cAC1ElI,IAAA,CAAC/C,UAAU,EAAA+E,QAAA,CAAEyE,MAAM,CAACmB,QAAQ,CAACO,kBAAkB,CAAa,CAAC,EACxD,CAAC,CACJ,CAAC,cACPnI,IAAA,CAAC9B,IAAI,EAACyF,IAAI,MAACoE,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAhG,QAAA,cACvB9B,KAAA,CAACjC,KAAK,EAAC2D,IAAI,cAAE5B,IAAA,CAACN,oBAAoB,GAAE,CAAE,CAACuI,QAAQ,CAAC,MAAM,CAAC9F,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,eAC9FhC,IAAA,CAAC/C,UAAU,EAACoG,UAAU,CAAE,GAAI,CAAArB,QAAA,CAAEyE,MAAM,CAACmB,QAAQ,CAACQ,kBAAkB,CAAa,CAAC,cAC9EpI,IAAA,CAAC/C,UAAU,EAAA+E,QAAA,CAAEyE,MAAM,CAACmB,QAAQ,CAACS,sBAAsB,CAAa,CAAC,EAC5D,CAAC,CACJ,CAAC,cACPrI,IAAA,CAAC9B,IAAI,EAACyF,IAAI,MAACoE,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAhG,QAAA,cACvB9B,KAAA,CAACjC,KAAK,EAAC2D,IAAI,cAAE5B,IAAA,CAACJ,WAAW,GAAE,CAAE,CAACqI,QAAQ,CAAC,MAAM,CAAC9F,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,eACrFhC,IAAA,CAAC/C,UAAU,EAACoG,UAAU,CAAE,GAAI,CAAArB,QAAA,CAAEyE,MAAM,CAACmB,QAAQ,CAACU,eAAe,CAAa,CAAC,cAC3EtI,IAAA,CAAC/C,UAAU,EAAA+E,QAAA,CAAEyE,MAAM,CAACmB,QAAQ,CAACW,mBAAmB,CAAa,CAAC,EACzD,CAAC,CACJ,CAAC,cACPvI,IAAA,CAAC9B,IAAI,EAACyF,IAAI,MAACoE,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAhG,QAAA,cACvB9B,KAAA,CAACjC,KAAK,EAAC2D,IAAI,cAAE5B,IAAA,CAACV,eAAe,GAAE,CAAE,CAAC2I,QAAQ,CAAC,SAAS,CAAC9F,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,eAC5FhC,IAAA,CAAC/C,UAAU,EAACoG,UAAU,CAAE,GAAI,CAAArB,QAAA,CAAEyE,MAAM,CAACmB,QAAQ,CAACY,mBAAmB,CAAa,CAAC,cAC/ExI,IAAA,CAAC/C,UAAU,EAAA+E,QAAA,CAAEyE,MAAM,CAACmB,QAAQ,CAACa,uBAAuB,CAAa,CAAC,EAC7D,CAAC,CACJ,CAAC,EACH,CAAC,EACP,CACH,CAGAhC,MAAM,CAACkB,QAAQ,EAAIlB,MAAM,CAACkB,QAAQ,CAACvB,KAAK,eACvClG,KAAA,CAAC/C,GAAG,EAAA6E,QAAA,eACFhC,IAAA,CAAC/C,UAAU,EAACmG,OAAO,CAAC,IAAI,CAACC,UAAU,CAAE,GAAI,CAACjB,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAC7CyE,MAAM,CAACkB,QAAQ,CAAC5F,KAAK,EAAI0E,MAAM,CAACkB,QAAQ,CAACzB,WAAW,CAC3C,CAAC,cACblG,IAAA,CAAC/C,UAAU,EAACmF,EAAE,CAAE,CAAE,CAAAJ,QAAA,CAAEyE,MAAM,CAACkB,QAAQ,CAACzB,WAAW,CAAa,CAAC,cAC7DlG,IAAA,CAAC/B,KAAK,EAAC2D,IAAI,cAAE5B,IAAA,CAACZ,SAAS,GAAE,CAAE,CAAC6I,QAAQ,CAAC,MAAM,CAAC9F,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAClFyE,MAAM,CAACkB,QAAQ,CAACvB,KAAK,CACjB,CAAC,EACL,CACN,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAsC,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,MAAM,CAAGrI,CAAC,CAAC,iBAAiB,CAAE,CAAEoF,aAAa,CAAE,IAAK,CAAC,CAAC,CAE5D,GAAI,CAACiD,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACE3I,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEsB,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cACtChC,IAAA,CAAC/C,UAAU,EAACmG,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACET,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjFhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED2G,MAAM,CAAC5G,KAAK,CACH,CAAC,cACb/B,IAAA,CAAC5C,OAAO,EAAC+E,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BlC,KAAA,CAAC2B,WAAW,EAACE,KAAK,CAAE4G,MAAM,CAACvB,QAAQ,CAACrF,KAAM,CAACH,IAAI,cAAE5B,IAAA,CAACV,eAAe,GAAE,CAAE,CAAA0C,QAAA,eACnEhC,IAAA,CAAC/C,UAAU,EACTmF,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED2G,MAAM,CAACvB,QAAQ,CAAClB,WAAW,CAClB,CAAC,cACblG,IAAA,CAACzC,IAAI,EAAC4E,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5C2G,MAAM,CAACvB,QAAQ,CAACwB,KAAK,CAAClF,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACnC1D,KAAA,CAAC1C,QAAQ,EAAW2E,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACAhC,IAAA,CAACtC,YAAY,EAACyE,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACAhC,IAAA,CAACV,eAAe,EAAC6D,KAAK,CAAC,SAAS,CAAE,CAAC,CACvB,CAAC,cACfnD,IAAA,CAACvC,YAAY,EACXgF,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CACH,CAAC,GAnBWiD,GAoBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGd1D,KAAA,CAAC2B,WAAW,EAACE,KAAK,CAAE4G,MAAM,CAACtB,QAAQ,CAACtF,KAAM,CAACH,IAAI,cAAE5B,IAAA,CAACR,UAAU,GAAE,CAAE,CAAAwC,QAAA,eAC9DhC,IAAA,CAAC/C,UAAU,EACTmF,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED2G,MAAM,CAACtB,QAAQ,CAACnB,WAAW,CAClB,CAAC,cACblG,IAAA,CAACzC,IAAI,EAAC4E,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5C2G,MAAM,CAACtB,QAAQ,CAACuB,KAAK,CAAClF,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBACnC1D,KAAA,CAAC1C,QAAQ,EAAW2E,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACAhC,IAAA,CAACtC,YAAY,EAACyE,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACAhC,IAAA,CAACR,UAAU,EAAC2D,KAAK,CAAC,OAAO,CAAE,CAAC,CAChB,CAAC,cACfnD,IAAA,CAACvC,YAAY,EACXgF,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CACH,CAAC,GAnBWiD,GAoBL,CACX,CAAC,CACE,CAAC,EACI,CAAC,cAGd5D,IAAA,CAAC6B,WAAW,EAACE,KAAK,CAAE4G,MAAM,CAACrB,QAAQ,CAACvF,KAAM,CAACH,IAAI,cAAE5B,IAAA,CAACpB,QAAQ,GAAE,CAAE,CAAAoD,QAAA,cAC5DhC,IAAA,CAAC/C,UAAU,EACTkF,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED2G,MAAM,CAACrB,QAAQ,CAACpB,WAAW,CAClB,CAAC,CACF,CAAC,CAGb,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACxC,GAAG,CAAEsC,GAAG,EAAK,CAC5B,KAAM,CAAAjB,OAAO,CAAG4D,MAAM,CAAC,UAAU3C,GAAG,EAAE,CAAC,CACvC,GAAI,CAACjB,OAAO,CAAE,MAAO,KAAI,CAEzB,mBACE/E,IAAA,CAAC6B,WAAW,EAAWE,KAAK,CAAEgD,OAAO,CAAChD,KAAM,CAACH,IAAI,cAAE5B,IAAA,CAAChB,WAAW,GAAE,CAAE,CAAAgD,QAAA,cACjE9B,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3C+C,OAAO,CAACmB,WAAW,eAClBlG,IAAA,CAAC/C,UAAU,EACTmF,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED+C,OAAO,CAACmB,WAAW,CACV,CACb,CACAnB,OAAO,CAAC6D,KAAK,eACZ5I,IAAA,CAACzC,IAAI,EAAC4E,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5C+C,OAAO,CAAC6D,KAAK,CAAClF,GAAG,CAAC,CAACC,IAAI,CAAEC,GAAG,gBAC3B1D,KAAA,CAAC1C,QAAQ,EAAW2E,EAAE,CAAE,CACtB0B,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmD,EAAE,CAAEnD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBoC,aAAa,CAAE,KAAK,CACpBC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,eACAhC,IAAA,CAACtC,YAAY,EAACyE,EAAE,CAAE,CAChB4B,QAAQ,CAAE,MAAM,CAChBC,EAAE,CAAErD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CAAE,CAAAqB,QAAA,cACAhC,IAAA,CAACpB,QAAQ,EAACuE,KAAK,CAAC,SAAS,CAAE,CAAC,CAChB,CAAC,cACfnD,IAAA,CAACvC,YAAY,EACXgF,OAAO,CAAEkB,IAAK,CACdxB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CACH,CAAC,GAnBWiD,GAoBL,CACX,CAAC,CACE,CACP,EACE,CAAC,EAxCUoC,GAyCL,CAAC,CAElB,CAAC,CAAC,cAGFhG,IAAA,CAAC5C,OAAO,EAAC+E,EAAE,CAAE,CAAE0F,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1B3H,KAAA,CAAC/C,GAAG,EAAA6E,QAAA,eACFhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED2G,MAAM,CAACxC,OAAO,CAACpE,KAAK,CACX,CAAC,cACb/B,IAAA,CAAC/B,KAAK,EACJ2D,IAAI,cAAE5B,IAAA,CAACZ,SAAS,GAAE,CAAE,CACpB6I,QAAQ,CAAC,MAAM,CACf9F,EAAE,CAAE,CACFG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED2G,MAAM,CAACxC,OAAO,CAACC,KAAK,CAChB,CAAC,EACL,CAAC,EACH,CAAC,CAEV,CAAC,CAED,mBACEpG,IAAA,CAACH,MAAM,EAAAmC,QAAA,cACLhC,IAAA,CAAC7C,GAAG,EACFgF,EAAE,CAAE,CACFmC,EAAE,CAAE,CAAC,CACLuE,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmB/K,KAAK,CAACyC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAK3E,KAAK,CAACyC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHJ,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAqB,QAAA,cAEFhC,IAAA,CAAChD,SAAS,EAAC+L,QAAQ,CAAC,IAAI,CAAC5G,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,cAChEhC,IAAA,CAAC1C,IAAI,EAAC0L,EAAE,MAACC,OAAO,CAAE,IAAK,CAAAjH,QAAA,cACrB9B,KAAA,CAAChD,KAAK,EACJgF,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACF+G,CAAC,CAAE,CAAEnB,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnB5F,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC,KAAK,CAAE,CACL2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAC,CACD,uBAAuB,CAAE,CACvBqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAC,CACD,iBAAiB,CAAE,CACjB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAC,CACD,qBAAqB,CAAE,CACrB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CACF,CAAE,CAAAqB,QAAA,eAGF9B,KAAA,CAAC/C,GAAG,EAACgF,EAAE,CAAE,CACPC,EAAE,CAAE,CAAC,CACLY,SAAS,CAAE,QAAQ,CACnBV,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAqB,QAAA,eACAhC,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,IAAI,CACZ+F,SAAS,CAAC,IAAI,CACdhH,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLiB,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCJ,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAE,QACb,CAAE,CAAAhB,QAAA,CAEDrB,KAAK,CAAG,eAAe,CAAG,mBAAmB,CACpC,CAAC,cACbX,IAAA,CAAC/C,UAAU,EACTmG,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,gBAAgB,CACtBhB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAE,QACb,CAAE,CAAAhB,QAAA,CAEDrB,KAAK,CACF,iEAAiE,CACjE,4DAA4D,CAEtD,CAAC,EACV,CAAC,cAGNX,IAAA,CAAC7C,GAAG,EAACgF,EAAE,CAAE,CAAEiH,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEjH,EAAE,CAAE,CAAC,CAAEE,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,cAC5FhC,IAAA,CAACrC,IAAI,EACHwG,KAAK,CAAErD,SAAU,CACjBwI,QAAQ,CAAEjI,eAAgB,CAC1B+B,OAAO,CAAC,YAAY,CACpBmG,aAAa,CAAC,MAAM,CACpBpH,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChBmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDkI,SAAS,CAAE,EAAE,CACb7F,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAC,CACD,0BAA0B,CAAE,CAC1B2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CACF,CAAE,CAAAqB,QAAA,CAEDN,UAAU,CAACgC,GAAG,CAAC,CAAC8F,GAAG,CAAEpF,KAAK,gBACzBpE,IAAA,CAACpC,GAAG,EAEFgE,IAAI,CAAE4H,GAAG,CAAC5H,IAAK,CACfD,KAAK,CAAE6H,GAAG,CAAC7H,KAAM,CACjB8H,YAAY,CAAC,OAAO,CACpBtH,EAAE,CAAE,CACFY,aAAa,CAAEpC,KAAK,CAAG,aAAa,CAAG,KAAK,CAC5CqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC,uBAAuB,CAAE,CACvBsC,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CACF,CAAE,EAXGyD,KAYN,CACF,CAAC,CACE,CAAC,CACJ,CAAC,cAGNpE,IAAA,CAACiE,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClCuC,wBAAwB,CAAC,CAAC,CACnB,CAAC,cAEXvE,IAAA,CAACiE,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClCwD,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXxF,IAAA,CAACiE,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClCwE,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXxG,IAAA,CAACiE,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClCkF,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXlH,IAAA,CAACiE,QAAQ,EAACE,KAAK,CAAErD,SAAU,CAACsD,KAAK,CAAE,CAAE,CAAApC,QAAA,CAClC0G,kBAAkB,CAAC,CAAC,CACb,CAAC,EAEN,CAAC,CACJ,CAAC,CACE,CAAC,CACT,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAArI,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}