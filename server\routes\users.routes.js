const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const mysql = require('mysql2/promise');
const crypto = require('crypto');
const pool = require('../config/database');
const emailService = require('../utils/emailService');
const usersController = require('../controllers/users.controller');
const { authenticateToken } = require('../middleware/auth');

// Configure multer for profile picture uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/profile-pictures');
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, 'profile-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// لا حاجة لإعداد nodemailer - سنستخدم emailService

// دالة للتحقق من صحة المنطقة الزمنية
const isValidTimezone = (timezone) => {
  try {
    Intl.DateTimeFormat(undefined, { timeZone: timezone });
    return true;
  } catch (e) {
    return false;
  }
};

// دالة لتحويل UTC offset إلى تاريخ مع المنطقة الزمنية الصحيحة
const formatDateWithUTCOffset = (date, utcOffset, locale = 'ar-EG', options = {}) => {
  if (!utcOffset || !utcOffset.match(/^UTC[+-]\d{2}:\d{2}$/)) {
    // إذا لم تكن بصيغة UTC، جرب استخدامها مباشرة
    try {
      return date.toLocaleString(locale, { ...options, timeZone: utcOffset });
    } catch (e) {
      // إذا فشلت، استخدم التوقيت المحلي
      return date.toLocaleString(locale, options);
    }
  }

  // استخراج الإزاحة من UTC+08:00
  const match = utcOffset.match(/^UTC([+-])(\d{2}):(\d{2})$/);
  if (!match) {
    return date.toLocaleString(locale, options);
  }

  const sign = match[1] === '+' ? 1 : -1;
  const hours = parseInt(match[2]);
  const minutes = parseInt(match[3]);
  const offsetMinutes = sign * (hours * 60 + minutes);

  // إنشاء تاريخ جديد مع الإزاحة
  const utcTime = date.getTime() + (date.getTimezoneOffset() * 60000);
  const targetTime = new Date(utcTime + (offsetMinutes * 60000));

  // تنسيق التاريخ
  return targetTime.toLocaleString(locale, options);
};

// All routes require authentication
router.use(authenticateToken);

// Profile routes
router.get('/profile', async (req, res) => {
  try {
    const userId = req.user.id;

    const [userRows] = await pool.execute(
      'SELECT id, email, full_name, status, delete_scheduled_at, role FROM users WHERE id = ?',
      [userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = userRows[0];
    let timezone = 'Africa/Cairo'; // القيمة الافتراضية

    // جلب المنطقة الزمنية من الجدول المناسب
    if (user.role === 'platform_teacher' || user.role === 'new_teacher') {
      const [teacherRows] = await pool.execute(
        'SELECT timezone FROM teacher_profiles WHERE user_id = ?',
        [userId]
      );
      if (teacherRows.length > 0 && teacherRows[0].timezone) {
        timezone = teacherRows[0].timezone;
      }
    } else if (user.role === 'student') {
      const [studentRows] = await pool.execute(
        'SELECT timezone FROM student_completion_data WHERE user_id = ?',
        [userId]
      );
      if (studentRows.length > 0 && studentRows[0].timezone) {
        timezone = studentRows[0].timezone;
      }
    }

    // إضافة المنطقة الزمنية للاستجابة
    user.timezone = timezone;

    res.json({
      success: true,
      user: user
    });

  } catch (error) {
    console.error('Error fetching user profile:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch user profile'
    });
  }
});

router.put('/profile', usersController.updateProfile);
router.put('/password', usersController.changePassword);
router.post('/upload-profile-picture', upload.single('profilePicture'), usersController.uploadProfilePicture);

// حفظ المنطقة الزمنية للمستخدم
router.put('/timezone', async (req, res) => {
  try {
    const userId = req.user.id;
    const { timezone } = req.body;

    if (!timezone) {
      return res.status(400).json({
        success: false,
        message: 'Timezone is required'
      });
    }

    // المنطقة الزمنية كما هي
    const userTimezone = timezone;

    // جلب دور المستخدم
    const [userRows] = await pool.execute(
      'SELECT role FROM users WHERE id = ?',
      [userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const userRole = userRows[0].role;

    // حفظ المنطقة الزمنية في الجدول المناسب
    if (userRole === 'platform_teacher' || userRole === 'new_teacher') {
      await pool.execute(
        'UPDATE teacher_profiles SET timezone = ? WHERE user_id = ?',
        [userTimezone, userId]
      );
    } else if (userRole === 'student') {
      await pool.execute(
        'UPDATE student_completion_data SET timezone = ? WHERE user_id = ?',
        [userTimezone, userId]
      );
    }

    res.json({
      success: true,
      message: 'Timezone updated successfully'
    });

  } catch (error) {
    console.error('Error updating timezone:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update timezone'
    });
  }
});

// Account deletion routes
router.post('/request-delete', async (req, res) => {
  try {
    const userId = req.user.id;

    // التحقق من وجود المستخدم
    const [userRows] = await pool.execute(
      'SELECT email, full_name FROM users WHERE id = ?',
      [userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = userRows[0];

    // تنظيف الأكواد المنتهية الصلاحية
    await pool.execute(
      'DELETE FROM user_delete_requests WHERE expires_at <= NOW()'
    );

    // إنشاء رمز التحقق
    const deleteCode = crypto.randomInt(100000, 999999).toString();

    // حفظ رمز التحقق في قاعدة البيانات (استخدام MySQL لحساب وقت الانتهاء)
    await pool.execute(
      `INSERT INTO user_delete_requests (user_id, delete_code, expires_at, created_at)
       VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 15 MINUTE), NOW())
       ON DUPLICATE KEY UPDATE
       delete_code = VALUES(delete_code),
       expires_at = DATE_ADD(NOW(), INTERVAL 15 MINUTE),
       created_at = NOW()`,
      [userId, deleteCode]
    );

    console.log(`Generated delete code for user ${userId}: ${deleteCode}`);

    // التحقق من حفظ الكود
    const [savedCodeRows] = await pool.execute(
      'SELECT delete_code, expires_at, NOW() as server_time FROM user_delete_requests WHERE user_id = ?',
      [userId]
    );
    console.log('Saved code in database:', savedCodeRows[0]);

    // إرسال الإيميل
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #4a6da7; margin: 0;">Allemnionline</h1>
        </div>

        <!-- Arabic Section -->
        <div dir="rtl" style="margin-bottom: 40px; padding: 20px; background-color: #f9f9f9; border-radius: 5px;">
          <h2 style="color: #4a6da7; text-align: center; margin-bottom: 20px;">رمز تأكيد حذف الحساب</h2>
          <p style="font-size: 16px;">مرحباً ${user.full_name}!</p>
          <p style="font-size: 16px;">لقد طلبت حذف حسابك على منصة Allemnionline. لتأكيد هذا الإجراء، يرجى استخدام الرمز التالي:</p>
          <div style="background-color: #fff; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px; border: 2px solid #4a6da7;">
            ${deleteCode}
          </div>
          <p style="font-size: 16px;"><strong>تنبيه: هذا الرمز صالح لمدة 15 دقيقة فقط.</strong></p>
          <p style="font-size: 16px;">إذا لم تطلب حذف حسابك، يرجى تجاهل هذا الإيميل.</p>
        </div>

        <!-- English Section -->
        <div dir="ltr" style="margin-bottom: 30px; padding: 20px; background-color: #f0f4f8; border-radius: 5px;">
          <h2 style="color: #4a6da7; text-align: center; margin-bottom: 20px;">Account Deletion Verification Code</h2>
          <p style="font-size: 16px;">Hello ${user.full_name}!</p>
          <p style="font-size: 16px;">You have requested to delete your account on Allemnionline platform. To confirm this action, please use the following code:</p>
          <div style="background-color: #fff; padding: 15px; text-align: center; font-size: 24px; font-weight: bold; letter-spacing: 5px; margin: 20px 0; border-radius: 5px; border: 2px solid #4a6da7;">
            ${deleteCode}
          </div>
          <p style="font-size: 16px;"><strong>Warning: This code is valid for 15 minutes only.</strong></p>
          <p style="font-size: 16px;">If you did not request account deletion, please ignore this email.</p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            Visit our platform: <a href="https://allemnionline.com" style="color: #4a6da7; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
          </p>
        </div>
      </div>
    `;

    await emailService.sendEmail({
      to: user.email,
      subject: 'رمز تأكيد حذف الحساب - Account Deletion Code - Allemnionline',
      html: emailHtml
    });

    res.json({
      success: true,
      message: 'Delete verification code sent to your email'
    });

  } catch (error) {
    console.error('Error requesting account deletion:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send delete verification code'
    });
  }
});

// تحقق من رمز الحذف وجدولة الحذف
router.post('/verify-delete', async (req, res) => {
  try {
    const userId = req.user.id;
    const { code, timezone } = req.body; // إضافة المنطقة الزمنية

    if (!code) {
      return res.status(400).json({
        success: false,
        message: 'Verification code is required'
      });
    }

    console.log(`Verifying delete code for user ${userId}: ${code}`);

    // تنظيف الأكواد المنتهية الصلاحية
    await pool.execute(
      'DELETE FROM user_delete_requests WHERE expires_at <= NOW()'
    );

    // جلب المنطقة الزمنية للمستخدم من الجداول المناسبة
    const [userRows] = await pool.execute(
      'SELECT role FROM users WHERE id = ?',
      [userId]
    );

    let userTimezone = 'Africa/Cairo'; // القيمة الافتراضية

    if (userRows.length > 0) {
      const userRole = userRows[0].role;

      if (userRole === 'platform_teacher' || userRole === 'new_teacher') {
        // للمعلمين: جلب من teacher_profiles
        const [teacherRows] = await pool.execute(
          'SELECT timezone FROM teacher_profiles WHERE user_id = ?',
          [userId]
        );
        if (teacherRows.length > 0 && teacherRows[0].timezone) {
          userTimezone = teacherRows[0].timezone;
        }
      } else if (userRole === 'student') {
        // للطلاب: جلب من student_completion_data
        const [studentRows] = await pool.execute(
          'SELECT timezone FROM student_completion_data WHERE user_id = ?',
          [userId]
        );
        if (studentRows.length > 0 && studentRows[0].timezone) {
          userTimezone = studentRows[0].timezone;
        }
      }

      // إذا لم تكن محفوظة وتم إرسالها من الـ frontend، احفظها
      if (userTimezone === 'Africa/Cairo' && timezone) {
        userTimezone = timezone;
        if (userRole === 'platform_teacher' || userRole === 'new_teacher') {
          await pool.execute(
            'UPDATE teacher_profiles SET timezone = ? WHERE user_id = ?',
            [userTimezone, userId]
          );
        } else if (userRole === 'student') {
          await pool.execute(
            'UPDATE student_completion_data SET timezone = ? WHERE user_id = ?',
            [userTimezone, userId]
          );
        }
      }
    }

    // التحقق من وجود الكود أولاً
    const [allCodesRows] = await pool.execute(
      'SELECT *, NOW() as server_time FROM user_delete_requests WHERE user_id = ?',
      [userId]
    );

    console.log('All codes for user:', allCodesRows);
    console.log('Provided code:', code);

    // التحقق من رمز التحقق
    const [codeRows] = await pool.execute(
      'SELECT *, NOW() as server_time FROM user_delete_requests WHERE user_id = ? AND delete_code = ? AND expires_at > NOW()',
      [userId, code]
    );

    console.log('Matching codes:', codeRows);

    if (codeRows.length === 0) {
      // التحقق من سبب الفشل
      const [expiredRows] = await pool.execute(
        'SELECT *, NOW() as server_time FROM user_delete_requests WHERE user_id = ? AND delete_code = ?',
        [userId, code]
      );

      if (expiredRows.length > 0) {
        console.log('Code found but expired:', expiredRows[0]);
        return res.status(400).json({
          success: false,
          message: 'Verification code has expired. Please request a new one.'
        });
      } else {
        console.log('Code not found at all');
        return res.status(400).json({
          success: false,
          message: 'Invalid verification code'
        });
      }
    }

    // حساب وقت الحذف بعد 10 أيام حسب المنطقة الزمنية للمستخدم
    const now = new Date();
    const deleteAt = new Date(now.getTime() + 10 * 24 * 60 * 60 * 1000);

    // حفظ التوقيت كـ UTC في قاعدة البيانات
    await pool.execute(
      'UPDATE users SET delete_scheduled_at = ?, status = ? WHERE id = ?',
      [deleteAt, 'pending_deletion', userId]
    );

    // حذف طلب الحذف
    await pool.execute(
      'DELETE FROM user_delete_requests WHERE user_id = ?',
      [userId]
    );

    // الحصول على بيانات المستخدم لإرسال إيميل التأكيد
    const [emailUserRows] = await pool.execute(
      'SELECT email, full_name FROM users WHERE id = ?',
      [userId]
    );

    if (emailUserRows.length > 0) {
      const user = emailUserRows[0];

      // استخدام المنطقة الزمنية المحفوظة من الخطوة السابقة
      const emailTimezone = userTimezone;

      const deleteDate = formatDateWithUTCOffset(new Date(deleteAt), emailTimezone, 'ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });

      const deleteDateEn = formatDateWithUTCOffset(new Date(deleteAt), emailTimezone, 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });

      // إرسال إيميل تأكيد جدولة الحذف
      const confirmationEmailHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #4a6da7; margin: 0;">Allemnionline</h1>
          </div>

          <!-- Arabic Section -->
          <div dir="rtl" style="margin-bottom: 40px; padding: 20px; background-color: #fff3e0; border-radius: 5px; border-left: 4px solid #ff9800;">
            <h2 style="color: #ff9800; text-align: center; margin-bottom: 20px;">⏰ تم جدولة حذف الحساب</h2>
            <p style="font-size: 16px;">مرحباً ${user.full_name}!</p>
            <p style="font-size: 16px;">تم تأكيد طلب حذف حسابك على منصة Allemnionline.</p>
            <p style="font-size: 16px;"><strong>موعد الحذف المجدول: ${deleteDate}</strong></p>
            <p style="font-size: 14px; color: #666;">المنطقة الزمنية: ${emailTimezone}</p>
            <p style="font-size: 16px; color: #4caf50;"><strong>يمكنك إلغاء عملية الحذف في أي وقت قبل الموعد المحدد من خلال صفحة الملف الشخصي.</strong></p>
            <p style="font-size: 16px;">إذا لم تقم بطلب حذف الحساب، يرجى التواصل معنا فوراً على: <EMAIL></p>
          </div>

          <!-- English Section -->
          <div dir="ltr" style="margin-bottom: 30px; padding: 20px; background-color: #f0f4f8; border-radius: 5px; border-left: 4px solid #ff9800;">
            <h2 style="color: #ff9800; text-align: center; margin-bottom: 20px;">⏰ Account Deletion Scheduled</h2>
            <p style="font-size: 16px;">Hello ${user.full_name}!</p>
            <p style="font-size: 16px;">Your account deletion request on Allemnionline platform has been confirmed.</p>
            <p style="font-size: 16px;"><strong>Scheduled deletion time: ${deleteDateEn}</strong></p>
            <p style="font-size: 14px; color: #666;">Timezone: ${emailTimezone}</p>
            <p style="font-size: 16px; color: #4caf50;"><strong>You can cancel the deletion process at any time before the scheduled time through your profile page.</strong></p>
            <p style="font-size: 16px;">If you did not request account deletion, please contact us immediately at: <EMAIL></p>
          </div>

          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
            <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
              Visit our platform: <a href="https://allemnionline.com" style="color: #4a6da7; text-decoration: none;">allemnionline.com</a>
            </p>
            <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
              &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
            </p>
          </div>
        </div>
      `;

      try {
        await emailService.sendEmail({
          to: user.email,
          subject: 'تم جدولة حذف الحساب - Account Deletion Scheduled - Allemnionline',
          html: confirmationEmailHtml
        });
      } catch (emailError) {
        console.error('Error sending deletion confirmation email:', emailError);
        // لا نفشل العملية إذا فشل إرسال الإيميل
      }
    }

    res.json({
      success: true,
      message: 'Account deletion scheduled for 10 days from now'
    });

  } catch (error) {
    console.error('Error verifying delete code:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify delete code'
    });
  }
});

// إلغاء عملية حذف الحساب
router.post('/cancel-delete', async (req, res) => {
  try {
    const userId = req.user.id;

    // التحقق من وجود المستخدم وحالة الحذف
    const [userRows] = await pool.execute(
      'SELECT id, email, full_name, status, delete_scheduled_at FROM users WHERE id = ?',
      [userId]
    );

    if (userRows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const user = userRows[0];

    if (user.status !== 'pending_deletion') {
      return res.status(400).json({
        success: false,
        message: 'No pending deletion found for this account'
      });
    }

    // إلغاء جدولة الحذف
    await pool.execute(
      'UPDATE users SET delete_scheduled_at = NULL, status = ? WHERE id = ?',
      ['active', userId]
    );

    // حذف أي طلبات حذف معلقة
    await pool.execute(
      'DELETE FROM user_delete_requests WHERE user_id = ?',
      [userId]
    );

    // إرسال إيميل تأكيد الإلغاء
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #4a6da7; margin: 0;">Allemnionline</h1>
        </div>

        <!-- Arabic Section -->
        <div dir="rtl" style="margin-bottom: 40px; padding: 20px; background-color: #f0f8f0; border-radius: 5px; border-left: 4px solid #4caf50;">
          <h2 style="color: #4caf50; text-align: center; margin-bottom: 20px;">✅ تم إلغاء حذف الحساب بنجاح</h2>
          <p style="font-size: 16px;">مرحباً ${user.full_name}!</p>
          <p style="font-size: 16px;">تم إلغاء عملية حذف حسابك على منصة Allemnionline بنجاح.</p>
          <p style="font-size: 16px;">حسابك الآن نشط ويمكنك الاستمرار في استخدام المنصة بشكل طبيعي.</p>
          <p style="font-size: 16px; color: #d32f2f;"><strong>إذا لم تقم بإلغاء عملية الحذف، يرجى التواصل معنا فوراً على: <EMAIL></strong></p>
        </div>

        <!-- English Section -->
        <div dir="ltr" style="margin-bottom: 30px; padding: 20px; background-color: #f0f4f8; border-radius: 5px; border-left: 4px solid #4caf50;">
          <h2 style="color: #4caf50; text-align: center; margin-bottom: 20px;">✅ Account Deletion Successfully Cancelled</h2>
          <p style="font-size: 16px;">Hello ${user.full_name}!</p>
          <p style="font-size: 16px;">Your account deletion process on Allemnionline platform has been successfully cancelled.</p>
          <p style="font-size: 16px;">Your account is now active and you can continue using the platform normally.</p>
          <p style="font-size: 16px; color: #d32f2f;"><strong>If you did not cancel the deletion process, please contact us immediately at: <EMAIL></strong></p>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0;">
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            Visit our platform: <a href="https://allemnionline.com" style="color: #4a6da7; text-decoration: none;">allemnionline.com</a>
          </p>
          <p style="margin: 0; font-size: 12px; color: #777; text-align: center;">
            &copy; ${new Date().getFullYear()} Allemnionline. All rights reserved.
          </p>
        </div>
      </div>
    `;

    try {
      await emailService.sendEmail({
        to: user.email,
        subject: 'تم إلغاء حذف الحساب - Account Deletion Cancelled - Allemnionline',
        html: emailHtml
      });
    } catch (emailError) {
      console.error('Error sending cancellation email:', emailError);
      // لا نفشل العملية إذا فشل إرسال الإيميل
    }

    res.json({
      success: true,
      message: 'Account deletion cancelled successfully'
    });

  } catch (error) {
    console.error('Error cancelling account deletion:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel account deletion'
    });
  }
});

module.exports = router;
