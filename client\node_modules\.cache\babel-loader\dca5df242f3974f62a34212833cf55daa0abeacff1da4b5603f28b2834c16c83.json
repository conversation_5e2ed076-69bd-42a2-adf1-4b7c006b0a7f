{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useLocation,useNavigate}from'react-router-dom';import{Container,Typography,Paper,Box,Divider,useTheme,Fade,List,ListItem,ListItemText,ListItemIcon,Tabs,Tab,Card,CardContent,alpha,Stack}from'@mui/material';import{Security as SecurityIcon,Schedule as ScheduleIcon,Person as PersonIcon,School as SchoolIcon,Info as InfoIcon,Payment as PaymentIcon,Refresh as RefreshIcon,Description as DescriptionIcon}from'@mui/icons-material';import Layout from'../components/Layout';import termsConditions from'../i18n/translations/termsConditions';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PlatformPolicy=()=>{const{t,i18n}=useTranslation();const theme=useTheme();const location=useLocation();const navigate=useNavigate();const[isRTL,setIsRTL]=useState(i18n.language==='ar');const[activeTab,setActiveTab]=useState(0);useEffect(()=>{setIsRTL(i18n.language==='ar');document.dir=i18n.language==='ar'?'rtl':'ltr';// Handle URL-based navigation to specific sections\nconst hash=location.hash;if(hash==='#terms')setActiveTab(0);else if(hash==='#privacy')setActiveTab(1);else if(hash==='#booking')setActiveTab(2);else if(hash==='#payment')setActiveTab(3);else if(hash==='#refund')setActiveTab(4);},[i18n.language,location.hash]);const handleTabChange=(event,newValue)=>{setActiveTab(newValue);const tabs=['#terms','#privacy','#booking','#payment','#refund'];navigate(`/platform-policy${tabs[newValue]}`,{replace:true});};const tabsConfig=[{label:i18n.language==='ar'?'الشروط والأحكام':'Terms & Conditions',icon:/*#__PURE__*/_jsx(DescriptionIcon,{})},{label:i18n.language==='ar'?'سياسة الخصوصية':'Privacy Policy',icon:/*#__PURE__*/_jsx(SecurityIcon,{})},{label:i18n.language==='ar'?'سياسة الحجز والإلغاء':'Booking & Cancellation',icon:/*#__PURE__*/_jsx(ScheduleIcon,{})},{label:i18n.language==='ar'?'سياسة الدفع':'Payment Policy',icon:/*#__PURE__*/_jsx(PaymentIcon,{})},{label:i18n.language==='ar'?'سياسة الاسترداد':'Refund Policy',icon:/*#__PURE__*/_jsx(RefreshIcon,{})}];// Helper component for section cards\nconst SectionCard=_ref=>{let{title,children,icon,bg}=_ref;return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{mb:4,borderRadius:3,...(isRTL?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:bg||alpha(theme.palette.primary.main,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2,flexDirection:isRTL?'row-reverse':'row'},children:[icon&&/*#__PURE__*/_jsx(Box,{sx:{mr:isRTL?0:2,ml:isRTL?2:0,color:theme.palette.primary.main},children:icon}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif'},children:title})]}),children]})});};// Helper function to render subsections\nconst renderSubSection=section=>/*#__PURE__*/_jsxs(Box,{sx:{mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:1,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:section.title}),section.description&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:section.description}),section.points&&/*#__PURE__*/_jsx(List,{sx:{pl:isRTL?0:2,pr:isRTL?2:0},children:section.points.map((point,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:point,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))}),section.items&&/*#__PURE__*/_jsx(List,{sx:{pl:isRTL?0:2,pr:isRTL?2:0},children:section.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]});// Tab panel component\nconst TabPanel=_ref2=>{let{children,value,index}=_ref2;return/*#__PURE__*/_jsx(\"div\",{hidden:value!==index,children:value===index&&/*#__PURE__*/_jsx(Box,{sx:{py:3},children:children})});};// Render Terms and Conditions\nconst renderTermsAndConditions=()=>{const termsContent=i18n.language==='ar'?termsConditions.ar.termsConditions:termsConditions.en.termsConditions;return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:3,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:termsContent.title}),/*#__PURE__*/_jsx(Stack,{spacing:3,children:termsContent.content.split(/\\n\\s*\\n/).filter(section=>section.trim()!=='').map((section,idx)=>{const lines=section.split('\\n');const title=lines[0];const body=lines.slice(1).join('\\n');return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{borderRadius:3,...(isRTL?{borderRight:`4px solid ${theme.palette.primary.main}`}:{borderLeft:`4px solid ${theme.palette.primary.main}`}),backgroundColor:idx%2===0?alpha(theme.palette.primary.main,0.02):alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:title}),body&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',whiteSpace:'pre-line'},children:body})]})},idx);})})]});};// Render Privacy Policy\nconst renderPrivacyPolicy=()=>{const privacy=t('privacy',{returnObjects:true});return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:privacy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:4,lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:privacy.intro}),/*#__PURE__*/_jsx(Divider,{sx:{mb:4}}),[1,2,3,4,5,6,7,8,9].map(num=>{const section=privacy[`section${num}`];if(!section)return null;return/*#__PURE__*/_jsxs(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(SecurityIcon,{}),children:[section.subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:section.subtitle}),section.description&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:section.description}),section.item1&&/*#__PURE__*/_jsx(List,{children:[section.item1,section.item2,section.item3,section.item4,section.item5].filter(Boolean).map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item})]},idx))})]},num);})]});};// Render Booking & Cancellation Policy\nconst renderBookingPolicy=()=>{const policy=t('policies.bookingCancellation',{returnObjects:true});return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:4,lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:4}}),/*#__PURE__*/_jsxs(SectionCard,{title:policy.studentPolicy.title,icon:/*#__PURE__*/_jsx(PersonIcon,{}),children:[renderSubSection(policy.studentPolicy.booking),renderSubSection(policy.studentPolicy.cancellation),renderSubSection(policy.studentPolicy.rescheduling),renderSubSection(policy.studentPolicy.lateArrival)]}),/*#__PURE__*/_jsxs(SectionCard,{title:policy.tutorPolicy.title,icon:/*#__PURE__*/_jsx(SchoolIcon,{}),bg:alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02),children:[renderSubSection(policy.tutorPolicy.availability),renderSubSection(policy.tutorPolicy.cancellation),renderSubSection(policy.tutorPolicy.rescheduling),renderSubSection(policy.tutorPolicy.lateArrival)]}),/*#__PURE__*/_jsx(SectionCard,{title:policy.generalNotes.title,icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsx(List,{children:policy.generalNotes.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"info\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})})]});};// Render Payment Policy\nconst renderPaymentPolicy=()=>{const policy=t('policies.bookingPayment',{returnObjects:true});return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:policy.title}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:4,lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:4}}),[1,2,3,4,5,6,7,8].map(num=>{const section=policy[`section${num}`];if(!section)return null;return/*#__PURE__*/_jsxs(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(PaymentIcon,{}),children:[section.description&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:section.description}),section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((point,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:point,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]},num);})]});};// Render Refund Policy\nconst renderRefundPolicy=()=>{const policy=t('policies.refund',{returnObjects:true});return/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:policy.title}),/*#__PURE__*/_jsx(Divider,{sx:{mb:4}}),[1,2,3,4,5,6,7,8].map(num=>{const section=policy[`section${num}`];if(!section)return null;return/*#__PURE__*/_jsxs(SectionCard,{title:section.title,icon:/*#__PURE__*/_jsx(RefreshIcon,{}),children:[section.description&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:section.description}),section.items&&/*#__PURE__*/_jsx(List,{children:section.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))}),section.points&&/*#__PURE__*/_jsx(List,{children:section.points.map((point,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:point,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]},num);})]});};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Box,{sx:{py:4,minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:1000,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:2,md:4},mb:4,borderRadius:3,direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:4,textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",component:\"h1\",sx:{mb:2,fontWeight:800,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main},children:isRTL?'سياسات المنصة':'Platform Policies'}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'},children:isRTL?'جميع السياسات والشروط الخاصة بمنصة علّمني أون لاين في مكان واحد':'All Allemnionline platform policies and terms in one place'})]}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider',mb:3},children:/*#__PURE__*/_jsx(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{'& .MuiTab-root':{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',minHeight:64}},children:tabsConfig.map((tab,index)=>/*#__PURE__*/_jsx(Tab,{icon:tab.icon,label:tab.label,iconPosition:\"start\",sx:{flexDirection:isRTL?'row-reverse':'row','& .MuiTab-iconWrapper':{mr:isRTL?0:1,ml:isRTL?1:0}}},index))})}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:0,children:renderTermsAndConditions()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:1,children:renderPrivacyPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:2,children:renderBookingPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:3,children:renderPaymentPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:4,children:renderRefundPolicy()})]})})})})});};export default PlatformPolicy;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useLocation", "useNavigate", "Container", "Typography", "Paper", "Box", "Divider", "useTheme", "Fade", "List", "ListItem", "ListItemText", "ListItemIcon", "Tabs", "Tab", "Card", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "<PERSON><PERSON>", "Security", "SecurityIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Refresh", "RefreshIcon", "Description", "DescriptionIcon", "Layout", "termsConditions", "jsx", "_jsx", "jsxs", "_jsxs", "PlatformPolicy", "t", "i18n", "theme", "location", "navigate", "isRTL", "setIsRTL", "language", "activeTab", "setActiveTab", "document", "dir", "hash", "handleTabChange", "event", "newValue", "tabs", "replace", "tabsConfig", "label", "icon", "SectionCard", "_ref", "title", "children", "bg", "elevation", "sx", "mb", "borderRadius", "borderRight", "palette", "primary", "main", "borderLeft", "backgroundColor", "display", "alignItems", "flexDirection", "mr", "ml", "color", "variant", "fontWeight", "fontFamily", "renderSubSection", "section", "description", "lineHeight", "points", "pl", "pr", "map", "point", "idx", "py", "min<PERSON><PERSON><PERSON>", "fontSize", "items", "item", "TabPanel", "_ref2", "value", "index", "hidden", "renderTermsAndConditions", "termsContent", "ar", "en", "spacing", "content", "split", "filter", "trim", "lines", "body", "slice", "join", "secondary", "light", "whiteSpace", "renderPrivacyPolicy", "privacy", "returnObjects", "intro", "num", "subtitle", "item1", "item2", "item3", "item4", "item5", "Boolean", "renderBookingPolicy", "policy", "studentPolicy", "booking", "cancellation", "rescheduling", "lateArrival", "tutorPolicy", "availability", "generalNotes", "renderPaymentPolicy", "renderRefundPolicy", "minHeight", "background", "max<PERSON><PERSON><PERSON>", "in", "timeout", "p", "xs", "md", "direction", "textAlign", "component", "borderBottom", "borderColor", "onChange", "scrollButtons", "tab", "iconPosition"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PlatformPolicy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Paper,\r\n  Box,\r\n  Divider,\r\n  useTheme,\r\n  Fade,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Tabs,\r\n  Tab,\r\n  Card,\r\n  CardContent,\r\n  alpha,\r\n  Stack,\r\n} from '@mui/material';\r\nimport {\r\n  Security as SecurityIcon,\r\n  Schedule as ScheduleIcon,\r\n  Person as PersonIcon,\r\n  School as SchoolIcon,\r\n  Info as InfoIcon,\r\n  Payment as PaymentIcon,\r\n  Refresh as RefreshIcon,\r\n  Description as DescriptionIcon,\r\n} from '@mui/icons-material';\r\nimport Layout from '../components/Layout';\r\nimport termsConditions from '../i18n/translations/termsConditions';\r\n\r\nconst PlatformPolicy = () => {\r\n  const { t, i18n } = useTranslation();\r\n  const theme = useTheme();\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const [isRTL, setIsRTL] = useState(i18n.language === 'ar');\r\n  const [activeTab, setActiveTab] = useState(0);\r\n\r\n  useEffect(() => {\r\n    setIsRTL(i18n.language === 'ar');\r\n    document.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n\r\n    // Handle URL-based navigation to specific sections\r\n    const hash = location.hash;\r\n    if (hash === '#terms') setActiveTab(0);\r\n    else if (hash === '#privacy') setActiveTab(1);\r\n    else if (hash === '#booking') setActiveTab(2);\r\n    else if (hash === '#payment') setActiveTab(3);\r\n    else if (hash === '#refund') setActiveTab(4);\r\n  }, [i18n.language, location.hash]);\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    const tabs = ['#terms', '#privacy', '#booking', '#payment', '#refund'];\r\n    navigate(`/platform-policy${tabs[newValue]}`, { replace: true });\r\n  };\r\n\r\n  const tabsConfig = [\r\n    { label: i18n.language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions', icon: <DescriptionIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy', icon: <SecurityIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation', icon: <ScheduleIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الدفع' : 'Payment Policy', icon: <PaymentIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الاسترداد' : 'Refund Policy', icon: <RefreshIcon /> },\r\n  ];\r\n\r\n  // Helper component for section cards\r\n  const SectionCard = ({ title, children, icon, bg }) => (\r\n    <Card\r\n      elevation={2}\r\n      sx={{\r\n        mb: 4,\r\n        borderRadius: 3,\r\n        ...(isRTL\r\n          ? { borderRight: `6px solid ${theme.palette.primary.main}` }\r\n          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\r\n        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),\r\n      }}\r\n    >\r\n      <CardContent>\r\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, flexDirection: isRTL ? 'row-reverse' : 'row' }}>\r\n          {icon && (\r\n            <Box sx={{ mr: isRTL ? 0 : 2, ml: isRTL ? 2 : 0, color: theme.palette.primary.main }}>\r\n              {icon}\r\n            </Box>\r\n          )}\r\n          <Typography\r\n            variant=\"h5\"\r\n            fontWeight={600}\r\n            color={theme.palette.primary.main}\r\n            sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif' }}\r\n          >\r\n            {title}\r\n          </Typography>\r\n        </Box>\r\n        {children}\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n\r\n  // Helper function to render subsections\r\n  const renderSubSection = (section) => (\r\n    <Box sx={{ mb: 3 }}>\r\n      <Typography\r\n        variant=\"h6\"\r\n        fontWeight={600}\r\n        mb={1}\r\n        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n      >\r\n        {section.title}\r\n      </Typography>\r\n      {section.description && (\r\n        <Typography\r\n          variant=\"body1\"\r\n          sx={{ mb: 2, lineHeight: 1.8, fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n        >\r\n          {section.description}\r\n        </Typography>\r\n      )}\r\n      {section.points && (\r\n        <List sx={{ pl: isRTL ? 0 : 2, pr: isRTL ? 2 : 0 }}>\r\n          {section.points.map((point, idx) => (\r\n            <ListItem key={idx} sx={{ py: 0.5 }}>\r\n              <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n              </ListItemIcon>\r\n              <ListItemText\r\n                primary={point}\r\n                sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n              />\r\n            </ListItem>\r\n          ))}\r\n        </List>\r\n      )}\r\n      {section.items && (\r\n        <List sx={{ pl: isRTL ? 0 : 2, pr: isRTL ? 2 : 0 }}>\r\n          {section.items.map((item, idx) => (\r\n            <ListItem key={idx} sx={{ py: 0.5 }}>\r\n              <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n              </ListItemIcon>\r\n              <ListItemText\r\n                primary={item}\r\n                sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n              />\r\n            </ListItem>\r\n          ))}\r\n        </List>\r\n      )}\r\n    </Box>\r\n  );\r\n\r\n  // Tab panel component\r\n  const TabPanel = ({ children, value, index }) => (\r\n    <div hidden={value !== index}>\r\n      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}\r\n    </div>\r\n  );\r\n\r\n  // Render Terms and Conditions\r\n  const renderTermsAndConditions = () => {\r\n    const termsContent = i18n.language === 'ar' ? termsConditions.ar.termsConditions : termsConditions.en.termsConditions;\r\n\r\n    return (\r\n      <Box>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={3}\r\n          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit', color: theme.palette.primary.main }}\r\n        >\r\n          {termsContent.title}\r\n        </Typography>\r\n        <Stack spacing={3}>\r\n          {termsContent.content\r\n            .split(/\\n\\s*\\n/)\r\n            .filter((section) => section.trim() !== '')\r\n            .map((section, idx) => {\r\n              const lines = section.split('\\n');\r\n              const title = lines[0];\r\n              const body = lines.slice(1).join('\\n');\r\n              return (\r\n                <Card\r\n                  key={idx}\r\n                  elevation={2}\r\n                  sx={{\r\n                    borderRadius: 3,\r\n                    ...(isRTL\r\n                      ? { borderRight: `4px solid ${theme.palette.primary.main}` }\r\n                      : { borderLeft: `4px solid ${theme.palette.primary.main}` }),\r\n                    backgroundColor: idx % 2 === 0\r\n                      ? alpha(theme.palette.primary.main, 0.02)\r\n                      : alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02),\r\n                  }}\r\n                >\r\n                  <CardContent>\r\n                    <Typography\r\n                      variant=\"h6\"\r\n                      fontWeight={600}\r\n                      mb={2}\r\n                      sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit', color: theme.palette.primary.main }}\r\n                    >\r\n                      {title}\r\n                    </Typography>\r\n                    {body && (\r\n                      <Typography\r\n                        variant=\"body1\"\r\n                        sx={{\r\n                          lineHeight: 1.8,\r\n                          fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                          whiteSpace: 'pre-line'\r\n                        }}\r\n                      >\r\n                        {body}\r\n                      </Typography>\r\n                    )}\r\n                  </CardContent>\r\n                </Card>\r\n              );\r\n            })}\r\n        </Stack>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Privacy Policy\r\n  const renderPrivacyPolicy = () => {\r\n    const privacy = t('privacy', { returnObjects: true });\r\n\r\n    return (\r\n      <Box>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit', color: theme.palette.primary.main }}\r\n        >\r\n          {privacy.title}\r\n        </Typography>\r\n        <Typography\r\n          variant=\"body1\"\r\n          sx={{ mb: 4, lineHeight: 1.8, fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n        >\r\n          {privacy.intro}\r\n        </Typography>\r\n        <Divider sx={{ mb: 4 }} />\r\n\r\n        {/* Render privacy sections */}\r\n        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => {\r\n          const section = privacy[`section${num}`];\r\n          if (!section) return null;\r\n\r\n          return (\r\n            <SectionCard key={num} title={section.title} icon={<SecurityIcon />}>\r\n              {section.subtitle && (\r\n                <Typography variant=\"body1\" sx={{ mb: 2, fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}>\r\n                  {section.subtitle}\r\n                </Typography>\r\n              )}\r\n              {section.description && (\r\n                <Typography variant=\"body1\" sx={{ mb: 2, fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}>\r\n                  {section.description}\r\n                </Typography>\r\n              )}\r\n              {section.item1 && (\r\n                <List>\r\n                  {[section.item1, section.item2, section.item3, section.item4, section.item5]\r\n                    .filter(Boolean)\r\n                    .map((item, idx) => (\r\n                      <ListItem key={idx}>\r\n                        <ListItemIcon><InfoIcon color=\"primary\" fontSize=\"small\" /></ListItemIcon>\r\n                        <ListItemText primary={item} />\r\n                      </ListItem>\r\n                    ))}\r\n                </List>\r\n              )}\r\n            </SectionCard>\r\n          );\r\n        })}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Booking & Cancellation Policy\r\n  const renderBookingPolicy = () => {\r\n    const policy = t('policies.bookingCancellation', { returnObjects: true });\r\n\r\n    return (\r\n      <Box>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit', color: theme.palette.primary.main }}\r\n        >\r\n          {policy.title}\r\n        </Typography>\r\n        <Typography\r\n          variant=\"body1\"\r\n          sx={{ mb: 4, lineHeight: 1.8, fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n        >\r\n          {policy.subtitle}\r\n        </Typography>\r\n        <Divider sx={{ mb: 4 }} />\r\n\r\n        {/* Student Policy */}\r\n        <SectionCard title={policy.studentPolicy.title} icon={<PersonIcon />}>\r\n          {renderSubSection(policy.studentPolicy.booking)}\r\n          {renderSubSection(policy.studentPolicy.cancellation)}\r\n          {renderSubSection(policy.studentPolicy.rescheduling)}\r\n          {renderSubSection(policy.studentPolicy.lateArrival)}\r\n        </SectionCard>\r\n\r\n        {/* Tutor Policy */}\r\n        <SectionCard\r\n          title={policy.tutorPolicy.title}\r\n          icon={<SchoolIcon />}\r\n          bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}\r\n        >\r\n          {renderSubSection(policy.tutorPolicy.availability)}\r\n          {renderSubSection(policy.tutorPolicy.cancellation)}\r\n          {renderSubSection(policy.tutorPolicy.rescheduling)}\r\n          {renderSubSection(policy.tutorPolicy.lateArrival)}\r\n        </SectionCard>\r\n\r\n        {/* General Notes */}\r\n        <SectionCard title={policy.generalNotes.title} icon={<InfoIcon />}>\r\n          <List>\r\n            {policy.generalNotes.points.map((item, idx) => (\r\n              <ListItem key={idx} sx={{ py: 0.5 }}>\r\n                <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                  <InfoIcon color=\"info\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        </SectionCard>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Payment Policy\r\n  const renderPaymentPolicy = () => {\r\n    const policy = t('policies.bookingPayment', { returnObjects: true });\r\n\r\n    return (\r\n      <Box>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit', color: theme.palette.primary.main }}\r\n        >\r\n          {policy.title}\r\n        </Typography>\r\n        <Typography\r\n          variant=\"body1\"\r\n          sx={{ mb: 4, lineHeight: 1.8, fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n        >\r\n          {policy.subtitle}\r\n        </Typography>\r\n        <Divider sx={{ mb: 4 }} />\r\n\r\n        {/* Render payment sections */}\r\n        {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => {\r\n          const section = policy[`section${num}`];\r\n          if (!section) return null;\r\n\r\n          return (\r\n            <SectionCard key={num} title={section.title} icon={<PaymentIcon />}>\r\n              {section.description && (\r\n                <Typography variant=\"body1\" sx={{ mb: 2, fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}>\r\n                  {section.description}\r\n                </Typography>\r\n              )}\r\n              {section.points && (\r\n                <List>\r\n                  {section.points.map((point, idx) => (\r\n                    <ListItem key={idx} sx={{ py: 0.5 }}>\r\n                      <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                        <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                      </ListItemIcon>\r\n                      <ListItemText\r\n                        primary={point}\r\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                      />\r\n                    </ListItem>\r\n                  ))}\r\n                </List>\r\n              )}\r\n            </SectionCard>\r\n          );\r\n        })}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Refund Policy\r\n  const renderRefundPolicy = () => {\r\n    const policy = t('policies.refund', { returnObjects: true });\r\n\r\n    return (\r\n      <Box>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit', color: theme.palette.primary.main }}\r\n        >\r\n          {policy.title}\r\n        </Typography>\r\n        <Divider sx={{ mb: 4 }} />\r\n\r\n        {/* Render refund sections */}\r\n        {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => {\r\n          const section = policy[`section${num}`];\r\n          if (!section) return null;\r\n\r\n          return (\r\n            <SectionCard key={num} title={section.title} icon={<RefreshIcon />}>\r\n              {section.description && (\r\n                <Typography variant=\"body1\" sx={{ mb: 2, fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}>\r\n                  {section.description}\r\n                </Typography>\r\n              )}\r\n              {section.items && (\r\n                <List>\r\n                  {section.items.map((item, idx) => (\r\n                    <ListItem key={idx} sx={{ py: 0.5 }}>\r\n                      <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                        <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                      </ListItemIcon>\r\n                      <ListItemText\r\n                        primary={item}\r\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                      />\r\n                    </ListItem>\r\n                  ))}\r\n                </List>\r\n              )}\r\n              {section.points && (\r\n                <List>\r\n                  {section.points.map((point, idx) => (\r\n                    <ListItem key={idx} sx={{ py: 0.5 }}>\r\n                      <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                        <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                      </ListItemIcon>\r\n                      <ListItemText\r\n                        primary={point}\r\n                        sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                      />\r\n                    </ListItem>\r\n                  ))}\r\n                </List>\r\n              )}\r\n            </SectionCard>\r\n          );\r\n        })}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Layout>\r\n      <Box\r\n        sx={{\r\n          py: 4,\r\n          minHeight: '100vh',\r\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\r\n        }}\r\n      >\r\n        <Container maxWidth=\"lg\">\r\n          <Fade in timeout={1000}>\r\n            <Paper\r\n              elevation={3}\r\n              sx={{\r\n                p: { xs: 2, md: 4 },\r\n                mb: 4,\r\n                borderRadius: 3,\r\n                direction: isRTL ? 'rtl' : 'ltr',\r\n              }}\r\n            >\r\n              {/* Header */}\r\n              <Box sx={{ mb: 4, textAlign: 'center' }}>\r\n                <Typography\r\n                  variant=\"h3\"\r\n                  component=\"h1\"\r\n                  sx={{\r\n                    mb: 2,\r\n                    fontWeight: 800,\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    color: theme.palette.primary.main,\r\n                  }}\r\n                >\r\n                  {isRTL ? 'سياسات المنصة' : 'Platform Policies'}\r\n                </Typography>\r\n                <Typography\r\n                  variant=\"subtitle1\"\r\n                  color=\"text.secondary\"\r\n                  sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                >\r\n                  {isRTL\r\n                    ? 'جميع السياسات والشروط الخاصة بمنصة علّمني أون لاين في مكان واحد'\r\n                    : 'All Allemnionline platform policies and terms in one place'\r\n                  }\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Tabs */}\r\n              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>\r\n                <Tabs\r\n                  value={activeTab}\r\n                  onChange={handleTabChange}\r\n                  variant=\"scrollable\"\r\n                  scrollButtons=\"auto\"\r\n                  sx={{\r\n                    '& .MuiTab-root': {\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                      minHeight: 64,\r\n                    }\r\n                  }}\r\n                >\r\n                  {tabsConfig.map((tab, index) => (\r\n                    <Tab\r\n                      key={index}\r\n                      icon={tab.icon}\r\n                      label={tab.label}\r\n                      iconPosition=\"start\"\r\n                      sx={{\r\n                        flexDirection: isRTL ? 'row-reverse' : 'row',\r\n                        '& .MuiTab-iconWrapper': {\r\n                          mr: isRTL ? 0 : 1,\r\n                          ml: isRTL ? 1 : 0,\r\n                        }\r\n                      }}\r\n                    />\r\n                  ))}\r\n                </Tabs>\r\n              </Box>\r\n\r\n              {/* Tab Panels */}\r\n              <TabPanel value={activeTab} index={0}>\r\n                {renderTermsAndConditions()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={1}>\r\n                {renderPrivacyPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={2}>\r\n                {renderBookingPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={3}>\r\n                {renderPaymentPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={4}>\r\n                {renderRefundPolicy()}\r\n              </TabPanel>\r\n\r\n            </Paper>\r\n          </Fade>\r\n        </Container>\r\n      </Box>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport default PlatformPolicy;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,SAAS,CACTC,UAAU,CACVC,KAAK,CACLC,GAAG,CACHC,OAAO,CACPC,QAAQ,CACRC,IAAI,CACJC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,IAAI,CACJC,GAAG,CACHC,IAAI,CACJC,WAAW,CACXC,KAAK,CACLC,KAAK,KACA,eAAe,CACtB,OACEC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,WAAW,GAAI,CAAAC,eAAe,KACzB,qBAAqB,CAC5B,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG5C,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA6C,KAAK,CAAGrC,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAsC,QAAQ,CAAG7C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8C,QAAQ,CAAG7C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC8C,KAAK,CAAEC,QAAQ,CAAC,CAAGnD,QAAQ,CAAC8C,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAC1D,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,CAAC,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACdkD,QAAQ,CAACL,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAChCG,QAAQ,CAACC,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CAErD;AACA,KAAM,CAAAK,IAAI,CAAGT,QAAQ,CAACS,IAAI,CAC1B,GAAIA,IAAI,GAAK,QAAQ,CAAEH,YAAY,CAAC,CAAC,CAAC,CAAC,IAClC,IAAIG,IAAI,GAAK,UAAU,CAAEH,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIG,IAAI,GAAK,UAAU,CAAEH,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIG,IAAI,GAAK,UAAU,CAAEH,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIG,IAAI,GAAK,SAAS,CAAEH,YAAY,CAAC,CAAC,CAAC,CAC9C,CAAC,CAAE,CAACR,IAAI,CAACM,QAAQ,CAAEJ,QAAQ,CAACS,IAAI,CAAC,CAAC,CAElC,KAAM,CAAAC,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC3CN,YAAY,CAACM,QAAQ,CAAC,CACtB,KAAM,CAAAC,IAAI,CAAG,CAAC,QAAQ,CAAE,UAAU,CAAE,UAAU,CAAE,UAAU,CAAE,SAAS,CAAC,CACtEZ,QAAQ,CAAC,mBAAmBY,IAAI,CAACD,QAAQ,CAAC,EAAE,CAAE,CAAEE,OAAO,CAAE,IAAK,CAAC,CAAC,CAClE,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEC,KAAK,CAAElB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,iBAAiB,CAAG,oBAAoB,CAAEa,IAAI,cAAExB,IAAA,CAACJ,eAAe,GAAE,CAAE,CAAC,CACvG,CAAE2B,KAAK,CAAElB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,gBAAgB,CAAG,gBAAgB,CAAEa,IAAI,cAAExB,IAAA,CAAClB,YAAY,GAAE,CAAE,CAAC,CAC/F,CAAEyC,KAAK,CAAElB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,sBAAsB,CAAG,wBAAwB,CAAEa,IAAI,cAAExB,IAAA,CAAChB,YAAY,GAAE,CAAE,CAAC,CAC7G,CAAEuC,KAAK,CAAElB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,aAAa,CAAG,gBAAgB,CAAEa,IAAI,cAAExB,IAAA,CAACR,WAAW,GAAE,CAAE,CAAC,CAC3F,CAAE+B,KAAK,CAAElB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,iBAAiB,CAAG,eAAe,CAAEa,IAAI,cAAExB,IAAA,CAACN,WAAW,GAAE,CAAE,CAAC,CAC/F,CAED;AACA,KAAM,CAAA+B,WAAW,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,QAAQ,CAAEJ,IAAI,CAAEK,EAAG,CAAC,CAAAH,IAAA,oBAChD1B,IAAA,CAACvB,IAAI,EACHqD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACf,IAAIxB,KAAK,CACL,CAAEyB,WAAW,CAAE,aAAa5B,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAahC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEV,EAAE,EAAIlD,KAAK,CAAC2B,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAC/D,CAAE,CAAAT,QAAA,cAEF1B,KAAA,CAACxB,WAAW,EAAAkD,QAAA,eACV1B,KAAA,CAACnC,GAAG,EAACgE,EAAE,CAAE,CAAES,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAET,EAAE,CAAE,CAAC,CAAEU,aAAa,CAAEjC,KAAK,CAAG,aAAa,CAAG,KAAM,CAAE,CAAAmB,QAAA,EACrGJ,IAAI,eACHxB,IAAA,CAACjC,GAAG,EAACgE,EAAE,CAAE,CAAEY,EAAE,CAAElC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEmC,EAAE,CAAEnC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEoC,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAT,QAAA,CAClFJ,IAAI,CACF,CACN,cACDxB,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCN,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,oBAAqB,CAAE,CAAAmB,QAAA,CAExED,KAAK,CACI,CAAC,EACV,CAAC,CACLC,QAAQ,EACE,CAAC,CACV,CAAC,EACR,CAED;AACA,KAAM,CAAAqB,gBAAgB,CAAIC,OAAO,eAC/BhD,KAAA,CAACnC,GAAG,EAACgE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjB5B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBf,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAE7DsB,OAAO,CAACvB,KAAK,CACJ,CAAC,CACZuB,OAAO,CAACC,WAAW,eAClBnD,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,OAAO,CACff,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEoB,UAAU,CAAE,GAAG,CAAEJ,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAErFsB,OAAO,CAACC,WAAW,CACV,CACb,CACAD,OAAO,CAACG,MAAM,eACbrD,IAAA,CAAC7B,IAAI,EAAC4D,EAAE,CAAE,CAAEuB,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE8C,EAAE,CAAE9C,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,CAChDsB,OAAO,CAACG,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,CAAEC,GAAG,gBAC7BxD,KAAA,CAAC9B,QAAQ,EAAW2D,EAAE,CAAE,CAAE4B,EAAE,CAAE,GAAI,CAAE,CAAA/B,QAAA,eAClC5B,IAAA,CAAC1B,YAAY,EAACyD,EAAE,CAAE,CAAE6B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAElC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEmC,EAAE,CAAEnC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,cAC3E5B,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,SAAS,CAACgB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACf7D,IAAA,CAAC3B,YAAY,EACX+D,OAAO,CAAEqB,KAAM,CACf1B,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWiD,GAQL,CACX,CAAC,CACE,CACP,CACAR,OAAO,CAACY,KAAK,eACZ9D,IAAA,CAAC7B,IAAI,EAAC4D,EAAE,CAAE,CAAEuB,EAAE,CAAE7C,KAAK,CAAG,CAAC,CAAG,CAAC,CAAE8C,EAAE,CAAE9C,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,CAChDsB,OAAO,CAACY,KAAK,CAACN,GAAG,CAAC,CAACO,IAAI,CAAEL,GAAG,gBAC3BxD,KAAA,CAAC9B,QAAQ,EAAW2D,EAAE,CAAE,CAAE4B,EAAE,CAAE,GAAI,CAAE,CAAA/B,QAAA,eAClC5B,IAAA,CAAC1B,YAAY,EAACyD,EAAE,CAAE,CAAE6B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAElC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEmC,EAAE,CAAEnC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,cAC3E5B,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,SAAS,CAACgB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACf7D,IAAA,CAAC3B,YAAY,EACX+D,OAAO,CAAE2B,IAAK,CACdhC,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWiD,GAQL,CACX,CAAC,CACE,CACP,EACE,CACN,CAED;AACA,KAAM,CAAAM,QAAQ,CAAGC,KAAA,MAAC,CAAErC,QAAQ,CAAEsC,KAAK,CAAEC,KAAM,CAAC,CAAAF,KAAA,oBAC1CjE,IAAA,QAAKoE,MAAM,CAAEF,KAAK,GAAKC,KAAM,CAAAvC,QAAA,CAC1BsC,KAAK,GAAKC,KAAK,eAAInE,IAAA,CAACjC,GAAG,EAACgE,EAAE,CAAE,CAAE4B,EAAE,CAAE,CAAE,CAAE,CAAA/B,QAAA,CAAEA,QAAQ,CAAM,CAAC,CACrD,CAAC,EACP,CAED;AACA,KAAM,CAAAyC,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAC,YAAY,CAAGjE,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAGb,eAAe,CAACyE,EAAE,CAACzE,eAAe,CAAGA,eAAe,CAAC0E,EAAE,CAAC1E,eAAe,CAErH,mBACEI,KAAA,CAACnC,GAAG,EAAA6D,QAAA,eACF5B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBf,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CAAEoC,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAT,QAAA,CAEhG0C,YAAY,CAAC3C,KAAK,CACT,CAAC,cACb3B,IAAA,CAACpB,KAAK,EAAC6F,OAAO,CAAE,CAAE,CAAA7C,QAAA,CACf0C,YAAY,CAACI,OAAO,CAClBC,KAAK,CAAC,SAAS,CAAC,CAChBC,MAAM,CAAE1B,OAAO,EAAKA,OAAO,CAAC2B,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC1CrB,GAAG,CAAC,CAACN,OAAO,CAAEQ,GAAG,GAAK,CACrB,KAAM,CAAAoB,KAAK,CAAG5B,OAAO,CAACyB,KAAK,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAhD,KAAK,CAAGmD,KAAK,CAAC,CAAC,CAAC,CACtB,KAAM,CAAAC,IAAI,CAAGD,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACtC,mBACEjF,IAAA,CAACvB,IAAI,EAEHqD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFE,YAAY,CAAE,CAAC,CACf,IAAIxB,KAAK,CACL,CAAEyB,WAAW,CAAE,aAAa5B,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAahC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEmB,GAAG,CAAG,CAAC,GAAK,CAAC,CAC1B/E,KAAK,CAAC2B,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvC1D,KAAK,CAAC2B,KAAK,CAAC6B,OAAO,CAAC+C,SAAS,CAAC7C,IAAI,EAAI/B,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAAC+C,KAAK,CAAE,IAAI,CAC7E,CAAE,CAAAvD,QAAA,cAEF1B,KAAA,CAACxB,WAAW,EAAAkD,QAAA,eACV5B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBf,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CAAEoC,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAT,QAAA,CAEhGD,KAAK,CACI,CAAC,CACZoD,IAAI,eACH/E,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,OAAO,CACff,EAAE,CAAE,CACFqB,UAAU,CAAE,GAAG,CACfJ,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD2E,UAAU,CAAE,UACd,CAAE,CAAAxD,QAAA,CAEDmD,IAAI,CACK,CACb,EACU,CAAC,EAjCTrB,GAkCD,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACL,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA2B,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,OAAO,CAAGlF,CAAC,CAAC,SAAS,CAAE,CAAEmF,aAAa,CAAE,IAAK,CAAC,CAAC,CAErD,mBACErF,KAAA,CAACnC,GAAG,EAAA6D,QAAA,eACF5B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBf,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CAAEoC,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAT,QAAA,CAEhG0D,OAAO,CAAC3D,KAAK,CACJ,CAAC,cACb3B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,OAAO,CACff,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEoB,UAAU,CAAE,GAAG,CAAEJ,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAErF0D,OAAO,CAACE,KAAK,CACJ,CAAC,cACbxF,IAAA,CAAChC,OAAO,EAAC+D,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACwB,GAAG,CAAEiC,GAAG,EAAK,CACxC,KAAM,CAAAvC,OAAO,CAAGoC,OAAO,CAAC,UAAUG,GAAG,EAAE,CAAC,CACxC,GAAI,CAACvC,OAAO,CAAE,MAAO,KAAI,CAEzB,mBACEhD,KAAA,CAACuB,WAAW,EAAWE,KAAK,CAAEuB,OAAO,CAACvB,KAAM,CAACH,IAAI,cAAExB,IAAA,CAAClB,YAAY,GAAE,CAAE,CAAA8C,QAAA,EACjEsB,OAAO,CAACwC,QAAQ,eACf1F,IAAA,CAACnC,UAAU,EAACiF,OAAO,CAAC,OAAO,CAACf,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAC9FsB,OAAO,CAACwC,QAAQ,CACP,CACb,CACAxC,OAAO,CAACC,WAAW,eAClBnD,IAAA,CAACnC,UAAU,EAACiF,OAAO,CAAC,OAAO,CAACf,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAC9FsB,OAAO,CAACC,WAAW,CACV,CACb,CACAD,OAAO,CAACyC,KAAK,eACZ3F,IAAA,CAAC7B,IAAI,EAAAyD,QAAA,CACF,CAACsB,OAAO,CAACyC,KAAK,CAAEzC,OAAO,CAAC0C,KAAK,CAAE1C,OAAO,CAAC2C,KAAK,CAAE3C,OAAO,CAAC4C,KAAK,CAAE5C,OAAO,CAAC6C,KAAK,CAAC,CACzEnB,MAAM,CAACoB,OAAO,CAAC,CACfxC,GAAG,CAAC,CAACO,IAAI,CAAEL,GAAG,gBACbxD,KAAA,CAAC9B,QAAQ,EAAAwD,QAAA,eACP5B,IAAA,CAAC1B,YAAY,EAAAsD,QAAA,cAAC5B,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,SAAS,CAACgB,QAAQ,CAAC,OAAO,CAAE,CAAC,CAAc,CAAC,cAC1E7D,IAAA,CAAC3B,YAAY,EAAC+D,OAAO,CAAE2B,IAAK,CAAE,CAAC,GAFlBL,GAGL,CACX,CAAC,CACA,CACP,GAtBe+B,GAuBL,CAAC,CAElB,CAAC,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAQ,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,MAAM,CAAG9F,CAAC,CAAC,8BAA8B,CAAE,CAAEmF,aAAa,CAAE,IAAK,CAAC,CAAC,CAEzE,mBACErF,KAAA,CAACnC,GAAG,EAAA6D,QAAA,eACF5B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBf,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CAAEoC,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAT,QAAA,CAEhGsE,MAAM,CAACvE,KAAK,CACH,CAAC,cACb3B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,OAAO,CACff,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEoB,UAAU,CAAE,GAAG,CAAEJ,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAErFsE,MAAM,CAACR,QAAQ,CACN,CAAC,cACb1F,IAAA,CAAChC,OAAO,EAAC+D,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1B9B,KAAA,CAACuB,WAAW,EAACE,KAAK,CAAEuE,MAAM,CAACC,aAAa,CAACxE,KAAM,CAACH,IAAI,cAAExB,IAAA,CAACd,UAAU,GAAE,CAAE,CAAA0C,QAAA,EAClEqB,gBAAgB,CAACiD,MAAM,CAACC,aAAa,CAACC,OAAO,CAAC,CAC9CnD,gBAAgB,CAACiD,MAAM,CAACC,aAAa,CAACE,YAAY,CAAC,CACnDpD,gBAAgB,CAACiD,MAAM,CAACC,aAAa,CAACG,YAAY,CAAC,CACnDrD,gBAAgB,CAACiD,MAAM,CAACC,aAAa,CAACI,WAAW,CAAC,EACxC,CAAC,cAGdrG,KAAA,CAACuB,WAAW,EACVE,KAAK,CAAEuE,MAAM,CAACM,WAAW,CAAC7E,KAAM,CAChCH,IAAI,cAAExB,IAAA,CAACZ,UAAU,GAAE,CAAE,CACrByC,EAAE,CAAElD,KAAK,CAAC2B,KAAK,CAAC6B,OAAO,CAAC+C,SAAS,CAAC7C,IAAI,EAAI/B,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAAC+C,KAAK,CAAE,IAAI,CAAE,CAAAvD,QAAA,EAE5EqB,gBAAgB,CAACiD,MAAM,CAACM,WAAW,CAACC,YAAY,CAAC,CACjDxD,gBAAgB,CAACiD,MAAM,CAACM,WAAW,CAACH,YAAY,CAAC,CACjDpD,gBAAgB,CAACiD,MAAM,CAACM,WAAW,CAACF,YAAY,CAAC,CACjDrD,gBAAgB,CAACiD,MAAM,CAACM,WAAW,CAACD,WAAW,CAAC,EACtC,CAAC,cAGdvG,IAAA,CAACyB,WAAW,EAACE,KAAK,CAAEuE,MAAM,CAACQ,YAAY,CAAC/E,KAAM,CAACH,IAAI,cAAExB,IAAA,CAACV,QAAQ,GAAE,CAAE,CAAAsC,QAAA,cAChE5B,IAAA,CAAC7B,IAAI,EAAAyD,QAAA,CACFsE,MAAM,CAACQ,YAAY,CAACrD,MAAM,CAACG,GAAG,CAAC,CAACO,IAAI,CAAEL,GAAG,gBACxCxD,KAAA,CAAC9B,QAAQ,EAAW2D,EAAE,CAAE,CAAE4B,EAAE,CAAE,GAAI,CAAE,CAAA/B,QAAA,eAClC5B,IAAA,CAAC1B,YAAY,EAACyD,EAAE,CAAE,CAAE6B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAElC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEmC,EAAE,CAAEnC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,cAC3E5B,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,MAAM,CAAE,CAAC,CACb,CAAC,cACf7C,IAAA,CAAC3B,YAAY,EACX+D,OAAO,CAAE2B,IAAK,CACdhC,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWiD,GAQL,CACX,CAAC,CACE,CAAC,CACI,CAAC,EACX,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAiD,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAT,MAAM,CAAG9F,CAAC,CAAC,yBAAyB,CAAE,CAAEmF,aAAa,CAAE,IAAK,CAAC,CAAC,CAEpE,mBACErF,KAAA,CAACnC,GAAG,EAAA6D,QAAA,eACF5B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBf,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CAAEoC,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAT,QAAA,CAEhGsE,MAAM,CAACvE,KAAK,CACH,CAAC,cACb3B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,OAAO,CACff,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEoB,UAAU,CAAE,GAAG,CAAEJ,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAErFsE,MAAM,CAACR,QAAQ,CACN,CAAC,cACb1F,IAAA,CAAChC,OAAO,EAAC+D,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACwB,GAAG,CAAEiC,GAAG,EAAK,CACrC,KAAM,CAAAvC,OAAO,CAAGgD,MAAM,CAAC,UAAUT,GAAG,EAAE,CAAC,CACvC,GAAI,CAACvC,OAAO,CAAE,MAAO,KAAI,CAEzB,mBACEhD,KAAA,CAACuB,WAAW,EAAWE,KAAK,CAAEuB,OAAO,CAACvB,KAAM,CAACH,IAAI,cAAExB,IAAA,CAACR,WAAW,GAAE,CAAE,CAAAoC,QAAA,EAChEsB,OAAO,CAACC,WAAW,eAClBnD,IAAA,CAACnC,UAAU,EAACiF,OAAO,CAAC,OAAO,CAACf,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAC9FsB,OAAO,CAACC,WAAW,CACV,CACb,CACAD,OAAO,CAACG,MAAM,eACbrD,IAAA,CAAC7B,IAAI,EAAAyD,QAAA,CACFsB,OAAO,CAACG,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,CAAEC,GAAG,gBAC7BxD,KAAA,CAAC9B,QAAQ,EAAW2D,EAAE,CAAE,CAAE4B,EAAE,CAAE,GAAI,CAAE,CAAA/B,QAAA,eAClC5B,IAAA,CAAC1B,YAAY,EAACyD,EAAE,CAAE,CAAE6B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAElC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEmC,EAAE,CAAEnC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,cAC3E5B,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,SAAS,CAACgB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACf7D,IAAA,CAAC3B,YAAY,EACX+D,OAAO,CAAEqB,KAAM,CACf1B,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWiD,GAQL,CACX,CAAC,CACE,CACP,GApBe+B,GAqBL,CAAC,CAElB,CAAC,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAmB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAV,MAAM,CAAG9F,CAAC,CAAC,iBAAiB,CAAE,CAAEmF,aAAa,CAAE,IAAK,CAAC,CAAC,CAE5D,mBACErF,KAAA,CAACnC,GAAG,EAAA6D,QAAA,eACF5B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBf,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CAAEoC,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAT,QAAA,CAEhGsE,MAAM,CAACvE,KAAK,CACH,CAAC,cACb3B,IAAA,CAAChC,OAAO,EAAC+D,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAACwB,GAAG,CAAEiC,GAAG,EAAK,CACrC,KAAM,CAAAvC,OAAO,CAAGgD,MAAM,CAAC,UAAUT,GAAG,EAAE,CAAC,CACvC,GAAI,CAACvC,OAAO,CAAE,MAAO,KAAI,CAEzB,mBACEhD,KAAA,CAACuB,WAAW,EAAWE,KAAK,CAAEuB,OAAO,CAACvB,KAAM,CAACH,IAAI,cAAExB,IAAA,CAACN,WAAW,GAAE,CAAE,CAAAkC,QAAA,EAChEsB,OAAO,CAACC,WAAW,eAClBnD,IAAA,CAACnC,UAAU,EAACiF,OAAO,CAAC,OAAO,CAACf,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAC9FsB,OAAO,CAACC,WAAW,CACV,CACb,CACAD,OAAO,CAACY,KAAK,eACZ9D,IAAA,CAAC7B,IAAI,EAAAyD,QAAA,CACFsB,OAAO,CAACY,KAAK,CAACN,GAAG,CAAC,CAACO,IAAI,CAAEL,GAAG,gBAC3BxD,KAAA,CAAC9B,QAAQ,EAAW2D,EAAE,CAAE,CAAE4B,EAAE,CAAE,GAAI,CAAE,CAAA/B,QAAA,eAClC5B,IAAA,CAAC1B,YAAY,EAACyD,EAAE,CAAE,CAAE6B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAElC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEmC,EAAE,CAAEnC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,cAC3E5B,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,SAAS,CAACgB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACf7D,IAAA,CAAC3B,YAAY,EACX+D,OAAO,CAAE2B,IAAK,CACdhC,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWiD,GAQL,CACX,CAAC,CACE,CACP,CACAR,OAAO,CAACG,MAAM,eACbrD,IAAA,CAAC7B,IAAI,EAAAyD,QAAA,CACFsB,OAAO,CAACG,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,CAAEC,GAAG,gBAC7BxD,KAAA,CAAC9B,QAAQ,EAAW2D,EAAE,CAAE,CAAE4B,EAAE,CAAE,GAAI,CAAE,CAAA/B,QAAA,eAClC5B,IAAA,CAAC1B,YAAY,EAACyD,EAAE,CAAE,CAAE6B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAElC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEmC,EAAE,CAAEnC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAmB,QAAA,cAC3E5B,IAAA,CAACV,QAAQ,EAACuD,KAAK,CAAC,SAAS,CAACgB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACf7D,IAAA,CAAC3B,YAAY,EACX+D,OAAO,CAAEqB,KAAM,CACf1B,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWiD,GAQL,CACX,CAAC,CACE,CACP,GAnCe+B,GAoCL,CAAC,CAElB,CAAC,CAAC,EACC,CAAC,CAEV,CAAC,CAED,mBACEzF,IAAA,CAACH,MAAM,EAAA+B,QAAA,cACL5B,IAAA,CAACjC,GAAG,EACFgE,EAAE,CAAE,CACF4B,EAAE,CAAE,CAAC,CACLkD,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBnI,KAAK,CAAC2B,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAK1D,KAAK,CAAC2B,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GACnH,CAAE,CAAAT,QAAA,cAEF5B,IAAA,CAACpC,SAAS,EAACmJ,QAAQ,CAAC,IAAI,CAAAnF,QAAA,cACtB5B,IAAA,CAAC9B,IAAI,EAAC8I,EAAE,MAACC,OAAO,CAAE,IAAK,CAAArF,QAAA,cACrB1B,KAAA,CAACpC,KAAK,EACJgE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFmF,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBpF,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACfoF,SAAS,CAAE5G,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAmB,QAAA,eAGF1B,KAAA,CAACnC,GAAG,EAACgE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEsF,SAAS,CAAE,QAAS,CAAE,CAAA1F,QAAA,eACtC5B,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZyE,SAAS,CAAC,IAAI,CACdxF,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLe,UAAU,CAAE,GAAG,CACfC,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoC,KAAK,CAAEvC,KAAK,CAAC6B,OAAO,CAACC,OAAO,CAACC,IAC/B,CAAE,CAAAT,QAAA,CAEDnB,KAAK,CAAG,eAAe,CAAG,mBAAmB,CACpC,CAAC,cACbT,IAAA,CAACnC,UAAU,EACTiF,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,gBAAgB,CACtBd,EAAE,CAAE,CAAEiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAmB,QAAA,CAE7DnB,KAAK,CACF,iEAAiE,CACjE,4DAA4D,CAEtD,CAAC,EACV,CAAC,cAGNT,IAAA,CAACjC,GAAG,EAACgE,EAAE,CAAE,CAAEyF,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEzF,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAC1D5B,IAAA,CAACzB,IAAI,EACH2F,KAAK,CAAEtD,SAAU,CACjB8G,QAAQ,CAAEzG,eAAgB,CAC1B6B,OAAO,CAAC,YAAY,CACpB6E,aAAa,CAAC,MAAM,CACpB5F,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChBiB,UAAU,CAAEvC,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDoG,SAAS,CAAE,EACb,CACF,CAAE,CAAAjF,QAAA,CAEDN,UAAU,CAACkC,GAAG,CAAC,CAACoE,GAAG,CAAEzD,KAAK,gBACzBnE,IAAA,CAACxB,GAAG,EAEFgD,IAAI,CAAEoG,GAAG,CAACpG,IAAK,CACfD,KAAK,CAAEqG,GAAG,CAACrG,KAAM,CACjBsG,YAAY,CAAC,OAAO,CACpB9F,EAAE,CAAE,CACFW,aAAa,CAAEjC,KAAK,CAAG,aAAa,CAAG,KAAK,CAC5C,uBAAuB,CAAE,CACvBkC,EAAE,CAAElC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBmC,EAAE,CAAEnC,KAAK,CAAG,CAAC,CAAG,CAClB,CACF,CAAE,EAVG0D,KAWN,CACF,CAAC,CACE,CAAC,CACJ,CAAC,cAGNnE,IAAA,CAACgE,QAAQ,EAACE,KAAK,CAAEtD,SAAU,CAACuD,KAAK,CAAE,CAAE,CAAAvC,QAAA,CAClCyC,wBAAwB,CAAC,CAAC,CACnB,CAAC,cAEXrE,IAAA,CAACgE,QAAQ,EAACE,KAAK,CAAEtD,SAAU,CAACuD,KAAK,CAAE,CAAE,CAAAvC,QAAA,CAClCyD,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXrF,IAAA,CAACgE,QAAQ,EAACE,KAAK,CAAEtD,SAAU,CAACuD,KAAK,CAAE,CAAE,CAAAvC,QAAA,CAClCqE,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXjG,IAAA,CAACgE,QAAQ,EAACE,KAAK,CAAEtD,SAAU,CAACuD,KAAK,CAAE,CAAE,CAAAvC,QAAA,CAClC+E,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEX3G,IAAA,CAACgE,QAAQ,EAACE,KAAK,CAAEtD,SAAU,CAACuD,KAAK,CAAE,CAAE,CAAAvC,QAAA,CAClCgF,kBAAkB,CAAC,CAAC,CACb,CAAC,EAEN,CAAC,CACJ,CAAC,CACE,CAAC,CACT,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAAzG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}