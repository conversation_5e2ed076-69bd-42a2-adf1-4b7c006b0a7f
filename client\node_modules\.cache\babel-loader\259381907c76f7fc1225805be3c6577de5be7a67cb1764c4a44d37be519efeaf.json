{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{useTranslation}from'react-i18next';import axios from'../../utils/axios';import{Container,Paper,Typography,TextField,Button,Grid,Box,Alert,Divider,IconButton,FormControl,InputLabel,Select,MenuItem,Chip,Avatar,Dialog,DialogTitle,DialogContent,DialogActions,InputAdornment,CircularProgress}from'@mui/material';import{Language as LanguageIcon,Visibility as VisibilityIcon,VisibilityOff as VisibilityOffIcon,Lock as LockIcon,Edit as EditIcon,Warning as WarningIcon,CheckCircle as CheckCircleIcon,Delete as DeleteIcon}from'@mui/icons-material';import{useAuth}from'../../contexts/AuthContext';import Layout from'../../components/Layout';import ReactPlayer from'react-player';import{timezones}from'../../utils/constants';// Function to calculate teacher earnings after commission\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const calculateTeacherEarnings=price=>{const lessonPrice=Number(price);if(!lessonPrice||lessonPrice<3)return 0;let commissionRate;if(lessonPrice===3){commissionRate=0.333;// 33.3%\n}else if(lessonPrice===4){commissionRate=0.25;// 25%\n}else if(lessonPrice===5){commissionRate=0.20;// 20%\n}else if(lessonPrice===6){commissionRate=0.167;// 16.7%\n}else{commissionRate=0.15;// 15% for $7+\n}const teacherEarnings=lessonPrice*(1-commissionRate);return teacherEarnings;};// Function to extract YouTube video ID from URL\nconst getYoutubeVideoId=url=>{if(!url)return null;// Regular expressions to match different YouTube URL formats\nconst regExp=/^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;const match=url.match(regExp);return match&&match[2].length===11?match[2]:null;};// Function to check if a URL is a local video file\nconst isLocalVideoFile=url=>{if(!url)return false;return url.startsWith('/uploads/videos/');};// Function to check if a URL is a YouTube video\nconst isYoutubeVideo=url=>{if(!url)return false;return getYoutubeVideoId(url)!==null;};const Profile=()=>{var _userData$fullName,_profileData$teaching,_profileData$courseTy;const{t,i18n}=useTranslation();const navigate=useNavigate();const{user,updateUser}=useAuth();const[isRtl,setIsRtl]=useState(i18n.language==='ar');const[loading,setLoading]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');const[showPassword,setShowPassword]=useState({currentPassword:false,newPassword:false,confirmPassword:false});const[openPasswordDialog,setOpenPasswordDialog]=useState(false);const[openEditApplicationDialog,setOpenEditApplicationDialog]=useState(false);const[openEditTeachingInfoDialog,setOpenEditTeachingInfoDialog]=useState(false);const[languages,setLanguages]=useState([]);const[categories,setCategories]=useState([]);const[applicationStatus,setApplicationStatus]=useState('');const[profileUpdateStatus,setProfileUpdateStatus]=useState(null);// Account deletion states\nconst[deleteDialog,setDeleteDialog]=useState(false);const[deleteCodeDialog,setDeleteCodeDialog]=useState(false);const[deleteCode,setDeleteCode]=useState('');const[deleteStatus,setDeleteStatus]=useState('');const[deleteScheduledAt,setDeleteScheduledAt]=useState(null);// State للمنطقة الزمنية\nconst[userTimezone,setUserTimezone]=useState('Africa/Cairo');// دالة لتنسيق التاريخ مع المنطقة الزمنية\nconst formatDateWithTimezone=(dateString,timezone)=>{if(!dateString)return'';try{const date=new Date(dateString);// إذا كانت المنطقة الزمنية بصيغة UTC+XX:XX\nif(timezone&&timezone.match(/^UTC[+-]\\d{2}:\\d{2}$/)){const match=timezone.match(/^UTC([+-])(\\d{2}):(\\d{2})$/);const sign=match[1]==='+'?1:-1;const hours=parseInt(match[2]);const minutes=parseInt(match[3]);const offsetMinutes=sign*(hours*60+minutes);const utcTime=date.getTime()+date.getTimezoneOffset()*60000;const targetTime=new Date(utcTime+offsetMinutes*60000);return targetTime.toLocaleString(i18n.language==='ar'?'ar-EG':'en-US',{year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit',hour12:i18n.language!=='ar'});}else{// استخدام المنطقة الزمنية مباشرة\nreturn date.toLocaleString(i18n.language==='ar'?'ar-EG':'en-US',{year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit',hour12:i18n.language!=='ar',timeZone:timezone});}}catch(error){console.error('Error formatting date:',error);return new Date(dateString).toLocaleString();}};// Separate state for user info and teacher profile\nconst[userData,setUserData]=useState({fullName:(user===null||user===void 0?void 0:user.full_name)||'',email:(user===null||user===void 0?void 0:user.email)||'',gender:(user===null||user===void 0?void 0:user.gender)||''});const[passwordData,setPasswordData]=useState({currentPassword:'',newPassword:'',confirmPassword:''});const[profileData,setProfileData]=useState({phone:'',country:'',residence:'',nativeLanguage:'',teachingLanguages:[],courseTypes:[],qualifications:'',teachingExperience:'',availableHours:{},pricePerLesson:'',trialLessonPrice:'',timezone:'',profilePictureUrl:'',cv:'',introVideoUrl:'',commitmentAccepted:false});const[editTeachingData,setEditTeachingData]=useState({phone:'',nativeLanguage:'',teachingLanguages:[],courseTypes:[],pricePerLesson:'',trialLessonPrice:'',timezone:''});const[fieldErrors,setFieldErrors]=useState({});// Auto-hide messages after 3 seconds\nuseEffect(()=>{let timer;if(success||error){timer=setTimeout(()=>{setSuccess('');setError('');},3000);}return()=>clearTimeout(timer);},[success,error]);// دالة جلب حالة طلب التعديل\nconst fetchProfileUpdateStatus=async()=>{try{const response=await axios.get('/api/teacher/profile-update-status');if(response.data.success&&response.data.hasUpdate){setProfileUpdateStatus(response.data.update);}else{setProfileUpdateStatus(null);}}catch(error){console.error('Error fetching profile update status:',error);}};useEffect(()=>{const fetchData=async()=>{try{// Fetch teacher profile\nconst profileResponse=await axios.get('/teacher/profile');if(profileResponse.data.success){const{user,profile}=profileResponse.data;setUserData(prev=>({...prev,fullName:(user===null||user===void 0?void 0:user.full_name)||'',email:(user===null||user===void 0?void 0:user.email)||'',gender:(user===null||user===void 0?void 0:user.gender)||''}));// Set application status\nsetApplicationStatus((profile===null||profile===void 0?void 0:profile.status)||'');// Fetch languages\nconst languagesResponse=await axios.get('/teacher/languages');const languagesMap={};languagesResponse.data.forEach(lang=>{languagesMap[lang.id]=lang.name;});setLanguages(languagesResponse.data);// Store as array for dialog\n// Fetch categories\nconst categoriesResponse=await axios.get('/teacher/categories');setCategories(categoriesResponse.data);// Ensure teaching_languages is an array and map IDs to names\nconst teachingLangs=Array.isArray(profile.teaching_languages)?profile.teaching_languages.map(id=>{const lang=languagesResponse.data.find(l=>l.id===id);return lang?lang.name:id;}):[];// Ensure course_types is an array and map IDs to names\nconst courseTypeNames=Array.isArray(profile.course_types)?profile.course_types.map(id=>{const category=categoriesResponse.data.find(c=>c.id===id);return category?category.name:id;}):[];// Update profile data with language names instead of IDs\nsetProfileData({phone:(profile===null||profile===void 0?void 0:profile.phone)||'',country:(profile===null||profile===void 0?void 0:profile.country)||'',residence:(profile===null||profile===void 0?void 0:profile.residence)||'',nativeLanguage:(profile===null||profile===void 0?void 0:profile.native_language)||'',teachingLanguages:teachingLangs,// استخدام الأسماء المحولة بدلاً من IDs\ncourseTypes:courseTypeNames,// استخدام الأسماء المحولة بدلاً من IDs\nqualifications:(profile===null||profile===void 0?void 0:profile.qualifications)||'',teachingExperience:(profile===null||profile===void 0?void 0:profile.teaching_experience)||'',availableHours:(profile===null||profile===void 0?void 0:profile.available_hours)||{},pricePerLesson:(profile===null||profile===void 0?void 0:profile.price_per_lesson)||'',trialLessonPrice:(profile===null||profile===void 0?void 0:profile.trial_lesson_price)||'',timezone:(profile===null||profile===void 0?void 0:profile.timezone)||'',profilePictureUrl:(profile===null||profile===void 0?void 0:profile.profile_picture_url)||'',cv:(profile===null||profile===void 0?void 0:profile.cv)||'',introVideoUrl:(profile===null||profile===void 0?void 0:profile.intro_video_url)||'',commitmentAccepted:(profile===null||profile===void 0?void 0:profile.commitment_accepted)||false});console.log('Profile data loaded:',{...profile,commitment_accepted:profile===null||profile===void 0?void 0:profile.commitment_accepted,commitmentAccepted:(profile===null||profile===void 0?void 0:profile.commitment_accepted)||false});console.log('Setting profileData.commitmentAccepted to:',(profile===null||profile===void 0?void 0:profile.commitment_accepted)||false);}// جلب حالة طلب التعديل\nawait fetchProfileUpdateStatus();}catch(error){console.error('Error fetching data:',error);setError(t('profile.errors.fetch'));}};fetchData();},[t]);// التحقق من حالة الحذف عند تحميل الصفحة\nuseEffect(()=>{const checkDeleteStatus=async()=>{try{const token=localStorage.getItem('token');const response=await axios.get('/api/users/profile',{headers:{Authorization:`Bearer ${token}`}});if(response.data.user&&response.data.user.status==='pending_deletion'){setDeleteStatus('pending');setDeleteScheduledAt(response.data.user.delete_scheduled_at);}// حفظ المنطقة الزمنية للمستخدم\nif(response.data.user.timezone){setUserTimezone(response.data.user.timezone);}else{const detectedTimezone=Intl.DateTimeFormat().resolvedOptions().timeZone;setUserTimezone(detectedTimezone);// حفظ المنطقة الزمنية المكتشفة\naxios.put('/api/users/timezone',{timezone:detectedTimezone},{headers:{Authorization:`Bearer ${token}`}}).catch(err=>console.log('Failed to save timezone:',err));}}catch(error){console.error('Error checking delete status:',error);}};checkDeleteStatus();},[]);const handlePasswordChange=prop=>event=>{setPasswordData({...passwordData,[prop]:event.target.value});};const handleClickShowPassword=field=>{setShowPassword({...showPassword,[field]:!showPassword[field]});};const handleOpenPasswordDialog=()=>setOpenPasswordDialog(true);const handleClosePasswordDialog=()=>{setOpenPasswordDialog(false);setPasswordData({currentPassword:'',newPassword:'',confirmPassword:''});setShowPassword({currentPassword:false,newPassword:false,confirmPassword:false});};const handleUpdatePassword=async e=>{e.preventDefault();setLoading(true);setError('');setSuccess('');try{const response=await axios.put('/auth/change-password',{current_password:passwordData.currentPassword,new_password:passwordData.newPassword});console.log('Password update response:',response.data);if(response.data.success){const successMsg=t('profile.passwordUpdateSuccess');handleClosePasswordDialog();setSuccess(successMsg);setError('');}}catch(error){var _error$response,_error$response$data;console.error('Password update error:',error);setError(((_error$response=error.response)===null||_error$response===void 0?void 0:(_error$response$data=_error$response.data)===null||_error$response$data===void 0?void 0:_error$response$data.message)||t('profile.errors.updateFailed'));}finally{setLoading(false);}};// Teaching info edit handlers\nconst handleOpenEditTeachingInfoDialog=()=>{// Convert language names back to IDs for editing\nconst teachingLanguageIds=profileData.teachingLanguages.map(langName=>{const langEntry=languages.find(lang=>lang.name===langName);return langEntry?langEntry.id:langName;});// Convert category names back to IDs for editing\nconst courseTypeIds=profileData.courseTypes.map(catName=>{const catEntry=categories.find(cat=>cat.name===catName);return catEntry?catEntry.id:catName;});setEditTeachingData({phone:profileData.phone,nativeLanguage:profileData.nativeLanguage,teachingLanguages:teachingLanguageIds,courseTypes:courseTypeIds,pricePerLesson:profileData.pricePerLesson,trialLessonPrice:profileData.trialLessonPrice,timezone:profileData.timezone});setFieldErrors({});setOpenEditTeachingInfoDialog(true);};const handleCloseEditTeachingInfoDialog=()=>{setOpenEditTeachingInfoDialog(false);setEditTeachingData({phone:profileData.phone,nativeLanguage:profileData.nativeLanguage,teachingLanguages:profileData.teachingLanguages,courseTypes:profileData.courseTypes,pricePerLesson:profileData.pricePerLesson,trialLessonPrice:profileData.trialLessonPrice,timezone:profileData.timezone});};const handleEditTeachingChange=prop=>event=>{const value=event.target.value;const newData={...editTeachingData,[prop]:value};setEditTeachingData(newData);// Real-time validation for price fields\nif(prop==='pricePerLesson'||prop==='trialLessonPrice'){const pricePerLesson=prop==='pricePerLesson'?value:newData.pricePerLesson;const trialLessonPrice=prop==='trialLessonPrice'?value:newData.trialLessonPrice;// Clear any existing price-related errors first\nsetFieldErrors(prev=>({...prev,trialLessonPrice:''}));// Validate trial lesson price if both prices are provided\nif(pricePerLesson&&trialLessonPrice){const regularPrice=Number(pricePerLesson);const trialPrice=Number(trialLessonPrice);if(trialPrice>regularPrice){setFieldErrors(prev=>({...prev,trialLessonPrice:t('teacher.trialPriceLessThanRegular')||'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي'}));}}}else{// For non-price fields, just clear any existing error for that field\nsetFieldErrors(prev=>({...prev,[prop]:''}));}};const handleEditTeachingSubmit=async e=>{e.preventDefault();setLoading(true);setError('');setSuccess('');setFieldErrors({});// Validate form fields\nconst errors={};const requiredFields=['nativeLanguage','teachingLanguages','courseTypes','pricePerLesson','trialLessonPrice','timezone'];// Check required fields (phone is optional)\nrequiredFields.forEach(field=>{if(!editTeachingData[field]||Array.isArray(editTeachingData[field])&&editTeachingData[field].length===0||editTeachingData[field]===''){errors[field]=t('teacher.required');}});// Validate price range\nif(editTeachingData.pricePerLesson&&(editTeachingData.pricePerLesson<3||editTeachingData.pricePerLesson>100)){errors.pricePerLesson=t('teacher.priceRange')||'السعر يجب أن يكون بين $3 و $100';}// Validate trial lesson price\nif(!editTeachingData.trialLessonPrice){errors.trialLessonPrice=t('teacher.required');}else if(Number(editTeachingData.trialLessonPrice)>Number(editTeachingData.pricePerLesson)){errors.trialLessonPrice=t('teacher.trialPriceLessThanRegular')||'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي';}// If there are validation errors, stop submission\nif(Object.keys(errors).length>0){setFieldErrors(errors);setError(t('teacher.formHasErrors')||'يرجى تصحيح الأخطاء في النموذج');setLoading(false);return;}try{const updateData={phone:editTeachingData.phone,native_language:editTeachingData.nativeLanguage,teaching_languages:editTeachingData.teachingLanguages,course_types:editTeachingData.courseTypes,price_per_lesson:editTeachingData.pricePerLesson,trial_lesson_price:editTeachingData.trialLessonPrice,timezone:editTeachingData.timezone};const response=await axios.put('/teacher/update-teaching-info',updateData);console.log('Teaching info update response:',response.data);if(response.data.success){// Convert IDs back to names for display\nconst teachingLanguageNames=editTeachingData.teachingLanguages.map(langId=>{const langEntry=languages.find(lang=>lang.id===langId);return langEntry?langEntry.name:langId;});const courseTypeNames=editTeachingData.courseTypes.map(catId=>{const catEntry=categories.find(cat=>cat.id===catId);return catEntry?catEntry.name:catId;});// Update local state\nsetProfileData(prev=>({...prev,phone:editTeachingData.phone,nativeLanguage:editTeachingData.nativeLanguage,teachingLanguages:teachingLanguageNames,courseTypes:courseTypeNames,pricePerLesson:editTeachingData.pricePerLesson,trialLessonPrice:editTeachingData.trialLessonPrice,timezone:editTeachingData.timezone}));const successMsg=t('profile.teachingInfoUpdateSuccess');handleCloseEditTeachingInfoDialog();setSuccess(successMsg);setError('');}}catch(error){var _error$response2,_error$response2$data;console.error('Teaching info update error:',error);setError(((_error$response2=error.response)===null||_error$response2===void 0?void 0:(_error$response2$data=_error$response2.data)===null||_error$response2$data===void 0?void 0:_error$response2$data.message)||t('profile.errors.updateFailed'));}finally{setLoading(false);}};// Función para manejar la edición de la aplicación del profesor\nconst handleEditApplication=()=>{setOpenEditApplicationDialog(true);};// Función para redirigir al profesor a la página de edición de aplicación\nconst prepareApplicationEdit=()=>{try{// Cerrar el diálogo\nsetOpenEditApplicationDialog(false);// Redirigir a la página de edición de aplicación\nnavigate('/teacher/edit-application');}catch(error){console.error('Error preparing application edit:',error);setError(t('profile.errors.applicationPreparation'));}};// دوال حذف الحساب\nconst handleDeleteAccount=async()=>{setError('');setSuccess('');setLoading(true);try{const token=localStorage.getItem('token');const response=await axios.post('/api/users/request-delete',{},{headers:{Authorization:`Bearer ${token}`}});if(response.data.success){setDeleteDialog(false);setDeleteCodeDialog(true);setSuccess(t('profile.deleteCodeSent'));}}catch(err){var _err$response,_err$response$data;setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||t('profile.errors.deleteRequest'));}finally{setLoading(false);}};const handleVerifyDeleteCode=async()=>{if(!deleteCode.trim()){setError(t('profile.errors.codeRequired'));return;}setError('');setSuccess('');setLoading(true);try{const token=localStorage.getItem('token');// الحصول على المنطقة الزمنية للمستخدم\nconst timezone=Intl.DateTimeFormat().resolvedOptions().timeZone;const response=await axios.post('/api/users/verify-delete',{code:deleteCode,timezone:timezone},{headers:{Authorization:`Bearer ${token}`}});if(response.data.success){setDeleteCodeDialog(false);setDeleteCode('');setDeleteStatus('pending');setSuccess(t('profile.deletePending'));}}catch(err){var _err$response2,_err$response2$data;setError(((_err$response2=err.response)===null||_err$response2===void 0?void 0:(_err$response2$data=_err$response2.data)===null||_err$response2$data===void 0?void 0:_err$response2$data.message)||t('profile.errors.invalidCode'));}finally{setLoading(false);}};const handleCancelDelete=async()=>{setError('');setSuccess('');setLoading(true);try{const token=localStorage.getItem('token');const response=await axios.post('/api/users/cancel-delete',{},{headers:{Authorization:`Bearer ${token}`}});if(response.data.success){setDeleteStatus('');setDeleteScheduledAt(null);setSuccess(t('profile.deleteCancelled'));}}catch(err){var _err$response3,_err$response3$data;setError(((_err$response3=err.response)===null||_err$response3===void 0?void 0:(_err$response3$data=_err$response3.data)===null||_err$response3$data===void 0?void 0:_err$response3$data.message)||t('profile.errors.cancelDelete'));}finally{setLoading(false);}};// تحديث حالة طلب التعديل عند العودة من صفحة التعديل\nuseEffect(()=>{const handleVisibilityChange=()=>{if(!document.hidden){fetchProfileUpdateStatus();}};document.addEventListener('visibilitychange',handleVisibilityChange);window.addEventListener('focus',handleVisibilityChange);return()=>{document.removeEventListener('visibilitychange',handleVisibilityChange);window.removeEventListener('focus',handleVisibilityChange);};},[]);return/*#__PURE__*/_jsxs(Layout,{children:[/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",sx:{mt:4,mb:4},children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',mb:4},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"h1\",gutterBottom:true,children:t('profile.title')}),/*#__PURE__*/_jsx(Box,{sx:{position:'relative',mb:3},children:/*#__PURE__*/_jsx(Avatar,{src:profileData.profilePictureUrl?profileData.profilePictureUrl.startsWith('http')?profileData.profilePictureUrl:`https://allemnionline.com${profileData.profilePictureUrl}`:'',alt:userData.fullName,sx:{width:120,height:120,border:'2px solid',borderColor:'primary.main',bgcolor:'primary.main',fontSize:'3rem'},children:!profileData.profilePictureUrl&&((_userData$fullName=userData.fullName)===null||_userData$fullName===void 0?void 0:_userData$fullName.charAt(0))})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:userData.fullName}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"textSecondary\",sx:{mb:3},children:t(`role.${(user===null||user===void 0?void 0:user.role)||'teacher'}`)}),/*#__PURE__*/_jsxs(Box,{sx:{width:'100%',maxWidth:'500px',display:'flex',flexDirection:'column',gap:2},children:[error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",variant:\"filled\",sx:{width:'100%','& .MuiAlert-message':{width:'100%',textAlign:'center'}},children:error}),success&&/*#__PURE__*/_jsx(Alert,{severity:\"success\",variant:\"filled\",sx:{width:'100%','& .MuiAlert-message':{width:'100%',textAlign:'center'}},children:success}),deleteStatus==='pending'&&/*#__PURE__*/_jsxs(Alert,{severity:\"warning\",variant:\"filled\",sx:{width:'100%','& .MuiAlert-message':{width:'100%',textAlign:'center'}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'bold',mb:1},children:t('profile.deletePendingAlert')}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:t('profile.deletePendingMessage')}),deleteScheduledAt&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{mt:1},children:[t('profile.deleteScheduledFor'),\": \",formatDateWithTimezone(deleteScheduledAt,userTimezone)]})]})]})]}),/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:4,mb:4,borderRadius:2},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",gutterBottom:true,sx:{mb:0},children:t('profile.basicInfo')}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(EditIcon,{}),onClick:handleOpenEditTeachingInfoDialog,sx:{borderRadius:2,mr:2},children:t('profile.editTeachingInfo')}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(LockIcon,{}),onClick:handleOpenPasswordDialog,sx:{borderRadius:2,mr:2},children:t('profile.changePassword')}),deleteStatus==='pending'?/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"warning\",onClick:handleCancelDelete,disabled:loading,sx:{borderRadius:2},children:loading?t('common.loading'):t('profile.cancelDelete')}):/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"error\",startIcon:/*#__PURE__*/_jsx(DeleteIcon,{}),onClick:()=>setDeleteDialog(true),sx:{borderRadius:2},children:t('profile.deleteAccount')})]})]}),profileUpdateStatus&&/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,mb:3,borderRadius:2,backgroundColor:profileUpdateStatus.status==='pending'?'#fff3cd':profileUpdateStatus.status==='approved'?'#d1edff':'#f8d7da'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[profileUpdateStatus.status==='pending'&&/*#__PURE__*/_jsx(WarningIcon,{color:\"warning\",sx:{mr:1}}),profileUpdateStatus.status==='approved'&&/*#__PURE__*/_jsx(CheckCircleIcon,{color:\"success\",sx:{mr:1}}),profileUpdateStatus.status==='rejected'&&/*#__PURE__*/_jsx(WarningIcon,{color:\"error\",sx:{mr:1}}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",component:\"h3\",children:[profileUpdateStatus.status==='pending'&&t('profile.updateStatus.pending'),profileUpdateStatus.status==='approved'&&t('profile.updateStatus.approved'),profileUpdateStatus.status==='rejected'&&t('profile.updateStatus.rejected')]})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{mb:1},children:[t('profile.updateStatus.requestDate'),\": \",new Date(profileUpdateStatus.createdAt).toLocaleDateString('ar-EG')]}),profileUpdateStatus.reviewedAt&&/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"textSecondary\",sx:{mb:1},children:[t('profile.updateStatus.reviewDate'),\": \",new Date(profileUpdateStatus.reviewedAt).toLocaleDateString('ar-EG')]}),profileUpdateStatus.adminNotes&&/*#__PURE__*/_jsx(Box,{sx:{mt:1,p:1,backgroundColor:'rgba(0,0,0,0.05)',borderRadius:1},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('profile.updateStatus.adminNotes'),\":\"]}),\" \",profileUpdateStatus.adminNotes]})}),profileUpdateStatus.status==='pending'&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:1,fontStyle:'italic'},children:t('profile.updateStatus.pendingNote')})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",color:\"text.secondary\",sx:{mb:2},children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('profile.fullName'),\":\"]}),\" \",userData.fullName]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",color:\"text.secondary\",sx:{mb:2},children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('profile.email'),\":\"]}),\" \",userData.email]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",color:\"text.secondary\",children:[/*#__PURE__*/_jsxs(\"strong\",{children:[t('profile.gender'),\":\"]}),\" \",t(`gender.${userData.gender}`)]})]}),/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:4,mb:4,borderRadius:2,bgcolor:'background.paper'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",gutterBottom:true,sx:{mb:0},children:t('teacher.commitment')||'تعهد المعلم'}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('teacher.commitmentProfileDescription')||'حالة التعهد الخاص بك كمعلم في المنصة'})]}),/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'center',p:1,borderRadius:1,bgcolor:profileData.commitmentAccepted?'success.light':'error.light',color:'white'},children:profileData.commitmentAccepted?/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(CheckCircleIcon,{sx:{mr:1}}),t('teacher.commitmentStatus.accepted')||'تم الموافقة على التعهد']}):/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(WarningIcon,{sx:{mr:1}}),t('teacher.commitmentStatus.rejected')||'لم توافق على التعهد بعد']})})]})}),applicationStatus==='approved'&&/*#__PURE__*/_jsx(Paper,{elevation:3,sx:{p:4,mb:4,borderRadius:2,bgcolor:'background.paper'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",gutterBottom:true,sx:{mb:0},children:t('teacher.application.title')}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('teacher.application.editDescription')})]}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",startIcon:/*#__PURE__*/_jsx(EditIcon,{}),onClick:handleEditApplication,sx:{borderRadius:2},children:t('teacher.application.edit')})]})}),/*#__PURE__*/_jsxs(Paper,{sx:{p:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:t('teacher.profile.personalInfo')}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.profile.country'),value:profileData.country,disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.profile.residence'),value:profileData.residence,disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.profile.nativeLanguage'),value:profileData.nativeLanguage,disabled:true})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{mt:4,mb:2},children:t('teacher.profile.teachingInfo')}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{children:t('teacher.profile.teachingLanguages.title')}),/*#__PURE__*/_jsx(Select,{multiple:true,value:profileData.teachingLanguages,disabled:true,renderValue:selected=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:0.5},children:(selected===null||selected===void 0?void 0:selected.map(value=>/*#__PURE__*/_jsx(Chip,{label:value},value)))||[]}),children:(_profileData$teaching=profileData.teachingLanguages)===null||_profileData$teaching===void 0?void 0:_profileData$teaching.map(lang=>/*#__PURE__*/_jsx(MenuItem,{value:lang,children:lang},lang))})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{children:t('teacher.profile.courseTypes')}),/*#__PURE__*/_jsx(Select,{multiple:true,value:profileData.courseTypes,disabled:true,renderValue:selected=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:0.5},children:(selected===null||selected===void 0?void 0:selected.map(value=>/*#__PURE__*/_jsx(Chip,{label:value},value)))||[]}),children:(_profileData$courseTy=profileData.courseTypes)===null||_profileData$courseTy===void 0?void 0:_profileData$courseTy.map(type=>/*#__PURE__*/_jsx(MenuItem,{value:type,children:type},type))})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.profile.pricePerLesson'),value:profileData.pricePerLesson,disabled:true,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"$\"})}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.profile.trialLessonPrice'),value:profileData.trialLessonPrice,disabled:true,InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"$\"})}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.profile.timezone'),value:profileData.timezone,disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.profile.qualifications'),value:profileData.qualifications,disabled:true,multiline:true,rows:3})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.profile.teachingExperience'),value:profileData.teachingExperience,disabled:true})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Paper,{elevation:2,sx:{p:3,mb:3,borderRadius:2},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,sx:{mb:0},children:t('teacher.availableHours')}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('teacher.availableHoursProfileDescription')})]}),/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:()=>navigate('/teacher/view-hours'),sx:{borderRadius:2},children:t('teacher.viewAvailableHours')})})]})})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:t('teacher.introVideo')}),profileData.introVideoUrl?isYoutubeVideo(profileData.introVideoUrl)?/*#__PURE__*/// YouTube video\n_jsx(Box,{sx:{position:'relative',paddingTop:'56.25%'/* 16:9 Aspect Ratio */},children:/*#__PURE__*/_jsx(ReactPlayer,{url:profileData.introVideoUrl,width:\"100%\",height:\"100%\",controls:true,style:{position:'absolute',top:0,left:0},config:{youtube:{playerVars:{origin:window.location.origin}}}})}):isLocalVideoFile(profileData.introVideoUrl)?/*#__PURE__*/// Local video file\n_jsx(Box,{sx:{position:'relative',paddingTop:'56.25%'/* 16:9 Aspect Ratio */},children:/*#__PURE__*/_jsx(\"video\",{src:`https://allemnionline.com${profileData.introVideoUrl}`,width:\"100%\",height:\"100%\",controls:true,style:{position:'absolute',top:0,left:0}})}):/*#__PURE__*/// External video URL (not YouTube)\n_jsx(Box,{sx:{textAlign:'center',p:2},children:/*#__PURE__*/_jsx(Typography,{variant:\"body1\",children:/*#__PURE__*/_jsx(\"a\",{href:profileData.introVideoUrl,target:\"_blank\",rel:\"noopener noreferrer\",children:t('teacher.openVideo')})})}):/*#__PURE__*/_jsx(Paper,{elevation:0,variant:\"outlined\",sx:{p:2,backgroundColor:'background.default',textAlign:'center',color:'text.secondary'},children:t('teacher.noIntroVideo')})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,children:t('teacher.cv')}),/*#__PURE__*/_jsx(Paper,{elevation:0,variant:\"outlined\",sx:{p:2,backgroundColor:'background.default',whiteSpace:'pre-wrap'// This preserves line breaks\n},children:profileData.cv})]})]})]})]})}),/*#__PURE__*/_jsxs(Dialog,{open:openPasswordDialog,onClose:handleClosePasswordDialog,maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:t('profile.changePassword')}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:2,minWidth:300},children:[error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:error}),success&&/*#__PURE__*/_jsx(Alert,{severity:\"success\",children:success}),/*#__PURE__*/_jsx(TextField,{label:t('profile.currentPassword'),type:showPassword.currentPassword?'text':'password',value:passwordData.currentPassword,onChange:handlePasswordChange('currentPassword'),required:true,InputProps:{endAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":t('profile.togglePasswordVisibility'),onClick:()=>handleClickShowPassword('currentPassword'),edge:\"end\",type:\"button\",children:showPassword.currentPassword?/*#__PURE__*/_jsx(VisibilityOffIcon,{}):/*#__PURE__*/_jsx(VisibilityIcon,{})})})}}),/*#__PURE__*/_jsx(TextField,{label:t('profile.newPassword'),type:showPassword.newPassword?'text':'password',value:passwordData.newPassword,onChange:handlePasswordChange('newPassword'),required:true,InputProps:{endAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":t('profile.togglePasswordVisibility'),onClick:()=>handleClickShowPassword('newPassword'),edge:\"end\",type:\"button\",children:showPassword.newPassword?/*#__PURE__*/_jsx(VisibilityOffIcon,{}):/*#__PURE__*/_jsx(VisibilityIcon,{})})})}}),/*#__PURE__*/_jsx(TextField,{label:t('profile.confirmPassword'),type:showPassword.confirmPassword?'text':'password',value:passwordData.confirmPassword,onChange:handlePasswordChange('confirmPassword'),required:true,InputProps:{endAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":t('profile.togglePasswordVisibility'),onClick:()=>handleClickShowPassword('confirmPassword'),edge:\"end\",type:\"button\",children:showPassword.confirmPassword?/*#__PURE__*/_jsx(VisibilityOffIcon,{}):/*#__PURE__*/_jsx(VisibilityIcon,{})})})}})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleClosePasswordDialog,children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",disabled:loading,onClick:handleUpdatePassword,children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):t('common.save')})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:openEditTeachingInfoDialog,onClose:handleCloseEditTeachingInfoDialog,maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{children:t('profile.editTeachingInfo')}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',gap:2,minWidth:300,mt:2},children:[error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",children:error}),success&&/*#__PURE__*/_jsx(Alert,{severity:\"success\",children:success}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('teacher.phone'),value:editTeachingData.phone,onChange:handleEditTeachingChange('phone'),error:!!fieldErrors.phone,helperText:fieldErrors.phone||t('teacher.phoneHelp')})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,label:t('teacher.nativeLanguage'),value:editTeachingData.nativeLanguage,onChange:handleEditTeachingChange('nativeLanguage'),error:!!fieldErrors.nativeLanguage,helperText:fieldErrors.nativeLanguage||''})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,error:!!fieldErrors.teachingLanguages,children:[/*#__PURE__*/_jsx(InputLabel,{children:t('teacher.teachingLanguages')}),/*#__PURE__*/_jsx(Select,{multiple:true,value:editTeachingData.teachingLanguages,onChange:handleEditTeachingChange('teachingLanguages'),renderValue:selected=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:0.5},children:(selected===null||selected===void 0?void 0:selected.map(value=>{var _languages$find;return/*#__PURE__*/_jsx(Chip,{label:((_languages$find=languages.find(lang=>lang.id===value))===null||_languages$find===void 0?void 0:_languages$find.name)||value},value);}))||[]}),children:languages.map(lang=>/*#__PURE__*/_jsx(MenuItem,{value:lang.id,children:lang.name},lang.id))}),fieldErrors.teachingLanguages&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:fieldErrors.teachingLanguages})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,error:!!fieldErrors.courseTypes,children:[/*#__PURE__*/_jsx(InputLabel,{children:t('teacher.courseTypes')}),/*#__PURE__*/_jsx(Select,{multiple:true,value:editTeachingData.courseTypes,onChange:handleEditTeachingChange('courseTypes'),renderValue:selected=>/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:0.5},children:(selected===null||selected===void 0?void 0:selected.map(value=>{var _categories$find;return/*#__PURE__*/_jsx(Chip,{label:((_categories$find=categories.find(cat=>cat.id===value))===null||_categories$find===void 0?void 0:_categories$find.name)||value},value);}))||[]}),children:categories.map(category=>/*#__PURE__*/_jsx(MenuItem,{value:category.id,children:category.name},category.id))}),fieldErrors.courseTypes&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:fieldErrors.courseTypes})]})}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:6,children:[/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,type:\"number\",label:t('teacher.pricePerLesson'),placeholder:t('teacher.pricePerLessonPlaceholder'),value:editTeachingData.pricePerLesson,onChange:handleEditTeachingChange('pricePerLesson'),error:!!fieldErrors.pricePerLesson,helperText:fieldErrors.pricePerLesson||'',InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"$\"})},inputProps:{min:3,max:100}}),editTeachingData.pricePerLesson>0&&/*#__PURE__*/_jsxs(Box,{sx:{mt:1,p:1.5,bgcolor:'background.paper',border:'1px dashed',borderColor:'primary.main',borderRadius:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"primary\",gutterBottom:true,children:t('teacher.yourEarnings')||'ما ستحصل عليه بعد خصم العمولة:'}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{fontWeight:'bold',color:'success.main'},children:[\"$\",calculateTeacherEarnings(editTeachingData.pricePerLesson).toFixed(2)]})]})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsx(TextField,{required:true,fullWidth:true,type:\"number\",label:t('teacher.trialLessonPrice'),placeholder:t('teacher.trialLessonPricePlaceholder'),value:editTeachingData.trialLessonPrice,onChange:handleEditTeachingChange('trialLessonPrice'),error:!!fieldErrors.trialLessonPrice,helperText:fieldErrors.trialLessonPrice||'',InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:\"$\"})},inputProps:{min:1,max:99}})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,children:/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,required:true,error:!!fieldErrors.timezone,children:[/*#__PURE__*/_jsx(InputLabel,{children:t('teacher.timezone')}),/*#__PURE__*/_jsx(Select,{value:editTeachingData.timezone,onChange:handleEditTeachingChange('timezone'),children:timezones.map(timezone=>/*#__PURE__*/_jsx(MenuItem,{value:timezone.value,children:timezone.label},timezone.value))}),fieldErrors.timezone&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",children:fieldErrors.timezone})]})})]})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:handleCloseEditTeachingInfoDialog,children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",disabled:loading,onClick:handleEditTeachingSubmit,children:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24}):t('common.save')})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:openEditApplicationDialog,onClose:()=>setOpenEditApplicationDialog(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{bgcolor:'warning.light',color:'warning.contrastText'},children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(WarningIcon,{}),t('teacher.application.warningTitle')]})}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsx(Alert,{severity:\"warning\",sx:{mb:2},children:t('teacher.application.warningMessage')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,children:t('teacher.application.warningDescription1')}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,children:t('teacher.application.warningDescription2')}),/*#__PURE__*/_jsx(Alert,{severity:\"info\",sx:{mb:2},children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{fontWeight:'bold'},children:\"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0629: \\u0633\\u062A\\u0633\\u062A\\u0645\\u0631 \\u0641\\u064A \\u0627\\u0644\\u062A\\u062F\\u0631\\u064A\\u0633 \\u0628\\u0627\\u0644\\u0628\\u064A\\u0627\\u0646\\u0627\\u062A \\u0627\\u0644\\u062D\\u0627\\u0644\\u064A\\u0629 \\u062D\\u062A\\u0649 \\u064A\\u062A\\u0645 \\u0645\\u0631\\u0627\\u062C\\u0639\\u0629 \\u0637\\u0644\\u0628 \\u0627\\u0644\\u062A\\u0639\\u062F\\u064A\\u0644 \\u0648\\u0627\\u0644\\u0645\\u0648\\u0627\\u0641\\u0642\\u0629 \\u0639\\u0644\\u064A\\u0647 \\u0645\\u0646 \\u0642\\u0628\\u0644 \\u0627\\u0644\\u0625\\u062F\\u0627\\u0631\\u0629.\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontWeight:'bold'},children:t('teacher.application.warningConfirmation')})]})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setOpenEditApplicationDialog(false),children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"warning\",onClick:prepareApplicationEdit,children:t('teacher.application.confirmEdit')})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:deleteDialog,onClose:()=>setDeleteDialog(false),children:[/*#__PURE__*/_jsxs(DialogTitle,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(WarningIcon,{color:\"error\"}),t('profile.deleteAccountTitle')]}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2},children:t('profile.deleteAccountWarning')}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:t('profile.deleteAccountNote')})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDeleteDialog(false),children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"error\",onClick:handleDeleteAccount,disabled:loading,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(DeleteIcon,{}),children:loading?t('common.loading'):t('profile.sendDeleteCode')})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:deleteCodeDialog,onClose:()=>setDeleteCodeDialog(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:t('profile.verifyDeleteCode')}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2},children:t('profile.deleteCodeSentMessage')}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:t('profile.deleteCode'),value:deleteCode,onChange:e=>setDeleteCode(e.target.value),placeholder:\"123456\",sx:{mt:1}})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setDeleteCodeDialog(false),children:t('common.cancel')}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"error\",onClick:handleVerifyDeleteCode,disabled:loading||!deleteCode.trim(),startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(DeleteIcon,{}),children:loading?t('common.loading'):t('profile.confirmDelete')})]})]})]});};export default Profile;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useTranslation", "axios", "Container", "Paper", "Typography", "TextField", "<PERSON><PERSON>", "Grid", "Box", "<PERSON><PERSON>", "Divider", "IconButton", "FormControl", "InputLabel", "Select", "MenuItem", "Chip", "Avatar", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "InputAdornment", "CircularProgress", "Language", "LanguageIcon", "Visibility", "VisibilityIcon", "VisibilityOff", "VisibilityOffIcon", "Lock", "LockIcon", "Edit", "EditIcon", "Warning", "WarningIcon", "CheckCircle", "CheckCircleIcon", "Delete", "DeleteIcon", "useAuth", "Layout", "ReactPlayer", "timezones", "jsx", "_jsx", "jsxs", "_jsxs", "calculateTeacherEarnings", "price", "lessonPrice", "Number", "commissionRate", "teacherEarnings", "getYoutubeVideoId", "url", "regExp", "match", "length", "isLocalVideoFile", "startsWith", "isYoutubeVideo", "Profile", "_userData$fullName", "_profileData$teaching", "_profileData$courseTy", "t", "i18n", "navigate", "user", "updateUser", "isRtl", "setIsRtl", "language", "loading", "setLoading", "error", "setError", "success", "setSuccess", "showPassword", "setShowPassword", "currentPassword", "newPassword", "confirmPassword", "openPasswordDialog", "setOpenPasswordDialog", "openEditApplicationDialog", "setOpenEditApplicationDialog", "openEditTeachingInfoDialog", "setOpenEditTeachingInfoDialog", "languages", "setLanguages", "categories", "setCategories", "applicationStatus", "setApplicationStatus", "profileUpdateStatus", "setProfileUpdateStatus", "deleteDialog", "setDeleteDialog", "deleteCodeDialog", "setDeleteCodeDialog", "deleteCode", "setDeleteCode", "deleteStatus", "setDeleteStatus", "deleteScheduledAt", "setDeleteScheduledAt", "userTimezone", "setUserTimezone", "formatDateWithTimezone", "dateString", "timezone", "date", "Date", "sign", "hours", "parseInt", "minutes", "offsetMinutes", "utcTime", "getTime", "getTimezoneOffset", "targetTime", "toLocaleString", "year", "month", "day", "hour", "minute", "hour12", "timeZone", "console", "userData", "setUserData", "fullName", "full_name", "email", "gender", "passwordData", "setPasswordData", "profileData", "setProfileData", "phone", "country", "residence", "nativeLanguage", "teachingLanguages", "courseTypes", "qualifications", "teachingExperience", "availableHours", "pricePer<PERSON><PERSON>on", "trialLessonPrice", "profilePictureUrl", "cv", "introVideoUrl", "commitmentAccepted", "editTeachingData", "setEditTeachingData", "fieldErrors", "setFieldErrors", "timer", "setTimeout", "clearTimeout", "fetchProfileUpdateStatus", "response", "get", "data", "hasUpdate", "update", "fetchData", "profileResponse", "profile", "prev", "status", "languagesResponse", "languagesMap", "for<PERSON>ach", "lang", "id", "name", "categoriesResponse", "teachingLangs", "Array", "isArray", "teaching_languages", "map", "find", "l", "courseTypeNames", "course_types", "category", "c", "native_language", "teaching_experience", "available_hours", "price_per_lesson", "trial_lesson_price", "profile_picture_url", "intro_video_url", "commitment_accepted", "log", "checkDeleteStatus", "token", "localStorage", "getItem", "headers", "Authorization", "delete_scheduled_at", "detectedTimezone", "Intl", "DateTimeFormat", "resolvedOptions", "put", "catch", "err", "handlePasswordChange", "prop", "event", "target", "value", "handleClickShowPassword", "field", "handleOpenPasswordDialog", "handleClosePasswordDialog", "handleUpdatePassword", "e", "preventDefault", "current_password", "new_password", "successMsg", "_error$response", "_error$response$data", "message", "handleOpenEditTeachingInfoDialog", "teachingLanguageIds", "langName", "langEntry", "courseTypeIds", "catName", "catEntry", "cat", "handleCloseEditTeachingInfoDialog", "handleEditTeachingChange", "newData", "regularPrice", "trialPrice", "handleEditTeachingSubmit", "errors", "requiredFields", "Object", "keys", "updateData", "teachingLanguageNames", "langId", "catId", "_error$response2", "_error$response2$data", "handleEditApplication", "prepareApplicationEdit", "handleDeleteAccount", "post", "_err$response", "_err$response$data", "handleVerifyDeleteCode", "trim", "code", "_err$response2", "_err$response2$data", "handleCancelDelete", "_err$response3", "_err$response3$data", "handleVisibilityChange", "document", "hidden", "addEventListener", "window", "removeEventListener", "children", "max<PERSON><PERSON><PERSON>", "sx", "mt", "mb", "elevation", "p", "display", "flexDirection", "alignItems", "variant", "component", "gutterBottom", "position", "src", "alt", "width", "height", "border", "borderColor", "bgcolor", "fontSize", "char<PERSON>t", "color", "role", "gap", "severity", "textAlign", "fontWeight", "borderRadius", "justifyContent", "startIcon", "onClick", "mr", "disabled", "backgroundColor", "createdAt", "toLocaleDateString", "reviewedAt", "adminNotes", "fontStyle", "container", "spacing", "item", "xs", "sm", "fullWidth", "label", "multiple", "renderValue", "selected", "flexWrap", "type", "InputProps", "startAdornment", "multiline", "rows", "paddingTop", "controls", "style", "top", "left", "config", "youtube", "playerVars", "origin", "location", "href", "rel", "whiteSpace", "open", "onClose", "min<PERSON><PERSON><PERSON>", "onChange", "required", "endAdornment", "edge", "size", "helperText", "_languages$find", "_categories$find", "placeholder", "inputProps", "min", "max", "toFixed", "paragraph"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/teacher/Profile.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport axios from '../../utils/axios';\nimport {\n  Container,\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Grid,\n  Box,\n  Alert,\n  Divider,\n  IconButton,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Chip,\n  Avatar,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  InputAdornment,\n  CircularProgress\n} from '@mui/material';\nimport {\n  Language as LanguageIcon,\n  Visibility as VisibilityIcon,\n  VisibilityOff as VisibilityOffIcon,\n  Lock as LockIcon,\n  Edit as EditIcon,\n  Warning as WarningIcon,\n  CheckCircle as CheckCircleIcon,\n  Delete as DeleteIcon\n} from '@mui/icons-material';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Layout from '../../components/Layout';\nimport ReactPlayer from 'react-player';\nimport { timezones } from '../../utils/constants';\n\n// Function to calculate teacher earnings after commission\nconst calculateTeacherEarnings = (price) => {\n  const lessonPrice = Number(price);\n  if (!lessonPrice || lessonPrice < 3) return 0;\n\n  let commissionRate;\n  if (lessonPrice === 3) {\n    commissionRate = 0.333; // 33.3%\n  } else if (lessonPrice === 4) {\n    commissionRate = 0.25; // 25%\n  } else if (lessonPrice === 5) {\n    commissionRate = 0.20; // 20%\n  } else if (lessonPrice === 6) {\n    commissionRate = 0.167; // 16.7%\n  } else {\n    commissionRate = 0.15; // 15% for $7+\n  }\n\n  const teacherEarnings = lessonPrice * (1 - commissionRate);\n  return teacherEarnings;\n};\n\n// Function to extract YouTube video ID from URL\nconst getYoutubeVideoId = (url) => {\n  if (!url) return null;\n\n  // Regular expressions to match different YouTube URL formats\n  const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|\\&v=)([^#\\&\\?]*).*/;\n  const match = url.match(regExp);\n\n  return (match && match[2].length === 11) ? match[2] : null;\n};\n\n// Function to check if a URL is a local video file\nconst isLocalVideoFile = (url) => {\n  if (!url) return false;\n  return url.startsWith('/uploads/videos/');\n};\n\n// Function to check if a URL is a YouTube video\nconst isYoutubeVideo = (url) => {\n  if (!url) return false;\n  return getYoutubeVideoId(url) !== null;\n};\n\nconst Profile = () => {\n  const { t, i18n } = useTranslation();\n  const navigate = useNavigate();\n  const { user, updateUser } = useAuth();\n  const [isRtl, setIsRtl] = useState(i18n.language === 'ar');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [showPassword, setShowPassword] = useState({\n    currentPassword: false,\n    newPassword: false,\n    confirmPassword: false\n  });\n  const [openPasswordDialog, setOpenPasswordDialog] = useState(false);\n  const [openEditApplicationDialog, setOpenEditApplicationDialog] = useState(false);\n  const [openEditTeachingInfoDialog, setOpenEditTeachingInfoDialog] = useState(false);\n  const [languages, setLanguages] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [applicationStatus, setApplicationStatus] = useState('');\n  const [profileUpdateStatus, setProfileUpdateStatus] = useState(null);\n\n  // Account deletion states\n  const [deleteDialog, setDeleteDialog] = useState(false);\n  const [deleteCodeDialog, setDeleteCodeDialog] = useState(false);\n  const [deleteCode, setDeleteCode] = useState('');\n  const [deleteStatus, setDeleteStatus] = useState('');\n  const [deleteScheduledAt, setDeleteScheduledAt] = useState(null);\n\n  // State للمنطقة الزمنية\n  const [userTimezone, setUserTimezone] = useState('Africa/Cairo');\n\n  // دالة لتنسيق التاريخ مع المنطقة الزمنية\n  const formatDateWithTimezone = (dateString, timezone) => {\n    if (!dateString) return '';\n\n    try {\n      const date = new Date(dateString);\n\n      // إذا كانت المنطقة الزمنية بصيغة UTC+XX:XX\n      if (timezone && timezone.match(/^UTC[+-]\\d{2}:\\d{2}$/)) {\n        const match = timezone.match(/^UTC([+-])(\\d{2}):(\\d{2})$/);\n        const sign = match[1] === '+' ? 1 : -1;\n        const hours = parseInt(match[2]);\n        const minutes = parseInt(match[3]);\n        const offsetMinutes = sign * (hours * 60 + minutes);\n\n        const utcTime = date.getTime() + (date.getTimezoneOffset() * 60000);\n        const targetTime = new Date(utcTime + (offsetMinutes * 60000));\n\n        return targetTime.toLocaleString(i18n.language === 'ar' ? 'ar-EG' : 'en-US', {\n          year: 'numeric',\n          month: 'long',\n          day: 'numeric',\n          hour: '2-digit',\n          minute: '2-digit',\n          hour12: i18n.language !== 'ar'\n        });\n      } else {\n        // استخدام المنطقة الزمنية مباشرة\n        return date.toLocaleString(i18n.language === 'ar' ? 'ar-EG' : 'en-US', {\n          year: 'numeric',\n          month: 'long',\n          day: 'numeric',\n          hour: '2-digit',\n          minute: '2-digit',\n          hour12: i18n.language !== 'ar',\n          timeZone: timezone\n        });\n      }\n    } catch (error) {\n      console.error('Error formatting date:', error);\n      return new Date(dateString).toLocaleString();\n    }\n  };\n\n  // Separate state for user info and teacher profile\n  const [userData, setUserData] = useState({\n    fullName: user?.full_name || '',\n    email: user?.email || '',\n    gender: user?.gender || '',\n  });\n\n  const [passwordData, setPasswordData] = useState({\n    currentPassword: '',\n    newPassword: '',\n    confirmPassword: '',\n  });\n\n  const [profileData, setProfileData] = useState({\n    phone: '',\n    country: '',\n    residence: '',\n    nativeLanguage: '',\n    teachingLanguages: [],\n    courseTypes: [],\n    qualifications: '',\n    teachingExperience: '',\n    availableHours: {},\n    pricePerLesson: '',\n    trialLessonPrice: '',\n    timezone: '',\n    profilePictureUrl: '',\n    cv: '',\n    introVideoUrl: '',\n    commitmentAccepted: false,\n  });\n\n\n\n  const [editTeachingData, setEditTeachingData] = useState({\n    phone: '',\n    nativeLanguage: '',\n    teachingLanguages: [],\n    courseTypes: [],\n    pricePerLesson: '',\n    trialLessonPrice: '',\n    timezone: '',\n  });\n\n  const [fieldErrors, setFieldErrors] = useState({});\n\n  // Auto-hide messages after 3 seconds\n  useEffect(() => {\n    let timer;\n    if (success || error) {\n      timer = setTimeout(() => {\n        setSuccess('');\n        setError('');\n      }, 3000);\n    }\n    return () => clearTimeout(timer);\n  }, [success, error]);\n\n  // دالة جلب حالة طلب التعديل\n  const fetchProfileUpdateStatus = async () => {\n    try {\n      const response = await axios.get('/api/teacher/profile-update-status');\n      if (response.data.success && response.data.hasUpdate) {\n        setProfileUpdateStatus(response.data.update);\n      } else {\n        setProfileUpdateStatus(null);\n      }\n    } catch (error) {\n      console.error('Error fetching profile update status:', error);\n    }\n  };\n\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch teacher profile\n        const profileResponse = await axios.get('/teacher/profile');\n        if (profileResponse.data.success) {\n          const { user, profile } = profileResponse.data;\n          setUserData(prev => ({\n            ...prev,\n            fullName: user?.full_name || '',\n            email: user?.email || '',\n            gender: user?.gender || '',\n          }));\n\n          // Set application status\n          setApplicationStatus(profile?.status || '');\n\n          // Fetch languages\n          const languagesResponse = await axios.get('/teacher/languages');\n          const languagesMap = {};\n          languagesResponse.data.forEach(lang => {\n            languagesMap[lang.id] = lang.name;\n          });\n          setLanguages(languagesResponse.data); // Store as array for dialog\n\n          // Fetch categories\n          const categoriesResponse = await axios.get('/teacher/categories');\n          setCategories(categoriesResponse.data);\n\n          // Ensure teaching_languages is an array and map IDs to names\n          const teachingLangs = Array.isArray(profile.teaching_languages)\n            ? profile.teaching_languages.map(id => {\n                const lang = languagesResponse.data.find(l => l.id === id);\n                return lang ? lang.name : id;\n              })\n            : [];\n\n          // Ensure course_types is an array and map IDs to names\n          const courseTypeNames = Array.isArray(profile.course_types)\n            ? profile.course_types.map(id => {\n                const category = categoriesResponse.data.find(c => c.id === id);\n                return category ? category.name : id;\n              })\n            : [];\n\n          // Update profile data with language names instead of IDs\n          setProfileData({\n            phone: profile?.phone || '',\n            country: profile?.country || '',\n            residence: profile?.residence || '',\n            nativeLanguage: profile?.native_language || '',\n            teachingLanguages: teachingLangs, // استخدام الأسماء المحولة بدلاً من IDs\n            courseTypes: courseTypeNames, // استخدام الأسماء المحولة بدلاً من IDs\n            qualifications: profile?.qualifications || '',\n            teachingExperience: profile?.teaching_experience || '',\n            availableHours: profile?.available_hours || {},\n            pricePerLesson: profile?.price_per_lesson || '',\n            trialLessonPrice: profile?.trial_lesson_price || '',\n            timezone: profile?.timezone || '',\n\n            profilePictureUrl: profile?.profile_picture_url || '',\n            cv: profile?.cv || '',\n            introVideoUrl: profile?.intro_video_url || '',\n            commitmentAccepted: profile?.commitment_accepted || false,\n          });\n\n          console.log('Profile data loaded:', {\n            ...profile,\n            commitment_accepted: profile?.commitment_accepted,\n            commitmentAccepted: profile?.commitment_accepted || false\n          });\n          console.log('Setting profileData.commitmentAccepted to:', profile?.commitment_accepted || false);\n        }\n\n        // جلب حالة طلب التعديل\n        await fetchProfileUpdateStatus();\n      } catch (error) {\n        console.error('Error fetching data:', error);\n        setError(t('profile.errors.fetch'));\n      }\n    };\n\n    fetchData();\n  }, [t]);\n\n  // التحقق من حالة الحذف عند تحميل الصفحة\n  useEffect(() => {\n    const checkDeleteStatus = async () => {\n      try {\n        const token = localStorage.getItem('token');\n        const response = await axios.get('/api/users/profile', {\n          headers: { Authorization: `Bearer ${token}` }\n        });\n\n        if (response.data.user && response.data.user.status === 'pending_deletion') {\n          setDeleteStatus('pending');\n          setDeleteScheduledAt(response.data.user.delete_scheduled_at);\n        }\n\n        // حفظ المنطقة الزمنية للمستخدم\n        if (response.data.user.timezone) {\n          setUserTimezone(response.data.user.timezone);\n        } else {\n          const detectedTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n          setUserTimezone(detectedTimezone);\n          // حفظ المنطقة الزمنية المكتشفة\n          axios.put('/api/users/timezone', { timezone: detectedTimezone }, {\n            headers: { Authorization: `Bearer ${token}` }\n          }).catch(err => console.log('Failed to save timezone:', err));\n        }\n      } catch (error) {\n        console.error('Error checking delete status:', error);\n      }\n    };\n\n    checkDeleteStatus();\n  }, []);\n\n  const handlePasswordChange = (prop) => (event) => {\n    setPasswordData({ ...passwordData, [prop]: event.target.value });\n  };\n\n  const handleClickShowPassword = (field) => {\n    setShowPassword({ ...showPassword, [field]: !showPassword[field] });\n  };\n\n  const handleOpenPasswordDialog = () => setOpenPasswordDialog(true);\n  const handleClosePasswordDialog = () => {\n    setOpenPasswordDialog(false);\n    setPasswordData({\n      currentPassword: '',\n      newPassword: '',\n      confirmPassword: '',\n    });\n    setShowPassword({\n      currentPassword: false,\n      newPassword: false,\n      confirmPassword: false\n    });\n  };\n\n  const handleUpdatePassword = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const response = await axios.put('/auth/change-password', {\n        current_password: passwordData.currentPassword,\n        new_password: passwordData.newPassword\n      });\n      console.log('Password update response:', response.data);\n\n      if (response.data.success) {\n        const successMsg = t('profile.passwordUpdateSuccess');\n        handleClosePasswordDialog();\n        setSuccess(successMsg);\n        setError('');\n      }\n    } catch (error) {\n      console.error('Password update error:', error);\n      setError(error.response?.data?.message || t('profile.errors.updateFailed'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n\n\n\n\n  // Teaching info edit handlers\n  const handleOpenEditTeachingInfoDialog = () => {\n    // Convert language names back to IDs for editing\n    const teachingLanguageIds = profileData.teachingLanguages.map(langName => {\n      const langEntry = languages.find(lang => lang.name === langName);\n      return langEntry ? langEntry.id : langName;\n    });\n\n    // Convert category names back to IDs for editing\n    const courseTypeIds = profileData.courseTypes.map(catName => {\n      const catEntry = categories.find(cat => cat.name === catName);\n      return catEntry ? catEntry.id : catName;\n    });\n\n    setEditTeachingData({\n      phone: profileData.phone,\n      nativeLanguage: profileData.nativeLanguage,\n      teachingLanguages: teachingLanguageIds,\n      courseTypes: courseTypeIds,\n      pricePerLesson: profileData.pricePerLesson,\n      trialLessonPrice: profileData.trialLessonPrice,\n      timezone: profileData.timezone,\n    });\n    setFieldErrors({});\n    setOpenEditTeachingInfoDialog(true);\n  };\n\n  const handleCloseEditTeachingInfoDialog = () => {\n    setOpenEditTeachingInfoDialog(false);\n    setEditTeachingData({\n      phone: profileData.phone,\n      nativeLanguage: profileData.nativeLanguage,\n      teachingLanguages: profileData.teachingLanguages,\n      courseTypes: profileData.courseTypes,\n      pricePerLesson: profileData.pricePerLesson,\n      trialLessonPrice: profileData.trialLessonPrice,\n      timezone: profileData.timezone,\n    });\n  };\n\n  const handleEditTeachingChange = (prop) => (event) => {\n    const value = event.target.value;\n    const newData = { ...editTeachingData, [prop]: value };\n    setEditTeachingData(newData);\n\n    // Real-time validation for price fields\n    if (prop === 'pricePerLesson' || prop === 'trialLessonPrice') {\n      const pricePerLesson = prop === 'pricePerLesson' ? value : newData.pricePerLesson;\n      const trialLessonPrice = prop === 'trialLessonPrice' ? value : newData.trialLessonPrice;\n\n      // Clear any existing price-related errors first\n      setFieldErrors(prev => ({\n        ...prev,\n        trialLessonPrice: ''\n      }));\n\n      // Validate trial lesson price if both prices are provided\n      if (pricePerLesson && trialLessonPrice) {\n        const regularPrice = Number(pricePerLesson);\n        const trialPrice = Number(trialLessonPrice);\n\n        if (trialPrice > regularPrice) {\n          setFieldErrors(prev => ({\n            ...prev,\n            trialLessonPrice: t('teacher.trialPriceLessThanRegular') || 'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي'\n          }));\n        }\n      }\n    } else {\n      // For non-price fields, just clear any existing error for that field\n      setFieldErrors(prev => ({\n        ...prev,\n        [prop]: ''\n      }));\n    }\n  };\n\n  const handleEditTeachingSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    setSuccess('');\n    setFieldErrors({});\n\n    // Validate form fields\n    const errors = {};\n    const requiredFields = [\n      'nativeLanguage', 'teachingLanguages', 'courseTypes',\n      'pricePerLesson', 'trialLessonPrice', 'timezone'\n    ];\n\n    // Check required fields (phone is optional)\n    requiredFields.forEach(field => {\n      if (!editTeachingData[field] ||\n          (Array.isArray(editTeachingData[field]) && editTeachingData[field].length === 0) ||\n          editTeachingData[field] === '') {\n        errors[field] = t('teacher.required');\n      }\n    });\n\n    // Validate price range\n    if (editTeachingData.pricePerLesson && (editTeachingData.pricePerLesson < 3 || editTeachingData.pricePerLesson > 100)) {\n      errors.pricePerLesson = t('teacher.priceRange') || 'السعر يجب أن يكون بين $3 و $100';\n    }\n\n    // Validate trial lesson price\n    if (!editTeachingData.trialLessonPrice) {\n      errors.trialLessonPrice = t('teacher.required');\n    } else if (Number(editTeachingData.trialLessonPrice) > Number(editTeachingData.pricePerLesson)) {\n      errors.trialLessonPrice = t('teacher.trialPriceLessThanRegular') || 'يجب أن يكون سعر الدرس التجريبي أقل من أو يساوي سعر الدرس العادي';\n    }\n\n    // If there are validation errors, stop submission\n    if (Object.keys(errors).length > 0) {\n      setFieldErrors(errors);\n      setError(t('teacher.formHasErrors') || 'يرجى تصحيح الأخطاء في النموذج');\n      setLoading(false);\n      return;\n    }\n\n    try {\n      const updateData = {\n        phone: editTeachingData.phone,\n        native_language: editTeachingData.nativeLanguage,\n        teaching_languages: editTeachingData.teachingLanguages,\n        course_types: editTeachingData.courseTypes,\n        price_per_lesson: editTeachingData.pricePerLesson,\n         trial_lesson_price: editTeachingData.trialLessonPrice,\n        timezone: editTeachingData.timezone,\n      };\n\n      const response = await axios.put('/teacher/update-teaching-info', updateData);\n      console.log('Teaching info update response:', response.data);\n\n      if (response.data.success) {\n        // Convert IDs back to names for display\n        const teachingLanguageNames = editTeachingData.teachingLanguages.map(langId => {\n          const langEntry = languages.find(lang => lang.id === langId);\n          return langEntry ? langEntry.name : langId;\n        });\n\n        const courseTypeNames = editTeachingData.courseTypes.map(catId => {\n          const catEntry = categories.find(cat => cat.id === catId);\n          return catEntry ? catEntry.name : catId;\n        });\n\n        // Update local state\n        setProfileData(prev => ({\n          ...prev,\n          phone: editTeachingData.phone,\n          nativeLanguage: editTeachingData.nativeLanguage,\n          teachingLanguages: teachingLanguageNames,\n          courseTypes: courseTypeNames,\n          pricePerLesson: editTeachingData.pricePerLesson,\n           trialLessonPrice: editTeachingData.trialLessonPrice,\n          timezone: editTeachingData.timezone,\n        }));\n\n        const successMsg = t('profile.teachingInfoUpdateSuccess');\n        handleCloseEditTeachingInfoDialog();\n        setSuccess(successMsg);\n        setError('');\n      }\n    } catch (error) {\n      console.error('Teaching info update error:', error);\n      setError(error.response?.data?.message || t('profile.errors.updateFailed'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Función para manejar la edición de la aplicación del profesor\n  const handleEditApplication = () => {\n    setOpenEditApplicationDialog(true);\n  };\n\n  // Función para redirigir al profesor a la página de edición de aplicación\n  const prepareApplicationEdit = () => {\n    try {\n      // Cerrar el diálogo\n      setOpenEditApplicationDialog(false);\n\n      // Redirigir a la página de edición de aplicación\n      navigate('/teacher/edit-application');\n    } catch (error) {\n      console.error('Error preparing application edit:', error);\n      setError(t('profile.errors.applicationPreparation'));\n    }\n  };\n\n  // دوال حذف الحساب\n  const handleDeleteAccount = async () => {\n    setError('');\n    setSuccess('');\n    setLoading(true);\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.post('/api/users/request-delete', {}, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setDeleteDialog(false);\n        setDeleteCodeDialog(true);\n        setSuccess(t('profile.deleteCodeSent'));\n      }\n    } catch (err) {\n      setError(err.response?.data?.message || t('profile.errors.deleteRequest'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleVerifyDeleteCode = async () => {\n    if (!deleteCode.trim()) {\n      setError(t('profile.errors.codeRequired'));\n      return;\n    }\n\n    setError('');\n    setSuccess('');\n    setLoading(true);\n\n    try {\n      const token = localStorage.getItem('token');\n      // الحصول على المنطقة الزمنية للمستخدم\n      const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;\n\n      const response = await axios.post('/api/users/verify-delete', {\n        code: deleteCode,\n        timezone: timezone\n      }, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setDeleteCodeDialog(false);\n        setDeleteCode('');\n        setDeleteStatus('pending');\n        setSuccess(t('profile.deletePending'));\n      }\n    } catch (err) {\n      setError(err.response?.data?.message || t('profile.errors.invalidCode'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCancelDelete = async () => {\n    setError('');\n    setSuccess('');\n    setLoading(true);\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.post('/api/users/cancel-delete', {}, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setDeleteStatus('');\n        setDeleteScheduledAt(null);\n        setSuccess(t('profile.deleteCancelled'));\n      }\n    } catch (err) {\n      setError(err.response?.data?.message || t('profile.errors.cancelDelete'));\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // تحديث حالة طلب التعديل عند العودة من صفحة التعديل\n  useEffect(() => {\n    const handleVisibilityChange = () => {\n      if (!document.hidden) {\n        fetchProfileUpdateStatus();\n      }\n    };\n\n    document.addEventListener('visibilitychange', handleVisibilityChange);\n    window.addEventListener('focus', handleVisibilityChange);\n\n    return () => {\n      document.removeEventListener('visibilitychange', handleVisibilityChange);\n      window.removeEventListener('focus', handleVisibilityChange);\n    };\n  }, []);\n\n  return (\n    <Layout>\n      <Container maxWidth=\"lg\" sx={{ mt: 4, mb: 4 }}>\n        <Paper elevation={3} sx={{ p: 4 }}>\n          {/* Profile Picture Section */}\n          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 4 }}>\n            <Typography variant=\"h4\" component=\"h1\" gutterBottom>\n              {t('profile.title')}\n            </Typography>\n\n            <Box sx={{ position: 'relative', mb: 3 }}>\n              <Avatar\n                src={profileData.profilePictureUrl ? (\n                  profileData.profilePictureUrl.startsWith('http')\n                    ? profileData.profilePictureUrl\n                    : `https://allemnionline.com${profileData.profilePictureUrl}`\n                ) : ''}\n                alt={userData.fullName}\n                sx={{\n                  width: 120,\n                  height: 120,\n                  border: '2px solid',\n                  borderColor: 'primary.main',\n                  bgcolor: 'primary.main',\n                  fontSize: '3rem'\n                }}\n              >\n                {!profileData.profilePictureUrl && userData.fullName?.charAt(0)}\n              </Avatar>\n            </Box>\n\n            <Typography variant=\"h6\" gutterBottom>\n              {userData.fullName}\n            </Typography>\n            <Typography variant=\"body1\" color=\"textSecondary\" sx={{ mb: 3 }}>\n              {t(`role.${user?.role || 'teacher'}`)}\n            </Typography>\n\n            <Box sx={{ width: '100%', maxWidth: '500px', display: 'flex', flexDirection: 'column', gap: 2 }}>\n              {error && (\n                <Alert\n                  severity=\"error\"\n                  variant=\"filled\"\n                  sx={{\n                    width: '100%',\n                    '& .MuiAlert-message': {\n                      width: '100%',\n                      textAlign: 'center'\n                    }\n                  }}\n                >\n                  {error}\n                </Alert>\n              )}\n\n              {success && (\n                <Alert\n                  severity=\"success\"\n                  variant=\"filled\"\n                  sx={{\n                    width: '100%',\n                    '& .MuiAlert-message': {\n                      width: '100%',\n                      textAlign: 'center'\n                    }\n                  }}\n                >\n                  {success}\n                </Alert>\n              )}\n\n              {deleteStatus === 'pending' && (\n                <Alert\n                  severity=\"warning\"\n                  variant=\"filled\"\n                  sx={{\n                    width: '100%',\n                    '& .MuiAlert-message': {\n                      width: '100%',\n                      textAlign: 'center'\n                    }\n                  }}\n                >\n                  <Typography variant=\"body1\" sx={{ fontWeight: 'bold', mb: 1 }}>\n                    {t('profile.deletePendingAlert')}\n                  </Typography>\n                  <Typography variant=\"body2\">\n                    {t('profile.deletePendingMessage')}\n                  </Typography>\n                  {deleteScheduledAt && (\n                    <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                      {t('profile.deleteScheduledFor')}: {formatDateWithTimezone(deleteScheduledAt, userTimezone)}\n                    </Typography>\n                  )}\n                </Alert>\n              )}\n            </Box>\n          </Box>\n\n          <Paper elevation={3} sx={{ p: 4, mb: 4, borderRadius: 2 }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n              <Typography variant=\"h5\" gutterBottom sx={{ mb: 0 }}>\n                {t('profile.basicInfo')}\n              </Typography>\n              <Box>\n                <Button\n                  variant=\"outlined\"\n                  color=\"primary\"\n                  startIcon={<EditIcon />}\n                  onClick={handleOpenEditTeachingInfoDialog}\n                  sx={{ borderRadius: 2, mr: 2 }}\n                >\n                  {t('profile.editTeachingInfo')}\n                </Button>\n                <Button\n                  variant=\"outlined\"\n                  color=\"primary\"\n                  startIcon={<LockIcon />}\n                  onClick={handleOpenPasswordDialog}\n                  sx={{ borderRadius: 2, mr: 2 }}\n                >\n                  {t('profile.changePassword')}\n                </Button>\n                {deleteStatus === 'pending' ? (\n                  <Button\n                    variant=\"outlined\"\n                    color=\"warning\"\n                    onClick={handleCancelDelete}\n                    disabled={loading}\n                    sx={{ borderRadius: 2 }}\n                  >\n                    {loading ? t('common.loading') : t('profile.cancelDelete')}\n                  </Button>\n                ) : (\n                  <Button\n                    variant=\"outlined\"\n                    color=\"error\"\n                    startIcon={<DeleteIcon />}\n                    onClick={() => setDeleteDialog(true)}\n                    sx={{ borderRadius: 2 }}\n                  >\n                    {t('profile.deleteAccount')}\n                  </Button>\n                )}\n              </Box>\n            </Box>\n\n            {/* مربع حالة طلب التعديل */}\n            {profileUpdateStatus && (\n              <Paper\n                elevation={2}\n                sx={{\n                  p: 2,\n                  mb: 3,\n                  borderRadius: 2,\n                  backgroundColor: profileUpdateStatus.status === 'pending' ? '#fff3cd' :\n                                 profileUpdateStatus.status === 'approved' ? '#d1edff' : '#f8d7da'\n                }}\n              >\n                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                  {profileUpdateStatus.status === 'pending' && <WarningIcon color=\"warning\" sx={{ mr: 1 }} />}\n                  {profileUpdateStatus.status === 'approved' && <CheckCircleIcon color=\"success\" sx={{ mr: 1 }} />}\n                  {profileUpdateStatus.status === 'rejected' && <WarningIcon color=\"error\" sx={{ mr: 1 }} />}\n                  <Typography variant=\"h6\" component=\"h3\">\n                    {profileUpdateStatus.status === 'pending' && t('profile.updateStatus.pending')}\n                    {profileUpdateStatus.status === 'approved' && t('profile.updateStatus.approved')}\n                    {profileUpdateStatus.status === 'rejected' && t('profile.updateStatus.rejected')}\n                  </Typography>\n                </Box>\n                <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mb: 1 }}>\n                  {t('profile.updateStatus.requestDate')}: {new Date(profileUpdateStatus.createdAt).toLocaleDateString('ar-EG')}\n                </Typography>\n                {profileUpdateStatus.reviewedAt && (\n                  <Typography variant=\"body2\" color=\"textSecondary\" sx={{ mb: 1 }}>\n                    {t('profile.updateStatus.reviewDate')}: {new Date(profileUpdateStatus.reviewedAt).toLocaleDateString('ar-EG')}\n                  </Typography>\n                )}\n                {profileUpdateStatus.adminNotes && (\n                  <Box sx={{ mt: 1, p: 1, backgroundColor: 'rgba(0,0,0,0.05)', borderRadius: 1 }}>\n                    <Typography variant=\"body2\">\n                      <strong>{t('profile.updateStatus.adminNotes')}:</strong> {profileUpdateStatus.adminNotes}\n                    </Typography>\n                  </Box>\n                )}\n                {profileUpdateStatus.status === 'pending' && (\n                  <Typography variant=\"body2\" sx={{ mt: 1, fontStyle: 'italic' }}>\n                    {t('profile.updateStatus.pendingNote')}\n                  </Typography>\n                )}\n              </Paper>\n            )}\n\n            <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              <strong>{t('profile.fullName')}:</strong> {userData.fullName}\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\" sx={{ mb: 2 }}>\n              <strong>{t('profile.email')}:</strong> {userData.email}\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\">\n              <strong>{t('profile.gender')}:</strong> {t(`gender.${userData.gender}`)}\n            </Typography>\n          </Paper>\n\n          {/* Sección de compromiso del profesor */}\n          <Paper elevation={3} sx={{ p: 4, mb: 4, borderRadius: 2, bgcolor: 'background.paper' }}>\n            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\n              <Box>\n                <Typography variant=\"h5\" gutterBottom sx={{ mb: 0 }}>\n                  {t('teacher.commitment') || 'تعهد المعلم'}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  {t('teacher.commitmentProfileDescription') || 'حالة التعهد الخاص بك كمعلم في المنصة'}\n                </Typography>\n              </Box>\n              <Box\n                sx={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  p: 1,\n                  borderRadius: 1,\n                  bgcolor: profileData.commitmentAccepted ? 'success.light' : 'error.light',\n                  color: 'white'\n                }}\n              >\n                {profileData.commitmentAccepted ? (\n                  <Typography variant=\"body1\" sx={{ display: 'flex', alignItems: 'center' }}>\n                    <CheckCircleIcon sx={{ mr: 1 }} />\n                    {t('teacher.commitmentStatus.accepted') || 'تم الموافقة على التعهد'}\n                  </Typography>\n                ) : (\n                  <Typography variant=\"body1\" sx={{ display: 'flex', alignItems: 'center' }}>\n                    <WarningIcon sx={{ mr: 1 }} />\n                    {t('teacher.commitmentStatus.rejected') || 'لم توافق على التعهد بعد'}\n                  </Typography>\n                )}\n              </Box>\n            </Box>\n            {/* Eliminado el botón de leer y aceptar el compromiso */}\n          </Paper>\n\n          {/* Botón para editar la aplicación del profesor */}\n          {applicationStatus === 'approved' && (\n            <Paper elevation={3} sx={{ p: 4, mb: 4, borderRadius: 2, bgcolor: 'background.paper' }}>\n              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                <Box>\n                  <Typography variant=\"h5\" gutterBottom sx={{ mb: 0 }}>\n                    {t('teacher.application.title')}\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"text.secondary\">\n                    {t('teacher.application.editDescription')}\n                  </Typography>\n                </Box>\n                <Button\n                  variant=\"contained\"\n                  color=\"primary\"\n                  startIcon={<EditIcon />}\n                  onClick={handleEditApplication}\n                  sx={{ borderRadius: 2 }}\n                >\n                  {t('teacher.application.edit')}\n                </Button>\n              </Box>\n            </Paper>\n          )}\n\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              {t('teacher.profile.personalInfo')}\n            </Typography>\n            <Grid container spacing={3}>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.profile.country')}\n                  value={profileData.country}\n                  disabled\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.profile.residence')}\n                  value={profileData.residence}\n                  disabled\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.profile.nativeLanguage')}\n                  value={profileData.nativeLanguage}\n                  disabled\n                />\n              </Grid>\n            </Grid>\n\n            <Typography variant=\"h6\" sx={{ mt: 4, mb: 2 }}>\n              {t('teacher.profile.teachingInfo')}\n            </Typography>\n            <Grid container spacing={3}>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>{t('teacher.profile.teachingLanguages.title')}</InputLabel>\n                  <Select\n                    multiple\n                    value={profileData.teachingLanguages}\n                    disabled\n                    renderValue={(selected) => (\n                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                        {selected?.map((value) => (\n                          <Chip key={value} label={value} />\n                        )) || []}\n                      </Box>\n                    )}\n                  >\n                    {profileData.teachingLanguages?.map((lang) => (\n                      <MenuItem key={lang} value={lang}>\n                        {lang}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth>\n                  <InputLabel>{t('teacher.profile.courseTypes')}</InputLabel>\n                  <Select\n                    multiple\n                    value={profileData.courseTypes}\n                    disabled\n                    renderValue={(selected) => (\n                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                        {selected?.map((value) => (\n                          <Chip key={value} label={value} />\n                        )) || []}\n                      </Box>\n                    )}\n                  >\n                    {profileData.courseTypes?.map((type) => (\n                      <MenuItem key={type} value={type}>\n                        {type}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                </FormControl>\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.profile.pricePerLesson')}\n                  value={profileData.pricePerLesson}\n                  disabled\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.profile.trialLessonPrice')}\n                  value={profileData.trialLessonPrice}\n                  disabled\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                  }}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.profile.timezone')}\n                  value={profileData.timezone}\n                  disabled\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.profile.qualifications')}\n                  value={profileData.qualifications}\n                  disabled\n                  multiline\n                  rows={3}\n                />\n              </Grid>\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.profile.teachingExperience')}\n                  value={profileData.teachingExperience}\n                  disabled\n                />\n              </Grid>\n              <Grid item xs={12}>\n                <Paper elevation={2} sx={{ p: 3, mb: 3, borderRadius: 2 }}>\n                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n                    <Box>\n                      <Typography variant=\"h6\" gutterBottom sx={{ mb: 0 }}>\n                        {t('teacher.availableHours')}\n                      </Typography>\n                      <Typography variant=\"body2\" color=\"text.secondary\">\n                        {t('teacher.availableHoursProfileDescription')}\n                      </Typography>\n                    </Box>\n                    <Box>\n                      <Button\n                        variant=\"contained\"\n                        color=\"primary\"\n                        onClick={() => navigate('/teacher/view-hours')}\n                        sx={{ borderRadius: 2 }}\n                      >\n                        {t('teacher.viewAvailableHours')}\n                      </Button>\n                    </Box>\n                  </Box>\n                </Paper>\n              </Grid>\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" gutterBottom>\n                  {t('teacher.introVideo')}\n                </Typography>\n                {profileData.introVideoUrl ? (\n                  isYoutubeVideo(profileData.introVideoUrl) ? (\n                    // YouTube video\n                    <Box sx={{ position: 'relative', paddingTop: '56.25%' /* 16:9 Aspect Ratio */ }}>\n                      <ReactPlayer\n                        url={profileData.introVideoUrl}\n                        width=\"100%\"\n                        height=\"100%\"\n                        controls\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                        }}\n                        config={{\n                          youtube: {\n                            playerVars: { origin: window.location.origin }\n                          }\n                        }}\n                      />\n                    </Box>\n                  ) : isLocalVideoFile(profileData.introVideoUrl) ? (\n                    // Local video file\n                    <Box sx={{ position: 'relative', paddingTop: '56.25%' /* 16:9 Aspect Ratio */ }}>\n                      <video\n                        src={`https://allemnionline.com${profileData.introVideoUrl}`}\n                        width=\"100%\"\n                        height=\"100%\"\n                        controls\n                        style={{\n                          position: 'absolute',\n                          top: 0,\n                          left: 0,\n                        }}\n                      />\n                    </Box>\n                  ) : (\n                    // External video URL (not YouTube)\n                    <Box sx={{ textAlign: 'center', p: 2 }}>\n                      <Typography variant=\"body1\">\n                        <a href={profileData.introVideoUrl} target=\"_blank\" rel=\"noopener noreferrer\">\n                          {t('teacher.openVideo')}\n                        </a>\n                      </Typography>\n                    </Box>\n                  )\n                ) : (\n                  <Paper\n                    elevation={0}\n                    variant=\"outlined\"\n                    sx={{\n                      p: 2,\n                      backgroundColor: 'background.default',\n                      textAlign: 'center',\n                      color: 'text.secondary'\n                    }}\n                  >\n                    {t('teacher.noIntroVideo')}\n                  </Paper>\n                )}\n              </Grid>\n              <Grid item xs={12}>\n                <Typography variant=\"h6\" gutterBottom>\n                  {t('teacher.cv')}\n                </Typography>\n                <Paper\n                  elevation={0}\n                  variant=\"outlined\"\n                  sx={{\n                    p: 2,\n                    backgroundColor: 'background.default',\n                    whiteSpace: 'pre-wrap'  // This preserves line breaks\n                  }}\n                >\n                  {profileData.cv}\n                </Paper>\n              </Grid>\n            </Grid>\n          </Paper>\n        </Paper>\n      </Container>\n\n      {/* Password Update Dialog */}\n      <Dialog\n        open={openPasswordDialog}\n        onClose={handleClosePasswordDialog}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle>\n          {t('profile.changePassword')}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300 }}>\n            {error && (\n              <Alert severity=\"error\">\n                {error}\n              </Alert>\n            )}\n            {success && (\n              <Alert severity=\"success\">\n                {success}\n              </Alert>\n            )}\n            <TextField\n              label={t('profile.currentPassword')}\n              type={showPassword.currentPassword ? 'text' : 'password'}\n              value={passwordData.currentPassword}\n              onChange={handlePasswordChange('currentPassword')}\n              required\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      aria-label={t('profile.togglePasswordVisibility')}\n                      onClick={() => handleClickShowPassword('currentPassword')}\n                      edge=\"end\"\n                      type=\"button\"\n                    >\n                      {showPassword.currentPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n            <TextField\n              label={t('profile.newPassword')}\n              type={showPassword.newPassword ? 'text' : 'password'}\n              value={passwordData.newPassword}\n              onChange={handlePasswordChange('newPassword')}\n              required\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      aria-label={t('profile.togglePasswordVisibility')}\n                      onClick={() => handleClickShowPassword('newPassword')}\n                      edge=\"end\"\n                      type=\"button\"\n                    >\n                      {showPassword.newPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n            <TextField\n              label={t('profile.confirmPassword')}\n              type={showPassword.confirmPassword ? 'text' : 'password'}\n              value={passwordData.confirmPassword}\n              onChange={handlePasswordChange('confirmPassword')}\n              required\n              InputProps={{\n                endAdornment: (\n                  <InputAdornment position=\"end\">\n                    <IconButton\n                      aria-label={t('profile.togglePasswordVisibility')}\n                      onClick={() => handleClickShowPassword('confirmPassword')}\n                      edge=\"end\"\n                      type=\"button\"\n                    >\n                      {showPassword.confirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}\n                    </IconButton>\n                  </InputAdornment>\n                ),\n              }}\n            />\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleClosePasswordDialog}>{t('common.cancel')}</Button>\n          <Button type=\"submit\" variant=\"contained\" disabled={loading} onClick={handleUpdatePassword}>\n            {loading ? <CircularProgress size={24} /> : t('common.save')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n\n\n      {/* Edit Teaching Info Dialog */}\n      <Dialog\n        open={openEditTeachingInfoDialog}\n        onClose={handleCloseEditTeachingInfoDialog}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {t('profile.editTeachingInfo')}\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, minWidth: 300, mt: 2 }}>\n            {error && (\n              <Alert severity=\"error\">\n                {error}\n              </Alert>\n            )}\n            {success && (\n              <Alert severity=\"success\">\n                {success}\n              </Alert>\n            )}\n\n            <Grid container spacing={2}>\n              {/* Phone */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  fullWidth\n                  label={t('teacher.phone')}\n                  value={editTeachingData.phone}\n                  onChange={handleEditTeachingChange('phone')}\n                  error={!!fieldErrors.phone}\n                  helperText={fieldErrors.phone || t('teacher.phoneHelp')}\n                />\n              </Grid>\n\n              {/* Native Language */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  label={t('teacher.nativeLanguage')}\n                  value={editTeachingData.nativeLanguage}\n                  onChange={handleEditTeachingChange('nativeLanguage')}\n                  error={!!fieldErrors.nativeLanguage}\n                  helperText={fieldErrors.nativeLanguage || ''}\n                />\n              </Grid>\n\n              {/* Teaching Languages */}\n              <Grid item xs={12}>\n                <FormControl fullWidth required error={!!fieldErrors.teachingLanguages}>\n                  <InputLabel>{t('teacher.teachingLanguages')}</InputLabel>\n                  <Select\n                    multiple\n                    value={editTeachingData.teachingLanguages}\n                    onChange={handleEditTeachingChange('teachingLanguages')}\n                    renderValue={(selected) => (\n                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                        {selected?.map((value) => (\n                          <Chip\n                            key={value}\n                            label={languages.find(lang => lang.id === value)?.name || value}\n                          />\n                        )) || []}\n                      </Box>\n                    )}\n                  >\n                    {languages.map((lang) => (\n                      <MenuItem key={lang.id} value={lang.id}>\n                        {lang.name}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {fieldErrors.teachingLanguages && (\n                    <Typography variant=\"body2\" color=\"error\">\n                      {fieldErrors.teachingLanguages}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n\n              {/* Course Types */}\n              <Grid item xs={12}>\n                <FormControl fullWidth required error={!!fieldErrors.courseTypes}>\n                  <InputLabel>{t('teacher.courseTypes')}</InputLabel>\n                  <Select\n                    multiple\n                    value={editTeachingData.courseTypes}\n                    onChange={handleEditTeachingChange('courseTypes')}\n                    renderValue={(selected) => (\n                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>\n                        {selected?.map((value) => (\n                          <Chip\n                            key={value}\n                            label={categories.find(cat => cat.id === value)?.name || value}\n                          />\n                        )) || []}\n                      </Box>\n                    )}\n                  >\n                    {categories.map((category) => (\n                      <MenuItem key={category.id} value={category.id}>\n                        {category.name}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {fieldErrors.courseTypes && (\n                    <Typography variant=\"body2\" color=\"error\">\n                      {fieldErrors.courseTypes}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n\n              {/* Price per Lesson */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  type=\"number\"\n                  label={t('teacher.pricePerLesson')}\n                  placeholder={t('teacher.pricePerLessonPlaceholder')}\n                  value={editTeachingData.pricePerLesson}\n                  onChange={handleEditTeachingChange('pricePerLesson')}\n                  error={!!fieldErrors.pricePerLesson}\n                  helperText={fieldErrors.pricePerLesson || ''}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                  }}\n                  inputProps={{ min: 3, max: 100 }}\n                />\n\n                {/* Teacher's Earnings Display */}\n                {editTeachingData.pricePerLesson > 0 && (\n                  <Box sx={{ mt: 1, p: 1.5, bgcolor: 'background.paper', border: '1px dashed', borderColor: 'primary.main', borderRadius: 1 }}>\n                    <Typography variant=\"subtitle2\" color=\"primary\" gutterBottom>\n                      {t('teacher.yourEarnings') || 'ما ستحصل عليه بعد خصم العمولة:'}\n                    </Typography>\n                    <Typography variant=\"body1\" sx={{ fontWeight: 'bold', color: 'success.main' }}>\n                      ${calculateTeacherEarnings(editTeachingData.pricePerLesson).toFixed(2)}\n                    </Typography>\n                  </Box>\n                )}\n              </Grid>\n\n              {/* Trial Lesson Price */}\n              <Grid item xs={12} sm={6}>\n                <TextField\n                  required\n                  fullWidth\n                  type=\"number\"\n                  label={t('teacher.trialLessonPrice')}\n                  placeholder={t('teacher.trialLessonPricePlaceholder')}\n                  value={editTeachingData.trialLessonPrice}\n                  onChange={handleEditTeachingChange('trialLessonPrice')}\n                  error={!!fieldErrors.trialLessonPrice}\n                  helperText={fieldErrors.trialLessonPrice || ''}\n                  InputProps={{\n                    startAdornment: <InputAdornment position=\"start\">$</InputAdornment>,\n                  }}\n                  inputProps={{ min: 1, max: 99 }}\n                />\n              </Grid>\n\n              {/* Timezone */}\n              <Grid item xs={12} sm={6}>\n                <FormControl fullWidth required error={!!fieldErrors.timezone}>\n                  <InputLabel>{t('teacher.timezone')}</InputLabel>\n                  <Select\n                    value={editTeachingData.timezone}\n                    onChange={handleEditTeachingChange('timezone')}\n                  >\n                    {timezones.map((timezone) => (\n                      <MenuItem key={timezone.value} value={timezone.value}>\n                        {timezone.label}\n                      </MenuItem>\n                    ))}\n                  </Select>\n                  {fieldErrors.timezone && (\n                    <Typography variant=\"body2\" color=\"error\">\n                      {fieldErrors.timezone}\n                    </Typography>\n                  )}\n                </FormControl>\n              </Grid>\n            </Grid>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={handleCloseEditTeachingInfoDialog}>{t('common.cancel')}</Button>\n          <Button type=\"submit\" variant=\"contained\" disabled={loading} onClick={handleEditTeachingSubmit}>\n            {loading ? <CircularProgress size={24} /> : t('common.save')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Edit Application Dialog */}\n      <Dialog\n        open={openEditApplicationDialog}\n        onClose={() => setOpenEditApplicationDialog(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ bgcolor: 'warning.light', color: 'warning.contrastText' }}>\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <WarningIcon />\n            {t('teacher.application.warningTitle')}\n          </Box>\n        </DialogTitle>\n        <DialogContent>\n          <Box sx={{ mt: 2 }}>\n            <Alert severity=\"warning\" sx={{ mb: 2 }}>\n              {t('teacher.application.warningMessage')}\n            </Alert>\n            <Typography variant=\"body1\" paragraph>\n              {t('teacher.application.warningDescription1')}\n            </Typography>\n            <Typography variant=\"body1\" paragraph>\n              {t('teacher.application.warningDescription2')}\n            </Typography>\n            <Alert severity=\"info\" sx={{ mb: 2 }}>\n              <Typography variant=\"body2\" sx={{ fontWeight: 'bold' }}>\n                ملاحظة: ستستمر في التدريس بالبيانات الحالية حتى يتم مراجعة طلب التعديل والموافقة عليه من قبل الإدارة.\n              </Typography>\n            </Alert>\n            <Typography variant=\"body1\" sx={{ fontWeight: 'bold' }}>\n              {t('teacher.application.warningConfirmation')}\n            </Typography>\n          </Box>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setOpenEditApplicationDialog(false)}>\n            {t('common.cancel')}\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"warning\"\n            onClick={prepareApplicationEdit}\n          >\n            {t('teacher.application.confirmEdit')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Delete Account Confirmation Dialog */}\n      <Dialog open={deleteDialog} onClose={() => setDeleteDialog(false)}>\n        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <WarningIcon color=\"error\" />\n          {t('profile.deleteAccountTitle')}\n        </DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mb: 2 }}>\n            {t('profile.deleteAccountWarning')}\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            {t('profile.deleteAccountNote')}\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteDialog(false)}>\n            {t('common.cancel')}\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"error\"\n            onClick={handleDeleteAccount}\n            disabled={loading}\n            startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n          >\n            {loading ? t('common.loading') : t('profile.sendDeleteCode')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Delete Code Verification Dialog */}\n      <Dialog open={deleteCodeDialog} onClose={() => setDeleteCodeDialog(false)}>\n        <DialogTitle>{t('profile.verifyDeleteCode')}</DialogTitle>\n        <DialogContent>\n          <Typography variant=\"body1\" sx={{ mb: 2 }}>\n            {t('profile.deleteCodeSentMessage')}\n          </Typography>\n          <TextField\n            fullWidth\n            label={t('profile.deleteCode')}\n            value={deleteCode}\n            onChange={(e) => setDeleteCode(e.target.value)}\n            placeholder=\"123456\"\n            sx={{ mt: 1 }}\n          />\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setDeleteCodeDialog(false)}>\n            {t('common.cancel')}\n          </Button>\n          <Button\n            variant=\"contained\"\n            color=\"error\"\n            onClick={handleVerifyDeleteCode}\n            disabled={loading || !deleteCode.trim()}\n            startIcon={loading ? <CircularProgress size={20} /> : <DeleteIcon />}\n          >\n            {loading ? t('common.loading') : t('profile.confirmDelete')}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Layout>\n  );\n};\n\nexport default Profile;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,cAAc,KAAQ,eAAe,CAC9C,MAAO,CAAAC,KAAK,KAAM,mBAAmB,CACrC,OACEC,SAAS,CACTC,KAAK,CACLC,UAAU,CACVC,SAAS,CACTC,MAAM,CACNC,IAAI,CACJC,GAAG,CACHC,KAAK,CACLC,OAAO,CACPC,UAAU,CACVC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,MAAM,CACNC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,cAAc,CACdC,gBAAgB,KACX,eAAe,CACtB,OACEC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,UAAU,GAAI,CAAAC,cAAc,CAC5BC,aAAa,GAAI,CAAAC,iBAAiB,CAClCC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,WAAW,GAAI,CAAAC,eAAe,CAC9BC,MAAM,GAAI,CAAAC,UAAU,KACf,qBAAqB,CAC5B,OAASC,OAAO,KAAQ,4BAA4B,CACpD,MAAO,CAAAC,MAAM,KAAM,yBAAyB,CAC5C,MAAO,CAAAC,WAAW,KAAM,cAAc,CACtC,OAASC,SAAS,KAAQ,uBAAuB,CAEjD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,wBAAwB,CAAIC,KAAK,EAAK,CAC1C,KAAM,CAAAC,WAAW,CAAGC,MAAM,CAACF,KAAK,CAAC,CACjC,GAAI,CAACC,WAAW,EAAIA,WAAW,CAAG,CAAC,CAAE,MAAO,EAAC,CAE7C,GAAI,CAAAE,cAAc,CAClB,GAAIF,WAAW,GAAK,CAAC,CAAE,CACrBE,cAAc,CAAG,KAAK,CAAE;AAC1B,CAAC,IAAM,IAAIF,WAAW,GAAK,CAAC,CAAE,CAC5BE,cAAc,CAAG,IAAI,CAAE;AACzB,CAAC,IAAM,IAAIF,WAAW,GAAK,CAAC,CAAE,CAC5BE,cAAc,CAAG,IAAI,CAAE;AACzB,CAAC,IAAM,IAAIF,WAAW,GAAK,CAAC,CAAE,CAC5BE,cAAc,CAAG,KAAK,CAAE;AAC1B,CAAC,IAAM,CACLA,cAAc,CAAG,IAAI,CAAE;AACzB,CAEA,KAAM,CAAAC,eAAe,CAAGH,WAAW,EAAI,CAAC,CAAGE,cAAc,CAAC,CAC1D,MAAO,CAAAC,eAAe,CACxB,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIC,GAAG,EAAK,CACjC,GAAI,CAACA,GAAG,CAAE,MAAO,KAAI,CAErB;AACA,KAAM,CAAAC,MAAM,CAAG,iEAAiE,CAChF,KAAM,CAAAC,KAAK,CAAGF,GAAG,CAACE,KAAK,CAACD,MAAM,CAAC,CAE/B,MAAQ,CAAAC,KAAK,EAAIA,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,GAAK,EAAE,CAAID,KAAK,CAAC,CAAC,CAAC,CAAG,IAAI,CAC5D,CAAC,CAED;AACA,KAAM,CAAAE,gBAAgB,CAAIJ,GAAG,EAAK,CAChC,GAAI,CAACA,GAAG,CAAE,MAAO,MAAK,CACtB,MAAO,CAAAA,GAAG,CAACK,UAAU,CAAC,kBAAkB,CAAC,CAC3C,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAIN,GAAG,EAAK,CAC9B,GAAI,CAACA,GAAG,CAAE,MAAO,MAAK,CACtB,MAAO,CAAAD,iBAAiB,CAACC,GAAG,CAAC,GAAK,IAAI,CACxC,CAAC,CAED,KAAM,CAAAO,OAAO,CAAGA,CAAA,GAAM,KAAAC,kBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CACpB,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGnE,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAoE,QAAQ,CAAGrE,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEsE,IAAI,CAAEC,UAAW,CAAC,CAAG9B,OAAO,CAAC,CAAC,CACtC,KAAM,CAAC+B,KAAK,CAAEC,QAAQ,CAAC,CAAG3E,QAAQ,CAACsE,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAC1D,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG9E,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC+E,KAAK,CAAEC,QAAQ,CAAC,CAAGhF,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACiF,OAAO,CAAEC,UAAU,CAAC,CAAGlF,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAACmF,YAAY,CAAEC,eAAe,CAAC,CAAGpF,QAAQ,CAAC,CAC/CqF,eAAe,CAAE,KAAK,CACtBC,WAAW,CAAE,KAAK,CAClBC,eAAe,CAAE,KACnB,CAAC,CAAC,CACF,KAAM,CAACC,kBAAkB,CAAEC,qBAAqB,CAAC,CAAGzF,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC0F,yBAAyB,CAAEC,4BAA4B,CAAC,CAAG3F,QAAQ,CAAC,KAAK,CAAC,CACjF,KAAM,CAAC4F,0BAA0B,CAAEC,6BAA6B,CAAC,CAAG7F,QAAQ,CAAC,KAAK,CAAC,CACnF,KAAM,CAAC8F,SAAS,CAAEC,YAAY,CAAC,CAAG/F,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACgG,UAAU,CAAEC,aAAa,CAAC,CAAGjG,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAACkG,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGnG,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAACoG,mBAAmB,CAAEC,sBAAsB,CAAC,CAAGrG,QAAQ,CAAC,IAAI,CAAC,CAEpE;AACA,KAAM,CAACsG,YAAY,CAAEC,eAAe,CAAC,CAAGvG,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAACwG,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGzG,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC0G,UAAU,CAAEC,aAAa,CAAC,CAAG3G,QAAQ,CAAC,EAAE,CAAC,CAChD,KAAM,CAAC4G,YAAY,CAAEC,eAAe,CAAC,CAAG7G,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAAC8G,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG/G,QAAQ,CAAC,IAAI,CAAC,CAEhE;AACA,KAAM,CAACgH,YAAY,CAAEC,eAAe,CAAC,CAAGjH,QAAQ,CAAC,cAAc,CAAC,CAEhE;AACA,KAAM,CAAAkH,sBAAsB,CAAGA,CAACC,UAAU,CAAEC,QAAQ,GAAK,CACvD,GAAI,CAACD,UAAU,CAAE,MAAO,EAAE,CAE1B,GAAI,CACF,KAAM,CAAAE,IAAI,CAAG,GAAI,CAAAC,IAAI,CAACH,UAAU,CAAC,CAEjC;AACA,GAAIC,QAAQ,EAAIA,QAAQ,CAACxD,KAAK,CAAC,sBAAsB,CAAC,CAAE,CACtD,KAAM,CAAAA,KAAK,CAAGwD,QAAQ,CAACxD,KAAK,CAAC,4BAA4B,CAAC,CAC1D,KAAM,CAAA2D,IAAI,CAAG3D,KAAK,CAAC,CAAC,CAAC,GAAK,GAAG,CAAG,CAAC,CAAG,CAAC,CAAC,CACtC,KAAM,CAAA4D,KAAK,CAAGC,QAAQ,CAAC7D,KAAK,CAAC,CAAC,CAAC,CAAC,CAChC,KAAM,CAAA8D,OAAO,CAAGD,QAAQ,CAAC7D,KAAK,CAAC,CAAC,CAAC,CAAC,CAClC,KAAM,CAAA+D,aAAa,CAAGJ,IAAI,EAAIC,KAAK,CAAG,EAAE,CAAGE,OAAO,CAAC,CAEnD,KAAM,CAAAE,OAAO,CAAGP,IAAI,CAACQ,OAAO,CAAC,CAAC,CAAIR,IAAI,CAACS,iBAAiB,CAAC,CAAC,CAAG,KAAM,CACnE,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAT,IAAI,CAACM,OAAO,CAAID,aAAa,CAAG,KAAM,CAAC,CAE9D,MAAO,CAAAI,UAAU,CAACC,cAAc,CAAC1D,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,OAAO,CAAG,OAAO,CAAE,CAC3EqD,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAEhE,IAAI,CAACM,QAAQ,GAAK,IAC5B,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA,MAAO,CAAAyC,IAAI,CAACW,cAAc,CAAC1D,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,OAAO,CAAG,OAAO,CAAE,CACrEqD,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAEhE,IAAI,CAACM,QAAQ,GAAK,IAAI,CAC9B2D,QAAQ,CAAEnB,QACZ,CAAC,CAAC,CACJ,CACF,CAAE,MAAOrC,KAAK,CAAE,CACdyD,OAAO,CAACzD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9C,MAAO,IAAI,CAAAuC,IAAI,CAACH,UAAU,CAAC,CAACa,cAAc,CAAC,CAAC,CAC9C,CACF,CAAC,CAED;AACA,KAAM,CAACS,QAAQ,CAAEC,WAAW,CAAC,CAAG1I,QAAQ,CAAC,CACvC2I,QAAQ,CAAE,CAAAnE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoE,SAAS,GAAI,EAAE,CAC/BC,KAAK,CAAE,CAAArE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEqE,KAAK,GAAI,EAAE,CACxBC,MAAM,CAAE,CAAAtE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEsE,MAAM,GAAI,EAC1B,CAAC,CAAC,CAEF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAGhJ,QAAQ,CAAC,CAC/CqF,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EACnB,CAAC,CAAC,CAEF,KAAM,CAAC0D,WAAW,CAAEC,cAAc,CAAC,CAAGlJ,QAAQ,CAAC,CAC7CmJ,KAAK,CAAE,EAAE,CACTC,OAAO,CAAE,EAAE,CACXC,SAAS,CAAE,EAAE,CACbC,cAAc,CAAE,EAAE,CAClBC,iBAAiB,CAAE,EAAE,CACrBC,WAAW,CAAE,EAAE,CACfC,cAAc,CAAE,EAAE,CAClBC,kBAAkB,CAAE,EAAE,CACtBC,cAAc,CAAE,CAAC,CAAC,CAClBC,cAAc,CAAE,EAAE,CAClBC,gBAAgB,CAAE,EAAE,CACpBzC,QAAQ,CAAE,EAAE,CACZ0C,iBAAiB,CAAE,EAAE,CACrBC,EAAE,CAAE,EAAE,CACNC,aAAa,CAAE,EAAE,CACjBC,kBAAkB,CAAE,KACtB,CAAC,CAAC,CAIF,KAAM,CAACC,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGnK,QAAQ,CAAC,CACvDmJ,KAAK,CAAE,EAAE,CACTG,cAAc,CAAE,EAAE,CAClBC,iBAAiB,CAAE,EAAE,CACrBC,WAAW,CAAE,EAAE,CACfI,cAAc,CAAE,EAAE,CAClBC,gBAAgB,CAAE,EAAE,CACpBzC,QAAQ,CAAE,EACZ,CAAC,CAAC,CAEF,KAAM,CAACgD,WAAW,CAAEC,cAAc,CAAC,CAAGrK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAElD;AACAC,SAAS,CAAC,IAAM,CACd,GAAI,CAAAqK,KAAK,CACT,GAAIrF,OAAO,EAAIF,KAAK,CAAE,CACpBuF,KAAK,CAAGC,UAAU,CAAC,IAAM,CACvBrF,UAAU,CAAC,EAAE,CAAC,CACdF,QAAQ,CAAC,EAAE,CAAC,CACd,CAAC,CAAE,IAAI,CAAC,CACV,CACA,MAAO,IAAMwF,YAAY,CAACF,KAAK,CAAC,CAClC,CAAC,CAAE,CAACrF,OAAO,CAAEF,KAAK,CAAC,CAAC,CAEpB;AACA,KAAM,CAAA0F,wBAAwB,CAAG,KAAAA,CAAA,GAAY,CAC3C,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAtK,KAAK,CAACuK,GAAG,CAAC,oCAAoC,CAAC,CACtE,GAAID,QAAQ,CAACE,IAAI,CAAC3F,OAAO,EAAIyF,QAAQ,CAACE,IAAI,CAACC,SAAS,CAAE,CACpDxE,sBAAsB,CAACqE,QAAQ,CAACE,IAAI,CAACE,MAAM,CAAC,CAC9C,CAAC,IAAM,CACLzE,sBAAsB,CAAC,IAAI,CAAC,CAC9B,CACF,CAAE,MAAOtB,KAAK,CAAE,CACdyD,OAAO,CAACzD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC/D,CACF,CAAC,CAED9E,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8K,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5B,GAAI,CACF;AACA,KAAM,CAAAC,eAAe,CAAG,KAAM,CAAA5K,KAAK,CAACuK,GAAG,CAAC,kBAAkB,CAAC,CAC3D,GAAIK,eAAe,CAACJ,IAAI,CAAC3F,OAAO,CAAE,CAChC,KAAM,CAAET,IAAI,CAAEyG,OAAQ,CAAC,CAAGD,eAAe,CAACJ,IAAI,CAC9ClC,WAAW,CAACwC,IAAI,GAAK,CACnB,GAAGA,IAAI,CACPvC,QAAQ,CAAE,CAAAnE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoE,SAAS,GAAI,EAAE,CAC/BC,KAAK,CAAE,CAAArE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEqE,KAAK,GAAI,EAAE,CACxBC,MAAM,CAAE,CAAAtE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEsE,MAAM,GAAI,EAC1B,CAAC,CAAC,CAAC,CAEH;AACA3C,oBAAoB,CAAC,CAAA8E,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEE,MAAM,GAAI,EAAE,CAAC,CAE3C;AACA,KAAM,CAAAC,iBAAiB,CAAG,KAAM,CAAAhL,KAAK,CAACuK,GAAG,CAAC,oBAAoB,CAAC,CAC/D,KAAM,CAAAU,YAAY,CAAG,CAAC,CAAC,CACvBD,iBAAiB,CAACR,IAAI,CAACU,OAAO,CAACC,IAAI,EAAI,CACrCF,YAAY,CAACE,IAAI,CAACC,EAAE,CAAC,CAAGD,IAAI,CAACE,IAAI,CACnC,CAAC,CAAC,CACF1F,YAAY,CAACqF,iBAAiB,CAACR,IAAI,CAAC,CAAE;AAEtC;AACA,KAAM,CAAAc,kBAAkB,CAAG,KAAM,CAAAtL,KAAK,CAACuK,GAAG,CAAC,qBAAqB,CAAC,CACjE1E,aAAa,CAACyF,kBAAkB,CAACd,IAAI,CAAC,CAEtC;AACA,KAAM,CAAAe,aAAa,CAAGC,KAAK,CAACC,OAAO,CAACZ,OAAO,CAACa,kBAAkB,CAAC,CAC3Db,OAAO,CAACa,kBAAkB,CAACC,GAAG,CAACP,EAAE,EAAI,CACnC,KAAM,CAAAD,IAAI,CAAGH,iBAAiB,CAACR,IAAI,CAACoB,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACT,EAAE,GAAKA,EAAE,CAAC,CAC1D,MAAO,CAAAD,IAAI,CAAGA,IAAI,CAACE,IAAI,CAAGD,EAAE,CAC9B,CAAC,CAAC,CACF,EAAE,CAEN;AACA,KAAM,CAAAU,eAAe,CAAGN,KAAK,CAACC,OAAO,CAACZ,OAAO,CAACkB,YAAY,CAAC,CACvDlB,OAAO,CAACkB,YAAY,CAACJ,GAAG,CAACP,EAAE,EAAI,CAC7B,KAAM,CAAAY,QAAQ,CAAGV,kBAAkB,CAACd,IAAI,CAACoB,IAAI,CAACK,CAAC,EAAIA,CAAC,CAACb,EAAE,GAAKA,EAAE,CAAC,CAC/D,MAAO,CAAAY,QAAQ,CAAGA,QAAQ,CAACX,IAAI,CAAGD,EAAE,CACtC,CAAC,CAAC,CACF,EAAE,CAEN;AACAtC,cAAc,CAAC,CACbC,KAAK,CAAE,CAAA8B,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE9B,KAAK,GAAI,EAAE,CAC3BC,OAAO,CAAE,CAAA6B,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE7B,OAAO,GAAI,EAAE,CAC/BC,SAAS,CAAE,CAAA4B,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE5B,SAAS,GAAI,EAAE,CACnCC,cAAc,CAAE,CAAA2B,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEqB,eAAe,GAAI,EAAE,CAC9C/C,iBAAiB,CAAEoC,aAAa,CAAE;AAClCnC,WAAW,CAAE0C,eAAe,CAAE;AAC9BzC,cAAc,CAAE,CAAAwB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAExB,cAAc,GAAI,EAAE,CAC7CC,kBAAkB,CAAE,CAAAuB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEsB,mBAAmB,GAAI,EAAE,CACtD5C,cAAc,CAAE,CAAAsB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEuB,eAAe,GAAI,CAAC,CAAC,CAC9C5C,cAAc,CAAE,CAAAqB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEwB,gBAAgB,GAAI,EAAE,CAC/C5C,gBAAgB,CAAE,CAAAoB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEyB,kBAAkB,GAAI,EAAE,CACnDtF,QAAQ,CAAE,CAAA6D,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE7D,QAAQ,GAAI,EAAE,CAEjC0C,iBAAiB,CAAE,CAAAmB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE0B,mBAAmB,GAAI,EAAE,CACrD5C,EAAE,CAAE,CAAAkB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAElB,EAAE,GAAI,EAAE,CACrBC,aAAa,CAAE,CAAAiB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE2B,eAAe,GAAI,EAAE,CAC7C3C,kBAAkB,CAAE,CAAAgB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE4B,mBAAmB,GAAI,KACtD,CAAC,CAAC,CAEFrE,OAAO,CAACsE,GAAG,CAAC,sBAAsB,CAAE,CAClC,GAAG7B,OAAO,CACV4B,mBAAmB,CAAE5B,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE4B,mBAAmB,CACjD5C,kBAAkB,CAAE,CAAAgB,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE4B,mBAAmB,GAAI,KACtD,CAAC,CAAC,CACFrE,OAAO,CAACsE,GAAG,CAAC,4CAA4C,CAAE,CAAA7B,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAE4B,mBAAmB,GAAI,KAAK,CAAC,CAClG,CAEA;AACA,KAAM,CAAApC,wBAAwB,CAAC,CAAC,CAClC,CAAE,MAAO1F,KAAK,CAAE,CACdyD,OAAO,CAACzD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC5CC,QAAQ,CAACX,CAAC,CAAC,sBAAsB,CAAC,CAAC,CACrC,CACF,CAAC,CAED0G,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,CAAC1G,CAAC,CAAC,CAAC,CAEP;AACApE,SAAS,CAAC,IAAM,CACd,KAAM,CAAA8M,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,GAAI,CACF,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAxC,QAAQ,CAAG,KAAM,CAAAtK,KAAK,CAACuK,GAAG,CAAC,oBAAoB,CAAE,CACrDwC,OAAO,CAAE,CAAEC,aAAa,CAAE,UAAUJ,KAAK,EAAG,CAC9C,CAAC,CAAC,CAEF,GAAItC,QAAQ,CAACE,IAAI,CAACpG,IAAI,EAAIkG,QAAQ,CAACE,IAAI,CAACpG,IAAI,CAAC2G,MAAM,GAAK,kBAAkB,CAAE,CAC1EtE,eAAe,CAAC,SAAS,CAAC,CAC1BE,oBAAoB,CAAC2D,QAAQ,CAACE,IAAI,CAACpG,IAAI,CAAC6I,mBAAmB,CAAC,CAC9D,CAEA;AACA,GAAI3C,QAAQ,CAACE,IAAI,CAACpG,IAAI,CAAC4C,QAAQ,CAAE,CAC/BH,eAAe,CAACyD,QAAQ,CAACE,IAAI,CAACpG,IAAI,CAAC4C,QAAQ,CAAC,CAC9C,CAAC,IAAM,CACL,KAAM,CAAAkG,gBAAgB,CAAGC,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAAClF,QAAQ,CACzEtB,eAAe,CAACqG,gBAAgB,CAAC,CACjC;AACAlN,KAAK,CAACsN,GAAG,CAAC,qBAAqB,CAAE,CAAEtG,QAAQ,CAAEkG,gBAAiB,CAAC,CAAE,CAC/DH,OAAO,CAAE,CAAEC,aAAa,CAAE,UAAUJ,KAAK,EAAG,CAC9C,CAAC,CAAC,CAACW,KAAK,CAACC,GAAG,EAAIpF,OAAO,CAACsE,GAAG,CAAC,0BAA0B,CAAEc,GAAG,CAAC,CAAC,CAC/D,CACF,CAAE,MAAO7I,KAAK,CAAE,CACdyD,OAAO,CAACzD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACvD,CACF,CAAC,CAEDgI,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAc,oBAAoB,CAAIC,IAAI,EAAMC,KAAK,EAAK,CAChD/E,eAAe,CAAC,CAAE,GAAGD,YAAY,CAAE,CAAC+E,IAAI,EAAGC,KAAK,CAACC,MAAM,CAACC,KAAM,CAAC,CAAC,CAClE,CAAC,CAED,KAAM,CAAAC,uBAAuB,CAAIC,KAAK,EAAK,CACzC/I,eAAe,CAAC,CAAE,GAAGD,YAAY,CAAE,CAACgJ,KAAK,EAAG,CAAChJ,YAAY,CAACgJ,KAAK,CAAE,CAAC,CAAC,CACrE,CAAC,CAED,KAAM,CAAAC,wBAAwB,CAAGA,CAAA,GAAM3I,qBAAqB,CAAC,IAAI,CAAC,CAClE,KAAM,CAAA4I,yBAAyB,CAAGA,CAAA,GAAM,CACtC5I,qBAAqB,CAAC,KAAK,CAAC,CAC5BuD,eAAe,CAAC,CACd3D,eAAe,CAAE,EAAE,CACnBC,WAAW,CAAE,EAAE,CACfC,eAAe,CAAE,EACnB,CAAC,CAAC,CACFH,eAAe,CAAC,CACdC,eAAe,CAAE,KAAK,CACtBC,WAAW,CAAE,KAAK,CAClBC,eAAe,CAAE,KACnB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAA+I,oBAAoB,CAAG,KAAO,CAAAC,CAAC,EAAK,CACxCA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB1J,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,KAAM,CAAAwF,QAAQ,CAAG,KAAM,CAAAtK,KAAK,CAACsN,GAAG,CAAC,uBAAuB,CAAE,CACxDe,gBAAgB,CAAE1F,YAAY,CAAC1D,eAAe,CAC9CqJ,YAAY,CAAE3F,YAAY,CAACzD,WAC7B,CAAC,CAAC,CACFkD,OAAO,CAACsE,GAAG,CAAC,2BAA2B,CAAEpC,QAAQ,CAACE,IAAI,CAAC,CAEvD,GAAIF,QAAQ,CAACE,IAAI,CAAC3F,OAAO,CAAE,CACzB,KAAM,CAAA0J,UAAU,CAAGtK,CAAC,CAAC,+BAA+B,CAAC,CACrDgK,yBAAyB,CAAC,CAAC,CAC3BnJ,UAAU,CAACyJ,UAAU,CAAC,CACtB3J,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAAE,MAAOD,KAAK,CAAE,KAAA6J,eAAA,CAAAC,oBAAA,CACdrG,OAAO,CAACzD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC9CC,QAAQ,CAAC,EAAA4J,eAAA,CAAA7J,KAAK,CAAC2F,QAAQ,UAAAkE,eAAA,kBAAAC,oBAAA,CAAdD,eAAA,CAAgBhE,IAAI,UAAAiE,oBAAA,iBAApBA,oBAAA,CAAsBC,OAAO,GAAIzK,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAC7E,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAMD;AACA,KAAM,CAAAiK,gCAAgC,CAAGA,CAAA,GAAM,CAC7C;AACA,KAAM,CAAAC,mBAAmB,CAAG/F,WAAW,CAACM,iBAAiB,CAACwC,GAAG,CAACkD,QAAQ,EAAI,CACxE,KAAM,CAAAC,SAAS,CAAGpJ,SAAS,CAACkG,IAAI,CAACT,IAAI,EAAIA,IAAI,CAACE,IAAI,GAAKwD,QAAQ,CAAC,CAChE,MAAO,CAAAC,SAAS,CAAGA,SAAS,CAAC1D,EAAE,CAAGyD,QAAQ,CAC5C,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,aAAa,CAAGlG,WAAW,CAACO,WAAW,CAACuC,GAAG,CAACqD,OAAO,EAAI,CAC3D,KAAM,CAAAC,QAAQ,CAAGrJ,UAAU,CAACgG,IAAI,CAACsD,GAAG,EAAIA,GAAG,CAAC7D,IAAI,GAAK2D,OAAO,CAAC,CAC7D,MAAO,CAAAC,QAAQ,CAAGA,QAAQ,CAAC7D,EAAE,CAAG4D,OAAO,CACzC,CAAC,CAAC,CAEFjF,mBAAmB,CAAC,CAClBhB,KAAK,CAAEF,WAAW,CAACE,KAAK,CACxBG,cAAc,CAAEL,WAAW,CAACK,cAAc,CAC1CC,iBAAiB,CAAEyF,mBAAmB,CACtCxF,WAAW,CAAE2F,aAAa,CAC1BvF,cAAc,CAAEX,WAAW,CAACW,cAAc,CAC1CC,gBAAgB,CAAEZ,WAAW,CAACY,gBAAgB,CAC9CzC,QAAQ,CAAE6B,WAAW,CAAC7B,QACxB,CAAC,CAAC,CACFiD,cAAc,CAAC,CAAC,CAAC,CAAC,CAClBxE,6BAA6B,CAAC,IAAI,CAAC,CACrC,CAAC,CAED,KAAM,CAAA0J,iCAAiC,CAAGA,CAAA,GAAM,CAC9C1J,6BAA6B,CAAC,KAAK,CAAC,CACpCsE,mBAAmB,CAAC,CAClBhB,KAAK,CAAEF,WAAW,CAACE,KAAK,CACxBG,cAAc,CAAEL,WAAW,CAACK,cAAc,CAC1CC,iBAAiB,CAAEN,WAAW,CAACM,iBAAiB,CAChDC,WAAW,CAAEP,WAAW,CAACO,WAAW,CACpCI,cAAc,CAAEX,WAAW,CAACW,cAAc,CAC1CC,gBAAgB,CAAEZ,WAAW,CAACY,gBAAgB,CAC9CzC,QAAQ,CAAE6B,WAAW,CAAC7B,QACxB,CAAC,CAAC,CACJ,CAAC,CAED,KAAM,CAAAoI,wBAAwB,CAAI1B,IAAI,EAAMC,KAAK,EAAK,CACpD,KAAM,CAAAE,KAAK,CAAGF,KAAK,CAACC,MAAM,CAACC,KAAK,CAChC,KAAM,CAAAwB,OAAO,CAAG,CAAE,GAAGvF,gBAAgB,CAAE,CAAC4D,IAAI,EAAGG,KAAM,CAAC,CACtD9D,mBAAmB,CAACsF,OAAO,CAAC,CAE5B;AACA,GAAI3B,IAAI,GAAK,gBAAgB,EAAIA,IAAI,GAAK,kBAAkB,CAAE,CAC5D,KAAM,CAAAlE,cAAc,CAAGkE,IAAI,GAAK,gBAAgB,CAAGG,KAAK,CAAGwB,OAAO,CAAC7F,cAAc,CACjF,KAAM,CAAAC,gBAAgB,CAAGiE,IAAI,GAAK,kBAAkB,CAAGG,KAAK,CAAGwB,OAAO,CAAC5F,gBAAgB,CAEvF;AACAQ,cAAc,CAACa,IAAI,GAAK,CACtB,GAAGA,IAAI,CACPrB,gBAAgB,CAAE,EACpB,CAAC,CAAC,CAAC,CAEH;AACA,GAAID,cAAc,EAAIC,gBAAgB,CAAE,CACtC,KAAM,CAAA6F,YAAY,CAAGpM,MAAM,CAACsG,cAAc,CAAC,CAC3C,KAAM,CAAA+F,UAAU,CAAGrM,MAAM,CAACuG,gBAAgB,CAAC,CAE3C,GAAI8F,UAAU,CAAGD,YAAY,CAAE,CAC7BrF,cAAc,CAACa,IAAI,GAAK,CACtB,GAAGA,IAAI,CACPrB,gBAAgB,CAAExF,CAAC,CAAC,mCAAmC,CAAC,EAAI,iEAC9D,CAAC,CAAC,CAAC,CACL,CACF,CACF,CAAC,IAAM,CACL;AACAgG,cAAc,CAACa,IAAI,GAAK,CACtB,GAAGA,IAAI,CACP,CAAC4C,IAAI,EAAG,EACV,CAAC,CAAC,CAAC,CACL,CACF,CAAC,CAED,KAAM,CAAA8B,wBAAwB,CAAG,KAAO,CAAArB,CAAC,EAAK,CAC5CA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB1J,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CACdmF,cAAc,CAAC,CAAC,CAAC,CAAC,CAElB;AACA,KAAM,CAAAwF,MAAM,CAAG,CAAC,CAAC,CACjB,KAAM,CAAAC,cAAc,CAAG,CACrB,gBAAgB,CAAE,mBAAmB,CAAE,aAAa,CACpD,gBAAgB,CAAE,kBAAkB,CAAE,UAAU,CACjD,CAED;AACAA,cAAc,CAACxE,OAAO,CAAC6C,KAAK,EAAI,CAC9B,GAAI,CAACjE,gBAAgB,CAACiE,KAAK,CAAC,EACvBvC,KAAK,CAACC,OAAO,CAAC3B,gBAAgB,CAACiE,KAAK,CAAC,CAAC,EAAIjE,gBAAgB,CAACiE,KAAK,CAAC,CAACtK,MAAM,GAAK,CAAE,EAChFqG,gBAAgB,CAACiE,KAAK,CAAC,GAAK,EAAE,CAAE,CAClC0B,MAAM,CAAC1B,KAAK,CAAC,CAAG9J,CAAC,CAAC,kBAAkB,CAAC,CACvC,CACF,CAAC,CAAC,CAEF;AACA,GAAI6F,gBAAgB,CAACN,cAAc,GAAKM,gBAAgB,CAACN,cAAc,CAAG,CAAC,EAAIM,gBAAgB,CAACN,cAAc,CAAG,GAAG,CAAC,CAAE,CACrHiG,MAAM,CAACjG,cAAc,CAAGvF,CAAC,CAAC,oBAAoB,CAAC,EAAI,iCAAiC,CACtF,CAEA;AACA,GAAI,CAAC6F,gBAAgB,CAACL,gBAAgB,CAAE,CACtCgG,MAAM,CAAChG,gBAAgB,CAAGxF,CAAC,CAAC,kBAAkB,CAAC,CACjD,CAAC,IAAM,IAAIf,MAAM,CAAC4G,gBAAgB,CAACL,gBAAgB,CAAC,CAAGvG,MAAM,CAAC4G,gBAAgB,CAACN,cAAc,CAAC,CAAE,CAC9FiG,MAAM,CAAChG,gBAAgB,CAAGxF,CAAC,CAAC,mCAAmC,CAAC,EAAI,iEAAiE,CACvI,CAEA;AACA,GAAI0L,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAAChM,MAAM,CAAG,CAAC,CAAE,CAClCwG,cAAc,CAACwF,MAAM,CAAC,CACtB7K,QAAQ,CAACX,CAAC,CAAC,uBAAuB,CAAC,EAAI,+BAA+B,CAAC,CACvES,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA,GAAI,CACF,KAAM,CAAAmL,UAAU,CAAG,CACjB9G,KAAK,CAAEe,gBAAgB,CAACf,KAAK,CAC7BmD,eAAe,CAAEpC,gBAAgB,CAACZ,cAAc,CAChDwC,kBAAkB,CAAE5B,gBAAgB,CAACX,iBAAiB,CACtD4C,YAAY,CAAEjC,gBAAgB,CAACV,WAAW,CAC1CiD,gBAAgB,CAAEvC,gBAAgB,CAACN,cAAc,CAChD8C,kBAAkB,CAAExC,gBAAgB,CAACL,gBAAgB,CACtDzC,QAAQ,CAAE8C,gBAAgB,CAAC9C,QAC7B,CAAC,CAED,KAAM,CAAAsD,QAAQ,CAAG,KAAM,CAAAtK,KAAK,CAACsN,GAAG,CAAC,+BAA+B,CAAEuC,UAAU,CAAC,CAC7EzH,OAAO,CAACsE,GAAG,CAAC,gCAAgC,CAAEpC,QAAQ,CAACE,IAAI,CAAC,CAE5D,GAAIF,QAAQ,CAACE,IAAI,CAAC3F,OAAO,CAAE,CACzB;AACA,KAAM,CAAAiL,qBAAqB,CAAGhG,gBAAgB,CAACX,iBAAiB,CAACwC,GAAG,CAACoE,MAAM,EAAI,CAC7E,KAAM,CAAAjB,SAAS,CAAGpJ,SAAS,CAACkG,IAAI,CAACT,IAAI,EAAIA,IAAI,CAACC,EAAE,GAAK2E,MAAM,CAAC,CAC5D,MAAO,CAAAjB,SAAS,CAAGA,SAAS,CAACzD,IAAI,CAAG0E,MAAM,CAC5C,CAAC,CAAC,CAEF,KAAM,CAAAjE,eAAe,CAAGhC,gBAAgB,CAACV,WAAW,CAACuC,GAAG,CAACqE,KAAK,EAAI,CAChE,KAAM,CAAAf,QAAQ,CAAGrJ,UAAU,CAACgG,IAAI,CAACsD,GAAG,EAAIA,GAAG,CAAC9D,EAAE,GAAK4E,KAAK,CAAC,CACzD,MAAO,CAAAf,QAAQ,CAAGA,QAAQ,CAAC5D,IAAI,CAAG2E,KAAK,CACzC,CAAC,CAAC,CAEF;AACAlH,cAAc,CAACgC,IAAI,GAAK,CACtB,GAAGA,IAAI,CACP/B,KAAK,CAAEe,gBAAgB,CAACf,KAAK,CAC7BG,cAAc,CAAEY,gBAAgB,CAACZ,cAAc,CAC/CC,iBAAiB,CAAE2G,qBAAqB,CACxC1G,WAAW,CAAE0C,eAAe,CAC5BtC,cAAc,CAAEM,gBAAgB,CAACN,cAAc,CAC9CC,gBAAgB,CAAEK,gBAAgB,CAACL,gBAAgB,CACpDzC,QAAQ,CAAE8C,gBAAgB,CAAC9C,QAC7B,CAAC,CAAC,CAAC,CAEH,KAAM,CAAAuH,UAAU,CAAGtK,CAAC,CAAC,mCAAmC,CAAC,CACzDkL,iCAAiC,CAAC,CAAC,CACnCrK,UAAU,CAACyJ,UAAU,CAAC,CACtB3J,QAAQ,CAAC,EAAE,CAAC,CACd,CACF,CAAE,MAAOD,KAAK,CAAE,KAAAsL,gBAAA,CAAAC,qBAAA,CACd9H,OAAO,CAACzD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACnDC,QAAQ,CAAC,EAAAqL,gBAAA,CAAAtL,KAAK,CAAC2F,QAAQ,UAAA2F,gBAAA,kBAAAC,qBAAA,CAAdD,gBAAA,CAAgBzF,IAAI,UAAA0F,qBAAA,iBAApBA,qBAAA,CAAsBxB,OAAO,GAAIzK,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAC7E,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAyL,qBAAqB,CAAGA,CAAA,GAAM,CAClC5K,4BAA4B,CAAC,IAAI,CAAC,CACpC,CAAC,CAED;AACA,KAAM,CAAA6K,sBAAsB,CAAGA,CAAA,GAAM,CACnC,GAAI,CACF;AACA7K,4BAA4B,CAAC,KAAK,CAAC,CAEnC;AACApB,QAAQ,CAAC,2BAA2B,CAAC,CACvC,CAAE,MAAOQ,KAAK,CAAE,CACdyD,OAAO,CAACzD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzDC,QAAQ,CAACX,CAAC,CAAC,uCAAuC,CAAC,CAAC,CACtD,CACF,CAAC,CAED;AACA,KAAM,CAAAoM,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtCzL,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CACdJ,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAkI,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAxC,QAAQ,CAAG,KAAM,CAAAtK,KAAK,CAACsQ,IAAI,CAAC,2BAA2B,CAAE,CAAC,CAAC,CAAE,CACjEvD,OAAO,CAAE,CAAEC,aAAa,CAAE,UAAUJ,KAAK,EAAG,CAC9C,CAAC,CAAC,CAEF,GAAItC,QAAQ,CAACE,IAAI,CAAC3F,OAAO,CAAE,CACzBsB,eAAe,CAAC,KAAK,CAAC,CACtBE,mBAAmB,CAAC,IAAI,CAAC,CACzBvB,UAAU,CAACb,CAAC,CAAC,wBAAwB,CAAC,CAAC,CACzC,CACF,CAAE,MAAOuJ,GAAG,CAAE,KAAA+C,aAAA,CAAAC,kBAAA,CACZ5L,QAAQ,CAAC,EAAA2L,aAAA,CAAA/C,GAAG,CAAClD,QAAQ,UAAAiG,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAc/F,IAAI,UAAAgG,kBAAA,iBAAlBA,kBAAA,CAAoB9B,OAAO,GAAIzK,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAC5E,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA+L,sBAAsB,CAAG,KAAAA,CAAA,GAAY,CACzC,GAAI,CAACnK,UAAU,CAACoK,IAAI,CAAC,CAAC,CAAE,CACtB9L,QAAQ,CAACX,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAC1C,OACF,CAEAW,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CACdJ,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAkI,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C;AACA,KAAM,CAAA9F,QAAQ,CAAGmG,IAAI,CAACC,cAAc,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC,CAAClF,QAAQ,CAEjE,KAAM,CAAAmC,QAAQ,CAAG,KAAM,CAAAtK,KAAK,CAACsQ,IAAI,CAAC,0BAA0B,CAAE,CAC5DK,IAAI,CAAErK,UAAU,CAChBU,QAAQ,CAAEA,QACZ,CAAC,CAAE,CACD+F,OAAO,CAAE,CAAEC,aAAa,CAAE,UAAUJ,KAAK,EAAG,CAC9C,CAAC,CAAC,CAEF,GAAItC,QAAQ,CAACE,IAAI,CAAC3F,OAAO,CAAE,CACzBwB,mBAAmB,CAAC,KAAK,CAAC,CAC1BE,aAAa,CAAC,EAAE,CAAC,CACjBE,eAAe,CAAC,SAAS,CAAC,CAC1B3B,UAAU,CAACb,CAAC,CAAC,uBAAuB,CAAC,CAAC,CACxC,CACF,CAAE,MAAOuJ,GAAG,CAAE,KAAAoD,cAAA,CAAAC,mBAAA,CACZjM,QAAQ,CAAC,EAAAgM,cAAA,CAAApD,GAAG,CAAClD,QAAQ,UAAAsG,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcpG,IAAI,UAAAqG,mBAAA,iBAAlBA,mBAAA,CAAoBnC,OAAO,GAAIzK,CAAC,CAAC,4BAA4B,CAAC,CAAC,CAC1E,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAoM,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACrClM,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CACdJ,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAkI,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAxC,QAAQ,CAAG,KAAM,CAAAtK,KAAK,CAACsQ,IAAI,CAAC,0BAA0B,CAAE,CAAC,CAAC,CAAE,CAChEvD,OAAO,CAAE,CAAEC,aAAa,CAAE,UAAUJ,KAAK,EAAG,CAC9C,CAAC,CAAC,CAEF,GAAItC,QAAQ,CAACE,IAAI,CAAC3F,OAAO,CAAE,CACzB4B,eAAe,CAAC,EAAE,CAAC,CACnBE,oBAAoB,CAAC,IAAI,CAAC,CAC1B7B,UAAU,CAACb,CAAC,CAAC,yBAAyB,CAAC,CAAC,CAC1C,CACF,CAAE,MAAOuJ,GAAG,CAAE,KAAAuD,cAAA,CAAAC,mBAAA,CACZpM,QAAQ,CAAC,EAAAmM,cAAA,CAAAvD,GAAG,CAAClD,QAAQ,UAAAyG,cAAA,kBAAAC,mBAAA,CAAZD,cAAA,CAAcvG,IAAI,UAAAwG,mBAAA,iBAAlBA,mBAAA,CAAoBtC,OAAO,GAAIzK,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAC3E,CAAC,OAAS,CACRS,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA7E,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoR,sBAAsB,CAAGA,CAAA,GAAM,CACnC,GAAI,CAACC,QAAQ,CAACC,MAAM,CAAE,CACpB9G,wBAAwB,CAAC,CAAC,CAC5B,CACF,CAAC,CAED6G,QAAQ,CAACE,gBAAgB,CAAC,kBAAkB,CAAEH,sBAAsB,CAAC,CACrEI,MAAM,CAACD,gBAAgB,CAAC,OAAO,CAAEH,sBAAsB,CAAC,CAExD,MAAO,IAAM,CACXC,QAAQ,CAACI,mBAAmB,CAAC,kBAAkB,CAAEL,sBAAsB,CAAC,CACxEI,MAAM,CAACC,mBAAmB,CAAC,OAAO,CAAEL,sBAAsB,CAAC,CAC7D,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEnO,KAAA,CAACN,MAAM,EAAA+O,QAAA,eACL3O,IAAA,CAAC3C,SAAS,EAACuR,QAAQ,CAAC,IAAI,CAACC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAC5CzO,KAAA,CAAC5C,KAAK,EAAC0R,SAAS,CAAE,CAAE,CAACH,EAAE,CAAE,CAAEI,CAAC,CAAE,CAAE,CAAE,CAAAN,QAAA,eAEhCzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAQ,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACjF3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAZ,QAAA,CACjDtN,CAAC,CAAC,eAAe,CAAC,CACT,CAAC,cAEbrB,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEW,QAAQ,CAAE,UAAU,CAAET,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cACvC3O,IAAA,CAAC5B,MAAM,EACLqR,GAAG,CAAExJ,WAAW,CAACa,iBAAiB,CAChCb,WAAW,CAACa,iBAAiB,CAAC/F,UAAU,CAAC,MAAM,CAAC,CAC5CkF,WAAW,CAACa,iBAAiB,CAC7B,4BAA4Bb,WAAW,CAACa,iBAAiB,EAAE,CAC7D,EAAG,CACP4I,GAAG,CAAEjK,QAAQ,CAACE,QAAS,CACvBkJ,EAAE,CAAE,CACFc,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,WAAW,CACnBC,WAAW,CAAE,cAAc,CAC3BC,OAAO,CAAE,cAAc,CACvBC,QAAQ,CAAE,MACZ,CAAE,CAAArB,QAAA,CAED,CAAC1I,WAAW,CAACa,iBAAiB,IAAA5F,kBAAA,CAAIuE,QAAQ,CAACE,QAAQ,UAAAzE,kBAAA,iBAAjBA,kBAAA,CAAmB+O,MAAM,CAAC,CAAC,CAAC,EACzD,CAAC,CACN,CAAC,cAENjQ,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAZ,QAAA,CAClClJ,QAAQ,CAACE,QAAQ,CACR,CAAC,cACb3F,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,eAAe,CAACrB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAC7DtN,CAAC,CAAC,QAAQ,CAAAG,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAE2O,IAAI,GAAI,SAAS,EAAE,CAAC,CAC3B,CAAC,cAEbjQ,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEc,KAAK,CAAE,MAAM,CAAEf,QAAQ,CAAE,OAAO,CAAEM,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEiB,GAAG,CAAE,CAAE,CAAE,CAAAzB,QAAA,EAC7F5M,KAAK,eACJ/B,IAAA,CAACpC,KAAK,EACJyS,QAAQ,CAAC,OAAO,CAChBhB,OAAO,CAAC,QAAQ,CAChBR,EAAE,CAAE,CACFc,KAAK,CAAE,MAAM,CACb,qBAAqB,CAAE,CACrBA,KAAK,CAAE,MAAM,CACbW,SAAS,CAAE,QACb,CACF,CAAE,CAAA3B,QAAA,CAED5M,KAAK,CACD,CACR,CAEAE,OAAO,eACNjC,IAAA,CAACpC,KAAK,EACJyS,QAAQ,CAAC,SAAS,CAClBhB,OAAO,CAAC,QAAQ,CAChBR,EAAE,CAAE,CACFc,KAAK,CAAE,MAAM,CACb,qBAAqB,CAAE,CACrBA,KAAK,CAAE,MAAM,CACbW,SAAS,CAAE,QACb,CACF,CAAE,CAAA3B,QAAA,CAED1M,OAAO,CACH,CACR,CAEA2B,YAAY,GAAK,SAAS,eACzB1D,KAAA,CAACtC,KAAK,EACJyS,QAAQ,CAAC,SAAS,CAClBhB,OAAO,CAAC,QAAQ,CAChBR,EAAE,CAAE,CACFc,KAAK,CAAE,MAAM,CACb,qBAAqB,CAAE,CACrBA,KAAK,CAAE,MAAM,CACbW,SAAS,CAAE,QACb,CACF,CAAE,CAAA3B,QAAA,eAEF3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAE0B,UAAU,CAAE,MAAM,CAAExB,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAC3DtN,CAAC,CAAC,4BAA4B,CAAC,CACtB,CAAC,cACbrB,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAAAV,QAAA,CACxBtN,CAAC,CAAC,8BAA8B,CAAC,CACxB,CAAC,CACZyC,iBAAiB,eAChB5D,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,EACvCtN,CAAC,CAAC,4BAA4B,CAAC,CAAC,IAAE,CAAC6C,sBAAsB,CAACJ,iBAAiB,CAAEE,YAAY,CAAC,EACjF,CACb,EACI,CACR,EACE,CAAC,EACH,CAAC,cAEN9D,KAAA,CAAC5C,KAAK,EAAC0R,SAAS,CAAE,CAAE,CAACH,EAAE,CAAE,CAAEI,CAAC,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAC,CAAEyB,YAAY,CAAE,CAAE,CAAE,CAAA7B,QAAA,eACxDzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEuB,cAAc,CAAE,eAAe,CAAErB,UAAU,CAAE,QAAQ,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACzF3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACE,YAAY,MAACV,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACjDtN,CAAC,CAAC,mBAAmB,CAAC,CACb,CAAC,cACbnB,KAAA,CAACvC,GAAG,EAAAgR,QAAA,eACF3O,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,UAAU,CAClBa,KAAK,CAAC,SAAS,CACfQ,SAAS,cAAE1Q,IAAA,CAACZ,QAAQ,GAAE,CAAE,CACxBuR,OAAO,CAAE5E,gCAAiC,CAC1C8C,EAAE,CAAE,CAAE2B,YAAY,CAAE,CAAC,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,CAE9BtN,CAAC,CAAC,0BAA0B,CAAC,CACxB,CAAC,cACTrB,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,UAAU,CAClBa,KAAK,CAAC,SAAS,CACfQ,SAAS,cAAE1Q,IAAA,CAACd,QAAQ,GAAE,CAAE,CACxByR,OAAO,CAAEvF,wBAAyB,CAClCyD,EAAE,CAAE,CAAE2B,YAAY,CAAE,CAAC,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,CAE9BtN,CAAC,CAAC,wBAAwB,CAAC,CACtB,CAAC,CACRuC,YAAY,GAAK,SAAS,cACzB5D,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,UAAU,CAClBa,KAAK,CAAC,SAAS,CACfS,OAAO,CAAEzC,kBAAmB,CAC5B2C,QAAQ,CAAEhP,OAAQ,CAClBgN,EAAE,CAAE,CAAE2B,YAAY,CAAE,CAAE,CAAE,CAAA7B,QAAA,CAEvB9M,OAAO,CAAGR,CAAC,CAAC,gBAAgB,CAAC,CAAGA,CAAC,CAAC,sBAAsB,CAAC,CACpD,CAAC,cAETrB,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,UAAU,CAClBa,KAAK,CAAC,OAAO,CACbQ,SAAS,cAAE1Q,IAAA,CAACN,UAAU,GAAE,CAAE,CAC1BiR,OAAO,CAAEA,CAAA,GAAMpN,eAAe,CAAC,IAAI,CAAE,CACrCsL,EAAE,CAAE,CAAE2B,YAAY,CAAE,CAAE,CAAE,CAAA7B,QAAA,CAEvBtN,CAAC,CAAC,uBAAuB,CAAC,CACrB,CACT,EACE,CAAC,EACH,CAAC,CAGL+B,mBAAmB,eAClBlD,KAAA,CAAC5C,KAAK,EACJ0R,SAAS,CAAE,CAAE,CACbH,EAAE,CAAE,CACFI,CAAC,CAAE,CAAC,CACJF,EAAE,CAAE,CAAC,CACLyB,YAAY,CAAE,CAAC,CACfM,eAAe,CAAE1N,mBAAmB,CAAC+E,MAAM,GAAK,SAAS,CAAG,SAAS,CACtD/E,mBAAmB,CAAC+E,MAAM,GAAK,UAAU,CAAG,SAAS,CAAG,SACzE,CAAE,CAAAwG,QAAA,eAEFzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,EACvDvL,mBAAmB,CAAC+E,MAAM,GAAK,SAAS,eAAInI,IAAA,CAACV,WAAW,EAAC4Q,KAAK,CAAC,SAAS,CAACrB,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAC1FxN,mBAAmB,CAAC+E,MAAM,GAAK,UAAU,eAAInI,IAAA,CAACR,eAAe,EAAC0Q,KAAK,CAAC,SAAS,CAACrB,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAC/FxN,mBAAmB,CAAC+E,MAAM,GAAK,UAAU,eAAInI,IAAA,CAACV,WAAW,EAAC4Q,KAAK,CAAC,OAAO,CAACrB,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1F1Q,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAAX,QAAA,EACpCvL,mBAAmB,CAAC+E,MAAM,GAAK,SAAS,EAAI9G,CAAC,CAAC,8BAA8B,CAAC,CAC7E+B,mBAAmB,CAAC+E,MAAM,GAAK,UAAU,EAAI9G,CAAC,CAAC,+BAA+B,CAAC,CAC/E+B,mBAAmB,CAAC+E,MAAM,GAAK,UAAU,EAAI9G,CAAC,CAAC,+BAA+B,CAAC,EACtE,CAAC,EACV,CAAC,cACNnB,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,eAAe,CAACrB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,EAC7DtN,CAAC,CAAC,kCAAkC,CAAC,CAAC,IAAE,CAAC,GAAI,CAAAiD,IAAI,CAAClB,mBAAmB,CAAC2N,SAAS,CAAC,CAACC,kBAAkB,CAAC,OAAO,CAAC,EACnG,CAAC,CACZ5N,mBAAmB,CAAC6N,UAAU,eAC7B/Q,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,eAAe,CAACrB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,EAC7DtN,CAAC,CAAC,iCAAiC,CAAC,CAAC,IAAE,CAAC,GAAI,CAAAiD,IAAI,CAAClB,mBAAmB,CAAC6N,UAAU,CAAC,CAACD,kBAAkB,CAAC,OAAO,CAAC,EACnG,CACb,CACA5N,mBAAmB,CAAC8N,UAAU,eAC7BlR,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEG,CAAC,CAAE,CAAC,CAAE6B,eAAe,CAAE,kBAAkB,CAAEN,YAAY,CAAE,CAAE,CAAE,CAAA7B,QAAA,cAC7EzO,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAAAV,QAAA,eACzBzO,KAAA,WAAAyO,QAAA,EAAStN,CAAC,CAAC,iCAAiC,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAAC+B,mBAAmB,CAAC8N,UAAU,EAC9E,CAAC,CACV,CACN,CACA9N,mBAAmB,CAAC+E,MAAM,GAAK,SAAS,eACvCnI,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEqC,SAAS,CAAE,QAAS,CAAE,CAAAxC,QAAA,CAC5DtN,CAAC,CAAC,kCAAkC,CAAC,CAC5B,CACb,EACI,CACR,cAEDnB,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAACrB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC/DzO,KAAA,WAAAyO,QAAA,EAAStN,CAAC,CAAC,kBAAkB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACoE,QAAQ,CAACE,QAAQ,EAClD,CAAC,cACbzF,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAACrB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eAC/DzO,KAAA,WAAAyO,QAAA,EAAStN,CAAC,CAAC,eAAe,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACoE,QAAQ,CAACI,KAAK,EAC5C,CAAC,cACb3F,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAvB,QAAA,eAChDzO,KAAA,WAAAyO,QAAA,EAAStN,CAAC,CAAC,gBAAgB,CAAC,CAAC,GAAC,EAAQ,CAAC,IAAC,CAACA,CAAC,CAAC,UAAUoE,QAAQ,CAACK,MAAM,EAAE,CAAC,EAC7D,CAAC,EACR,CAAC,cAGR9F,IAAA,CAAC1C,KAAK,EAAC0R,SAAS,CAAE,CAAE,CAACH,EAAE,CAAE,CAAEI,CAAC,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAC,CAAEyB,YAAY,CAAE,CAAC,CAAET,OAAO,CAAE,kBAAmB,CAAE,CAAApB,QAAA,cACrFzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEuB,cAAc,CAAE,eAAe,CAAErB,UAAU,CAAE,QAAQ,CAAEL,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACzFzO,KAAA,CAACvC,GAAG,EAAAgR,QAAA,eACF3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACE,YAAY,MAACV,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACjDtN,CAAC,CAAC,oBAAoB,CAAC,EAAI,aAAa,CAC/B,CAAC,cACbrB,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAvB,QAAA,CAC/CtN,CAAC,CAAC,sCAAsC,CAAC,EAAI,sCAAsC,CAC1E,CAAC,EACV,CAAC,cACNrB,IAAA,CAACrC,GAAG,EACFkR,EAAE,CAAE,CACFK,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBH,CAAC,CAAE,CAAC,CACJuB,YAAY,CAAE,CAAC,CACfT,OAAO,CAAE9J,WAAW,CAACgB,kBAAkB,CAAG,eAAe,CAAG,aAAa,CACzEiJ,KAAK,CAAE,OACT,CAAE,CAAAvB,QAAA,CAED1I,WAAW,CAACgB,kBAAkB,cAC7B/G,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAT,QAAA,eACxE3O,IAAA,CAACR,eAAe,EAACqP,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CACjCvP,CAAC,CAAC,mCAAmC,CAAC,EAAI,wBAAwB,EACzD,CAAC,cAEbnB,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAS,CAAE,CAAAT,QAAA,eACxE3O,IAAA,CAACV,WAAW,EAACuP,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAC7BvP,CAAC,CAAC,mCAAmC,CAAC,EAAI,yBAAyB,EAC1D,CACb,CACE,CAAC,EACH,CAAC,CAED,CAAC,CAGP6B,iBAAiB,GAAK,UAAU,eAC/BlD,IAAA,CAAC1C,KAAK,EAAC0R,SAAS,CAAE,CAAE,CAACH,EAAE,CAAE,CAAEI,CAAC,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAC,CAAEyB,YAAY,CAAE,CAAC,CAAET,OAAO,CAAE,kBAAmB,CAAE,CAAApB,QAAA,cACrFzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEuB,cAAc,CAAE,eAAe,CAAErB,UAAU,CAAE,QAAS,CAAE,CAAAT,QAAA,eAClFzO,KAAA,CAACvC,GAAG,EAAAgR,QAAA,eACF3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACE,YAAY,MAACV,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACjDtN,CAAC,CAAC,2BAA2B,CAAC,CACrB,CAAC,cACbrB,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAvB,QAAA,CAC/CtN,CAAC,CAAC,qCAAqC,CAAC,CAC/B,CAAC,EACV,CAAC,cACNrB,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,WAAW,CACnBa,KAAK,CAAC,SAAS,CACfQ,SAAS,cAAE1Q,IAAA,CAACZ,QAAQ,GAAE,CAAE,CACxBuR,OAAO,CAAEpD,qBAAsB,CAC/BsB,EAAE,CAAE,CAAE2B,YAAY,CAAE,CAAE,CAAE,CAAA7B,QAAA,CAEvBtN,CAAC,CAAC,0BAA0B,CAAC,CACxB,CAAC,EACN,CAAC,CACD,CACR,cAEDnB,KAAA,CAAC5C,KAAK,EAACuR,EAAE,CAAE,CAAEI,CAAC,CAAE,CAAE,CAAE,CAAAN,QAAA,eAClB3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAZ,QAAA,CAClCtN,CAAC,CAAC,8BAA8B,CAAC,CACxB,CAAC,cACbnB,KAAA,CAACxC,IAAI,EAAC0T,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA1C,QAAA,eACzB3O,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,yBAAyB,CAAE,CACpC4J,KAAK,CAAEhF,WAAW,CAACG,OAAQ,CAC3ByK,QAAQ,MACT,CAAC,CACE,CAAC,cACP7Q,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,2BAA2B,CAAE,CACtC4J,KAAK,CAAEhF,WAAW,CAACI,SAAU,CAC7BwK,QAAQ,MACT,CAAC,CACE,CAAC,cACP7Q,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,gCAAgC,CAAE,CAC3C4J,KAAK,CAAEhF,WAAW,CAACK,cAAe,CAClCuK,QAAQ,MACT,CAAC,CACE,CAAC,EACH,CAAC,cAEP7Q,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACR,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAC3CtN,CAAC,CAAC,8BAA8B,CAAC,CACxB,CAAC,cACbnB,KAAA,CAACxC,IAAI,EAAC0T,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA1C,QAAA,eACzB3O,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvBzO,KAAA,CAACnC,WAAW,EAAC0T,SAAS,MAAA9C,QAAA,eACpB3O,IAAA,CAAChC,UAAU,EAAA2Q,QAAA,CAAEtN,CAAC,CAAC,yCAAyC,CAAC,CAAa,CAAC,cACvErB,IAAA,CAAC/B,MAAM,EACL0T,QAAQ,MACR1G,KAAK,CAAEhF,WAAW,CAACM,iBAAkB,CACrCsK,QAAQ,MACRe,WAAW,CAAGC,QAAQ,eACpB7R,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAE4C,QAAQ,CAAE,MAAM,CAAE1B,GAAG,CAAE,GAAI,CAAE,CAAAzB,QAAA,CACtD,CAAAkD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE9I,GAAG,CAAEkC,KAAK,eACnBjL,IAAA,CAAC7B,IAAI,EAAauT,KAAK,CAAEzG,KAAM,EAApBA,KAAsB,CAClC,CAAC,GAAI,EAAE,CACL,CACL,CAAA0D,QAAA,EAAAxN,qBAAA,CAED8E,WAAW,CAACM,iBAAiB,UAAApF,qBAAA,iBAA7BA,qBAAA,CAA+B4H,GAAG,CAAER,IAAI,eACvCvI,IAAA,CAAC9B,QAAQ,EAAY+M,KAAK,CAAE1C,IAAK,CAAAoG,QAAA,CAC9BpG,IAAI,EADQA,IAEL,CACX,CAAC,CACI,CAAC,EACE,CAAC,CACV,CAAC,cACPvI,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvBzO,KAAA,CAACnC,WAAW,EAAC0T,SAAS,MAAA9C,QAAA,eACpB3O,IAAA,CAAChC,UAAU,EAAA2Q,QAAA,CAAEtN,CAAC,CAAC,6BAA6B,CAAC,CAAa,CAAC,cAC3DrB,IAAA,CAAC/B,MAAM,EACL0T,QAAQ,MACR1G,KAAK,CAAEhF,WAAW,CAACO,WAAY,CAC/BqK,QAAQ,MACRe,WAAW,CAAGC,QAAQ,eACpB7R,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAE4C,QAAQ,CAAE,MAAM,CAAE1B,GAAG,CAAE,GAAI,CAAE,CAAAzB,QAAA,CACtD,CAAAkD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE9I,GAAG,CAAEkC,KAAK,eACnBjL,IAAA,CAAC7B,IAAI,EAAauT,KAAK,CAAEzG,KAAM,EAApBA,KAAsB,CAClC,CAAC,GAAI,EAAE,CACL,CACL,CAAA0D,QAAA,EAAAvN,qBAAA,CAED6E,WAAW,CAACO,WAAW,UAAApF,qBAAA,iBAAvBA,qBAAA,CAAyB2H,GAAG,CAAEgJ,IAAI,eACjC/R,IAAA,CAAC9B,QAAQ,EAAY+M,KAAK,CAAE8G,IAAK,CAAApD,QAAA,CAC9BoD,IAAI,EADQA,IAEL,CACX,CAAC,CACI,CAAC,EACE,CAAC,CACV,CAAC,cACP/R,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,gCAAgC,CAAE,CAC3C4J,KAAK,CAAEhF,WAAW,CAACW,cAAe,CAClCiK,QAAQ,MACRmB,UAAU,CAAE,CACVC,cAAc,cAAEjS,IAAA,CAACvB,cAAc,EAAC+Q,QAAQ,CAAC,OAAO,CAAAb,QAAA,CAAC,GAAC,CAAgB,CACpE,CAAE,CACH,CAAC,CACE,CAAC,cACP3O,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,kCAAkC,CAAE,CAC7C4J,KAAK,CAAEhF,WAAW,CAACY,gBAAiB,CACpCgK,QAAQ,MACRmB,UAAU,CAAE,CACVC,cAAc,cAAEjS,IAAA,CAACvB,cAAc,EAAC+Q,QAAQ,CAAC,OAAO,CAAAb,QAAA,CAAC,GAAC,CAAgB,CACpE,CAAE,CACH,CAAC,CACE,CAAC,cACP3O,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,0BAA0B,CAAE,CACrC4J,KAAK,CAAEhF,WAAW,CAAC7B,QAAS,CAC5ByM,QAAQ,MACT,CAAC,CACE,CAAC,cACP7Q,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,gCAAgC,CAAE,CAC3C4J,KAAK,CAAEhF,WAAW,CAACQ,cAAe,CAClCoK,QAAQ,MACRqB,SAAS,MACTC,IAAI,CAAE,CAAE,CACT,CAAC,CACE,CAAC,cACPnS,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,oCAAoC,CAAE,CAC/C4J,KAAK,CAAEhF,WAAW,CAACS,kBAAmB,CACtCmK,QAAQ,MACT,CAAC,CACE,CAAC,cACP7Q,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA5C,QAAA,cAChB3O,IAAA,CAAC1C,KAAK,EAAC0R,SAAS,CAAE,CAAE,CAACH,EAAE,CAAE,CAAEI,CAAC,CAAE,CAAC,CAAEF,EAAE,CAAE,CAAC,CAAEyB,YAAY,CAAE,CAAE,CAAE,CAAA7B,QAAA,cACxDzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEuB,cAAc,CAAE,eAAe,CAAErB,UAAU,CAAE,QAAS,CAAE,CAAAT,QAAA,eAClFzO,KAAA,CAACvC,GAAG,EAAAgR,QAAA,eACF3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACE,YAAY,MAACV,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACjDtN,CAAC,CAAC,wBAAwB,CAAC,CAClB,CAAC,cACbrB,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAvB,QAAA,CAC/CtN,CAAC,CAAC,0CAA0C,CAAC,CACpC,CAAC,EACV,CAAC,cACNrB,IAAA,CAACrC,GAAG,EAAAgR,QAAA,cACF3O,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,WAAW,CACnBa,KAAK,CAAC,SAAS,CACfS,OAAO,CAAEA,CAAA,GAAMpP,QAAQ,CAAC,qBAAqB,CAAE,CAC/CsN,EAAE,CAAE,CAAE2B,YAAY,CAAE,CAAE,CAAE,CAAA7B,QAAA,CAEvBtN,CAAC,CAAC,4BAA4B,CAAC,CAC1B,CAAC,CACN,CAAC,EACH,CAAC,CACD,CAAC,CACJ,CAAC,cACPnB,KAAA,CAACxC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA5C,QAAA,eAChB3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAZ,QAAA,CAClCtN,CAAC,CAAC,oBAAoB,CAAC,CACd,CAAC,CACZ4E,WAAW,CAACe,aAAa,CACxBhG,cAAc,CAACiF,WAAW,CAACe,aAAa,CAAC,cACvC;AACAhH,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEW,QAAQ,CAAE,UAAU,CAAE4C,UAAU,CAAE,QAAS,uBAAwB,CAAE,CAAAzD,QAAA,cAC9E3O,IAAA,CAACH,WAAW,EACVa,GAAG,CAAEuF,WAAW,CAACe,aAAc,CAC/B2I,KAAK,CAAC,MAAM,CACZC,MAAM,CAAC,MAAM,CACbyC,QAAQ,MACRC,KAAK,CAAE,CACL9C,QAAQ,CAAE,UAAU,CACpB+C,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CACR,CAAE,CACFC,MAAM,CAAE,CACNC,OAAO,CAAE,CACPC,UAAU,CAAE,CAAEC,MAAM,CAAEnE,MAAM,CAACoE,QAAQ,CAACD,MAAO,CAC/C,CACF,CAAE,CACH,CAAC,CACC,CAAC,CACJ9R,gBAAgB,CAACmF,WAAW,CAACe,aAAa,CAAC,cAC7C;AACAhH,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEW,QAAQ,CAAE,UAAU,CAAE4C,UAAU,CAAE,QAAS,uBAAwB,CAAE,CAAAzD,QAAA,cAC9E3O,IAAA,UACEyP,GAAG,CAAE,4BAA4BxJ,WAAW,CAACe,aAAa,EAAG,CAC7D2I,KAAK,CAAC,MAAM,CACZC,MAAM,CAAC,MAAM,CACbyC,QAAQ,MACRC,KAAK,CAAE,CACL9C,QAAQ,CAAE,UAAU,CACpB+C,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CACR,CAAE,CACH,CAAC,CACC,CAAC,cAEN;AACAxS,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEyB,SAAS,CAAE,QAAQ,CAAErB,CAAC,CAAE,CAAE,CAAE,CAAAN,QAAA,cACrC3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAAAV,QAAA,cACzB3O,IAAA,MAAG8S,IAAI,CAAE7M,WAAW,CAACe,aAAc,CAACgE,MAAM,CAAC,QAAQ,CAAC+H,GAAG,CAAC,qBAAqB,CAAApE,QAAA,CAC1EtN,CAAC,CAAC,mBAAmB,CAAC,CACtB,CAAC,CACM,CAAC,CACV,CACN,cAEDrB,IAAA,CAAC1C,KAAK,EACJ0R,SAAS,CAAE,CAAE,CACbK,OAAO,CAAC,UAAU,CAClBR,EAAE,CAAE,CACFI,CAAC,CAAE,CAAC,CACJ6B,eAAe,CAAE,oBAAoB,CACrCR,SAAS,CAAE,QAAQ,CACnBJ,KAAK,CAAE,gBACT,CAAE,CAAAvB,QAAA,CAEDtN,CAAC,CAAC,sBAAsB,CAAC,CACrB,CACR,EACG,CAAC,cACPnB,KAAA,CAACxC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA5C,QAAA,eAChB3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,IAAI,CAACE,YAAY,MAAAZ,QAAA,CAClCtN,CAAC,CAAC,YAAY,CAAC,CACN,CAAC,cACbrB,IAAA,CAAC1C,KAAK,EACJ0R,SAAS,CAAE,CAAE,CACbK,OAAO,CAAC,UAAU,CAClBR,EAAE,CAAE,CACFI,CAAC,CAAE,CAAC,CACJ6B,eAAe,CAAE,oBAAoB,CACrCkC,UAAU,CAAE,UAAY;AAC1B,CAAE,CAAArE,QAAA,CAED1I,WAAW,CAACc,EAAE,CACV,CAAC,EACJ,CAAC,EACH,CAAC,EACF,CAAC,EACH,CAAC,CACC,CAAC,cAGZ7G,KAAA,CAAC7B,MAAM,EACL4U,IAAI,CAAEzQ,kBAAmB,CACzB0Q,OAAO,CAAE7H,yBAA0B,CACnCuD,QAAQ,CAAC,IAAI,CACb6C,SAAS,MAAA9C,QAAA,eAET3O,IAAA,CAAC1B,WAAW,EAAAqQ,QAAA,CACTtN,CAAC,CAAC,wBAAwB,CAAC,CACjB,CAAC,cACdrB,IAAA,CAACzB,aAAa,EAAAoQ,QAAA,cACZzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEiB,GAAG,CAAE,CAAC,CAAE+C,QAAQ,CAAE,GAAI,CAAE,CAAAxE,QAAA,EAC1E5M,KAAK,eACJ/B,IAAA,CAACpC,KAAK,EAACyS,QAAQ,CAAC,OAAO,CAAA1B,QAAA,CACpB5M,KAAK,CACD,CACR,CACAE,OAAO,eACNjC,IAAA,CAACpC,KAAK,EAACyS,QAAQ,CAAC,SAAS,CAAA1B,QAAA,CACtB1M,OAAO,CACH,CACR,cACDjC,IAAA,CAACxC,SAAS,EACRkU,KAAK,CAAErQ,CAAC,CAAC,yBAAyB,CAAE,CACpC0Q,IAAI,CAAE5P,YAAY,CAACE,eAAe,CAAG,MAAM,CAAG,UAAW,CACzD4I,KAAK,CAAElF,YAAY,CAAC1D,eAAgB,CACpC+Q,QAAQ,CAAEvI,oBAAoB,CAAC,iBAAiB,CAAE,CAClDwI,QAAQ,MACRrB,UAAU,CAAE,CACVsB,YAAY,cACVtT,IAAA,CAACvB,cAAc,EAAC+Q,QAAQ,CAAC,KAAK,CAAAb,QAAA,cAC5B3O,IAAA,CAAClC,UAAU,EACT,aAAYuD,CAAC,CAAC,kCAAkC,CAAE,CAClDsP,OAAO,CAAEA,CAAA,GAAMzF,uBAAuB,CAAC,iBAAiB,CAAE,CAC1DqI,IAAI,CAAC,KAAK,CACVxB,IAAI,CAAC,QAAQ,CAAApD,QAAA,CAEZxM,YAAY,CAACE,eAAe,cAAGrC,IAAA,CAAChB,iBAAiB,GAAE,CAAC,cAAGgB,IAAA,CAAClB,cAAc,GAAE,CAAC,CAChE,CAAC,CACC,CAEpB,CAAE,CACH,CAAC,cACFkB,IAAA,CAACxC,SAAS,EACRkU,KAAK,CAAErQ,CAAC,CAAC,qBAAqB,CAAE,CAChC0Q,IAAI,CAAE5P,YAAY,CAACG,WAAW,CAAG,MAAM,CAAG,UAAW,CACrD2I,KAAK,CAAElF,YAAY,CAACzD,WAAY,CAChC8Q,QAAQ,CAAEvI,oBAAoB,CAAC,aAAa,CAAE,CAC9CwI,QAAQ,MACRrB,UAAU,CAAE,CACVsB,YAAY,cACVtT,IAAA,CAACvB,cAAc,EAAC+Q,QAAQ,CAAC,KAAK,CAAAb,QAAA,cAC5B3O,IAAA,CAAClC,UAAU,EACT,aAAYuD,CAAC,CAAC,kCAAkC,CAAE,CAClDsP,OAAO,CAAEA,CAAA,GAAMzF,uBAAuB,CAAC,aAAa,CAAE,CACtDqI,IAAI,CAAC,KAAK,CACVxB,IAAI,CAAC,QAAQ,CAAApD,QAAA,CAEZxM,YAAY,CAACG,WAAW,cAAGtC,IAAA,CAAChB,iBAAiB,GAAE,CAAC,cAAGgB,IAAA,CAAClB,cAAc,GAAE,CAAC,CAC5D,CAAC,CACC,CAEpB,CAAE,CACH,CAAC,cACFkB,IAAA,CAACxC,SAAS,EACRkU,KAAK,CAAErQ,CAAC,CAAC,yBAAyB,CAAE,CACpC0Q,IAAI,CAAE5P,YAAY,CAACI,eAAe,CAAG,MAAM,CAAG,UAAW,CACzD0I,KAAK,CAAElF,YAAY,CAACxD,eAAgB,CACpC6Q,QAAQ,CAAEvI,oBAAoB,CAAC,iBAAiB,CAAE,CAClDwI,QAAQ,MACRrB,UAAU,CAAE,CACVsB,YAAY,cACVtT,IAAA,CAACvB,cAAc,EAAC+Q,QAAQ,CAAC,KAAK,CAAAb,QAAA,cAC5B3O,IAAA,CAAClC,UAAU,EACT,aAAYuD,CAAC,CAAC,kCAAkC,CAAE,CAClDsP,OAAO,CAAEA,CAAA,GAAMzF,uBAAuB,CAAC,iBAAiB,CAAE,CAC1DqI,IAAI,CAAC,KAAK,CACVxB,IAAI,CAAC,QAAQ,CAAApD,QAAA,CAEZxM,YAAY,CAACI,eAAe,cAAGvC,IAAA,CAAChB,iBAAiB,GAAE,CAAC,cAAGgB,IAAA,CAAClB,cAAc,GAAE,CAAC,CAChE,CAAC,CACC,CAEpB,CAAE,CACH,CAAC,EACC,CAAC,CACO,CAAC,cAChBoB,KAAA,CAAC1B,aAAa,EAAAmQ,QAAA,eACZ3O,IAAA,CAACvC,MAAM,EAACkT,OAAO,CAAEtF,yBAA0B,CAAAsD,QAAA,CAAEtN,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,cACzErB,IAAA,CAACvC,MAAM,EAACsU,IAAI,CAAC,QAAQ,CAAC1C,OAAO,CAAC,WAAW,CAACwB,QAAQ,CAAEhP,OAAQ,CAAC8O,OAAO,CAAErF,oBAAqB,CAAAqD,QAAA,CACxF9M,OAAO,cAAG7B,IAAA,CAACtB,gBAAgB,EAAC8U,IAAI,CAAE,EAAG,CAAE,CAAC,CAAGnS,CAAC,CAAC,aAAa,CAAC,CACtD,CAAC,EACI,CAAC,EACV,CAAC,cAKTnB,KAAA,CAAC7B,MAAM,EACL4U,IAAI,CAAErQ,0BAA2B,CACjCsQ,OAAO,CAAE3G,iCAAkC,CAC3CqC,QAAQ,CAAC,IAAI,CACb6C,SAAS,MAAA9C,QAAA,eAET3O,IAAA,CAAC1B,WAAW,EAAAqQ,QAAA,CACTtN,CAAC,CAAC,0BAA0B,CAAC,CACnB,CAAC,cACdrB,IAAA,CAACzB,aAAa,EAAAoQ,QAAA,cACZzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEC,aAAa,CAAE,QAAQ,CAAEiB,GAAG,CAAE,CAAC,CAAE+C,QAAQ,CAAE,GAAG,CAAErE,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,EACjF5M,KAAK,eACJ/B,IAAA,CAACpC,KAAK,EAACyS,QAAQ,CAAC,OAAO,CAAA1B,QAAA,CACpB5M,KAAK,CACD,CACR,CACAE,OAAO,eACNjC,IAAA,CAACpC,KAAK,EAACyS,QAAQ,CAAC,SAAS,CAAA1B,QAAA,CACtB1M,OAAO,CACH,CACR,cAED/B,KAAA,CAACxC,IAAI,EAAC0T,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA1C,QAAA,eAEzB3O,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,eAAe,CAAE,CAC1B4J,KAAK,CAAE/D,gBAAgB,CAACf,KAAM,CAC9BiN,QAAQ,CAAE5G,wBAAwB,CAAC,OAAO,CAAE,CAC5CzK,KAAK,CAAE,CAAC,CAACqF,WAAW,CAACjB,KAAM,CAC3BsN,UAAU,CAAErM,WAAW,CAACjB,KAAK,EAAI9E,CAAC,CAAC,mBAAmB,CAAE,CACzD,CAAC,CACE,CAAC,cAGPrB,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACR6V,QAAQ,MACR5B,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,wBAAwB,CAAE,CACnC4J,KAAK,CAAE/D,gBAAgB,CAACZ,cAAe,CACvC8M,QAAQ,CAAE5G,wBAAwB,CAAC,gBAAgB,CAAE,CACrDzK,KAAK,CAAE,CAAC,CAACqF,WAAW,CAACd,cAAe,CACpCmN,UAAU,CAAErM,WAAW,CAACd,cAAc,EAAI,EAAG,CAC9C,CAAC,CACE,CAAC,cAGPtG,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA5C,QAAA,cAChBzO,KAAA,CAACnC,WAAW,EAAC0T,SAAS,MAAC4B,QAAQ,MAACtR,KAAK,CAAE,CAAC,CAACqF,WAAW,CAACb,iBAAkB,CAAAoI,QAAA,eACrE3O,IAAA,CAAChC,UAAU,EAAA2Q,QAAA,CAAEtN,CAAC,CAAC,2BAA2B,CAAC,CAAa,CAAC,cACzDrB,IAAA,CAAC/B,MAAM,EACL0T,QAAQ,MACR1G,KAAK,CAAE/D,gBAAgB,CAACX,iBAAkB,CAC1C6M,QAAQ,CAAE5G,wBAAwB,CAAC,mBAAmB,CAAE,CACxDoF,WAAW,CAAGC,QAAQ,eACpB7R,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAE4C,QAAQ,CAAE,MAAM,CAAE1B,GAAG,CAAE,GAAI,CAAE,CAAAzB,QAAA,CACtD,CAAAkD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE9I,GAAG,CAAEkC,KAAK,OAAAyI,eAAA,oBACnB1T,IAAA,CAAC7B,IAAI,EAEHuT,KAAK,CAAE,EAAAgC,eAAA,CAAA5Q,SAAS,CAACkG,IAAI,CAACT,IAAI,EAAIA,IAAI,CAACC,EAAE,GAAKyC,KAAK,CAAC,UAAAyI,eAAA,iBAAzCA,eAAA,CAA2CjL,IAAI,GAAIwC,KAAM,EAD3DA,KAEN,CAAC,EACH,CAAC,GAAI,EAAE,CACL,CACL,CAAA0D,QAAA,CAED7L,SAAS,CAACiG,GAAG,CAAER,IAAI,eAClBvI,IAAA,CAAC9B,QAAQ,EAAe+M,KAAK,CAAE1C,IAAI,CAACC,EAAG,CAAAmG,QAAA,CACpCpG,IAAI,CAACE,IAAI,EADGF,IAAI,CAACC,EAEV,CACX,CAAC,CACI,CAAC,CACRpB,WAAW,CAACb,iBAAiB,eAC5BvG,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,OAAO,CAAAvB,QAAA,CACtCvH,WAAW,CAACb,iBAAiB,CACpB,CACb,EACU,CAAC,CACV,CAAC,cAGPvG,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAAA5C,QAAA,cAChBzO,KAAA,CAACnC,WAAW,EAAC0T,SAAS,MAAC4B,QAAQ,MAACtR,KAAK,CAAE,CAAC,CAACqF,WAAW,CAACZ,WAAY,CAAAmI,QAAA,eAC/D3O,IAAA,CAAChC,UAAU,EAAA2Q,QAAA,CAAEtN,CAAC,CAAC,qBAAqB,CAAC,CAAa,CAAC,cACnDrB,IAAA,CAAC/B,MAAM,EACL0T,QAAQ,MACR1G,KAAK,CAAE/D,gBAAgB,CAACV,WAAY,CACpC4M,QAAQ,CAAE5G,wBAAwB,CAAC,aAAa,CAAE,CAClDoF,WAAW,CAAGC,QAAQ,eACpB7R,IAAA,CAACrC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAE4C,QAAQ,CAAE,MAAM,CAAE1B,GAAG,CAAE,GAAI,CAAE,CAAAzB,QAAA,CACtD,CAAAkD,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAE9I,GAAG,CAAEkC,KAAK,OAAA0I,gBAAA,oBACnB3T,IAAA,CAAC7B,IAAI,EAEHuT,KAAK,CAAE,EAAAiC,gBAAA,CAAA3Q,UAAU,CAACgG,IAAI,CAACsD,GAAG,EAAIA,GAAG,CAAC9D,EAAE,GAAKyC,KAAK,CAAC,UAAA0I,gBAAA,iBAAxCA,gBAAA,CAA0ClL,IAAI,GAAIwC,KAAM,EAD1DA,KAEN,CAAC,EACH,CAAC,GAAI,EAAE,CACL,CACL,CAAA0D,QAAA,CAED3L,UAAU,CAAC+F,GAAG,CAAEK,QAAQ,eACvBpJ,IAAA,CAAC9B,QAAQ,EAAmB+M,KAAK,CAAE7B,QAAQ,CAACZ,EAAG,CAAAmG,QAAA,CAC5CvF,QAAQ,CAACX,IAAI,EADDW,QAAQ,CAACZ,EAEd,CACX,CAAC,CACI,CAAC,CACRpB,WAAW,CAACZ,WAAW,eACtBxG,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,OAAO,CAAAvB,QAAA,CACtCvH,WAAW,CAACZ,WAAW,CACd,CACb,EACU,CAAC,CACV,CAAC,cAGPtG,KAAA,CAACxC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,eACvB3O,IAAA,CAACxC,SAAS,EACR6V,QAAQ,MACR5B,SAAS,MACTM,IAAI,CAAC,QAAQ,CACbL,KAAK,CAAErQ,CAAC,CAAC,wBAAwB,CAAE,CACnCuS,WAAW,CAAEvS,CAAC,CAAC,mCAAmC,CAAE,CACpD4J,KAAK,CAAE/D,gBAAgB,CAACN,cAAe,CACvCwM,QAAQ,CAAE5G,wBAAwB,CAAC,gBAAgB,CAAE,CACrDzK,KAAK,CAAE,CAAC,CAACqF,WAAW,CAACR,cAAe,CACpC6M,UAAU,CAAErM,WAAW,CAACR,cAAc,EAAI,EAAG,CAC7CoL,UAAU,CAAE,CACVC,cAAc,cAAEjS,IAAA,CAACvB,cAAc,EAAC+Q,QAAQ,CAAC,OAAO,CAAAb,QAAA,CAAC,GAAC,CAAgB,CACpE,CAAE,CACFkF,UAAU,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAI,CAAE,CAClC,CAAC,CAGD7M,gBAAgB,CAACN,cAAc,CAAG,CAAC,eAClC1G,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEG,CAAC,CAAE,GAAG,CAAEc,OAAO,CAAE,kBAAkB,CAAEF,MAAM,CAAE,YAAY,CAAEC,WAAW,CAAE,cAAc,CAAEU,YAAY,CAAE,CAAE,CAAE,CAAA7B,QAAA,eAC1H3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,WAAW,CAACa,KAAK,CAAC,SAAS,CAACX,YAAY,MAAAZ,QAAA,CACzDtN,CAAC,CAAC,sBAAsB,CAAC,EAAI,gCAAgC,CACpD,CAAC,cACbnB,KAAA,CAAC3C,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAE0B,UAAU,CAAE,MAAM,CAAEL,KAAK,CAAE,cAAe,CAAE,CAAAvB,QAAA,EAAC,GAC5E,CAACxO,wBAAwB,CAAC+G,gBAAgB,CAACN,cAAc,CAAC,CAACoN,OAAO,CAAC,CAAC,CAAC,EAC5D,CAAC,EACV,CACN,EACG,CAAC,cAGPhU,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvB3O,IAAA,CAACxC,SAAS,EACR6V,QAAQ,MACR5B,SAAS,MACTM,IAAI,CAAC,QAAQ,CACbL,KAAK,CAAErQ,CAAC,CAAC,0BAA0B,CAAE,CACrCuS,WAAW,CAAEvS,CAAC,CAAC,qCAAqC,CAAE,CACtD4J,KAAK,CAAE/D,gBAAgB,CAACL,gBAAiB,CACzCuM,QAAQ,CAAE5G,wBAAwB,CAAC,kBAAkB,CAAE,CACvDzK,KAAK,CAAE,CAAC,CAACqF,WAAW,CAACP,gBAAiB,CACtC4M,UAAU,CAAErM,WAAW,CAACP,gBAAgB,EAAI,EAAG,CAC/CmL,UAAU,CAAE,CACVC,cAAc,cAAEjS,IAAA,CAACvB,cAAc,EAAC+Q,QAAQ,CAAC,OAAO,CAAAb,QAAA,CAAC,GAAC,CAAgB,CACpE,CAAE,CACFkF,UAAU,CAAE,CAAEC,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,EAAG,CAAE,CACjC,CAAC,CACE,CAAC,cAGP/T,IAAA,CAACtC,IAAI,EAAC4T,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAA7C,QAAA,cACvBzO,KAAA,CAACnC,WAAW,EAAC0T,SAAS,MAAC4B,QAAQ,MAACtR,KAAK,CAAE,CAAC,CAACqF,WAAW,CAAChD,QAAS,CAAAuK,QAAA,eAC5D3O,IAAA,CAAChC,UAAU,EAAA2Q,QAAA,CAAEtN,CAAC,CAAC,kBAAkB,CAAC,CAAa,CAAC,cAChDrB,IAAA,CAAC/B,MAAM,EACLgN,KAAK,CAAE/D,gBAAgB,CAAC9C,QAAS,CACjCgP,QAAQ,CAAE5G,wBAAwB,CAAC,UAAU,CAAE,CAAAmC,QAAA,CAE9C7O,SAAS,CAACiJ,GAAG,CAAE3E,QAAQ,eACtBpE,IAAA,CAAC9B,QAAQ,EAAsB+M,KAAK,CAAE7G,QAAQ,CAAC6G,KAAM,CAAA0D,QAAA,CAClDvK,QAAQ,CAACsN,KAAK,EADFtN,QAAQ,CAAC6G,KAEd,CACX,CAAC,CACI,CAAC,CACR7D,WAAW,CAAChD,QAAQ,eACnBpE,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,OAAO,CAAAvB,QAAA,CACtCvH,WAAW,CAAChD,QAAQ,CACX,CACb,EACU,CAAC,CACV,CAAC,EACH,CAAC,EACJ,CAAC,CACO,CAAC,cAChBlE,KAAA,CAAC1B,aAAa,EAAAmQ,QAAA,eACZ3O,IAAA,CAACvC,MAAM,EAACkT,OAAO,CAAEpE,iCAAkC,CAAAoC,QAAA,CAAEtN,CAAC,CAAC,eAAe,CAAC,CAAS,CAAC,cACjFrB,IAAA,CAACvC,MAAM,EAACsU,IAAI,CAAC,QAAQ,CAAC1C,OAAO,CAAC,WAAW,CAACwB,QAAQ,CAAEhP,OAAQ,CAAC8O,OAAO,CAAE/D,wBAAyB,CAAA+B,QAAA,CAC5F9M,OAAO,cAAG7B,IAAA,CAACtB,gBAAgB,EAAC8U,IAAI,CAAE,EAAG,CAAE,CAAC,CAAGnS,CAAC,CAAC,aAAa,CAAC,CACtD,CAAC,EACI,CAAC,EACV,CAAC,cAGTnB,KAAA,CAAC7B,MAAM,EACL4U,IAAI,CAAEvQ,yBAA0B,CAChCwQ,OAAO,CAAEA,CAAA,GAAMvQ,4BAA4B,CAAC,KAAK,CAAE,CACnDiM,QAAQ,CAAC,IAAI,CACb6C,SAAS,MAAA9C,QAAA,eAET3O,IAAA,CAAC1B,WAAW,EAACuQ,EAAE,CAAE,CAAEkB,OAAO,CAAE,eAAe,CAAEG,KAAK,CAAE,sBAAuB,CAAE,CAAAvB,QAAA,cAC3EzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEgB,GAAG,CAAE,CAAE,CAAE,CAAAzB,QAAA,eACzD3O,IAAA,CAACV,WAAW,GAAE,CAAC,CACd+B,CAAC,CAAC,kCAAkC,CAAC,EACnC,CAAC,CACK,CAAC,cACdrB,IAAA,CAACzB,aAAa,EAAAoQ,QAAA,cACZzO,KAAA,CAACvC,GAAG,EAACkR,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB3O,IAAA,CAACpC,KAAK,EAACyS,QAAQ,CAAC,SAAS,CAACxB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACrCtN,CAAC,CAAC,oCAAoC,CAAC,CACnC,CAAC,cACRrB,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAAC4E,SAAS,MAAAtF,QAAA,CAClCtN,CAAC,CAAC,yCAAyC,CAAC,CACnC,CAAC,cACbrB,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAAC4E,SAAS,MAAAtF,QAAA,CAClCtN,CAAC,CAAC,yCAAyC,CAAC,CACnC,CAAC,cACbrB,IAAA,CAACpC,KAAK,EAACyS,QAAQ,CAAC,MAAM,CAACxB,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cACnC3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAE0B,UAAU,CAAE,MAAO,CAAE,CAAA5B,QAAA,CAAC,2gBAExD,CAAY,CAAC,CACR,CAAC,cACR3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAE0B,UAAU,CAAE,MAAO,CAAE,CAAA5B,QAAA,CACpDtN,CAAC,CAAC,yCAAyC,CAAC,CACnC,CAAC,EACV,CAAC,CACO,CAAC,cAChBnB,KAAA,CAAC1B,aAAa,EAAAmQ,QAAA,eACZ3O,IAAA,CAACvC,MAAM,EAACkT,OAAO,CAAEA,CAAA,GAAMhO,4BAA4B,CAAC,KAAK,CAAE,CAAAgM,QAAA,CACxDtN,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTrB,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,WAAW,CACnBa,KAAK,CAAC,SAAS,CACfS,OAAO,CAAEnD,sBAAuB,CAAAmB,QAAA,CAE/BtN,CAAC,CAAC,iCAAiC,CAAC,CAC/B,CAAC,EACI,CAAC,EACV,CAAC,cAGTnB,KAAA,CAAC7B,MAAM,EAAC4U,IAAI,CAAE3P,YAAa,CAAC4P,OAAO,CAAEA,CAAA,GAAM3P,eAAe,CAAC,KAAK,CAAE,CAAAoL,QAAA,eAChEzO,KAAA,CAAC5B,WAAW,EAACuQ,EAAE,CAAE,CAAEK,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEgB,GAAG,CAAE,CAAE,CAAE,CAAAzB,QAAA,eACjE3O,IAAA,CAACV,WAAW,EAAC4Q,KAAK,CAAC,OAAO,CAAE,CAAC,CAC5B7O,CAAC,CAAC,4BAA4B,CAAC,EACrB,CAAC,cACdnB,KAAA,CAAC3B,aAAa,EAAAoQ,QAAA,eACZ3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACvCtN,CAAC,CAAC,8BAA8B,CAAC,CACxB,CAAC,cACbrB,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAvB,QAAA,CAC/CtN,CAAC,CAAC,2BAA2B,CAAC,CACrB,CAAC,EACA,CAAC,cAChBnB,KAAA,CAAC1B,aAAa,EAAAmQ,QAAA,eACZ3O,IAAA,CAACvC,MAAM,EAACkT,OAAO,CAAEA,CAAA,GAAMpN,eAAe,CAAC,KAAK,CAAE,CAAAoL,QAAA,CAC3CtN,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTrB,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,WAAW,CACnBa,KAAK,CAAC,OAAO,CACbS,OAAO,CAAElD,mBAAoB,CAC7BoD,QAAQ,CAAEhP,OAAQ,CAClB6O,SAAS,CAAE7O,OAAO,cAAG7B,IAAA,CAACtB,gBAAgB,EAAC8U,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGxT,IAAA,CAACN,UAAU,GAAE,CAAE,CAAAiP,QAAA,CAEpE9M,OAAO,CAAGR,CAAC,CAAC,gBAAgB,CAAC,CAAGA,CAAC,CAAC,wBAAwB,CAAC,CACtD,CAAC,EACI,CAAC,EACV,CAAC,cAGTnB,KAAA,CAAC7B,MAAM,EAAC4U,IAAI,CAAEzP,gBAAiB,CAAC0P,OAAO,CAAEA,CAAA,GAAMzP,mBAAmB,CAAC,KAAK,CAAE,CAAAkL,QAAA,eACxE3O,IAAA,CAAC1B,WAAW,EAAAqQ,QAAA,CAAEtN,CAAC,CAAC,0BAA0B,CAAC,CAAc,CAAC,cAC1DnB,KAAA,CAAC3B,aAAa,EAAAoQ,QAAA,eACZ3O,IAAA,CAACzC,UAAU,EAAC8R,OAAO,CAAC,OAAO,CAACR,EAAE,CAAE,CAAEE,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACvCtN,CAAC,CAAC,+BAA+B,CAAC,CACzB,CAAC,cACbrB,IAAA,CAACxC,SAAS,EACRiU,SAAS,MACTC,KAAK,CAAErQ,CAAC,CAAC,oBAAoB,CAAE,CAC/B4J,KAAK,CAAEvH,UAAW,CAClB0P,QAAQ,CAAG7H,CAAC,EAAK5H,aAAa,CAAC4H,CAAC,CAACP,MAAM,CAACC,KAAK,CAAE,CAC/C2I,WAAW,CAAC,QAAQ,CACpB/E,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,EACW,CAAC,cAChB5O,KAAA,CAAC1B,aAAa,EAAAmQ,QAAA,eACZ3O,IAAA,CAACvC,MAAM,EAACkT,OAAO,CAAEA,CAAA,GAAMlN,mBAAmB,CAAC,KAAK,CAAE,CAAAkL,QAAA,CAC/CtN,CAAC,CAAC,eAAe,CAAC,CACb,CAAC,cACTrB,IAAA,CAACvC,MAAM,EACL4R,OAAO,CAAC,WAAW,CACnBa,KAAK,CAAC,OAAO,CACbS,OAAO,CAAE9C,sBAAuB,CAChCgD,QAAQ,CAAEhP,OAAO,EAAI,CAAC6B,UAAU,CAACoK,IAAI,CAAC,CAAE,CACxC4C,SAAS,CAAE7O,OAAO,cAAG7B,IAAA,CAACtB,gBAAgB,EAAC8U,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGxT,IAAA,CAACN,UAAU,GAAE,CAAE,CAAAiP,QAAA,CAEpE9M,OAAO,CAAGR,CAAC,CAAC,gBAAgB,CAAC,CAAGA,CAAC,CAAC,uBAAuB,CAAC,CACrD,CAAC,EACI,CAAC,EACV,CAAC,EACH,CAAC,CAEb,CAAC,CAED,cAAe,CAAAJ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}