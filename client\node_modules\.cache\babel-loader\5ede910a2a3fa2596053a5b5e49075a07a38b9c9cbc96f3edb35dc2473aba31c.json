{"ast": null, "code": "import React,{useEffect}from'react';import{BrowserRouter as Router,Routes,Route,Navigate,useLocation}from'react-router-dom';import ResizeObserverFix from'./utils/ResizeObserverFix';import{ThemeProvider,createTheme}from'@mui/material/styles';import{CssBaseline,Box,CircularProgress}from'@mui/material';import{useTranslation}from'react-i18next';import{AuthProvider,useAuth}from'./contexts/AuthContext';import{SocketProvider}from'./contexts/SocketContext';import{UnreadMessagesProvider}from'./contexts/UnreadMessagesContext';import{Toaster}from'react-hot-toast';import{setupResizeObserverPolyfill}from'./utils/resizeObserver';import Header from'./components/layout/Header';import Footer from'./components/Footer';import AuthenticatedFooter from'./components/AuthenticatedFooter';import'./i18n/i18n';// Import pages here\nimport Home from'./pages/Home';import AboutUs from'./pages/AboutUs';import PlatformPolicy from'./pages/PlatformPolicy';import PendingDeletion from'./pages/PendingDeletion';import TermsAndConditions from'./pages/TermsAndConditions';import PrivacyPolicy from'./pages/PrivacyPolicy';import RefundPolicy from'./pages/RefundPolicy';import BookingPaymentPolicy from'./pages/BookingPaymentPolicy';import BookingCancellationPolicy from'./pages/BookingCancellationPolicy';import Login from'./pages/auth/Login';import RegisterChoice from'./pages/auth/RegisterChoice';import StudentRegister from'./pages/auth/StudentRegister';import TeacherRegister from'./pages/auth/TeacherRegister';import ForgotPassword from'./pages/auth/ForgotPassword';import VerifyResetCode from'./pages/auth/VerifyResetCode';import ResetPassword from'./pages/auth/ResetPassword';import VerifyEmail from'./pages/auth/VerifyEmail';import AdminDashboard from'./pages/admin/Dashboard';import AdminProfile from'./pages/admin/Profile';import TeacherApplications from'./pages/admin/TeacherApplications';import Teachers from'./pages/admin/Teachers';import Students from'./pages/admin/Students';import Categories from'./pages/admin/Categories';import Languages from'./pages/admin/Languages';import ProfileUpdates from'./pages/admin/ProfileUpdates';import MeetingSessions from'./pages/admin/MeetingSessions';import TeacherDashboard from'./pages/teacher/Dashboard';import TeacherApplication from'./pages/teacher/Application';import EditApplication from'./pages/teacher/EditApplication';import EditVideoUpload from'./pages/teacher/EditVideoUpload';import TeacherVideoUpload from'./pages/teacher/VideoUpload';import TeacherApplicationAvailableHours from'./pages/teacher/AvailableHours';import TeacherProfileAvailableHours from'./pages/teacher/TeacherAvailableHours';import TeacherViewAvailableHours from'./pages/teacher/ViewAvailableHours';import TeacherProfile from'./pages/teacher/Profile';import TeacherChat from'./pages/teacher/Chat';import TeacherMeetings from'./pages/teacher/Meetings';import TeacherBookings from'./pages/teacher/Bookings';import MyLessons from'./pages/teacher/MyLessons';import StudentDashboard from'./pages/student/Dashboard';import StudentProfile from'./pages/student/Profile';import CompleteProfile from'./pages/student/CompleteProfile';import StudentChat from'./pages/student/Chat';import StudentFindTeacher from'./pages/student/FindTeacher';import StudentTeacherProfile from'./pages/student/TeacherProfile';import StudentChatEmbed from'./pages/student/ChatEmbed';import FindTeacher from'./pages/FindTeacher';import TeacherDetails from'./pages/TeacherDetails';import JoinMeeting from'./pages/student/JoinMeeting';import StudentMeetings from'./pages/student/Meetings';import AdminWallet from'./pages/admin/Wallet';import TeacherWallet from'./pages/teacher/Wallet';import StudentWallet from'./pages/student/Wallet';import StudentContactUs from'./pages/student/ContactUs';import TeacherContactUs from'./pages/teacher/ContactUs';import AdminMessages from'./pages/admin/Messages';import StudentMyMessages from'./pages/student/MyMessages';import TeacherMyMessages from'./pages/teacher/MyMessages';import BookingPage from'./pages/student/BookingPage';import Bookings from'./pages/student/Bookings';import MyTeachers from'./pages/student/MyTeachers';import WriteReview from'./pages/student/WriteReview';import TeacherReviews from'./pages/teacher/Reviews';import TeacherWithdrawal from'./pages/teacher/Withdrawal';import AdminWithdrawalManagement from'./pages/admin/WithdrawalManagement';import AdminEarnings from'./pages/admin/AdminEarnings';import AdminMeetingIssues from'./pages/admin/MeetingIssues';import ContactUs from'./pages/ContactUs';// Define roles\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ROLES={ADMIN:'admin',PLATFORM_TEACHER:'platform_teacher',NEW_TEACHER:'new_teacher',STUDENT:'student'};// Protected Route Component\nconst ProtectedRoute=_ref=>{let{children,allowedRoles}=_ref;const{currentUser,isAuthenticated,loading}=useAuth();const location=useLocation();// If still loading, show loading spinner\nif(loading){return/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"center\",alignItems:\"center\",minHeight:\"100vh\",children:/*#__PURE__*/_jsx(CircularProgress,{})});}// If not authenticated or no user, redirect to login\nif(!isAuthenticated||!currentUser){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true,state:{from:location}});}// If user account is pending deletion, redirect to pending deletion page\n// unless they're already on that page\nif(currentUser.status==='pending_deletion'&&location.pathname!=='/pending-deletion'){return/*#__PURE__*/_jsx(Navigate,{to:\"/pending-deletion\",replace:true});}// Check if user has required role\nif(!allowedRoles.includes(currentUser.role)){// Special case: if a new teacher tries to access platform teacher routes\nif(currentUser.role===ROLES.NEW_TEACHER&&location.pathname==='/teacher/dashboard'){return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/application\",replace:true});}// Special case: if a platform teacher tries to access new teacher routes\nif(currentUser.role===ROLES.PLATFORM_TEACHER&&location.pathname==='/teacher/application'){return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/dashboard\",replace:true});}// Default redirects based on role\nswitch(currentUser.role){case ROLES.ADMIN:return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true});case ROLES.PLATFORM_TEACHER:return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/dashboard\",replace:true});case ROLES.NEW_TEACHER:return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/application\",replace:true});case ROLES.STUDENT:return/*#__PURE__*/_jsx(Navigate,{to:\"/student/dashboard\",replace:true});default:return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true});}}// All checks passed, render the protected component\nreturn children;};// Public Route Component\nconst PublicRoute=_ref2=>{let{children}=_ref2;const{isAuthenticated,currentUser}=useAuth();// If authenticated, redirect to appropriate dashboard\nif(isAuthenticated&&currentUser){// If user account is pending deletion, redirect to pending deletion page\nif(currentUser.status==='pending_deletion'){return/*#__PURE__*/_jsx(Navigate,{to:\"/pending-deletion\",replace:true});}switch(currentUser.role){case ROLES.ADMIN:return/*#__PURE__*/_jsx(Navigate,{to:\"/admin/dashboard\",replace:true});case ROLES.PLATFORM_TEACHER:return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/dashboard\",replace:true});case ROLES.NEW_TEACHER:return/*#__PURE__*/_jsx(Navigate,{to:\"/teacher/application\",replace:true});case ROLES.STUDENT:return/*#__PURE__*/_jsx(Navigate,{to:\"/student/dashboard\",replace:true});default:return/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true});}}return children;};// Component to determine which Footer should be shown\nconst ConditionalFooter=()=>{const location=useLocation();const{isAuthenticated,currentUser}=useAuth();// Check if user is on protected routes (when user is logged in)\nconst isProtectedRoute=isAuthenticated&&currentUser&&(location.pathname.startsWith('/admin/')||location.pathname.startsWith('/teacher/')||location.pathname.startsWith('/student/')||location.pathname.startsWith('/join-meeting/')||location.pathname==='/platform-policy');// Show authenticated footer on protected routes\nif(isProtectedRoute){return/*#__PURE__*/_jsx(AuthenticatedFooter,{});}// Show public footer on public routes\nreturn/*#__PURE__*/_jsx(Footer,{});};function App(){const{i18n}=useTranslation();const direction=i18n.language==='ar'?'rtl':'ltr';useEffect(()=>{setupResizeObserverPolyfill();},[]);const theme=createTheme({direction,palette:{primary:{main:'#0C4B33'// Deep Islamic green\n},secondary:{main:'#C3A343'// Islamic gold\n}},typography:{fontFamily:direction==='rtl'?'Tajawal, sans-serif':'Roboto, sans-serif'}});return/*#__PURE__*/_jsxs(Router,{children:[/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(ResizeObserverFix,{}),/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(SocketProvider,{children:/*#__PURE__*/_jsx(UnreadMessagesProvider,{children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',minHeight:'100vh'},children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(Box,{component:\"main\",sx:{flexGrow:1,py:3},children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(Home,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(Login,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(RegisterChoice,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/register/student\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(StudentRegister,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/register/teacher\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(TeacherRegister,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/verify-email\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(VerifyEmail,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/forgot-password\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(ForgotPassword,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/verify-reset-code\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(VerifyResetCode,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/reset-password\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(ResetPassword,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/about-us\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(AboutUs,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/pending-deletion\",element:/*#__PURE__*/_jsx(PendingDeletion,{})}),/*#__PURE__*/_jsx(Route,{path:\"/terms-and-conditions\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(TermsAndConditions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/privacy-policy\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(PrivacyPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/booking-cancellation-policy\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(BookingCancellationPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/booking-payment-policy\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(BookingPaymentPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/refund-policy\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(RefundPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/find-teacher\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(FindTeacher,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/:id\",element:/*#__PURE__*/_jsx(PublicRoute,{children:/*#__PURE__*/_jsx(TeacherDetails,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/teachers\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(Teachers,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/students\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(Students,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/applications\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(TeacherApplications,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/categories\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(Categories,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/languages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(Languages,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/wallet\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminWallet,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/messages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminMessages,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/profile-updates\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(ProfileUpdates,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/meeting-sessions\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(MeetingSessions,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/withdrawals\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminWithdrawalManagement,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/meeting-issues\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminMeetingIssues,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/admin/earnings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN],children:/*#__PURE__*/_jsx(AdminEarnings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/meetings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherMeetings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/bookings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherBookings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/application\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherApplication,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/edit-application\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(EditApplication,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/edit-video\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(EditVideoUpload,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/upload-video\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.NEW_TEACHER,ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherVideoUpload,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/available-hours\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherApplicationAvailableHours,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/manage-hours\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.TEACHER,ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherProfileAvailableHours,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/view-hours\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.TEACHER,ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherViewAvailableHours,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/chat\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherChat,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/wallet\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherWallet,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/withdrawal\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherWithdrawal,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/contact-us\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherContactUs,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/my-messages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER],children:/*#__PURE__*/_jsx(TeacherMyMessages,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/reviews\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(TeacherReviews,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/my-lessons\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(MyLessons,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentDashboard,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/meetings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentMeetings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/complete-profile\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(CompleteProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/chat\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentChat,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/find-teacher\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentFindTeacher,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/teacher/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentTeacherProfile,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/book/:id\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(BookingPage,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/my-teachers\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(MyTeachers,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/bookings\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(Bookings,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/chat-embed\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentChatEmbed,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/wallet\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentWallet,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/contact-us\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentContactUs,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/my-messages\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(StudentMyMessages,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/student/write-review\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT],children:/*#__PURE__*/_jsx(WriteReview,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/join-meeting/:roomName\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.STUDENT,ROLES.PLATFORM_TEACHER],children:/*#__PURE__*/_jsx(JoinMeeting,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/find-teacher\",element:/*#__PURE__*/_jsx(FindTeacher,{})}),/*#__PURE__*/_jsx(Route,{path:\"/teacher/:id\",element:/*#__PURE__*/_jsx(TeacherDetails,{})}),/*#__PURE__*/_jsx(Route,{path:\"/about-us\",element:/*#__PURE__*/_jsx(AboutUs,{})}),/*#__PURE__*/_jsx(Route,{path:\"/contact-us\",element:/*#__PURE__*/_jsx(ContactUs,{})}),/*#__PURE__*/_jsx(Route,{path:\"/pending-deletion\",element:/*#__PURE__*/_jsx(PendingDeletion,{})}),/*#__PURE__*/_jsx(Route,{path:\"/platform-policy\",element:/*#__PURE__*/_jsx(ProtectedRoute,{allowedRoles:[ROLES.ADMIN,ROLES.PLATFORM_TEACHER,ROLES.NEW_TEACHER,ROLES.STUDENT],children:/*#__PURE__*/_jsx(PlatformPolicy,{})})}),/*#__PURE__*/_jsx(Route,{path:\"/terms-and-conditions\",element:/*#__PURE__*/_jsx(TermsAndConditions,{})}),/*#__PURE__*/_jsx(Route,{path:\"/privacy-policy\",element:/*#__PURE__*/_jsx(PrivacyPolicy,{})}),/*#__PURE__*/_jsx(Route,{path:\"/booking-cancellation-policy\",element:/*#__PURE__*/_jsx(BookingCancellationPolicy,{})}),/*#__PURE__*/_jsx(Route,{path:\"/booking-payment-policy\",element:/*#__PURE__*/_jsx(BookingPaymentPolicy,{})}),/*#__PURE__*/_jsx(Route,{path:\"/refund-policy\",element:/*#__PURE__*/_jsx(RefundPolicy,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/\",replace:true})})]})}),/*#__PURE__*/_jsx(ConditionalFooter,{})]})})})})]}),/*#__PURE__*/_jsx(Toaster,{position:\"top-center\",toastOptions:{duration:4000,style:{background:'#333',color:'#fff'}}})]});}export default App;", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useLocation", "ResizeObserverFix", "ThemeProvider", "createTheme", "CssBaseline", "Box", "CircularProgress", "useTranslation", "<PERSON>th<PERSON><PERSON><PERSON>", "useAuth", "SocketProvider", "UnreadMessagesProvider", "Toaster", "setupResizeObserverPolyfill", "Header", "Footer", "Authenticated<PERSON><PERSON>er", "Home", "AboutUs", "PlatformPolicy", "PendingDeletion", "TermsAndConditions", "PrivacyPolicy", "RefundPolicy", "BookingPaymentPolicy", "BookingCancellationPolicy", "<PERSON><PERSON>", "RegisterChoice", "StudentRegister", "TeacherRegister", "ForgotPassword", "VerifyResetCode", "ResetPassword", "VerifyEmail", "AdminDashboard", "AdminProfile", "TeacherApplications", "Teachers", "Students", "Categories", "Languages", "ProfileUpdates", "MeetingSessions", "TeacherDashboard", "TeacherApplication", "EditApplication", "EditVideoUpload", "TeacherVideoUpload", "TeacherApplicationAvailableHours", "TeacherProfileAvailableHours", "TeacherViewAvailableHours", "TeacherP<PERSON><PERSON>le", "TeacherChat", "TeacherMeetings", "TeacherBookings", "MyLessons", "StudentDashboard", "StudentProfile", "CompleteProfile", "StudentChat", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "StudentTeacherProfile", "StudentChatEmbed", "<PERSON><PERSON><PERSON><PERSON>", "TeacherDetails", "Join<PERSON>eeting", "StudentMeetings", "AdminWallet", "TeacherWallet", "StudentWallet", "StudentContactUs", "TeacherContactUs", "AdminMessages", "StudentMyMessages", "TeacherMyMessages", "BookingPage", "Bookings", "MyTeachers", "WriteReview", "TeacherReviews", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AdminWithdrawalManagement", "AdminEarnings", "AdminMeeting<PERSON>ssues", "ContactUs", "jsx", "_jsx", "jsxs", "_jsxs", "ROLES", "ADMIN", "PLATFORM_TEACHER", "NEW_TEACHER", "STUDENT", "ProtectedRoute", "_ref", "children", "allowedRoles", "currentUser", "isAuthenticated", "loading", "location", "display", "justifyContent", "alignItems", "minHeight", "to", "replace", "state", "from", "status", "pathname", "includes", "role", "PublicRoute", "_ref2", "ConditionalFooter", "isProtectedRoute", "startsWith", "App", "i18n", "direction", "language", "theme", "palette", "primary", "main", "secondary", "typography", "fontFamily", "sx", "flexDirection", "component", "flexGrow", "py", "path", "element", "TEACHER", "position", "toastOptions", "duration", "style", "background", "color"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';\nimport ResizeObserverFix from './utils/ResizeObserverFix';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box, CircularProgress } from '@mui/material';\nimport { useTranslation } from 'react-i18next';\nimport { AuthProvider, useAuth } from './contexts/AuthContext';\nimport { SocketProvider } from './contexts/SocketContext';\nimport { UnreadMessagesProvider } from './contexts/UnreadMessagesContext';\nimport { Toaster } from 'react-hot-toast';\nimport { setupResizeObserverPolyfill } from './utils/resizeObserver';\nimport Header from './components/layout/Header';\nimport Footer from './components/Footer';\nimport AuthenticatedFooter from './components/AuthenticatedFooter';\nimport './i18n/i18n';\n\n// Import pages here\nimport Home from './pages/Home';\nimport AboutUs from './pages/AboutUs';\nimport PlatformPolicy from './pages/PlatformPolicy';\nimport PendingDeletion from './pages/PendingDeletion';\nimport TermsAndConditions from './pages/TermsAndConditions';\nimport PrivacyPolicy from './pages/PrivacyPolicy';\nimport RefundPolicy from './pages/RefundPolicy';\nimport BookingPaymentPolicy from './pages/BookingPaymentPolicy';\nimport BookingCancellationPolicy from './pages/BookingCancellationPolicy';\nimport Login from './pages/auth/Login';\nimport RegisterChoice from './pages/auth/RegisterChoice';\nimport StudentRegister from './pages/auth/StudentRegister';\nimport TeacherRegister from './pages/auth/TeacherRegister';\nimport ForgotPassword from './pages/auth/ForgotPassword';\nimport VerifyResetCode from './pages/auth/VerifyResetCode';\nimport ResetPassword from './pages/auth/ResetPassword';\nimport VerifyEmail from './pages/auth/VerifyEmail';\nimport AdminDashboard from './pages/admin/Dashboard';\nimport AdminProfile from './pages/admin/Profile';\nimport TeacherApplications from './pages/admin/TeacherApplications';\nimport Teachers from './pages/admin/Teachers';\nimport Students from './pages/admin/Students';\nimport Categories from './pages/admin/Categories';\nimport Languages from './pages/admin/Languages';\nimport ProfileUpdates from './pages/admin/ProfileUpdates';\nimport MeetingSessions from './pages/admin/MeetingSessions';\nimport TeacherDashboard from './pages/teacher/Dashboard';\nimport TeacherApplication from './pages/teacher/Application';\nimport EditApplication from './pages/teacher/EditApplication';\nimport EditVideoUpload from './pages/teacher/EditVideoUpload';\nimport TeacherVideoUpload from './pages/teacher/VideoUpload';\nimport TeacherApplicationAvailableHours from './pages/teacher/AvailableHours';\nimport TeacherProfileAvailableHours from './pages/teacher/TeacherAvailableHours';\nimport TeacherViewAvailableHours from './pages/teacher/ViewAvailableHours';\nimport TeacherProfile from './pages/teacher/Profile';\nimport TeacherChat from './pages/teacher/Chat';\nimport TeacherMeetings from './pages/teacher/Meetings';\nimport TeacherBookings from './pages/teacher/Bookings';\nimport MyLessons from './pages/teacher/MyLessons';\nimport StudentDashboard from './pages/student/Dashboard';\nimport StudentProfile from './pages/student/Profile';\nimport CompleteProfile from './pages/student/CompleteProfile';\nimport StudentChat from './pages/student/Chat';\nimport StudentFindTeacher from './pages/student/FindTeacher';\nimport StudentTeacherProfile from './pages/student/TeacherProfile';\nimport StudentChatEmbed from './pages/student/ChatEmbed';\nimport FindTeacher from './pages/FindTeacher';\nimport TeacherDetails from './pages/TeacherDetails';\nimport JoinMeeting from './pages/student/JoinMeeting';\nimport StudentMeetings from './pages/student/Meetings';\nimport AdminWallet from './pages/admin/Wallet';\nimport TeacherWallet from './pages/teacher/Wallet';\nimport StudentWallet from './pages/student/Wallet';\nimport StudentContactUs from './pages/student/ContactUs';\nimport TeacherContactUs from './pages/teacher/ContactUs';\nimport AdminMessages from './pages/admin/Messages';\nimport StudentMyMessages from './pages/student/MyMessages';\nimport TeacherMyMessages from './pages/teacher/MyMessages';\nimport BookingPage from './pages/student/BookingPage';\nimport Bookings from './pages/student/Bookings';\nimport MyTeachers from './pages/student/MyTeachers';\nimport WriteReview from './pages/student/WriteReview';\nimport TeacherReviews from './pages/teacher/Reviews';\nimport TeacherWithdrawal from './pages/teacher/Withdrawal';\nimport AdminWithdrawalManagement from './pages/admin/WithdrawalManagement';\nimport AdminEarnings from './pages/admin/AdminEarnings';\nimport AdminMeetingIssues from './pages/admin/MeetingIssues';\nimport ContactUs from './pages/ContactUs';\n\n// Define roles\nconst ROLES = {\n  ADMIN: 'admin',\n  PLATFORM_TEACHER: 'platform_teacher',\n  NEW_TEACHER: 'new_teacher',\n  STUDENT: 'student'\n};\n\n// Protected Route Component\nconst ProtectedRoute = ({ children, allowedRoles }) => {\n  const { currentUser, isAuthenticated, loading } = useAuth();\n  const location = useLocation();\n\n  // If still loading, show loading spinner\n  if (loading) {\n    return (\n      <Box display=\"flex\" justifyContent=\"center\" alignItems=\"center\" minHeight=\"100vh\">\n        <CircularProgress />\n      </Box>\n    );\n  }\n\n  // If not authenticated or no user, redirect to login\n  if (!isAuthenticated || !currentUser) {\n    return <Navigate to=\"/login\" replace state={{ from: location }} />;\n  }\n\n  // If user account is pending deletion, redirect to pending deletion page\n  // unless they're already on that page\n  if (currentUser.status === 'pending_deletion' && location.pathname !== '/pending-deletion') {\n    return <Navigate to=\"/pending-deletion\" replace />;\n  }\n\n  // Check if user has required role\n  if (!allowedRoles.includes(currentUser.role)) {\n    // Special case: if a new teacher tries to access platform teacher routes\n    if (currentUser.role === ROLES.NEW_TEACHER && location.pathname === '/teacher/dashboard') {\n      return <Navigate to=\"/teacher/application\" replace />;\n    }\n\n    // Special case: if a platform teacher tries to access new teacher routes\n    if (currentUser.role === ROLES.PLATFORM_TEACHER && location.pathname === '/teacher/application') {\n      return <Navigate to=\"/teacher/dashboard\" replace />;\n    }\n\n    // Default redirects based on role\n    switch (currentUser.role) {\n      case ROLES.ADMIN:\n        return <Navigate to=\"/admin/dashboard\" replace />;\n      case ROLES.PLATFORM_TEACHER:\n        return <Navigate to=\"/teacher/dashboard\" replace />;\n      case ROLES.NEW_TEACHER:\n        return <Navigate to=\"/teacher/application\" replace />;\n      case ROLES.STUDENT:\n        return <Navigate to=\"/student/dashboard\" replace />;\n      default:\n        return <Navigate to=\"/login\" replace />;\n    }\n  }\n\n  // All checks passed, render the protected component\n  return children;\n};\n\n// Public Route Component\nconst PublicRoute = ({ children }) => {\n  const { isAuthenticated, currentUser } = useAuth();\n\n  // If authenticated, redirect to appropriate dashboard\n  if (isAuthenticated && currentUser) {\n    // If user account is pending deletion, redirect to pending deletion page\n    if (currentUser.status === 'pending_deletion') {\n      return <Navigate to=\"/pending-deletion\" replace />;\n    }\n\n    switch (currentUser.role) {\n      case ROLES.ADMIN:\n        return <Navigate to=\"/admin/dashboard\" replace />;\n      case ROLES.PLATFORM_TEACHER:\n        return <Navigate to=\"/teacher/dashboard\" replace />;\n      case ROLES.NEW_TEACHER:\n        return <Navigate to=\"/teacher/application\" replace />;\n      case ROLES.STUDENT:\n        return <Navigate to=\"/student/dashboard\" replace />;\n      default:\n        return <Navigate to=\"/\" replace />;\n    }\n  }\n\n  return children;\n};\n\n// Component to determine which Footer should be shown\nconst ConditionalFooter = () => {\n  const location = useLocation();\n  const { isAuthenticated, currentUser } = useAuth();\n\n  // Check if user is on protected routes (when user is logged in)\n  const isProtectedRoute = isAuthenticated && currentUser && (\n    location.pathname.startsWith('/admin/') ||\n    location.pathname.startsWith('/teacher/') ||\n    location.pathname.startsWith('/student/') ||\n    location.pathname.startsWith('/join-meeting/') ||\n    location.pathname === '/platform-policy'\n  );\n\n  // Show authenticated footer on protected routes\n  if (isProtectedRoute) {\n    return <AuthenticatedFooter />;\n  }\n\n  // Show public footer on public routes\n  return <Footer />;\n};\n\nfunction App() {\n  const { i18n } = useTranslation();\n  const direction = i18n.language === 'ar' ? 'rtl' : 'ltr';\n\n  useEffect(() => {\n    setupResizeObserverPolyfill();\n  }, []);\n\n  const theme = createTheme({\n    direction,\n    palette: {\n      primary: {\n        main: '#0C4B33', // Deep Islamic green\n      },\n      secondary: {\n        main: '#C3A343', // Islamic gold\n      },\n    },\n    typography: {\n      fontFamily: direction === 'rtl' ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\n    },\n  });\n\n  return (\n    <Router>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <ResizeObserverFix />\n        <AuthProvider>\n          <SocketProvider>\n            <UnreadMessagesProvider>\n              <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>\n              <Header />\n              <Box component=\"main\" sx={{ flexGrow: 1, py: 3 }}>\n                <Routes>\n                  {/* Public Routes */}\n                  <Route path=\"/\" element={<PublicRoute><Home /></PublicRoute>} />\n                  <Route path=\"/login\" element={<PublicRoute><Login /></PublicRoute>} />\n                  <Route path=\"/register\" element={<PublicRoute><RegisterChoice /></PublicRoute>} />\n                  <Route path=\"/register/student\" element={<PublicRoute><StudentRegister /></PublicRoute>} />\n                  <Route path=\"/register/teacher\" element={<PublicRoute><TeacherRegister /></PublicRoute>} />\n                  <Route path=\"/verify-email\" element={<PublicRoute><VerifyEmail /></PublicRoute>} />\n                  <Route path=\"/forgot-password\" element={<PublicRoute><ForgotPassword /></PublicRoute>} />\n                  <Route path=\"/verify-reset-code\" element={<PublicRoute><VerifyResetCode /></PublicRoute>} />\n                  <Route path=\"/reset-password\" element={<PublicRoute><ResetPassword /></PublicRoute>} />\n                  <Route path=\"/about-us\" element={<PublicRoute><AboutUs /></PublicRoute>} />\n                  {/* Pending Deletion Page - accessible to users with pending_deletion status */}\n                  <Route path=\"/pending-deletion\" element={<PendingDeletion />} />\n                  {/* Policy Pages */}\n                  <Route path=\"/terms-and-conditions\" element={<PublicRoute><TermsAndConditions /></PublicRoute>} />\n                  <Route path=\"/privacy-policy\" element={<PublicRoute><PrivacyPolicy /></PublicRoute>} />\n                  <Route path=\"/booking-cancellation-policy\" element={<PublicRoute><BookingCancellationPolicy /></PublicRoute>} />\n                  <Route path=\"/booking-payment-policy\" element={<PublicRoute><BookingPaymentPolicy /></PublicRoute>} />\n                  <Route path=\"/refund-policy\" element={<PublicRoute><RefundPolicy /></PublicRoute>} />\n                  <Route path=\"/find-teacher\" element={<PublicRoute><FindTeacher /></PublicRoute>} />\n                  <Route path=\"/teacher/:id\" element={<PublicRoute><TeacherDetails /></PublicRoute>} />\n\n                  {/* Admin Routes */}\n                  <Route path=\"/admin/dashboard\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminDashboard />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/profile\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminProfile />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/teachers\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <Teachers />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/students\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <Students />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/applications\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <TeacherApplications />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/categories\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <Categories />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/languages\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <Languages />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/wallet\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminWallet />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/messages\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminMessages />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/profile-updates\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <ProfileUpdates />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/meeting-sessions\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <MeetingSessions />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/withdrawals\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminWithdrawalManagement />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/meeting-issues\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminMeetingIssues />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/admin/earnings\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN]}>\n                      <AdminEarnings />\n                    </ProtectedRoute>\n                  } />\n\n                  {/* Teacher Routes */}\n                  <Route path=\"/teacher/dashboard\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherDashboard />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/meetings\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherMeetings />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/bookings\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherBookings />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/application\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER]}>\n                      <TeacherApplication />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/edit-application\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <EditApplication />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/edit-video\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <EditVideoUpload />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/upload-video\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER, ROLES.PLATFORM_TEACHER]}>\n                      <TeacherVideoUpload />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/available-hours\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.NEW_TEACHER]}>\n                      <TeacherApplicationAvailableHours />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/manage-hours\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.TEACHER, ROLES.PLATFORM_TEACHER]}>\n                      <TeacherProfileAvailableHours />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/view-hours\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.TEACHER, ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherViewAvailableHours />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/profile\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherProfile />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/chat\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherChat />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/wallet\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherWallet />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/withdrawal\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherWithdrawal />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/contact-us\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherContactUs />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/my-messages\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER]}>\n                      <TeacherMyMessages />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/reviews\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <TeacherReviews />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/teacher/my-lessons\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.PLATFORM_TEACHER]}>\n                      <MyLessons />\n                    </ProtectedRoute>\n                  } />\n\n                  {/* Student Routes */}\n                  <Route\n                    path=\"/student/dashboard\"\n                    element={\n                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                        <StudentDashboard />\n                      </ProtectedRoute>\n                    }\n                  />\n                  <Route\n                    path=\"/student/meetings\"\n                    element={\n                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                        <StudentMeetings />\n                      </ProtectedRoute>\n                    }\n                  />\n                  <Route\n                    path=\"/student/profile\"\n                    element={\n                      <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                        <StudentProfile />\n                      </ProtectedRoute>\n                    }\n                  />\n                  <Route path=\"/student/complete-profile\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <CompleteProfile />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/chat\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentChat />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/find-teacher\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentFindTeacher />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/teacher/:id\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentTeacherProfile />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/book/:id\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <BookingPage />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/my-teachers\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <MyTeachers />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/bookings\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <Bookings />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/chat-embed\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentChatEmbed />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/wallet\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentWallet />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/contact-us\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentContactUs />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/my-messages\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <StudentMyMessages />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/student/write-review\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT]}>\n                      <WriteReview />\n                    </ProtectedRoute>\n                  } />\n                  <Route path=\"/join-meeting/:roomName\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.STUDENT, ROLES.PLATFORM_TEACHER]}>\n                      <JoinMeeting />\n                    </ProtectedRoute>\n                  } />\n\n                  {/* Common Routes */}\n                  <Route path=\"/find-teacher\" element={<FindTeacher />} />\n                  <Route path=\"/teacher/:id\" element={<TeacherDetails />} />\n                  <Route path=\"/about-us\" element={<AboutUs />} />\n                  <Route path=\"/contact-us\" element={<ContactUs />} />\n                  {/* Pending Deletion Page - accessible to users with pending_deletion status */}\n                  <Route path=\"/pending-deletion\" element={<PendingDeletion />} />\n                  {/* Unified Platform Policy - accessible to all authenticated users */}\n                  <Route path=\"/platform-policy\" element={\n                    <ProtectedRoute allowedRoles={[ROLES.ADMIN, ROLES.PLATFORM_TEACHER, ROLES.NEW_TEACHER, ROLES.STUDENT]}>\n                      <PlatformPolicy />\n                    </ProtectedRoute>\n                  } />\n\n                  {/* Policy Pages - accessible to authenticated users */}\n                  <Route path=\"/terms-and-conditions\" element={<TermsAndConditions />} />\n                  <Route path=\"/privacy-policy\" element={<PrivacyPolicy />} />\n                  <Route path=\"/booking-cancellation-policy\" element={<BookingCancellationPolicy />} />\n                  <Route path=\"/booking-payment-policy\" element={<BookingPaymentPolicy />} />\n                  <Route path=\"/refund-policy\" element={<RefundPolicy />} />\n\n                  {/* Catch all route */}\n                  <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n                </Routes>\n              </Box>\n              <ConditionalFooter />\n              </Box>\n            </UnreadMessagesProvider>\n          </SocketProvider>\n        </AuthProvider>\n      </ThemeProvider>\n      <Toaster\n        position=\"top-center\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#333',\n            color: '#fff',\n          },\n        }}\n      />\n    </Router>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CAChG,MAAO,CAAAC,iBAAiB,KAAM,2BAA2B,CACzD,OAASC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CACjE,OAASC,WAAW,CAAEC,GAAG,CAAEC,gBAAgB,KAAQ,eAAe,CAClE,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,YAAY,CAAEC,OAAO,KAAQ,wBAAwB,CAC9D,OAASC,cAAc,KAAQ,0BAA0B,CACzD,OAASC,sBAAsB,KAAQ,kCAAkC,CACzE,OAASC,OAAO,KAAQ,iBAAiB,CACzC,OAASC,2BAA2B,KAAQ,wBAAwB,CACpE,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,mBAAmB,KAAM,kCAAkC,CAClE,MAAO,aAAa,CAEpB;AACA,MAAO,CAAAC,IAAI,KAAM,cAAc,CAC/B,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,kBAAkB,KAAM,4BAA4B,CAC3D,MAAO,CAAAC,aAAa,KAAM,uBAAuB,CACjD,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAC/C,MAAO,CAAAC,oBAAoB,KAAM,8BAA8B,CAC/D,MAAO,CAAAC,yBAAyB,KAAM,mCAAmC,CACzE,MAAO,CAAAC,KAAK,KAAM,oBAAoB,CACtC,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,eAAe,KAAM,8BAA8B,CAC1D,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,YAAY,KAAM,uBAAuB,CAChD,MAAO,CAAAC,mBAAmB,KAAM,mCAAmC,CACnE,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,QAAQ,KAAM,wBAAwB,CAC7C,MAAO,CAAAC,UAAU,KAAM,0BAA0B,CACjD,MAAO,CAAAC,SAAS,KAAM,yBAAyB,CAC/C,MAAO,CAAAC,cAAc,KAAM,8BAA8B,CACzD,MAAO,CAAAC,eAAe,KAAM,+BAA+B,CAC3D,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,MAAO,CAAAC,gCAAgC,KAAM,gCAAgC,CAC7E,MAAO,CAAAC,4BAA4B,KAAM,uCAAuC,CAChF,MAAO,CAAAC,yBAAyB,KAAM,oCAAoC,CAC1E,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,eAAe,KAAM,0BAA0B,CACtD,MAAO,CAAAC,eAAe,KAAM,0BAA0B,CACtD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,MAAO,CAAAC,qBAAqB,KAAM,gCAAgC,CAClE,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,WAAW,KAAM,qBAAqB,CAC7C,MAAO,CAAAC,cAAc,KAAM,wBAAwB,CACnD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,eAAe,KAAM,0BAA0B,CACtD,MAAO,CAAAC,WAAW,KAAM,sBAAsB,CAC9C,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,gBAAgB,KAAM,2BAA2B,CACxD,MAAO,CAAAC,aAAa,KAAM,wBAAwB,CAClD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,cAAc,KAAM,yBAAyB,CACpD,MAAO,CAAAC,iBAAiB,KAAM,4BAA4B,CAC1D,MAAO,CAAAC,yBAAyB,KAAM,oCAAoC,CAC1E,MAAO,CAAAC,aAAa,KAAM,6BAA6B,CACvD,MAAO,CAAAC,kBAAkB,KAAM,6BAA6B,CAC5D,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CAEzC;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,KAAK,CAAG,CACZC,KAAK,CAAE,OAAO,CACdC,gBAAgB,CAAE,kBAAkB,CACpCC,WAAW,CAAE,aAAa,CAC1BC,OAAO,CAAE,SACX,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAgC,IAA/B,CAAEC,QAAQ,CAAEC,YAAa,CAAC,CAAAF,IAAA,CAChD,KAAM,CAAEG,WAAW,CAAEC,eAAe,CAAEC,OAAQ,CAAC,CAAG3F,OAAO,CAAC,CAAC,CAC3D,KAAM,CAAA4F,QAAQ,CAAGrG,WAAW,CAAC,CAAC,CAE9B;AACA,GAAIoG,OAAO,CAAE,CACX,mBACEd,IAAA,CAACjF,GAAG,EAACiG,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAACC,SAAS,CAAC,OAAO,CAAAT,QAAA,cAC/EV,IAAA,CAAChF,gBAAgB,GAAE,CAAC,CACjB,CAAC,CAEV,CAEA;AACA,GAAI,CAAC6F,eAAe,EAAI,CAACD,WAAW,CAAE,CACpC,mBAAOZ,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,QAAQ,CAACC,OAAO,MAACC,KAAK,CAAE,CAAEC,IAAI,CAAER,QAAS,CAAE,CAAE,CAAC,CACpE,CAEA;AACA;AACA,GAAIH,WAAW,CAACY,MAAM,GAAK,kBAAkB,EAAIT,QAAQ,CAACU,QAAQ,GAAK,mBAAmB,CAAE,CAC1F,mBAAOzB,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,mBAAmB,CAACC,OAAO,MAAE,CAAC,CACpD,CAEA;AACA,GAAI,CAACV,YAAY,CAACe,QAAQ,CAACd,WAAW,CAACe,IAAI,CAAC,CAAE,CAC5C;AACA,GAAIf,WAAW,CAACe,IAAI,GAAKxB,KAAK,CAACG,WAAW,EAAIS,QAAQ,CAACU,QAAQ,GAAK,oBAAoB,CAAE,CACxF,mBAAOzB,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,sBAAsB,CAACC,OAAO,MAAE,CAAC,CACvD,CAEA;AACA,GAAIT,WAAW,CAACe,IAAI,GAAKxB,KAAK,CAACE,gBAAgB,EAAIU,QAAQ,CAACU,QAAQ,GAAK,sBAAsB,CAAE,CAC/F,mBAAOzB,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,CAEA;AACA,OAAQT,WAAW,CAACe,IAAI,EACtB,IAAK,CAAAxB,KAAK,CAACC,KAAK,CACd,mBAAOJ,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,kBAAkB,CAACC,OAAO,MAAE,CAAC,CACnD,IAAK,CAAAlB,KAAK,CAACE,gBAAgB,CACzB,mBAAOL,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,IAAK,CAAAlB,KAAK,CAACG,WAAW,CACpB,mBAAON,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,sBAAsB,CAACC,OAAO,MAAE,CAAC,CACvD,IAAK,CAAAlB,KAAK,CAACI,OAAO,CAChB,mBAAOP,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,QACE,mBAAOrB,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAC,CAC3C,CACF,CAEA;AACA,MAAO,CAAAX,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAkB,WAAW,CAAGC,KAAA,EAAkB,IAAjB,CAAEnB,QAAS,CAAC,CAAAmB,KAAA,CAC/B,KAAM,CAAEhB,eAAe,CAAED,WAAY,CAAC,CAAGzF,OAAO,CAAC,CAAC,CAElD;AACA,GAAI0F,eAAe,EAAID,WAAW,CAAE,CAClC;AACA,GAAIA,WAAW,CAACY,MAAM,GAAK,kBAAkB,CAAE,CAC7C,mBAAOxB,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,mBAAmB,CAACC,OAAO,MAAE,CAAC,CACpD,CAEA,OAAQT,WAAW,CAACe,IAAI,EACtB,IAAK,CAAAxB,KAAK,CAACC,KAAK,CACd,mBAAOJ,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,kBAAkB,CAACC,OAAO,MAAE,CAAC,CACnD,IAAK,CAAAlB,KAAK,CAACE,gBAAgB,CACzB,mBAAOL,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,IAAK,CAAAlB,KAAK,CAACG,WAAW,CACpB,mBAAON,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,sBAAsB,CAACC,OAAO,MAAE,CAAC,CACvD,IAAK,CAAAlB,KAAK,CAACI,OAAO,CAChB,mBAAOP,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,oBAAoB,CAACC,OAAO,MAAE,CAAC,CACrD,QACE,mBAAOrB,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAC,CACtC,CACF,CAEA,MAAO,CAAAX,QAAQ,CACjB,CAAC,CAED;AACA,KAAM,CAAAoB,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAAf,QAAQ,CAAGrG,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEmG,eAAe,CAAED,WAAY,CAAC,CAAGzF,OAAO,CAAC,CAAC,CAElD;AACA,KAAM,CAAA4G,gBAAgB,CAAGlB,eAAe,EAAID,WAAW,GACrDG,QAAQ,CAACU,QAAQ,CAACO,UAAU,CAAC,SAAS,CAAC,EACvCjB,QAAQ,CAACU,QAAQ,CAACO,UAAU,CAAC,WAAW,CAAC,EACzCjB,QAAQ,CAACU,QAAQ,CAACO,UAAU,CAAC,WAAW,CAAC,EACzCjB,QAAQ,CAACU,QAAQ,CAACO,UAAU,CAAC,gBAAgB,CAAC,EAC9CjB,QAAQ,CAACU,QAAQ,GAAK,kBAAkB,CACzC,CAED;AACA,GAAIM,gBAAgB,CAAE,CACpB,mBAAO/B,IAAA,CAACtE,mBAAmB,GAAE,CAAC,CAChC,CAEA;AACA,mBAAOsE,IAAA,CAACvE,MAAM,GAAE,CAAC,CACnB,CAAC,CAED,QAAS,CAAAwG,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,IAAK,CAAC,CAAGjH,cAAc,CAAC,CAAC,CACjC,KAAM,CAAAkH,SAAS,CAAGD,IAAI,CAACE,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CAExDhI,SAAS,CAAC,IAAM,CACdmB,2BAA2B,CAAC,CAAC,CAC/B,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAA8G,KAAK,CAAGxH,WAAW,CAAC,CACxBsH,SAAS,CACTG,OAAO,CAAE,CACPC,OAAO,CAAE,CACPC,IAAI,CAAE,SAAW;AACnB,CAAC,CACDC,SAAS,CAAE,CACTD,IAAI,CAAE,SAAW;AACnB,CACF,CAAC,CACDE,UAAU,CAAE,CACVC,UAAU,CAAER,SAAS,GAAK,KAAK,CAAG,qBAAqB,CAAG,oBAC5D,CACF,CAAC,CAAC,CAEF,mBACEjC,KAAA,CAAC5F,MAAM,EAAAoG,QAAA,eACLR,KAAA,CAACtF,aAAa,EAACyH,KAAK,CAAEA,KAAM,CAAA3B,QAAA,eAC1BV,IAAA,CAAClF,WAAW,GAAE,CAAC,cACfkF,IAAA,CAACrF,iBAAiB,GAAE,CAAC,cACrBqF,IAAA,CAAC9E,YAAY,EAAAwF,QAAA,cACXV,IAAA,CAAC5E,cAAc,EAAAsF,QAAA,cACbV,IAAA,CAAC3E,sBAAsB,EAAAqF,QAAA,cACrBR,KAAA,CAACnF,GAAG,EAAC6H,EAAE,CAAE,CAAE5B,OAAO,CAAE,MAAM,CAAE6B,aAAa,CAAE,QAAQ,CAAE1B,SAAS,CAAE,OAAQ,CAAE,CAAAT,QAAA,eAC1EV,IAAA,CAACxE,MAAM,GAAE,CAAC,cACVwE,IAAA,CAACjF,GAAG,EAAC+H,SAAS,CAAC,MAAM,CAACF,EAAE,CAAE,CAAEG,QAAQ,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAtC,QAAA,cAC/CR,KAAA,CAAC3F,MAAM,EAAAmG,QAAA,eAELV,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,GAAG,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACrE,IAAI,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAChEqE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC5D,KAAK,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACtE4D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC3D,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAClF2D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC1D,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3F0D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACzD,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC3FyD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACrD,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFqD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACxD,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACzFwD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACvD,eAAe,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAC5FuD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACtD,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFsD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACpE,OAAO,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAE3EoE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAElD,IAAA,CAAClE,eAAe,GAAE,CAAE,CAAE,CAAC,cAEhEkE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACjE,kBAAkB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAClGiE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAChE,aAAa,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACvFgE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,8BAA8B,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC7D,yBAAyB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAChH6D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC9D,oBAAoB,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACtG8D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAAC/D,YAAY,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACrF+D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACvB,WAAW,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cACnFuB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,cAAc,CAACC,OAAO,cAAElD,IAAA,CAAC4B,WAAW,EAAAlB,QAAA,cAACV,IAAA,CAACtB,cAAc,GAAE,CAAC,CAAa,CAAE,CAAE,CAAC,cAGrFsB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACpD,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJoD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAClClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACnD,YAAY,GAAE,CAAC,CACF,CACjB,CAAE,CAAC,cACJmD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACjD,QAAQ,GAAE,CAAC,CACE,CACjB,CAAE,CAAC,cACJiD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAChD,QAAQ,GAAE,CAAC,CACE,CACjB,CAAE,CAAC,cACJgD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAClD,mBAAmB,GAAE,CAAC,CACT,CACjB,CAAE,CAAC,cACJkD,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAC/C,UAAU,GAAE,CAAC,CACA,CACjB,CAAE,CAAC,cACJ+C,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAC9C,SAAS,GAAE,CAAC,CACC,CACjB,CAAE,CAAC,cACJ8C,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,eAAe,CAACC,OAAO,cACjClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACnB,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJmB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACd,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cACJc,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,wBAAwB,CAACC,OAAO,cAC1ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAC7C,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJ6C,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAC3ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAAC5C,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJ4C,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,oBAAoB,CAACC,OAAO,cACtClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACL,yBAAyB,GAAE,CAAC,CACf,CACjB,CAAE,CAAC,cACJK,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACH,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJG,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAE,CAAAM,QAAA,cAC1CV,IAAA,CAACJ,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cAGJI,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,oBAAoB,CAACC,OAAO,cACtClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAAC3C,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJ2C,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACjC,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJiC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAAChC,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJgC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cAChDV,IAAA,CAAC1C,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJ0C,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,2BAA2B,CAACC,OAAO,cAC7ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACzC,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJyC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACxC,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJwC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACG,WAAW,CAAEH,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACxEV,IAAA,CAACvC,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJuC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,0BAA0B,CAACC,OAAO,cAC5ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cAChDV,IAAA,CAACtC,gCAAgC,GAAE,CAAC,CACtB,CACjB,CAAE,CAAC,cACJsC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACgD,OAAO,CAAEhD,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACpEV,IAAA,CAACrC,4BAA4B,GAAE,CAAC,CAClB,CACjB,CAAE,CAAC,cACJqC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACgD,OAAO,CAAEhD,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACvFV,IAAA,CAACpC,yBAAyB,GAAE,CAAC,CACf,CACjB,CAAE,CAAC,cACJoC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACxEV,IAAA,CAACnC,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJmC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,eAAe,CAACC,OAAO,cACjClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAAClC,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJkC,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACxEV,IAAA,CAAClB,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cACJkB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACN,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cACJM,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACxEV,IAAA,CAACf,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJe,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAE,CAAAI,QAAA,cACxEV,IAAA,CAACZ,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cACJY,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAACP,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cACJO,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACrDV,IAAA,CAAC/B,SAAS,GAAE,CAAC,CACC,CACjB,CAAE,CAAC,cAGJ+B,IAAA,CAACxF,KAAK,EACJyI,IAAI,CAAC,oBAAoB,CACzBC,OAAO,cACLlD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC9B,gBAAgB,GAAE,CAAC,CACN,CACjB,CACF,CAAC,cACF8B,IAAA,CAACxF,KAAK,EACJyI,IAAI,CAAC,mBAAmB,CACxBC,OAAO,cACLlD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACpB,eAAe,GAAE,CAAC,CACL,CACjB,CACF,CAAC,cACFoB,IAAA,CAACxF,KAAK,EACJyI,IAAI,CAAC,kBAAkB,CACvBC,OAAO,cACLlD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC7B,cAAc,GAAE,CAAC,CACJ,CACjB,CACF,CAAC,cACF6B,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,2BAA2B,CAACC,OAAO,cAC7ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC5B,eAAe,GAAE,CAAC,CACL,CACjB,CAAE,CAAC,cACJ4B,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,eAAe,CAACC,OAAO,cACjClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC3B,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJ2B,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAC1B,kBAAkB,GAAE,CAAC,CACR,CACjB,CAAE,CAAC,cACJ0B,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACzB,qBAAqB,GAAE,CAAC,CACX,CACjB,CAAE,CAAC,cACJyB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACX,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJW,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACT,UAAU,GAAE,CAAC,CACA,CACjB,CAAE,CAAC,cACJS,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cACrClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACV,QAAQ,GAAE,CAAC,CACE,CACjB,CAAE,CAAC,cACJU,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACxB,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJwB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cACnClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACjB,aAAa,GAAE,CAAC,CACH,CACjB,CAAE,CAAC,cACJiB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,qBAAqB,CAACC,OAAO,cACvClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAAChB,gBAAgB,GAAE,CAAC,CACN,CACjB,CAAE,CAAC,cACJgB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,sBAAsB,CAACC,OAAO,cACxClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACb,iBAAiB,GAAE,CAAC,CACP,CACjB,CAAE,CAAC,cACJa,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,uBAAuB,CAACC,OAAO,cACzClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cAC5CV,IAAA,CAACR,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cACJQ,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAC3ClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACI,OAAO,CAAEJ,KAAK,CAACE,gBAAgB,CAAE,CAAAK,QAAA,cACpEV,IAAA,CAACrB,WAAW,GAAE,CAAC,CACD,CACjB,CAAE,CAAC,cAGJqB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,eAAe,CAACC,OAAO,cAAElD,IAAA,CAACvB,WAAW,GAAE,CAAE,CAAE,CAAC,cACxDuB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,cAAc,CAACC,OAAO,cAAElD,IAAA,CAACtB,cAAc,GAAE,CAAE,CAAE,CAAC,cAC1DsB,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,WAAW,CAACC,OAAO,cAAElD,IAAA,CAACpE,OAAO,GAAE,CAAE,CAAE,CAAC,cAChDoE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,aAAa,CAACC,OAAO,cAAElD,IAAA,CAACF,SAAS,GAAE,CAAE,CAAE,CAAC,cAEpDE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAElD,IAAA,CAAClE,eAAe,GAAE,CAAE,CAAE,CAAC,cAEhEkE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,kBAAkB,CAACC,OAAO,cACpClD,IAAA,CAACQ,cAAc,EAACG,YAAY,CAAE,CAACR,KAAK,CAACC,KAAK,CAAED,KAAK,CAACE,gBAAgB,CAAEF,KAAK,CAACG,WAAW,CAAEH,KAAK,CAACI,OAAO,CAAE,CAAAG,QAAA,cACpGV,IAAA,CAACnE,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cAGJmE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAElD,IAAA,CAACjE,kBAAkB,GAAE,CAAE,CAAE,CAAC,cACvEiE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAElD,IAAA,CAAChE,aAAa,GAAE,CAAE,CAAE,CAAC,cAC5DgE,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,8BAA8B,CAACC,OAAO,cAAElD,IAAA,CAAC7D,yBAAyB,GAAE,CAAE,CAAE,CAAC,cACrF6D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,yBAAyB,CAACC,OAAO,cAAElD,IAAA,CAAC9D,oBAAoB,GAAE,CAAE,CAAE,CAAC,cAC3E8D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAElD,IAAA,CAAC/D,YAAY,GAAE,CAAE,CAAE,CAAC,cAG1D+D,IAAA,CAACxF,KAAK,EAACyI,IAAI,CAAC,GAAG,CAACC,OAAO,cAAElD,IAAA,CAACvF,QAAQ,EAAC2G,EAAE,CAAC,GAAG,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EAClD,CAAC,CACN,CAAC,cACNrB,IAAA,CAAC8B,iBAAiB,GAAE,CAAC,EAChB,CAAC,CACgB,CAAC,CACX,CAAC,CACL,CAAC,EACF,CAAC,cAChB9B,IAAA,CAAC1E,OAAO,EACN8H,QAAQ,CAAC,YAAY,CACrBC,YAAY,CAAE,CACZC,QAAQ,CAAE,IAAI,CACdC,KAAK,CAAE,CACLC,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,MACT,CACF,CAAE,CACH,CAAC,EACI,CAAC,CAEb,CAEA,cAAe,CAAAxB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}