{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useLocation,useNavigate}from'react-router-dom';import{Container,Typography,Paper,Box,Divider,useTheme,Fade,List,ListItem,ListItemText,ListItemIcon,Tabs,Tab,Card,CardContent,alpha,Stack}from'@mui/material';import{Security as SecurityIcon,Schedule as ScheduleIcon,Person as PersonIcon,School as SchoolIcon,Info as InfoIcon,Payment as PaymentIcon,Refresh as RefreshIcon,Description as DescriptionIcon}from'@mui/icons-material';import Layout from'../components/Layout';import termsConditions from'../i18n/translations/termsConditions';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PlatformPolicy=()=>{const{t,i18n}=useTranslation();const theme=useTheme();const location=useLocation();const navigate=useNavigate();const[isRTL,setIsRTL]=useState(i18n.language==='ar');const[activeTab,setActiveTab]=useState(0);useEffect(()=>{setIsRTL(i18n.language==='ar');document.dir=i18n.language==='ar'?'rtl':'ltr';document.documentElement.dir=i18n.language==='ar'?'rtl':'ltr';document.body.dir=i18n.language==='ar'?'rtl':'ltr';// Handle URL-based navigation to specific sections\nconst hash=location.hash;if(hash==='#terms')setActiveTab(0);else if(hash==='#privacy')setActiveTab(1);else if(hash==='#booking')setActiveTab(2);else if(hash==='#payment')setActiveTab(3);else if(hash==='#refund')setActiveTab(4);},[i18n.language,location.hash]);const handleTabChange=(event,newValue)=>{setActiveTab(newValue);const tabs=['#terms','#privacy','#booking','#payment','#refund'];navigate(`/platform-policy${tabs[newValue]}`,{replace:true});};const tabsConfig=[{label:i18n.language==='ar'?'الشروط والأحكام':'Terms & Conditions',icon:/*#__PURE__*/_jsx(DescriptionIcon,{})},{label:i18n.language==='ar'?'سياسة الخصوصية':'Privacy Policy',icon:/*#__PURE__*/_jsx(SecurityIcon,{})},{label:i18n.language==='ar'?'سياسة الحجز والإلغاء':'Booking & Cancellation',icon:/*#__PURE__*/_jsx(ScheduleIcon,{})},{label:i18n.language==='ar'?'سياسة الدفع':'Payment Policy',icon:/*#__PURE__*/_jsx(PaymentIcon,{})},{label:i18n.language==='ar'?'سياسة الاسترداد':'Refund Policy',icon:/*#__PURE__*/_jsx(RefreshIcon,{})}];// Helper component for section cards\nconst SectionCard=_ref=>{let{title,children,icon,bg}=_ref;return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{mb:4,borderRadius:3,direction:isRTL?'rtl':'ltr',...(isRTL?{borderRight:`6px solid ${theme.palette.primary.main}`}:{borderLeft:`6px solid ${theme.palette.primary.main}`}),backgroundColor:bg||alpha(theme.palette.primary.main,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{sx:{direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2,flexDirection:isRTL?'row-reverse':'row',direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[icon&&/*#__PURE__*/_jsx(Box,{sx:{mr:isRTL?0:2,ml:isRTL?2:0,color:theme.palette.primary.main},children:icon}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:theme.palette.primary.main,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'Roboto, sans-serif',direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:title})]}),/*#__PURE__*/_jsx(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:children})]})});};// Helper function to render subsections\nconst renderSubSection=section=>{if(!section||typeof section!=='object')return null;return/*#__PURE__*/_jsxs(Box,{sx:{mb:3,direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:1,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.title}),section.description&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.description}),section.points&&/*#__PURE__*/_jsx(List,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,direction:isRTL?'rtl':'ltr','& .MuiListItem-root':{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'}},children:section.points.map((point,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5,textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr',flexDirection:isRTL?'row-reverse':'row'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0,order:isRTL?2:1},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:point,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr',order:isRTL?1:2,'& .MuiListItemText-primary':{textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr'}}})]},idx))}),section.items&&/*#__PURE__*/_jsx(List,{sx:{pl:isRTL?0:2,pr:isRTL?2:0,direction:isRTL?'rtl':'ltr','& .MuiListItem-root':{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'}},children:section.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5,textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr',flexDirection:isRTL?'row-reverse':'row'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0,order:isRTL?2:1},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr',order:isRTL?1:2,'& .MuiListItemText-primary':{textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr'}}})]},idx))})]});};// Tab panel component\nconst TabPanel=_ref2=>{let{children,value,index}=_ref2;return/*#__PURE__*/_jsx(\"div\",{hidden:value!==index,children:value===index&&/*#__PURE__*/_jsx(Box,{sx:{py:3,direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:children})});};// Render Terms and Conditions\nconst renderTermsAndConditions=()=>{const termsContent=i18n.language==='ar'?termsConditions.ar.termsConditions:termsConditions.en.termsConditions;if(!termsContent||typeof termsContent!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:3,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:termsContent.title||(isRTL?'الشروط والأحكام':'Terms and Conditions')}),/*#__PURE__*/_jsx(Stack,{spacing:3,children:termsContent.content&&termsContent.content.split(/\\n\\s*\\n/).filter(section=>section.trim()!=='').map((section,idx)=>{const lines=section.split('\\n');const title=lines[0];const body=lines.slice(1).join('\\n');return/*#__PURE__*/_jsx(Card,{elevation:2,sx:{borderRadius:3,direction:isRTL?'rtl':'ltr',...(isRTL?{borderRight:`4px solid ${theme.palette.primary.main}`}:{borderLeft:`4px solid ${theme.palette.primary.main}`}),backgroundColor:idx%2===0?alpha(theme.palette.primary.main,0.02):alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02)},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:600,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:title}),body&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',whiteSpace:'pre-line',textAlign:isRTL?'right':'left'},children:body})]})},idx);})})]});};// Render Privacy Policy\nconst renderPrivacyPolicy=()=>{const privacy=t('privacy',{returnObjects:true});if(!privacy||typeof privacy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:privacy.title||(isRTL?'سياسة الخصوصية':'Privacy Policy')}),privacy.intro&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:4,lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:privacy.intro}),/*#__PURE__*/_jsx(Divider,{sx:{mb:4}}),[1,2,3,4,5,6,7,8,9].map(num=>{const section=privacy[`section${num}`];if(!section||typeof section!=='object')return null;return/*#__PURE__*/_jsxs(SectionCard,{title:section.title||`القسم ${num}`,icon:/*#__PURE__*/_jsx(SecurityIcon,{}),children:[section.subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.subtitle}),section.description&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.description}),section.item1&&/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.item1,section.item2,section.item3,section.item4,section.item5].filter(Boolean).map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]},num);})]});};// Render Booking & Cancellation Policy\nconst renderBookingPolicy=()=>{const policy=t('policies.bookingCancellation',{returnObjects:true});if(!policy||typeof policy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:policy.title||(isRTL?'سياسة الحجز والإلغاء':'Booking & Cancellation Policy')}),policy.subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:4,lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:4}}),policy.studentPolicy&&/*#__PURE__*/_jsx(SectionCard,{title:policy.studentPolicy.title||(isRTL?'سياسة الطلاب':'Student Policy'),icon:/*#__PURE__*/_jsx(PersonIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[policy.studentPolicy.booking&&renderSubSection(policy.studentPolicy.booking),policy.studentPolicy.cancellation&&renderSubSection(policy.studentPolicy.cancellation),policy.studentPolicy.rescheduling&&renderSubSection(policy.studentPolicy.rescheduling),policy.studentPolicy.lateArrival&&renderSubSection(policy.studentPolicy.lateArrival)]})}),policy.tutorPolicy&&/*#__PURE__*/_jsx(SectionCard,{title:policy.tutorPolicy.title||(isRTL?'سياسة المعلمين':'Tutor Policy'),icon:/*#__PURE__*/_jsx(SchoolIcon,{}),bg:alpha(theme.palette.secondary.main||theme.palette.primary.light,0.02),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[policy.tutorPolicy.availability&&renderSubSection(policy.tutorPolicy.availability),policy.tutorPolicy.cancellation&&renderSubSection(policy.tutorPolicy.cancellation),policy.tutorPolicy.rescheduling&&renderSubSection(policy.tutorPolicy.rescheduling),policy.tutorPolicy.lateArrival&&renderSubSection(policy.tutorPolicy.lateArrival)]})}),policy.generalNotes&&policy.generalNotes.points&&/*#__PURE__*/_jsx(SectionCard,{title:policy.generalNotes.title||(isRTL?'ملاحظات عامة':'General Notes'),icon:/*#__PURE__*/_jsx(InfoIcon,{}),children:/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:policy.generalNotes.points.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5,textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"info\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})})]});};// Render Payment Policy\nconst renderPaymentPolicy=()=>{const policy=t('policies.bookingPayment',{returnObjects:true});if(!policy||typeof policy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:policy.title||(isRTL?'سياسة الدفع':'Payment Policy')}),policy.subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:4,lineHeight:1.8,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:policy.subtitle}),/*#__PURE__*/_jsx(Divider,{sx:{mb:4}}),[1,2,3,4,5,6,7,8].map(num=>{const section=policy[`section${num}`];if(!section||typeof section!=='object')return null;return/*#__PURE__*/_jsx(SectionCard,{title:section.title||`القسم ${num}`,icon:/*#__PURE__*/_jsx(PaymentIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.description&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.description}),section.points&&/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:section.points.map((point,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5,textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:point,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]})},num);})]});};// Render Refund Policy\nconst renderRefundPolicy=()=>{const policy=t('policies.refund',{returnObjects:true});if(!policy||typeof policy!=='object'){return/*#__PURE__*/_jsx(Box,{sx:{textAlign:'center',py:4},children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"text.secondary\",children:isRTL?'لا توجد بيانات متاحة':'No data available'})});}return/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,mb:2,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,textAlign:isRTL?'right':'left'},children:policy.title||(isRTL?'سياسة الاسترداد':'Refund Policy')}),/*#__PURE__*/_jsx(Divider,{sx:{mb:4}}),[1,2,3,4,5,6,7,8].map(num=>{const section=policy[`section${num}`];if(!section||typeof section!=='object')return null;return/*#__PURE__*/_jsx(SectionCard,{title:section.title||`القسم ${num}`,icon:/*#__PURE__*/_jsx(RefreshIcon,{}),children:/*#__PURE__*/_jsxs(Box,{sx:{direction:isRTL?'rtl':'ltr'},children:[section.description&&/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:2,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',textAlign:isRTL?'right':'left'},children:section.description}),section.items&&/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:section.items.map((item,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5,textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:item,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))}),section.points&&/*#__PURE__*/_jsx(List,{sx:{direction:isRTL?'rtl':'ltr'},children:section.points.map((point,idx)=>/*#__PURE__*/_jsxs(ListItem,{sx:{py:0.5,textAlign:isRTL?'right':'left'},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:'auto',mr:isRTL?0:1,ml:isRTL?1:0},children:/*#__PURE__*/_jsx(InfoIcon,{color:\"primary\",fontSize:\"small\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:point,sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit'}})]},idx))})]})},num);})]});};return/*#__PURE__*/_jsx(Layout,{children:/*#__PURE__*/_jsx(Box,{sx:{py:4,minHeight:'100vh',background:`linear-gradient(${alpha(theme.palette.primary.main,0.05)}, ${alpha(theme.palette.primary.main,0.1)})`,direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Container,{maxWidth:\"lg\",sx:{direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:1000,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:2,md:4},mb:4,borderRadius:3,direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left','& *':{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'},'& .MuiTypography-root':{textAlign:isRTL?'right':'left',direction:isRTL?'rtl':'ltr'},'& .MuiList-root':{direction:isRTL?'rtl':'ltr'},'& .MuiListItem-root':{direction:isRTL?'rtl':'ltr',textAlign:isRTL?'right':'left'}},children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:4,textAlign:'center',direction:isRTL?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",component:\"h1\",sx:{mb:2,fontWeight:800,fontFamily:isRTL?'Tajawal, sans-serif':'inherit',color:theme.palette.primary.main,direction:isRTL?'rtl':'ltr',textAlign:'center'},children:isRTL?'سياسات المنصة':'Platform Policies'}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",sx:{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',direction:isRTL?'rtl':'ltr',textAlign:'center'},children:isRTL?'جميع السياسات والشروط الخاصة بمنصة علّمني أون لاين في مكان واحد':'All Allemnionline platform policies and terms in one place'})]}),/*#__PURE__*/_jsx(Box,{sx:{borderBottom:1,borderColor:'divider',mb:3,direction:isRTL?'rtl':'ltr'},children:/*#__PURE__*/_jsx(Tabs,{value:activeTab,onChange:handleTabChange,variant:\"scrollable\",scrollButtons:\"auto\",sx:{'& .MuiTab-root':{fontFamily:isRTL?'Tajawal, sans-serif':'inherit',minHeight:64,textAlign:isRTL?'right':'left'},'& .MuiTabs-flexContainer':{direction:isRTL?'rtl':'ltr'}},children:tabsConfig.map((tab,index)=>/*#__PURE__*/_jsx(Tab,{icon:tab.icon,label:tab.label,iconPosition:\"start\",sx:{flexDirection:isRTL?'row-reverse':'row',textAlign:isRTL?'right':'left','& .MuiTab-iconWrapper':{mr:isRTL?0:1,ml:isRTL?1:0}}},index))})}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:0,children:renderTermsAndConditions()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:1,children:renderPrivacyPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:2,children:renderBookingPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:3,children:renderPaymentPolicy()}),/*#__PURE__*/_jsx(TabPanel,{value:activeTab,index:4,children:renderRefundPolicy()})]})})})})});};export default PlatformPolicy;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useLocation", "useNavigate", "Container", "Typography", "Paper", "Box", "Divider", "useTheme", "Fade", "List", "ListItem", "ListItemText", "ListItemIcon", "Tabs", "Tab", "Card", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "<PERSON><PERSON>", "Security", "SecurityIcon", "Schedule", "ScheduleIcon", "Person", "PersonIcon", "School", "SchoolIcon", "Info", "InfoIcon", "Payment", "PaymentIcon", "Refresh", "RefreshIcon", "Description", "DescriptionIcon", "Layout", "termsConditions", "jsx", "_jsx", "jsxs", "_jsxs", "PlatformPolicy", "t", "i18n", "theme", "location", "navigate", "isRTL", "setIsRTL", "language", "activeTab", "setActiveTab", "document", "dir", "documentElement", "body", "hash", "handleTabChange", "event", "newValue", "tabs", "replace", "tabsConfig", "label", "icon", "SectionCard", "_ref", "title", "children", "bg", "elevation", "sx", "mb", "borderRadius", "direction", "borderRight", "palette", "primary", "main", "borderLeft", "backgroundColor", "display", "alignItems", "flexDirection", "textAlign", "mr", "ml", "color", "variant", "fontWeight", "fontFamily", "renderSubSection", "section", "description", "lineHeight", "points", "pl", "pr", "map", "point", "idx", "py", "min<PERSON><PERSON><PERSON>", "order", "fontSize", "items", "item", "TabPanel", "_ref2", "value", "index", "hidden", "renderTermsAndConditions", "termsContent", "ar", "en", "spacing", "content", "split", "filter", "trim", "lines", "slice", "join", "secondary", "light", "whiteSpace", "renderPrivacyPolicy", "privacy", "returnObjects", "intro", "num", "subtitle", "item1", "item2", "item3", "item4", "item5", "Boolean", "renderBookingPolicy", "policy", "studentPolicy", "booking", "cancellation", "rescheduling", "lateArrival", "tutorPolicy", "availability", "generalNotes", "renderPaymentPolicy", "renderRefundPolicy", "minHeight", "background", "max<PERSON><PERSON><PERSON>", "in", "timeout", "p", "xs", "md", "component", "borderBottom", "borderColor", "onChange", "scrollButtons", "tab", "iconPosition"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PlatformPolicy.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useTranslation } from 'react-i18next';\r\nimport { useLocation, useNavigate } from 'react-router-dom';\r\nimport {\r\n  Container,\r\n  Typography,\r\n  Paper,\r\n  Box,\r\n  Divider,\r\n  useTheme,\r\n  Fade,\r\n  List,\r\n  ListItem,\r\n  ListItemText,\r\n  ListItemIcon,\r\n  Tabs,\r\n  Tab,\r\n  Card,\r\n  CardContent,\r\n  alpha,\r\n  Stack,\r\n} from '@mui/material';\r\nimport {\r\n  Security as SecurityIcon,\r\n  Schedule as ScheduleIcon,\r\n  Person as PersonIcon,\r\n  School as SchoolIcon,\r\n  Info as InfoIcon,\r\n  Payment as PaymentIcon,\r\n  Refresh as RefreshIcon,\r\n  Description as DescriptionIcon,\r\n} from '@mui/icons-material';\r\nimport Layout from '../components/Layout';\r\nimport termsConditions from '../i18n/translations/termsConditions';\r\n\r\nconst PlatformPolicy = () => {\r\n  const { t, i18n } = useTranslation();\r\n  const theme = useTheme();\r\n  const location = useLocation();\r\n  const navigate = useNavigate();\r\n  const [isRTL, setIsRTL] = useState(i18n.language === 'ar');\r\n  const [activeTab, setActiveTab] = useState(0);\r\n\r\n  useEffect(() => {\r\n    setIsRTL(i18n.language === 'ar');\r\n    document.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n    document.documentElement.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n    document.body.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';\r\n\r\n    // Handle URL-based navigation to specific sections\r\n    const hash = location.hash;\r\n    if (hash === '#terms') setActiveTab(0);\r\n    else if (hash === '#privacy') setActiveTab(1);\r\n    else if (hash === '#booking') setActiveTab(2);\r\n    else if (hash === '#payment') setActiveTab(3);\r\n    else if (hash === '#refund') setActiveTab(4);\r\n  }, [i18n.language, location.hash]);\r\n\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    const tabs = ['#terms', '#privacy', '#booking', '#payment', '#refund'];\r\n    navigate(`/platform-policy${tabs[newValue]}`, { replace: true });\r\n  };\r\n\r\n  const tabsConfig = [\r\n    { label: i18n.language === 'ar' ? 'الشروط والأحكام' : 'Terms & Conditions', icon: <DescriptionIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy', icon: <SecurityIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation', icon: <ScheduleIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الدفع' : 'Payment Policy', icon: <PaymentIcon /> },\r\n    { label: i18n.language === 'ar' ? 'سياسة الاسترداد' : 'Refund Policy', icon: <RefreshIcon /> },\r\n  ];\r\n\r\n  // Helper component for section cards\r\n  const SectionCard = ({ title, children, icon, bg }) => (\r\n    <Card\r\n      elevation={2}\r\n      sx={{\r\n        mb: 4,\r\n        borderRadius: 3,\r\n        direction: isRTL ? 'rtl' : 'ltr',\r\n        ...(isRTL\r\n          ? { borderRight: `6px solid ${theme.palette.primary.main}` }\r\n          : { borderLeft: `6px solid ${theme.palette.primary.main}` }),\r\n        backgroundColor: bg || alpha(theme.palette.primary.main, 0.02),\r\n      }}\r\n    >\r\n      <CardContent sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n        <Box sx={{\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          mb: 2,\r\n          flexDirection: isRTL ? 'row-reverse' : 'row',\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n          textAlign: isRTL ? 'right' : 'left'\r\n        }}>\r\n          {icon && (\r\n            <Box sx={{ mr: isRTL ? 0 : 2, ml: isRTL ? 2 : 0, color: theme.palette.primary.main }}>\r\n              {icon}\r\n            </Box>\r\n          )}\r\n          <Typography\r\n            variant=\"h5\"\r\n            fontWeight={600}\r\n            color={theme.palette.primary.main}\r\n            sx={{\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'Roboto, sans-serif',\r\n              direction: isRTL ? 'rtl' : 'ltr',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {title}\r\n          </Typography>\r\n        </Box>\r\n        <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n          {children}\r\n        </Box>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n\r\n  // Helper function to render subsections\r\n  const renderSubSection = (section) => {\r\n    if (!section || typeof section !== 'object') return null;\r\n\r\n    return (\r\n      <Box sx={{ mb: 3, direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h6\"\r\n          fontWeight={600}\r\n          mb={1}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {section.title}\r\n        </Typography>\r\n        {section.description && (\r\n          <Typography\r\n            variant=\"body1\"\r\n            sx={{\r\n              mb: 2,\r\n              lineHeight: 1.8,\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {section.description}\r\n          </Typography>\r\n        )}\r\n        {section.points && (\r\n          <List sx={{\r\n            pl: isRTL ? 0 : 2,\r\n            pr: isRTL ? 2 : 0,\r\n            direction: isRTL ? 'rtl' : 'ltr',\r\n            '& .MuiListItem-root': {\r\n              direction: isRTL ? 'rtl' : 'ltr',\r\n              textAlign: isRTL ? 'right' : 'left',\r\n            }\r\n          }}>\r\n            {section.points.map((point, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                py: 0.5,\r\n                textAlign: isRTL ? 'right' : 'left',\r\n                direction: isRTL ? 'rtl' : 'ltr',\r\n                flexDirection: isRTL ? 'row-reverse' : 'row'\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mr: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                  order: isRTL ? 2 : 1\r\n                }}>\r\n                  <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={point}\r\n                  sx={{\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    textAlign: isRTL ? 'right' : 'left',\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    order: isRTL ? 1 : 2,\r\n                    '& .MuiListItemText-primary': {\r\n                      textAlign: isRTL ? 'right' : 'left',\r\n                      direction: isRTL ? 'rtl' : 'ltr',\r\n                    }\r\n                  }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        )}\r\n        {section.items && (\r\n          <List sx={{\r\n            pl: isRTL ? 0 : 2,\r\n            pr: isRTL ? 2 : 0,\r\n            direction: isRTL ? 'rtl' : 'ltr',\r\n            '& .MuiListItem-root': {\r\n              direction: isRTL ? 'rtl' : 'ltr',\r\n              textAlign: isRTL ? 'right' : 'left',\r\n            }\r\n          }}>\r\n            {section.items.map((item, idx) => (\r\n              <ListItem key={idx} sx={{\r\n                py: 0.5,\r\n                textAlign: isRTL ? 'right' : 'left',\r\n                direction: isRTL ? 'rtl' : 'ltr',\r\n                flexDirection: isRTL ? 'row-reverse' : 'row'\r\n              }}>\r\n                <ListItemIcon sx={{\r\n                  minWidth: 'auto',\r\n                  mr: isRTL ? 0 : 1,\r\n                  ml: isRTL ? 1 : 0,\r\n                  order: isRTL ? 2 : 1\r\n                }}>\r\n                  <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={item}\r\n                  sx={{\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    textAlign: isRTL ? 'right' : 'left',\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    order: isRTL ? 1 : 2,\r\n                    '& .MuiListItemText-primary': {\r\n                      textAlign: isRTL ? 'right' : 'left',\r\n                      direction: isRTL ? 'rtl' : 'ltr',\r\n                    }\r\n                  }}\r\n                />\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n        )}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Tab panel component\r\n  const TabPanel = ({ children, value, index }) => (\r\n    <div hidden={value !== index}>\r\n      {value === index && (\r\n        <Box sx={{\r\n          py: 3,\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n          textAlign: isRTL ? 'right' : 'left'\r\n        }}>\r\n          {children}\r\n        </Box>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  // Render Terms and Conditions\r\n  const renderTermsAndConditions = () => {\r\n    const termsContent = i18n.language === 'ar' ? termsConditions.ar.termsConditions : termsConditions.en.termsConditions;\r\n\r\n    if (!termsContent || typeof termsContent !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={3}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {termsContent.title || (isRTL ? 'الشروط والأحكام' : 'Terms and Conditions')}\r\n        </Typography>\r\n        <Stack spacing={3}>\r\n          {termsContent.content && termsContent.content\r\n            .split(/\\n\\s*\\n/)\r\n            .filter((section) => section.trim() !== '')\r\n            .map((section, idx) => {\r\n              const lines = section.split('\\n');\r\n              const title = lines[0];\r\n              const body = lines.slice(1).join('\\n');\r\n              return (\r\n                <Card\r\n                  key={idx}\r\n                  elevation={2}\r\n                  sx={{\r\n                    borderRadius: 3,\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    ...(isRTL\r\n                      ? { borderRight: `4px solid ${theme.palette.primary.main}` }\r\n                      : { borderLeft: `4px solid ${theme.palette.primary.main}` }),\r\n                    backgroundColor: idx % 2 === 0\r\n                      ? alpha(theme.palette.primary.main, 0.02)\r\n                      : alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02),\r\n                  }}\r\n                >\r\n                  <CardContent>\r\n                    <Typography\r\n                      variant=\"h6\"\r\n                      fontWeight={600}\r\n                      mb={2}\r\n                      sx={{\r\n                        fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                        color: theme.palette.primary.main,\r\n                        textAlign: isRTL ? 'right' : 'left'\r\n                      }}\r\n                    >\r\n                      {title}\r\n                    </Typography>\r\n                    {body && (\r\n                      <Typography\r\n                        variant=\"body1\"\r\n                        sx={{\r\n                          lineHeight: 1.8,\r\n                          fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                          whiteSpace: 'pre-line',\r\n                          textAlign: isRTL ? 'right' : 'left'\r\n                        }}\r\n                      >\r\n                        {body}\r\n                      </Typography>\r\n                    )}\r\n                  </CardContent>\r\n                </Card>\r\n              );\r\n            })}\r\n        </Stack>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Privacy Policy\r\n  const renderPrivacyPolicy = () => {\r\n    const privacy = t('privacy', { returnObjects: true });\r\n\r\n    if (!privacy || typeof privacy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {privacy.title || (isRTL ? 'سياسة الخصوصية' : 'Privacy Policy')}\r\n        </Typography>\r\n        {privacy.intro && (\r\n          <Typography\r\n            variant=\"body1\"\r\n            sx={{\r\n              mb: 4,\r\n              lineHeight: 1.8,\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {privacy.intro}\r\n          </Typography>\r\n        )}\r\n        <Divider sx={{ mb: 4 }} />\r\n\r\n        {/* Render privacy sections */}\r\n        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => {\r\n          const section = privacy[`section${num}`];\r\n          if (!section || typeof section !== 'object') return null;\r\n\r\n          return (\r\n            <SectionCard key={num} title={section.title || `القسم ${num}`} icon={<SecurityIcon />}>\r\n              {section.subtitle && (\r\n                <Typography\r\n                  variant=\"body1\"\r\n                  sx={{\r\n                    mb: 2,\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    textAlign: isRTL ? 'right' : 'left'\r\n                  }}\r\n                >\r\n                  {section.subtitle}\r\n                </Typography>\r\n              )}\r\n              {section.description && (\r\n                <Typography\r\n                  variant=\"body1\"\r\n                  sx={{\r\n                    mb: 2,\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    textAlign: isRTL ? 'right' : 'left'\r\n                  }}\r\n                >\r\n                  {section.description}\r\n                </Typography>\r\n              )}\r\n              {section.item1 && (\r\n                <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                  {[section.item1, section.item2, section.item3, section.item4, section.item5]\r\n                    .filter(Boolean)\r\n                    .map((item, idx) => (\r\n                      <ListItem key={idx} sx={{ textAlign: isRTL ? 'right' : 'left' }}>\r\n                        <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                          <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                        </ListItemIcon>\r\n                        <ListItemText\r\n                          primary={item}\r\n                          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                        />\r\n                      </ListItem>\r\n                    ))}\r\n                </List>\r\n              )}\r\n            </SectionCard>\r\n          );\r\n        })}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Booking & Cancellation Policy\r\n  const renderBookingPolicy = () => {\r\n    const policy = t('policies.bookingCancellation', { returnObjects: true });\r\n\r\n    if (!policy || typeof policy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.title || (isRTL ? 'سياسة الحجز والإلغاء' : 'Booking & Cancellation Policy')}\r\n        </Typography>\r\n        {policy.subtitle && (\r\n          <Typography\r\n            variant=\"body1\"\r\n            sx={{\r\n              mb: 4,\r\n              lineHeight: 1.8,\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {policy.subtitle}\r\n          </Typography>\r\n        )}\r\n        <Divider sx={{ mb: 4 }} />\r\n\r\n        {/* Student Policy */}\r\n        {policy.studentPolicy && (\r\n          <SectionCard title={policy.studentPolicy.title || (isRTL ? 'سياسة الطلاب' : 'Student Policy')} icon={<PersonIcon />}>\r\n            <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n              {policy.studentPolicy.booking && renderSubSection(policy.studentPolicy.booking)}\r\n              {policy.studentPolicy.cancellation && renderSubSection(policy.studentPolicy.cancellation)}\r\n              {policy.studentPolicy.rescheduling && renderSubSection(policy.studentPolicy.rescheduling)}\r\n              {policy.studentPolicy.lateArrival && renderSubSection(policy.studentPolicy.lateArrival)}\r\n            </Box>\r\n          </SectionCard>\r\n        )}\r\n\r\n        {/* Tutor Policy */}\r\n        {policy.tutorPolicy && (\r\n          <SectionCard\r\n            title={policy.tutorPolicy.title || (isRTL ? 'سياسة المعلمين' : 'Tutor Policy')}\r\n            icon={<SchoolIcon />}\r\n            bg={alpha(theme.palette.secondary.main || theme.palette.primary.light, 0.02)}\r\n          >\r\n            <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n              {policy.tutorPolicy.availability && renderSubSection(policy.tutorPolicy.availability)}\r\n              {policy.tutorPolicy.cancellation && renderSubSection(policy.tutorPolicy.cancellation)}\r\n              {policy.tutorPolicy.rescheduling && renderSubSection(policy.tutorPolicy.rescheduling)}\r\n              {policy.tutorPolicy.lateArrival && renderSubSection(policy.tutorPolicy.lateArrival)}\r\n            </Box>\r\n          </SectionCard>\r\n        )}\r\n\r\n        {/* General Notes */}\r\n        {policy.generalNotes && policy.generalNotes.points && (\r\n          <SectionCard title={policy.generalNotes.title || (isRTL ? 'ملاحظات عامة' : 'General Notes')} icon={<InfoIcon />}>\r\n            <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n              {policy.generalNotes.points.map((item, idx) => (\r\n                <ListItem key={idx} sx={{ py: 0.5, textAlign: isRTL ? 'right' : 'left' }}>\r\n                  <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                    <InfoIcon color=\"info\" />\r\n                  </ListItemIcon>\r\n                  <ListItemText\r\n                    primary={item}\r\n                    sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                  />\r\n                </ListItem>\r\n              ))}\r\n            </List>\r\n          </SectionCard>\r\n        )}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Payment Policy\r\n  const renderPaymentPolicy = () => {\r\n    const policy = t('policies.bookingPayment', { returnObjects: true });\r\n\r\n    if (!policy || typeof policy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.title || (isRTL ? 'سياسة الدفع' : 'Payment Policy')}\r\n        </Typography>\r\n        {policy.subtitle && (\r\n          <Typography\r\n            variant=\"body1\"\r\n            sx={{\r\n              mb: 4,\r\n              lineHeight: 1.8,\r\n              fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n              textAlign: isRTL ? 'right' : 'left'\r\n            }}\r\n          >\r\n            {policy.subtitle}\r\n          </Typography>\r\n        )}\r\n        <Divider sx={{ mb: 4 }} />\r\n\r\n        {/* Render payment sections */}\r\n        {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => {\r\n          const section = policy[`section${num}`];\r\n          if (!section || typeof section !== 'object') return null;\r\n\r\n          return (\r\n            <SectionCard key={num} title={section.title || `القسم ${num}`} icon={<PaymentIcon />}>\r\n              <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                {section.description && (\r\n                  <Typography\r\n                    variant=\"body1\"\r\n                    sx={{\r\n                      mb: 2,\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                      textAlign: isRTL ? 'right' : 'left'\r\n                    }}\r\n                  >\r\n                    {section.description}\r\n                  </Typography>\r\n                )}\r\n                {section.points && (\r\n                  <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                    {section.points.map((point, idx) => (\r\n                      <ListItem key={idx} sx={{ py: 0.5, textAlign: isRTL ? 'right' : 'left' }}>\r\n                        <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                          <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                        </ListItemIcon>\r\n                        <ListItemText\r\n                          primary={point}\r\n                          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                        />\r\n                      </ListItem>\r\n                    ))}\r\n                  </List>\r\n                )}\r\n              </Box>\r\n            </SectionCard>\r\n          );\r\n        })}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render Refund Policy\r\n  const renderRefundPolicy = () => {\r\n    const policy = t('policies.refund', { returnObjects: true });\r\n\r\n    if (!policy || typeof policy !== 'object') {\r\n      return (\r\n        <Box sx={{ textAlign: 'center', py: 4 }}>\r\n          <Typography variant=\"h6\" color=\"text.secondary\">\r\n            {isRTL ? 'لا توجد بيانات متاحة' : 'No data available'}\r\n          </Typography>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{ direction: isRTL ? 'rtl' : 'ltr', textAlign: isRTL ? 'right' : 'left' }}>\r\n        <Typography\r\n          variant=\"h4\"\r\n          fontWeight={700}\r\n          mb={2}\r\n          sx={{\r\n            fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n            color: theme.palette.primary.main,\r\n            textAlign: isRTL ? 'right' : 'left'\r\n          }}\r\n        >\r\n          {policy.title || (isRTL ? 'سياسة الاسترداد' : 'Refund Policy')}\r\n        </Typography>\r\n        <Divider sx={{ mb: 4 }} />\r\n\r\n        {/* Render refund sections */}\r\n        {[1, 2, 3, 4, 5, 6, 7, 8].map((num) => {\r\n          const section = policy[`section${num}`];\r\n          if (!section || typeof section !== 'object') return null;\r\n\r\n          return (\r\n            <SectionCard key={num} title={section.title || `القسم ${num}`} icon={<RefreshIcon />}>\r\n              <Box sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                {section.description && (\r\n                  <Typography\r\n                    variant=\"body1\"\r\n                    sx={{\r\n                      mb: 2,\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                      textAlign: isRTL ? 'right' : 'left'\r\n                    }}\r\n                  >\r\n                    {section.description}\r\n                  </Typography>\r\n                )}\r\n                {section.items && (\r\n                  <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                    {section.items.map((item, idx) => (\r\n                      <ListItem key={idx} sx={{ py: 0.5, textAlign: isRTL ? 'right' : 'left' }}>\r\n                        <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                          <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                        </ListItemIcon>\r\n                        <ListItemText\r\n                          primary={item}\r\n                          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                        />\r\n                      </ListItem>\r\n                    ))}\r\n                  </List>\r\n                )}\r\n                {section.points && (\r\n                  <List sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                    {section.points.map((point, idx) => (\r\n                      <ListItem key={idx} sx={{ py: 0.5, textAlign: isRTL ? 'right' : 'left' }}>\r\n                        <ListItemIcon sx={{ minWidth: 'auto', mr: isRTL ? 0 : 1, ml: isRTL ? 1 : 0 }}>\r\n                          <InfoIcon color=\"primary\" fontSize=\"small\" />\r\n                        </ListItemIcon>\r\n                        <ListItemText\r\n                          primary={point}\r\n                          sx={{ fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit' }}\r\n                        />\r\n                      </ListItem>\r\n                    ))}\r\n                  </List>\r\n                )}\r\n              </Box>\r\n            </SectionCard>\r\n          );\r\n        })}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Layout>\r\n      <Box\r\n        sx={{\r\n          py: 4,\r\n          minHeight: '100vh',\r\n          background: `linear-gradient(${alpha(theme.palette.primary.main, 0.05)}, ${alpha(theme.palette.primary.main, 0.1)})`,\r\n          direction: isRTL ? 'rtl' : 'ltr',\r\n        }}\r\n      >\r\n        <Container maxWidth=\"lg\" sx={{ direction: isRTL ? 'rtl' : 'ltr' }}>\r\n          <Fade in timeout={1000}>\r\n            <Paper\r\n              elevation={3}\r\n              sx={{\r\n                p: { xs: 2, md: 4 },\r\n                mb: 4,\r\n                borderRadius: 3,\r\n                direction: isRTL ? 'rtl' : 'ltr',\r\n                textAlign: isRTL ? 'right' : 'left',\r\n                '& *': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                },\r\n                '& .MuiTypography-root': {\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                },\r\n                '& .MuiList-root': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                },\r\n                '& .MuiListItem-root': {\r\n                  direction: isRTL ? 'rtl' : 'ltr',\r\n                  textAlign: isRTL ? 'right' : 'left',\r\n                },\r\n              }}\r\n            >\r\n              {/* Header */}\r\n              <Box sx={{\r\n                mb: 4,\r\n                textAlign: 'center',\r\n                direction: isRTL ? 'rtl' : 'ltr'\r\n              }}>\r\n                <Typography\r\n                  variant=\"h3\"\r\n                  component=\"h1\"\r\n                  sx={{\r\n                    mb: 2,\r\n                    fontWeight: 800,\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    color: theme.palette.primary.main,\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    textAlign: 'center',\r\n                  }}\r\n                >\r\n                  {isRTL ? 'سياسات المنصة' : 'Platform Policies'}\r\n                </Typography>\r\n                <Typography\r\n                  variant=\"subtitle1\"\r\n                  color=\"text.secondary\"\r\n                  sx={{\r\n                    fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                    direction: isRTL ? 'rtl' : 'ltr',\r\n                    textAlign: 'center',\r\n                  }}\r\n                >\r\n                  {isRTL\r\n                    ? 'جميع السياسات والشروط الخاصة بمنصة علّمني أون لاين في مكان واحد'\r\n                    : 'All Allemnionline platform policies and terms in one place'\r\n                  }\r\n                </Typography>\r\n              </Box>\r\n\r\n              {/* Tabs */}\r\n              <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3, direction: isRTL ? 'rtl' : 'ltr' }}>\r\n                <Tabs\r\n                  value={activeTab}\r\n                  onChange={handleTabChange}\r\n                  variant=\"scrollable\"\r\n                  scrollButtons=\"auto\"\r\n                  sx={{\r\n                    '& .MuiTab-root': {\r\n                      fontFamily: isRTL ? 'Tajawal, sans-serif' : 'inherit',\r\n                      minHeight: 64,\r\n                      textAlign: isRTL ? 'right' : 'left',\r\n                    },\r\n                    '& .MuiTabs-flexContainer': {\r\n                      direction: isRTL ? 'rtl' : 'ltr',\r\n                    }\r\n                  }}\r\n                >\r\n                  {tabsConfig.map((tab, index) => (\r\n                    <Tab\r\n                      key={index}\r\n                      icon={tab.icon}\r\n                      label={tab.label}\r\n                      iconPosition=\"start\"\r\n                      sx={{\r\n                        flexDirection: isRTL ? 'row-reverse' : 'row',\r\n                        textAlign: isRTL ? 'right' : 'left',\r\n                        '& .MuiTab-iconWrapper': {\r\n                          mr: isRTL ? 0 : 1,\r\n                          ml: isRTL ? 1 : 0,\r\n                        }\r\n                      }}\r\n                    />\r\n                  ))}\r\n                </Tabs>\r\n              </Box>\r\n\r\n              {/* Tab Panels */}\r\n              <TabPanel value={activeTab} index={0}>\r\n                {renderTermsAndConditions()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={1}>\r\n                {renderPrivacyPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={2}>\r\n                {renderBookingPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={3}>\r\n                {renderPaymentPolicy()}\r\n              </TabPanel>\r\n\r\n              <TabPanel value={activeTab} index={4}>\r\n                {renderRefundPolicy()}\r\n              </TabPanel>\r\n\r\n            </Paper>\r\n          </Fade>\r\n        </Container>\r\n      </Box>\r\n    </Layout>\r\n  );\r\n};\r\n\r\nexport default PlatformPolicy;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,CAAEC,WAAW,KAAQ,kBAAkB,CAC3D,OACEC,SAAS,CACTC,UAAU,CACVC,KAAK,CACLC,GAAG,CACHC,OAAO,CACPC,QAAQ,CACRC,IAAI,CACJC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,IAAI,CACJC,GAAG,CACHC,IAAI,CACJC,WAAW,CACXC,KAAK,CACLC,KAAK,KACA,eAAe,CACtB,OACEC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,IAAI,GAAI,CAAAC,QAAQ,CAChBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,OAAO,GAAI,CAAAC,WAAW,CACtBC,WAAW,GAAI,CAAAC,eAAe,KACzB,qBAAqB,CAC5B,MAAO,CAAAC,MAAM,KAAM,sBAAsB,CACzC,MAAO,CAAAC,eAAe,KAAM,sCAAsC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEnE,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAG5C,cAAc,CAAC,CAAC,CACpC,KAAM,CAAA6C,KAAK,CAAGrC,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAsC,QAAQ,CAAG7C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAA8C,QAAQ,CAAG7C,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAC8C,KAAK,CAAEC,QAAQ,CAAC,CAAGnD,QAAQ,CAAC8C,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAC1D,KAAM,CAACC,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,CAAC,CAAC,CAE7CC,SAAS,CAAC,IAAM,CACdkD,QAAQ,CAACL,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAC,CAChCG,QAAQ,CAACC,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CACrDG,QAAQ,CAACE,eAAe,CAACD,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CACrEG,QAAQ,CAACG,IAAI,CAACF,GAAG,CAAGV,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,KAAK,CAAG,KAAK,CAE1D;AACA,KAAM,CAAAO,IAAI,CAAGX,QAAQ,CAACW,IAAI,CAC1B,GAAIA,IAAI,GAAK,QAAQ,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IAClC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,UAAU,CAAEL,YAAY,CAAC,CAAC,CAAC,CAAC,IACzC,IAAIK,IAAI,GAAK,SAAS,CAAEL,YAAY,CAAC,CAAC,CAAC,CAC9C,CAAC,CAAE,CAACR,IAAI,CAACM,QAAQ,CAAEJ,QAAQ,CAACW,IAAI,CAAC,CAAC,CAElC,KAAM,CAAAC,eAAe,CAAGA,CAACC,KAAK,CAAEC,QAAQ,GAAK,CAC3CR,YAAY,CAACQ,QAAQ,CAAC,CACtB,KAAM,CAAAC,IAAI,CAAG,CAAC,QAAQ,CAAE,UAAU,CAAE,UAAU,CAAE,UAAU,CAAE,SAAS,CAAC,CACtEd,QAAQ,CAAC,mBAAmBc,IAAI,CAACD,QAAQ,CAAC,EAAE,CAAE,CAAEE,OAAO,CAAE,IAAK,CAAC,CAAC,CAClE,CAAC,CAED,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEC,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,iBAAiB,CAAG,oBAAoB,CAAEe,IAAI,cAAE1B,IAAA,CAACJ,eAAe,GAAE,CAAE,CAAC,CACvG,CAAE6B,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,gBAAgB,CAAG,gBAAgB,CAAEe,IAAI,cAAE1B,IAAA,CAAClB,YAAY,GAAE,CAAE,CAAC,CAC/F,CAAE2C,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,sBAAsB,CAAG,wBAAwB,CAAEe,IAAI,cAAE1B,IAAA,CAAChB,YAAY,GAAE,CAAE,CAAC,CAC7G,CAAEyC,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,aAAa,CAAG,gBAAgB,CAAEe,IAAI,cAAE1B,IAAA,CAACR,WAAW,GAAE,CAAE,CAAC,CAC3F,CAAEiC,KAAK,CAAEpB,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAG,iBAAiB,CAAG,eAAe,CAAEe,IAAI,cAAE1B,IAAA,CAACN,WAAW,GAAE,CAAE,CAAC,CAC/F,CAED;AACA,KAAM,CAAAiC,WAAW,CAAGC,IAAA,MAAC,CAAEC,KAAK,CAAEC,QAAQ,CAAEJ,IAAI,CAAEK,EAAG,CAAC,CAAAH,IAAA,oBAChD5B,IAAA,CAACvB,IAAI,EACHuD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChC,IAAIA,KAAK,CACL,CAAE4B,WAAW,CAAE,aAAa/B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAanC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEX,EAAE,EAAIpD,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAC/D,CAAE,CAAAV,QAAA,cAEF5B,KAAA,CAACxB,WAAW,EAACuD,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,eACpD5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CACPU,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBV,EAAE,CAAE,CAAC,CACLW,aAAa,CAAEpC,KAAK,CAAG,aAAa,CAAG,KAAK,CAC5C2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,EACCJ,IAAI,eACH1B,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEc,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAK,CAAE,CAAAV,QAAA,CAClFJ,IAAI,CACF,CACN,cACD1B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAK,CAClCP,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,oBAAoB,CAChE2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDD,KAAK,CACI,CAAC,EACV,CAAC,cACN7B,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,CAChFA,QAAQ,CACN,CAAC,EACK,CAAC,CACV,CAAC,EACR,CAED;AACA,KAAM,CAAAuB,gBAAgB,CAAIC,OAAO,EAAK,CACpC,GAAI,CAACA,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,MAAO,KAAI,CAExD,mBACEpD,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEE,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACxF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwB,OAAO,CAACzB,KAAK,CACJ,CAAC,CACZyB,OAAO,CAACC,WAAW,eAClBvD,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLsB,UAAU,CAAE,GAAG,CACfJ,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwB,OAAO,CAACC,WAAW,CACV,CACb,CACAD,OAAO,CAACG,MAAM,eACbzD,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CACRyB,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBkD,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChC,qBAAqB,CAAE,CACrB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CACF,CAAE,CAAAqB,QAAA,CACCwB,OAAO,CAACG,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,CAAEC,GAAG,gBAC7B5D,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CACtB8B,EAAE,CAAE,GAAG,CACPjB,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCoC,aAAa,CAAEpC,KAAK,CAAG,aAAa,CAAG,KACzC,CAAE,CAAAqB,QAAA,eACA9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAChB+B,QAAQ,CAAE,MAAM,CAChBjB,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,KAAK,CAAExD,KAAK,CAAG,CAAC,CAAG,CACrB,CAAE,CAAAqB,QAAA,cACA9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAACiB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACflE,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEsB,KAAM,CACf5B,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCwD,KAAK,CAAExD,KAAK,CAAG,CAAC,CAAG,CAAC,CACpB,4BAA4B,CAAE,CAC5BqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CACF,CAAE,CACH,CAAC,GA1BWqD,GA2BL,CACX,CAAC,CACE,CACP,CACAR,OAAO,CAACa,KAAK,eACZnE,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CACRyB,EAAE,CAAEjD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBkD,EAAE,CAAElD,KAAK,CAAG,CAAC,CAAG,CAAC,CACjB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChC,qBAAqB,CAAE,CACrB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CACF,CAAE,CAAAqB,QAAA,CACCwB,OAAO,CAACa,KAAK,CAACP,GAAG,CAAC,CAACQ,IAAI,CAAEN,GAAG,gBAC3B5D,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CACtB8B,EAAE,CAAE,GAAG,CACPjB,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCoC,aAAa,CAAEpC,KAAK,CAAG,aAAa,CAAG,KACzC,CAAE,CAAAqB,QAAA,eACA9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAChB+B,QAAQ,CAAE,MAAM,CAChBjB,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBwD,KAAK,CAAExD,KAAK,CAAG,CAAC,CAAG,CACrB,CAAE,CAAAqB,QAAA,cACA9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAACiB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACflE,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAE6B,IAAK,CACdnC,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCwD,KAAK,CAAExD,KAAK,CAAG,CAAC,CAAG,CAAC,CACpB,4BAA4B,CAAE,CAC5BqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CACF,CAAE,CACH,CAAC,GA1BWqD,GA2BL,CACX,CAAC,CACE,CACP,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAO,QAAQ,CAAGC,KAAA,MAAC,CAAExC,QAAQ,CAAEyC,KAAK,CAAEC,KAAM,CAAC,CAAAF,KAAA,oBAC1CtE,IAAA,QAAKyE,MAAM,CAAEF,KAAK,GAAKC,KAAM,CAAA1C,QAAA,CAC1ByC,KAAK,GAAKC,KAAK,eACdxE,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CACP8B,EAAE,CAAE,CAAC,CACL3B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CACCA,QAAQ,CACN,CACN,CACE,CAAC,EACP,CAED;AACA,KAAM,CAAA4C,wBAAwB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAAC,YAAY,CAAGtE,IAAI,CAACM,QAAQ,GAAK,IAAI,CAAGb,eAAe,CAAC8E,EAAE,CAAC9E,eAAe,CAAGA,eAAe,CAAC+E,EAAE,CAAC/E,eAAe,CAErH,GAAI,CAAC6E,YAAY,EAAI,MAAO,CAAAA,YAAY,GAAK,QAAQ,CAAE,CACrD,mBACE3E,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED6C,YAAY,CAAC9C,KAAK,GAAKpB,KAAK,CAAG,iBAAiB,CAAG,sBAAsB,CAAC,CACjE,CAAC,cACbT,IAAA,CAACpB,KAAK,EAACkG,OAAO,CAAE,CAAE,CAAAhD,QAAA,CACf6C,YAAY,CAACI,OAAO,EAAIJ,YAAY,CAACI,OAAO,CAC1CC,KAAK,CAAC,SAAS,CAAC,CAChBC,MAAM,CAAE3B,OAAO,EAAKA,OAAO,CAAC4B,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAC1CtB,GAAG,CAAC,CAACN,OAAO,CAAEQ,GAAG,GAAK,CACrB,KAAM,CAAAqB,KAAK,CAAG7B,OAAO,CAAC0B,KAAK,CAAC,IAAI,CAAC,CACjC,KAAM,CAAAnD,KAAK,CAAGsD,KAAK,CAAC,CAAC,CAAC,CACtB,KAAM,CAAAlE,IAAI,CAAGkE,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACtC,mBACErF,IAAA,CAACvB,IAAI,EAEHuD,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFE,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChC,IAAIA,KAAK,CACL,CAAE4B,WAAW,CAAE,aAAa/B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAC1D,CAAEC,UAAU,CAAE,aAAanC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,EAAG,CAAC,CAAC,CAC9DE,eAAe,CAAEoB,GAAG,CAAG,CAAC,GAAK,CAAC,CAC1BnF,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,CACvC7D,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACgD,SAAS,CAAC9C,IAAI,EAAIlC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACgD,KAAK,CAAE,IAAI,CAC7E,CAAE,CAAAzD,QAAA,cAEF5B,KAAA,CAACxB,WAAW,EAAAoD,QAAA,eACV9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDD,KAAK,CACI,CAAC,CACZZ,IAAI,eACHjB,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFuB,UAAU,CAAE,GAAG,CACfJ,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD+E,UAAU,CAAE,UAAU,CACtB1C,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDb,IAAI,CACK,CACb,EACU,CAAC,EAvCT6C,GAwCD,CAAC,CAEX,CAAC,CAAC,CACC,CAAC,EACL,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAA2B,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,OAAO,CAAGtF,CAAC,CAAC,SAAS,CAAE,CAAEuF,aAAa,CAAE,IAAK,CAAC,CAAC,CAErD,GAAI,CAACD,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,CAC3C,mBACE1F,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED4D,OAAO,CAAC7D,KAAK,GAAKpB,KAAK,CAAG,gBAAgB,CAAG,gBAAgB,CAAC,CACrD,CAAC,CACZiF,OAAO,CAACE,KAAK,eACZ5F,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLsB,UAAU,CAAE,GAAG,CACfJ,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAED4D,OAAO,CAACE,KAAK,CACJ,CACb,cACD5F,IAAA,CAAChC,OAAO,EAACiE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC0B,GAAG,CAAEiC,GAAG,EAAK,CACxC,KAAM,CAAAvC,OAAO,CAAGoC,OAAO,CAAC,UAAUG,GAAG,EAAE,CAAC,CACxC,GAAI,CAACvC,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,MAAO,KAAI,CAExD,mBACEpD,KAAA,CAACyB,WAAW,EAAWE,KAAK,CAAEyB,OAAO,CAACzB,KAAK,EAAI,SAASgE,GAAG,EAAG,CAACnE,IAAI,cAAE1B,IAAA,CAAClB,YAAY,GAAE,CAAE,CAAAgD,QAAA,EACnFwB,OAAO,CAACwC,QAAQ,eACf9F,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwB,OAAO,CAACwC,QAAQ,CACP,CACb,CACAxC,OAAO,CAACC,WAAW,eAClBvD,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwB,OAAO,CAACC,WAAW,CACV,CACb,CACAD,OAAO,CAACyC,KAAK,eACZ/F,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5C,CAACwB,OAAO,CAACyC,KAAK,CAAEzC,OAAO,CAAC0C,KAAK,CAAE1C,OAAO,CAAC2C,KAAK,CAAE3C,OAAO,CAAC4C,KAAK,CAAE5C,OAAO,CAAC6C,KAAK,CAAC,CACzElB,MAAM,CAACmB,OAAO,CAAC,CACfxC,GAAG,CAAC,CAACQ,IAAI,CAAEN,GAAG,gBACb5D,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CAAEa,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eAC9D9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAAE+B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAqB,QAAA,cAC3E9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAACiB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACflE,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAE6B,IAAK,CACdnC,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWqD,GAQL,CACX,CAAC,CACA,CACP,GAzCe+B,GA0CL,CAAC,CAElB,CAAC,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAQ,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,MAAM,CAAGlG,CAAC,CAAC,8BAA8B,CAAE,CAAEuF,aAAa,CAAE,IAAK,CAAC,CAAC,CAEzE,GAAI,CAACW,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACEtG,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwE,MAAM,CAACzE,KAAK,GAAKpB,KAAK,CAAG,sBAAsB,CAAG,+BAA+B,CAAC,CACzE,CAAC,CACZ6F,MAAM,CAACR,QAAQ,eACd9F,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLsB,UAAU,CAAE,GAAG,CACfJ,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwE,MAAM,CAACR,QAAQ,CACN,CACb,cACD9F,IAAA,CAAChC,OAAO,EAACiE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzBoE,MAAM,CAACC,aAAa,eACnBvG,IAAA,CAAC2B,WAAW,EAACE,KAAK,CAAEyE,MAAM,CAACC,aAAa,CAAC1E,KAAK,GAAKpB,KAAK,CAAG,cAAc,CAAG,gBAAgB,CAAE,CAACiB,IAAI,cAAE1B,IAAA,CAACd,UAAU,GAAE,CAAE,CAAA4C,QAAA,cAClH5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3CwE,MAAM,CAACC,aAAa,CAACC,OAAO,EAAInD,gBAAgB,CAACiD,MAAM,CAACC,aAAa,CAACC,OAAO,CAAC,CAC9EF,MAAM,CAACC,aAAa,CAACE,YAAY,EAAIpD,gBAAgB,CAACiD,MAAM,CAACC,aAAa,CAACE,YAAY,CAAC,CACxFH,MAAM,CAACC,aAAa,CAACG,YAAY,EAAIrD,gBAAgB,CAACiD,MAAM,CAACC,aAAa,CAACG,YAAY,CAAC,CACxFJ,MAAM,CAACC,aAAa,CAACI,WAAW,EAAItD,gBAAgB,CAACiD,MAAM,CAACC,aAAa,CAACI,WAAW,CAAC,EACpF,CAAC,CACK,CACd,CAGAL,MAAM,CAACM,WAAW,eACjB5G,IAAA,CAAC2B,WAAW,EACVE,KAAK,CAAEyE,MAAM,CAACM,WAAW,CAAC/E,KAAK,GAAKpB,KAAK,CAAG,gBAAgB,CAAG,cAAc,CAAE,CAC/EiB,IAAI,cAAE1B,IAAA,CAACZ,UAAU,GAAE,CAAE,CACrB2C,EAAE,CAAEpD,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACgD,SAAS,CAAC9C,IAAI,EAAIlC,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACgD,KAAK,CAAE,IAAI,CAAE,CAAAzD,QAAA,cAE7E5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3CwE,MAAM,CAACM,WAAW,CAACC,YAAY,EAAIxD,gBAAgB,CAACiD,MAAM,CAACM,WAAW,CAACC,YAAY,CAAC,CACpFP,MAAM,CAACM,WAAW,CAACH,YAAY,EAAIpD,gBAAgB,CAACiD,MAAM,CAACM,WAAW,CAACH,YAAY,CAAC,CACpFH,MAAM,CAACM,WAAW,CAACF,YAAY,EAAIrD,gBAAgB,CAACiD,MAAM,CAACM,WAAW,CAACF,YAAY,CAAC,CACpFJ,MAAM,CAACM,WAAW,CAACD,WAAW,EAAItD,gBAAgB,CAACiD,MAAM,CAACM,WAAW,CAACD,WAAW,CAAC,EAChF,CAAC,CACK,CACd,CAGAL,MAAM,CAACQ,YAAY,EAAIR,MAAM,CAACQ,YAAY,CAACrD,MAAM,eAChDzD,IAAA,CAAC2B,WAAW,EAACE,KAAK,CAAEyE,MAAM,CAACQ,YAAY,CAACjF,KAAK,GAAKpB,KAAK,CAAG,cAAc,CAAG,eAAe,CAAE,CAACiB,IAAI,cAAE1B,IAAA,CAACV,QAAQ,GAAE,CAAE,CAAAwC,QAAA,cAC9G9B,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5CwE,MAAM,CAACQ,YAAY,CAACrD,MAAM,CAACG,GAAG,CAAC,CAACQ,IAAI,CAAEN,GAAG,gBACxC5D,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CAAE8B,EAAE,CAAE,GAAG,CAAEjB,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACvE9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAAE+B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAqB,QAAA,cAC3E9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,MAAM,CAAE,CAAC,CACb,CAAC,cACfjD,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAE6B,IAAK,CACdnC,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWqD,GAQL,CACX,CAAC,CACE,CAAC,CACI,CACd,EACE,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAiD,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAT,MAAM,CAAGlG,CAAC,CAAC,yBAAyB,CAAE,CAAEuF,aAAa,CAAE,IAAK,CAAC,CAAC,CAEpE,GAAI,CAACW,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACEtG,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwE,MAAM,CAACzE,KAAK,GAAKpB,KAAK,CAAG,aAAa,CAAG,gBAAgB,CAAC,CACjD,CAAC,CACZ6F,MAAM,CAACR,QAAQ,eACd9F,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLsB,UAAU,CAAE,GAAG,CACfJ,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwE,MAAM,CAACR,QAAQ,CACN,CACb,cACD9F,IAAA,CAAChC,OAAO,EAACiE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC0B,GAAG,CAAEiC,GAAG,EAAK,CACrC,KAAM,CAAAvC,OAAO,CAAGgD,MAAM,CAAC,UAAUT,GAAG,EAAE,CAAC,CACvC,GAAI,CAACvC,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,MAAO,KAAI,CAExD,mBACEtD,IAAA,CAAC2B,WAAW,EAAWE,KAAK,CAAEyB,OAAO,CAACzB,KAAK,EAAI,SAASgE,GAAG,EAAG,CAACnE,IAAI,cAAE1B,IAAA,CAACR,WAAW,GAAE,CAAE,CAAAsC,QAAA,cACnF5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3CwB,OAAO,CAACC,WAAW,eAClBvD,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwB,OAAO,CAACC,WAAW,CACV,CACb,CACAD,OAAO,CAACG,MAAM,eACbzD,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5CwB,OAAO,CAACG,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,CAAEC,GAAG,gBAC7B5D,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CAAE8B,EAAE,CAAE,GAAG,CAAEjB,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACvE9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAAE+B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAqB,QAAA,cAC3E9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAACiB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACflE,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEsB,KAAM,CACf5B,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWqD,GAQL,CACX,CAAC,CACE,CACP,EACE,CAAC,EA7BU+B,GA8BL,CAAC,CAElB,CAAC,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAmB,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAV,MAAM,CAAGlG,CAAC,CAAC,iBAAiB,CAAE,CAAEuF,aAAa,CAAE,IAAK,CAAC,CAAC,CAE5D,GAAI,CAACW,MAAM,EAAI,MAAO,CAAAA,MAAM,GAAK,QAAQ,CAAE,CACzC,mBACEtG,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEa,SAAS,CAAE,QAAQ,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAjC,QAAA,cACtC9B,IAAA,CAACnC,UAAU,EAACqF,OAAO,CAAC,IAAI,CAACD,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAC5CrB,KAAK,CAAG,sBAAsB,CAAG,mBAAmB,CAC3C,CAAC,CACV,CAAC,CAEV,CAEA,mBACEP,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAAEqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACjF9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBjB,EAAE,CAAE,CAAE,CACND,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCM,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwE,MAAM,CAACzE,KAAK,GAAKpB,KAAK,CAAG,iBAAiB,CAAG,eAAe,CAAC,CACpD,CAAC,cACbT,IAAA,CAAChC,OAAO,EAACiE,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CAGzB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,CAAC,CAAC,CAAC0B,GAAG,CAAEiC,GAAG,EAAK,CACrC,KAAM,CAAAvC,OAAO,CAAGgD,MAAM,CAAC,UAAUT,GAAG,EAAE,CAAC,CACvC,GAAI,CAACvC,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,MAAO,KAAI,CAExD,mBACEtD,IAAA,CAAC2B,WAAW,EAAWE,KAAK,CAAEyB,OAAO,CAACzB,KAAK,EAAI,SAASgE,GAAG,EAAG,CAACnE,IAAI,cAAE1B,IAAA,CAACN,WAAW,GAAE,CAAE,CAAAoC,QAAA,cACnF5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,EAC3CwB,OAAO,CAACC,WAAW,eAClBvD,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,OAAO,CACfjB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLkB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAE,CAAAqB,QAAA,CAEDwB,OAAO,CAACC,WAAW,CACV,CACb,CACAD,OAAO,CAACa,KAAK,eACZnE,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5CwB,OAAO,CAACa,KAAK,CAACP,GAAG,CAAC,CAACQ,IAAI,CAAEN,GAAG,gBAC3B5D,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CAAE8B,EAAE,CAAE,GAAG,CAAEjB,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACvE9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAAE+B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAqB,QAAA,cAC3E9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAACiB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACflE,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAE6B,IAAK,CACdnC,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWqD,GAQL,CACX,CAAC,CACE,CACP,CACAR,OAAO,CAACG,MAAM,eACbzD,IAAA,CAAC7B,IAAI,EAAC8D,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,CAC5CwB,OAAO,CAACG,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,CAAEC,GAAG,gBAC7B5D,KAAA,CAAC9B,QAAQ,EAAW6D,EAAE,CAAE,CAAE8B,EAAE,CAAE,GAAG,CAAEjB,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAO,CAAE,CAAAqB,QAAA,eACvE9B,IAAA,CAAC1B,YAAY,EAAC2D,EAAE,CAAE,CAAE+B,QAAQ,CAAE,MAAM,CAAEjB,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CAAEuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAAE,CAAE,CAAAqB,QAAA,cAC3E9B,IAAA,CAACV,QAAQ,EAAC2D,KAAK,CAAC,SAAS,CAACiB,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC,CAAC,cACflE,IAAA,CAAC3B,YAAY,EACXkE,OAAO,CAAEsB,KAAM,CACf5B,EAAE,CAAE,CAAEmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAC/D,CAAC,GAPWqD,GAQL,CACX,CAAC,CACE,CACP,EACE,CAAC,EA5CU+B,GA6CL,CAAC,CAElB,CAAC,CAAC,EACC,CAAC,CAEV,CAAC,CAED,mBACE7F,IAAA,CAACH,MAAM,EAAAiC,QAAA,cACL9B,IAAA,CAACjC,GAAG,EACFkE,EAAE,CAAE,CACF8B,EAAE,CAAE,CAAC,CACLkD,SAAS,CAAE,OAAO,CAClBC,UAAU,CAAE,mBAAmBvI,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,IAAI,CAAC,KAAK7D,KAAK,CAAC2B,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CAAE,GAAG,CAAC,GAAG,CACpHJ,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAqB,QAAA,cAEF9B,IAAA,CAACpC,SAAS,EAACuJ,QAAQ,CAAC,IAAI,CAAClF,EAAE,CAAE,CAAEG,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,cAChE9B,IAAA,CAAC9B,IAAI,EAACkJ,EAAE,MAACC,OAAO,CAAE,IAAK,CAAAvF,QAAA,cACrB5B,KAAA,CAACpC,KAAK,EACJkE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFqF,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBtF,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC,KAAK,CAAE,CACL2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAC,CACD,uBAAuB,CAAE,CACvBqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAC,CACD,iBAAiB,CAAE,CACjB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAC,CACD,qBAAqB,CAAE,CACrB2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CACF,CAAE,CAAAqB,QAAA,eAGF5B,KAAA,CAACnC,GAAG,EAACkE,EAAE,CAAE,CACPC,EAAE,CAAE,CAAC,CACLY,SAAS,CAAE,QAAQ,CACnBV,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAqB,QAAA,eACA9B,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,IAAI,CACZuE,SAAS,CAAC,IAAI,CACdxF,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACLiB,UAAU,CAAE,GAAG,CACfC,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwC,KAAK,CAAE3C,KAAK,CAACgC,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCJ,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAE,QACb,CAAE,CAAAhB,QAAA,CAEDrB,KAAK,CAAG,eAAe,CAAG,mBAAmB,CACpC,CAAC,cACbT,IAAA,CAACnC,UAAU,EACTqF,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,gBAAgB,CACtBhB,EAAE,CAAE,CACFmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAK,CAChCqC,SAAS,CAAE,QACb,CAAE,CAAAhB,QAAA,CAEDrB,KAAK,CACF,iEAAiE,CACjE,4DAA4D,CAEtD,CAAC,EACV,CAAC,cAGNT,IAAA,CAACjC,GAAG,EAACkE,EAAE,CAAE,CAAEyF,YAAY,CAAE,CAAC,CAAEC,WAAW,CAAE,SAAS,CAAEzF,EAAE,CAAE,CAAC,CAAEE,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAAM,CAAE,CAAAqB,QAAA,cAC5F9B,IAAA,CAACzB,IAAI,EACHgG,KAAK,CAAE3D,SAAU,CACjBgH,QAAQ,CAAEzG,eAAgB,CAC1B+B,OAAO,CAAC,YAAY,CACpB2E,aAAa,CAAC,MAAM,CACpB5F,EAAE,CAAE,CACF,gBAAgB,CAAE,CAChBmB,UAAU,CAAE3C,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrDwG,SAAS,CAAE,EAAE,CACbnE,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAC/B,CAAC,CACD,0BAA0B,CAAE,CAC1B2B,SAAS,CAAE3B,KAAK,CAAG,KAAK,CAAG,KAC7B,CACF,CAAE,CAAAqB,QAAA,CAEDN,UAAU,CAACoC,GAAG,CAAC,CAACkE,GAAG,CAAEtD,KAAK,gBACzBxE,IAAA,CAACxB,GAAG,EAEFkD,IAAI,CAAEoG,GAAG,CAACpG,IAAK,CACfD,KAAK,CAAEqG,GAAG,CAACrG,KAAM,CACjBsG,YAAY,CAAC,OAAO,CACpB9F,EAAE,CAAE,CACFY,aAAa,CAAEpC,KAAK,CAAG,aAAa,CAAG,KAAK,CAC5CqC,SAAS,CAAErC,KAAK,CAAG,OAAO,CAAG,MAAM,CACnC,uBAAuB,CAAE,CACvBsC,EAAE,CAAEtC,KAAK,CAAG,CAAC,CAAG,CAAC,CACjBuC,EAAE,CAAEvC,KAAK,CAAG,CAAC,CAAG,CAClB,CACF,CAAE,EAXG+D,KAYN,CACF,CAAC,CACE,CAAC,CACJ,CAAC,cAGNxE,IAAA,CAACqE,QAAQ,EAACE,KAAK,CAAE3D,SAAU,CAAC4D,KAAK,CAAE,CAAE,CAAA1C,QAAA,CAClC4C,wBAAwB,CAAC,CAAC,CACnB,CAAC,cAEX1E,IAAA,CAACqE,QAAQ,EAACE,KAAK,CAAE3D,SAAU,CAAC4D,KAAK,CAAE,CAAE,CAAA1C,QAAA,CAClC2D,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXzF,IAAA,CAACqE,QAAQ,EAACE,KAAK,CAAE3D,SAAU,CAAC4D,KAAK,CAAE,CAAE,CAAA1C,QAAA,CAClCuE,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEXrG,IAAA,CAACqE,QAAQ,EAACE,KAAK,CAAE3D,SAAU,CAAC4D,KAAK,CAAE,CAAE,CAAA1C,QAAA,CAClCiF,mBAAmB,CAAC,CAAC,CACd,CAAC,cAEX/G,IAAA,CAACqE,QAAQ,EAACE,KAAK,CAAE3D,SAAU,CAAC4D,KAAK,CAAE,CAAE,CAAA1C,QAAA,CAClCkF,kBAAkB,CAAC,CAAC,CACb,CAAC,EAEN,CAAC,CACJ,CAAC,CACE,CAAC,CACT,CAAC,CACA,CAAC,CAEb,CAAC,CAED,cAAe,CAAA7G,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}