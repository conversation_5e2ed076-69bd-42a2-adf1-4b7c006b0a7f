{"ast": null, "code": "import React,{useState,useEffect}from'react';import{useTranslation}from'react-i18next';import{useNavigate}from'react-router-dom';import{Container,Typography,Box,Card,CardContent,Button,Alert,CircularProgress,Fade,useTheme,alpha,Dialog,DialogTitle,DialogContent,DialogActions,Divider}from'@mui/material';import{Warning as WarningIcon,Schedule as ScheduleIcon,Cancel as CancelIcon,CheckCircle as CheckCircleIcon}from'@mui/icons-material';import axios from'axios';import{useAuth}from'../contexts/AuthContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PendingDeletion=()=>{const{t,i18n}=useTranslation();const theme=useTheme();const navigate=useNavigate();const{user,logout}=useAuth();const isRtl=i18n.language==='ar';const[deleteScheduledAt,setDeleteScheduledAt]=useState(null);const[timeRemaining,setTimeRemaining]=useState('');const[loading,setLoading]=useState(true);const[cancelLoading,setCancelLoading]=useState(false);const[confirmDialog,setConfirmDialog]=useState(false);const[error,setError]=useState('');const[success,setSuccess]=useState('');// Fetch user deletion status\nuseEffect(()=>{const fetchDeletionStatus=async()=>{try{const token=localStorage.getItem('token');const response=await axios.get('/api/users/profile',{headers:{Authorization:`Bearer ${token}`}});if(response.data.user){const userData=response.data.user;if(userData.status==='pending_deletion'&&userData.delete_scheduled_at){setDeleteScheduledAt(userData.delete_scheduled_at);}else{// If not pending deletion, redirect to appropriate dashboard\nredirectToDashboard();}}}catch(error){console.error('Error fetching deletion status:',error);setError(t('profile.errors.loadFailed'));}finally{setLoading(false);}};fetchDeletionStatus();},[]);// Calculate time remaining\nuseEffect(()=>{if(!deleteScheduledAt)return;const updateTimeRemaining=()=>{const now=new Date();const deleteDate=new Date(deleteScheduledAt);const timeDiff=deleteDate.getTime()-now.getTime();if(timeDiff<=0){setTimeRemaining(t('profile.deletionExpired')||'Deletion time has passed');return;}const days=Math.floor(timeDiff/(1000*60*60*24));const hours=Math.floor(timeDiff%(1000*60*60*24)/(1000*60*60));const minutes=Math.floor(timeDiff%(1000*60*60)/(1000*60));if(days>0){setTimeRemaining(isRtl?`${days} أيام و ${hours} ساعات و ${minutes} دقائق`:`${days} days, ${hours} hours, and ${minutes} minutes`);}else if(hours>0){setTimeRemaining(isRtl?`${hours} ساعات و ${minutes} دقائق`:`${hours} hours and ${minutes} minutes`);}else{setTimeRemaining(isRtl?`${minutes} دقائق`:`${minutes} minutes`);}};updateTimeRemaining();const interval=setInterval(updateTimeRemaining,60000);// Update every minute\nreturn()=>clearInterval(interval);},[deleteScheduledAt,isRtl,t]);const redirectToDashboard=()=>{if(!user)return;switch(user.role){case'student':navigate('/student/dashboard');break;case'platform_teacher':case'new_teacher':navigate('/teacher/dashboard');break;case'admin':navigate('/admin/dashboard');break;default:navigate('/');}};const handleCancelDeletion=async()=>{setCancelLoading(true);setError('');setSuccess('');try{const token=localStorage.getItem('token');const response=await axios.post('/api/users/cancel-delete',{},{headers:{Authorization:`Bearer ${token}`}});if(response.data.success){setSuccess(t('profile.deleteCancelled'));setConfirmDialog(false);// Redirect to dashboard after 2 seconds\nsetTimeout(()=>{redirectToDashboard();},2000);}}catch(err){var _err$response,_err$response$data;setError(((_err$response=err.response)===null||_err$response===void 0?void 0:(_err$response$data=_err$response.data)===null||_err$response$data===void 0?void 0:_err$response$data.message)||t('profile.errors.cancelFailed'));}finally{setCancelLoading(false);}};const formatDeleteDate=()=>{if(!deleteScheduledAt)return'';const deleteDate=new Date(deleteScheduledAt);return deleteDate.toLocaleString(isRtl?'ar-EG':'en-US',{year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit',timeZoneName:'short'});};if(loading){return/*#__PURE__*/_jsx(Box,{sx:{minHeight:'100vh',display:'flex',alignItems:'center',justifyContent:'center',background:`linear-gradient(135deg, ${alpha(theme.palette.error.main,0.1)}, ${alpha(theme.palette.warning.main,0.1)})`},children:/*#__PURE__*/_jsx(CircularProgress,{size:60})});}return/*#__PURE__*/_jsxs(Box,{sx:{minHeight:'100vh',background:`linear-gradient(135deg, ${alpha(theme.palette.error.main,0.1)}, ${alpha(theme.palette.warning.main,0.1)})`,py:4,direction:isRtl?'rtl':'ltr'},children:[/*#__PURE__*/_jsx(Container,{maxWidth:\"md\",children:/*#__PURE__*/_jsx(Fade,{in:true,timeout:800,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',mb:4},children:[/*#__PURE__*/_jsx(WarningIcon,{sx:{fontSize:80,color:theme.palette.warning.main,mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h3\",fontWeight:700,color:\"warning.main\",sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit'},children:t('profile.deletePendingAlert')})]}),/*#__PURE__*/_jsxs(Card,{elevation:8,sx:{borderRadius:4,overflow:'hidden',border:`3px solid ${theme.palette.warning.main}`},children:[/*#__PURE__*/_jsx(Box,{sx:{background:`linear-gradient(45deg, ${theme.palette.warning.main}, ${theme.palette.warning.light})`,p:3,color:'white',textAlign:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:600,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit'},children:isRtl?'حسابك مجدول للحذف':'Your Account is Scheduled for Deletion'})}),/*#__PURE__*/_jsxs(CardContent,{sx:{p:4},children:[/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center',mb:4},children:[/*#__PURE__*/_jsx(ScheduleIcon,{sx:{fontSize:48,color:theme.palette.error.main,mb:2}}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:600,color:\"text.primary\",sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit',mb:1},children:isRtl?'الوقت المتبقي:':'Time Remaining:'}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:700,color:\"error.main\",sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit'},children:timeRemaining})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:3}}),/*#__PURE__*/_jsxs(Box,{sx:{mb:4},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",fontWeight:600,sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit',mb:2},children:[t('profile.deleteScheduledFor'),\":\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit',fontSize:'1.1rem',color:theme.palette.text.secondary},children:formatDeleteDate()})]}),/*#__PURE__*/_jsx(Alert,{severity:\"warning\",sx:{mb:4,'& .MuiAlert-message':{fontFamily:isRtl?'Tajawal, sans-serif':'inherit'}},children:t('profile.deletePendingMessage')}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2,justifyContent:'center',flexWrap:'wrap'},children:[/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"success\",size:\"large\",startIcon:/*#__PURE__*/_jsx(CancelIcon,{}),onClick:()=>setConfirmDialog(true),sx:{minWidth:200,py:1.5,fontFamily:isRtl?'Tajawal, sans-serif':'inherit',fontSize:'1.1rem'},children:t('profile.cancelDelete')}),/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"error\",size:\"large\",onClick:logout,sx:{minWidth:200,py:1.5,fontFamily:isRtl?'Tajawal, sans-serif':'inherit',fontSize:'1.1rem'},children:isRtl?'تسجيل الخروج':'Logout'})]}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mt:3},children:error}),success&&/*#__PURE__*/_jsx(Alert,{severity:\"success\",sx:{mt:3},children:success})]})]})]})})}),/*#__PURE__*/_jsxs(Dialog,{open:confirmDialog,onClose:()=>setConfirmDialog(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit'},children:isRtl?'تأكيد إلغاء الحذف':'Confirm Cancellation'}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(Typography,{sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit'},children:isRtl?'هل أنت متأكد من أنك تريد إلغاء حذف حسابك؟ سيتم إعادة تفعيل حسابك بالكامل.':'Are you sure you want to cancel the deletion of your account? Your account will be fully reactivated.'})}),/*#__PURE__*/_jsxs(DialogActions,{sx:{p:3,gap:2},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setConfirmDialog(false),color:\"inherit\",sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit'},children:isRtl?'إلغاء':'Cancel'}),/*#__PURE__*/_jsx(Button,{onClick:handleCancelDeletion,variant:\"contained\",color:\"success\",disabled:cancelLoading,startIcon:cancelLoading?/*#__PURE__*/_jsx(CircularProgress,{size:20}):/*#__PURE__*/_jsx(CheckCircleIcon,{}),sx:{fontFamily:isRtl?'Tajawal, sans-serif':'inherit'},children:cancelLoading?isRtl?'جاري الإلغاء...':'Cancelling...':isRtl?'نعم، إلغاء الحذف':'Yes, Cancel Deletion'})]})]})]});};export default PendingDeletion;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "useNavigate", "Container", "Typography", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "CircularProgress", "Fade", "useTheme", "alpha", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "Divider", "Warning", "WarningIcon", "Schedule", "ScheduleIcon", "Cancel", "CancelIcon", "CheckCircle", "CheckCircleIcon", "axios", "useAuth", "jsx", "_jsx", "jsxs", "_jsxs", "PendingDeletion", "t", "i18n", "theme", "navigate", "user", "logout", "isRtl", "language", "deleteScheduledAt", "setDeleteScheduledAt", "timeRemaining", "setTimeRemaining", "loading", "setLoading", "cancelLoading", "setCancelLoading", "confirmDialog", "setConfirmDialog", "error", "setError", "success", "setSuccess", "fetchDeletionStatus", "token", "localStorage", "getItem", "response", "get", "headers", "Authorization", "data", "userData", "status", "delete_scheduled_at", "redirectToDashboard", "console", "updateTimeRemaining", "now", "Date", "deleteDate", "timeDiff", "getTime", "days", "Math", "floor", "hours", "minutes", "interval", "setInterval", "clearInterval", "role", "handleCancelDeletion", "post", "setTimeout", "err", "_err$response", "_err$response$data", "message", "formatDeleteDate", "toLocaleString", "year", "month", "day", "hour", "minute", "timeZoneName", "sx", "minHeight", "display", "alignItems", "justifyContent", "background", "palette", "main", "warning", "children", "size", "py", "direction", "max<PERSON><PERSON><PERSON>", "in", "timeout", "textAlign", "mb", "fontSize", "color", "variant", "fontWeight", "fontFamily", "elevation", "borderRadius", "overflow", "border", "light", "p", "my", "text", "secondary", "severity", "gap", "flexWrap", "startIcon", "onClick", "min<PERSON><PERSON><PERSON>", "mt", "open", "onClose", "fullWidth", "disabled"], "sources": ["D:/xampp/htdocs/allemnionline/client/src/pages/PendingDeletion.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from 'react-i18next';\nimport { useNavigate } from 'react-router-dom';\nimport {\n  Container,\n  Typography,\n  Box,\n  Card,\n  CardContent,\n  Button,\n  Alert,\n  CircularProgress,\n  Fade,\n  useTheme,\n  alpha,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Divider,\n} from '@mui/material';\nimport {\n  Warning as WarningIcon,\n  Schedule as ScheduleIcon,\n  Cancel as CancelIcon,\n  CheckCircle as CheckCircleIcon,\n} from '@mui/icons-material';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\n\nconst PendingDeletion = () => {\n  const { t, i18n } = useTranslation();\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { user, logout } = useAuth();\n  const isRtl = i18n.language === 'ar';\n\n  const [deleteScheduledAt, setDeleteScheduledAt] = useState(null);\n  const [timeRemaining, setTimeRemaining] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [cancelLoading, setCancelLoading] = useState(false);\n  const [confirmDialog, setConfirmDialog] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Fetch user deletion status\n  useEffect(() => {\n    const fetchDeletionStatus = async () => {\n      try {\n        const token = localStorage.getItem('token');\n        const response = await axios.get('/api/users/profile', {\n          headers: { Authorization: `Bearer ${token}` }\n        });\n\n        if (response.data.user) {\n          const userData = response.data.user;\n          if (userData.status === 'pending_deletion' && userData.delete_scheduled_at) {\n            setDeleteScheduledAt(userData.delete_scheduled_at);\n          } else {\n            // If not pending deletion, redirect to appropriate dashboard\n            redirectToDashboard();\n          }\n        }\n      } catch (error) {\n        console.error('Error fetching deletion status:', error);\n        setError(t('profile.errors.loadFailed'));\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchDeletionStatus();\n  }, []);\n\n  // Calculate time remaining\n  useEffect(() => {\n    if (!deleteScheduledAt) return;\n\n    const updateTimeRemaining = () => {\n      const now = new Date();\n      const deleteDate = new Date(deleteScheduledAt);\n      const timeDiff = deleteDate.getTime() - now.getTime();\n\n      if (timeDiff <= 0) {\n        setTimeRemaining(t('profile.deletionExpired') || 'Deletion time has passed');\n        return;\n      }\n\n      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));\n      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));\n\n      if (days > 0) {\n        setTimeRemaining(\n          isRtl \n            ? `${days} أيام و ${hours} ساعات و ${minutes} دقائق`\n            : `${days} days, ${hours} hours, and ${minutes} minutes`\n        );\n      } else if (hours > 0) {\n        setTimeRemaining(\n          isRtl \n            ? `${hours} ساعات و ${minutes} دقائق`\n            : `${hours} hours and ${minutes} minutes`\n        );\n      } else {\n        setTimeRemaining(\n          isRtl \n            ? `${minutes} دقائق`\n            : `${minutes} minutes`\n        );\n      }\n    };\n\n    updateTimeRemaining();\n    const interval = setInterval(updateTimeRemaining, 60000); // Update every minute\n\n    return () => clearInterval(interval);\n  }, [deleteScheduledAt, isRtl, t]);\n\n  const redirectToDashboard = () => {\n    if (!user) return;\n    \n    switch (user.role) {\n      case 'student':\n        navigate('/student/dashboard');\n        break;\n      case 'platform_teacher':\n      case 'new_teacher':\n        navigate('/teacher/dashboard');\n        break;\n      case 'admin':\n        navigate('/admin/dashboard');\n        break;\n      default:\n        navigate('/');\n    }\n  };\n\n  const handleCancelDeletion = async () => {\n    setCancelLoading(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      const token = localStorage.getItem('token');\n      const response = await axios.post('/api/users/cancel-delete', {}, {\n        headers: { Authorization: `Bearer ${token}` }\n      });\n\n      if (response.data.success) {\n        setSuccess(t('profile.deleteCancelled'));\n        setConfirmDialog(false);\n        \n        // Redirect to dashboard after 2 seconds\n        setTimeout(() => {\n          redirectToDashboard();\n        }, 2000);\n      }\n    } catch (err) {\n      setError(err.response?.data?.message || t('profile.errors.cancelFailed'));\n    } finally {\n      setCancelLoading(false);\n    }\n  };\n\n  const formatDeleteDate = () => {\n    if (!deleteScheduledAt) return '';\n    \n    const deleteDate = new Date(deleteScheduledAt);\n    return deleteDate.toLocaleString(isRtl ? 'ar-EG' : 'en-US', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n      timeZoneName: 'short'\n    });\n  };\n\n  if (loading) {\n    return (\n      <Box\n        sx={{\n          minHeight: '100vh',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)}, ${alpha(theme.palette.warning.main, 0.1)})`\n        }}\n      >\n        <CircularProgress size={60} />\n      </Box>\n    );\n  }\n\n  return (\n    <Box\n      sx={{\n        minHeight: '100vh',\n        background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)}, ${alpha(theme.palette.warning.main, 0.1)})`,\n        py: 4,\n        direction: isRtl ? 'rtl' : 'ltr'\n      }}\n    >\n      <Container maxWidth=\"md\">\n        <Fade in timeout={800}>\n          <Box>\n            {/* Header */}\n            <Box sx={{ textAlign: 'center', mb: 4 }}>\n              <WarningIcon \n                sx={{ \n                  fontSize: 80, \n                  color: theme.palette.warning.main,\n                  mb: 2 \n                }} \n              />\n              <Typography\n                variant=\"h3\"\n                fontWeight={700}\n                color=\"warning.main\"\n                sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}\n              >\n                {t('profile.deletePendingAlert')}\n              </Typography>\n            </Box>\n\n            {/* Main Card */}\n            <Card\n              elevation={8}\n              sx={{\n                borderRadius: 4,\n                overflow: 'hidden',\n                border: `3px solid ${theme.palette.warning.main}`,\n              }}\n            >\n              <Box\n                sx={{\n                  background: `linear-gradient(45deg, ${theme.palette.warning.main}, ${theme.palette.warning.light})`,\n                  p: 3,\n                  color: 'white',\n                  textAlign: 'center'\n                }}\n              >\n                <Typography\n                  variant=\"h4\"\n                  fontWeight={600}\n                  sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}\n                >\n                  {isRtl ? 'حسابك مجدول للحذف' : 'Your Account is Scheduled for Deletion'}\n                </Typography>\n              </Box>\n\n              <CardContent sx={{ p: 4 }}>\n                {/* Time Remaining */}\n                <Box sx={{ textAlign: 'center', mb: 4 }}>\n                  <ScheduleIcon sx={{ fontSize: 48, color: theme.palette.error.main, mb: 2 }} />\n                  <Typography\n                    variant=\"h5\"\n                    fontWeight={600}\n                    color=\"text.primary\"\n                    sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit', mb: 1 }}\n                  >\n                    {isRtl ? 'الوقت المتبقي:' : 'Time Remaining:'}\n                  </Typography>\n                  <Typography\n                    variant=\"h4\"\n                    fontWeight={700}\n                    color=\"error.main\"\n                    sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}\n                  >\n                    {timeRemaining}\n                  </Typography>\n                </Box>\n\n                <Divider sx={{ my: 3 }} />\n\n                {/* Deletion Details */}\n                <Box sx={{ mb: 4 }}>\n                  <Typography\n                    variant=\"h6\"\n                    fontWeight={600}\n                    sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit', mb: 2 }}\n                  >\n                    {t('profile.deleteScheduledFor')}:\n                  </Typography>\n                  <Typography\n                    variant=\"body1\"\n                    sx={{ \n                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                      fontSize: '1.1rem',\n                      color: theme.palette.text.secondary\n                    }}\n                  >\n                    {formatDeleteDate()}\n                  </Typography>\n                </Box>\n\n                {/* Warning Message */}\n                <Alert \n                  severity=\"warning\" \n                  sx={{ \n                    mb: 4,\n                    '& .MuiAlert-message': {\n                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit'\n                    }\n                  }}\n                >\n                  {t('profile.deletePendingMessage')}\n                </Alert>\n\n                {/* Action Buttons */}\n                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>\n                  <Button\n                    variant=\"contained\"\n                    color=\"success\"\n                    size=\"large\"\n                    startIcon={<CancelIcon />}\n                    onClick={() => setConfirmDialog(true)}\n                    sx={{\n                      minWidth: 200,\n                      py: 1.5,\n                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                      fontSize: '1.1rem'\n                    }}\n                  >\n                    {t('profile.cancelDelete')}\n                  </Button>\n                  \n                  <Button\n                    variant=\"outlined\"\n                    color=\"error\"\n                    size=\"large\"\n                    onClick={logout}\n                    sx={{\n                      minWidth: 200,\n                      py: 1.5,\n                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',\n                      fontSize: '1.1rem'\n                    }}\n                  >\n                    {isRtl ? 'تسجيل الخروج' : 'Logout'}\n                  </Button>\n                </Box>\n\n                {/* Error/Success Messages */}\n                {error && (\n                  <Alert severity=\"error\" sx={{ mt: 3 }}>\n                    {error}\n                  </Alert>\n                )}\n                {success && (\n                  <Alert severity=\"success\" sx={{ mt: 3 }}>\n                    {success}\n                  </Alert>\n                )}\n              </CardContent>\n            </Card>\n          </Box>\n        </Fade>\n      </Container>\n\n      {/* Confirmation Dialog */}\n      <Dialog\n        open={confirmDialog}\n        onClose={() => setConfirmDialog(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}>\n          {isRtl ? 'تأكيد إلغاء الحذف' : 'Confirm Cancellation'}\n        </DialogTitle>\n        <DialogContent>\n          <Typography sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}>\n            {isRtl \n              ? 'هل أنت متأكد من أنك تريد إلغاء حذف حسابك؟ سيتم إعادة تفعيل حسابك بالكامل.'\n              : 'Are you sure you want to cancel the deletion of your account? Your account will be fully reactivated.'\n            }\n          </Typography>\n        </DialogContent>\n        <DialogActions sx={{ p: 3, gap: 2 }}>\n          <Button\n            onClick={() => setConfirmDialog(false)}\n            color=\"inherit\"\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}\n          >\n            {isRtl ? 'إلغاء' : 'Cancel'}\n          </Button>\n          <Button\n            onClick={handleCancelDeletion}\n            variant=\"contained\"\n            color=\"success\"\n            disabled={cancelLoading}\n            startIcon={cancelLoading ? <CircularProgress size={20} /> : <CheckCircleIcon />}\n            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}\n          >\n            {cancelLoading \n              ? (isRtl ? 'جاري الإلغاء...' : 'Cancelling...') \n              : (isRtl ? 'نعم، إلغاء الحذف' : 'Yes, Cancel Deletion')\n            }\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default PendingDeletion;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,SAAS,CACTC,UAAU,CACVC,GAAG,CACHC,IAAI,CACJC,WAAW,CACXC,MAAM,CACNC,KAAK,CACLC,gBAAgB,CAChBC,IAAI,CACJC,QAAQ,CACRC,KAAK,CACLC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,OAAO,KACF,eAAe,CACtB,OACEC,OAAO,GAAI,CAAAC,WAAW,CACtBC,QAAQ,GAAI,CAAAC,YAAY,CACxBC,MAAM,GAAI,CAAAC,UAAU,CACpBC,WAAW,GAAI,CAAAC,eAAe,KACzB,qBAAqB,CAC5B,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,yBAAyB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAElD,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAEC,CAAC,CAAEC,IAAK,CAAC,CAAGlC,cAAc,CAAC,CAAC,CACpC,KAAM,CAAAmC,KAAK,CAAGxB,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAyB,QAAQ,CAAGnC,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEoC,IAAI,CAAEC,MAAO,CAAC,CAAGX,OAAO,CAAC,CAAC,CAClC,KAAM,CAAAY,KAAK,CAAGL,IAAI,CAACM,QAAQ,GAAK,IAAI,CAEpC,KAAM,CAACC,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG5C,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAAC6C,aAAa,CAAEC,gBAAgB,CAAC,CAAG9C,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAAC+C,OAAO,CAAEC,UAAU,CAAC,CAAGhD,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACiD,aAAa,CAAEC,gBAAgB,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACmD,aAAa,CAAEC,gBAAgB,CAAC,CAAGpD,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACqD,KAAK,CAAEC,QAAQ,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACuD,OAAO,CAAEC,UAAU,CAAC,CAAGxD,QAAQ,CAAC,EAAE,CAAC,CAE1C;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwD,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CACF,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAjC,KAAK,CAACkC,GAAG,CAAC,oBAAoB,CAAE,CACrDC,OAAO,CAAE,CAAEC,aAAa,CAAE,UAAUN,KAAK,EAAG,CAC9C,CAAC,CAAC,CAEF,GAAIG,QAAQ,CAACI,IAAI,CAAC1B,IAAI,CAAE,CACtB,KAAM,CAAA2B,QAAQ,CAAGL,QAAQ,CAACI,IAAI,CAAC1B,IAAI,CACnC,GAAI2B,QAAQ,CAACC,MAAM,GAAK,kBAAkB,EAAID,QAAQ,CAACE,mBAAmB,CAAE,CAC1ExB,oBAAoB,CAACsB,QAAQ,CAACE,mBAAmB,CAAC,CACpD,CAAC,IAAM,CACL;AACAC,mBAAmB,CAAC,CAAC,CACvB,CACF,CACF,CAAE,MAAOhB,KAAK,CAAE,CACdiB,OAAO,CAACjB,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDC,QAAQ,CAACnB,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAC1C,CAAC,OAAS,CACRa,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDS,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,EAAE,CAAC,CAEN;AACAxD,SAAS,CAAC,IAAM,CACd,GAAI,CAAC0C,iBAAiB,CAAE,OAExB,KAAM,CAAA4B,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,UAAU,CAAG,GAAI,CAAAD,IAAI,CAAC9B,iBAAiB,CAAC,CAC9C,KAAM,CAAAgC,QAAQ,CAAGD,UAAU,CAACE,OAAO,CAAC,CAAC,CAAGJ,GAAG,CAACI,OAAO,CAAC,CAAC,CAErD,GAAID,QAAQ,EAAI,CAAC,CAAE,CACjB7B,gBAAgB,CAACX,CAAC,CAAC,yBAAyB,CAAC,EAAI,0BAA0B,CAAC,CAC5E,OACF,CAEA,KAAM,CAAA0C,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACJ,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CACzD,KAAM,CAAAK,KAAK,CAAGF,IAAI,CAACC,KAAK,CAAEJ,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,EAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAC/E,KAAM,CAAAM,OAAO,CAAGH,IAAI,CAACC,KAAK,CAAEJ,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,EAAK,IAAI,CAAG,EAAE,CAAC,CAAC,CAEvE,GAAIE,IAAI,CAAG,CAAC,CAAE,CACZ/B,gBAAgB,CACdL,KAAK,CACD,GAAGoC,IAAI,WAAWG,KAAK,YAAYC,OAAO,QAAQ,CAClD,GAAGJ,IAAI,UAAUG,KAAK,eAAeC,OAAO,UAClD,CAAC,CACH,CAAC,IAAM,IAAID,KAAK,CAAG,CAAC,CAAE,CACpBlC,gBAAgB,CACdL,KAAK,CACD,GAAGuC,KAAK,YAAYC,OAAO,QAAQ,CACnC,GAAGD,KAAK,cAAcC,OAAO,UACnC,CAAC,CACH,CAAC,IAAM,CACLnC,gBAAgB,CACdL,KAAK,CACD,GAAGwC,OAAO,QAAQ,CAClB,GAAGA,OAAO,UAChB,CAAC,CACH,CACF,CAAC,CAEDV,mBAAmB,CAAC,CAAC,CACrB,KAAM,CAAAW,QAAQ,CAAGC,WAAW,CAACZ,mBAAmB,CAAE,KAAK,CAAC,CAAE;AAE1D,MAAO,IAAMa,aAAa,CAACF,QAAQ,CAAC,CACtC,CAAC,CAAE,CAACvC,iBAAiB,CAAEF,KAAK,CAAEN,CAAC,CAAC,CAAC,CAEjC,KAAM,CAAAkC,mBAAmB,CAAGA,CAAA,GAAM,CAChC,GAAI,CAAC9B,IAAI,CAAE,OAEX,OAAQA,IAAI,CAAC8C,IAAI,EACf,IAAK,SAAS,CACZ/C,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MACF,IAAK,kBAAkB,CACvB,IAAK,aAAa,CAChBA,QAAQ,CAAC,oBAAoB,CAAC,CAC9B,MACF,IAAK,OAAO,CACVA,QAAQ,CAAC,kBAAkB,CAAC,CAC5B,MACF,QACEA,QAAQ,CAAC,GAAG,CAAC,CACjB,CACF,CAAC,CAED,KAAM,CAAAgD,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvCpC,gBAAgB,CAAC,IAAI,CAAC,CACtBI,QAAQ,CAAC,EAAE,CAAC,CACZE,UAAU,CAAC,EAAE,CAAC,CAEd,GAAI,CACF,KAAM,CAAAE,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAjC,KAAK,CAAC2D,IAAI,CAAC,0BAA0B,CAAE,CAAC,CAAC,CAAE,CAChExB,OAAO,CAAE,CAAEC,aAAa,CAAE,UAAUN,KAAK,EAAG,CAC9C,CAAC,CAAC,CAEF,GAAIG,QAAQ,CAACI,IAAI,CAACV,OAAO,CAAE,CACzBC,UAAU,CAACrB,CAAC,CAAC,yBAAyB,CAAC,CAAC,CACxCiB,gBAAgB,CAAC,KAAK,CAAC,CAEvB;AACAoC,UAAU,CAAC,IAAM,CACfnB,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,IAAI,CAAC,CACV,CACF,CAAE,MAAOoB,GAAG,CAAE,KAAAC,aAAA,CAAAC,kBAAA,CACZrC,QAAQ,CAAC,EAAAoC,aAAA,CAAAD,GAAG,CAAC5B,QAAQ,UAAA6B,aAAA,kBAAAC,kBAAA,CAAZD,aAAA,CAAczB,IAAI,UAAA0B,kBAAA,iBAAlBA,kBAAA,CAAoBC,OAAO,GAAIzD,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAC3E,CAAC,OAAS,CACRe,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAED,KAAM,CAAA2C,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,GAAI,CAAClD,iBAAiB,CAAE,MAAO,EAAE,CAEjC,KAAM,CAAA+B,UAAU,CAAG,GAAI,CAAAD,IAAI,CAAC9B,iBAAiB,CAAC,CAC9C,MAAO,CAAA+B,UAAU,CAACoB,cAAc,CAACrD,KAAK,CAAG,OAAO,CAAG,OAAO,CAAE,CAC1DsD,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,YAAY,CAAE,OAChB,CAAC,CAAC,CACJ,CAAC,CAED,GAAIrD,OAAO,CAAE,CACX,mBACEhB,IAAA,CAACzB,GAAG,EACF+F,EAAE,CAAE,CACFC,SAAS,CAAE,OAAO,CAClBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,UAAU,CAAE,2BAA2B5F,KAAK,CAACuB,KAAK,CAACsE,OAAO,CAACtD,KAAK,CAACuD,IAAI,CAAE,GAAG,CAAC,KAAK9F,KAAK,CAACuB,KAAK,CAACsE,OAAO,CAACE,OAAO,CAACD,IAAI,CAAE,GAAG,CAAC,GACxH,CAAE,CAAAE,QAAA,cAEF/E,IAAA,CAACpB,gBAAgB,EAACoG,IAAI,CAAE,EAAG,CAAE,CAAC,CAC3B,CAAC,CAEV,CAEA,mBACE9E,KAAA,CAAC3B,GAAG,EACF+F,EAAE,CAAE,CACFC,SAAS,CAAE,OAAO,CAClBI,UAAU,CAAE,2BAA2B5F,KAAK,CAACuB,KAAK,CAACsE,OAAO,CAACtD,KAAK,CAACuD,IAAI,CAAE,GAAG,CAAC,KAAK9F,KAAK,CAACuB,KAAK,CAACsE,OAAO,CAACE,OAAO,CAACD,IAAI,CAAE,GAAG,CAAC,GAAG,CACzHI,EAAE,CAAE,CAAC,CACLC,SAAS,CAAExE,KAAK,CAAG,KAAK,CAAG,KAC7B,CAAE,CAAAqE,QAAA,eAEF/E,IAAA,CAAC3B,SAAS,EAAC8G,QAAQ,CAAC,IAAI,CAAAJ,QAAA,cACtB/E,IAAA,CAACnB,IAAI,EAACuG,EAAE,MAACC,OAAO,CAAE,GAAI,CAAAN,QAAA,cACpB7E,KAAA,CAAC3B,GAAG,EAAAwG,QAAA,eAEF7E,KAAA,CAAC3B,GAAG,EAAC+F,EAAE,CAAE,CAAEgB,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACtC/E,IAAA,CAACV,WAAW,EACVgF,EAAE,CAAE,CACFkB,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAEnF,KAAK,CAACsE,OAAO,CAACE,OAAO,CAACD,IAAI,CACjCU,EAAE,CAAE,CACN,CAAE,CACH,CAAC,cACFvF,IAAA,CAAC1B,UAAU,EACToH,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAC,cAAc,CACpBnB,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAqE,QAAA,CAE7D3E,CAAC,CAAC,4BAA4B,CAAC,CACtB,CAAC,EACV,CAAC,cAGNF,KAAA,CAAC1B,IAAI,EACHqH,SAAS,CAAE,CAAE,CACbvB,EAAE,CAAE,CACFwB,YAAY,CAAE,CAAC,CACfC,QAAQ,CAAE,QAAQ,CAClBC,MAAM,CAAE,aAAa1F,KAAK,CAACsE,OAAO,CAACE,OAAO,CAACD,IAAI,EACjD,CAAE,CAAAE,QAAA,eAEF/E,IAAA,CAACzB,GAAG,EACF+F,EAAE,CAAE,CACFK,UAAU,CAAE,0BAA0BrE,KAAK,CAACsE,OAAO,CAACE,OAAO,CAACD,IAAI,KAAKvE,KAAK,CAACsE,OAAO,CAACE,OAAO,CAACmB,KAAK,GAAG,CACnGC,CAAC,CAAE,CAAC,CACJT,KAAK,CAAE,OAAO,CACdH,SAAS,CAAE,QACb,CAAE,CAAAP,QAAA,cAEF/E,IAAA,CAAC1B,UAAU,EACToH,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBrB,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAqE,QAAA,CAE7DrE,KAAK,CAAG,mBAAmB,CAAG,wCAAwC,CAC7D,CAAC,CACV,CAAC,cAENR,KAAA,CAACzB,WAAW,EAAC6F,EAAE,CAAE,CAAE4B,CAAC,CAAE,CAAE,CAAE,CAAAnB,QAAA,eAExB7E,KAAA,CAAC3B,GAAG,EAAC+F,EAAE,CAAE,CAAEgB,SAAS,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACtC/E,IAAA,CAACR,YAAY,EAAC8E,EAAE,CAAE,CAAEkB,QAAQ,CAAE,EAAE,CAAEC,KAAK,CAAEnF,KAAK,CAACsE,OAAO,CAACtD,KAAK,CAACuD,IAAI,CAAEU,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC9EvF,IAAA,CAAC1B,UAAU,EACToH,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAC,cAAc,CACpBnB,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAS,CAAE6E,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,CAEpErE,KAAK,CAAG,gBAAgB,CAAG,iBAAiB,CACnC,CAAC,cACbV,IAAA,CAAC1B,UAAU,EACToH,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBF,KAAK,CAAC,YAAY,CAClBnB,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAqE,QAAA,CAE7DjE,aAAa,CACJ,CAAC,EACV,CAAC,cAENd,IAAA,CAACZ,OAAO,EAACkF,EAAE,CAAE,CAAE6B,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAG1BjG,KAAA,CAAC3B,GAAG,EAAC+F,EAAE,CAAE,CAAEiB,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,eACjB7E,KAAA,CAAC5B,UAAU,EACToH,OAAO,CAAC,IAAI,CACZC,UAAU,CAAE,GAAI,CAChBrB,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAS,CAAE6E,EAAE,CAAE,CAAE,CAAE,CAAAR,QAAA,EAEpE3E,CAAC,CAAC,4BAA4B,CAAC,CAAC,GACnC,EAAY,CAAC,cACbJ,IAAA,CAAC1B,UAAU,EACToH,OAAO,CAAC,OAAO,CACfpB,EAAE,CAAE,CACFsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD8E,QAAQ,CAAE,QAAQ,CAClBC,KAAK,CAAEnF,KAAK,CAACsE,OAAO,CAACwB,IAAI,CAACC,SAC5B,CAAE,CAAAtB,QAAA,CAEDjB,gBAAgB,CAAC,CAAC,CACT,CAAC,EACV,CAAC,cAGN9D,IAAA,CAACrB,KAAK,EACJ2H,QAAQ,CAAC,SAAS,CAClBhC,EAAE,CAAE,CACFiB,EAAE,CAAE,CAAC,CACL,qBAAqB,CAAE,CACrBK,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAC9C,CACF,CAAE,CAAAqE,QAAA,CAED3E,CAAC,CAAC,8BAA8B,CAAC,CAC7B,CAAC,cAGRF,KAAA,CAAC3B,GAAG,EAAC+F,EAAE,CAAE,CAAEE,OAAO,CAAE,MAAM,CAAE+B,GAAG,CAAE,CAAC,CAAE7B,cAAc,CAAE,QAAQ,CAAE8B,QAAQ,CAAE,MAAO,CAAE,CAAAzB,QAAA,eAC/E/E,IAAA,CAACtB,MAAM,EACLgH,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,SAAS,CACfT,IAAI,CAAC,OAAO,CACZyB,SAAS,cAAEzG,IAAA,CAACN,UAAU,GAAE,CAAE,CAC1BgH,OAAO,CAAEA,CAAA,GAAMrF,gBAAgB,CAAC,IAAI,CAAE,CACtCiD,EAAE,CAAE,CACFqC,QAAQ,CAAE,GAAG,CACb1B,EAAE,CAAE,GAAG,CACPW,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD8E,QAAQ,CAAE,QACZ,CAAE,CAAAT,QAAA,CAED3E,CAAC,CAAC,sBAAsB,CAAC,CACpB,CAAC,cAETJ,IAAA,CAACtB,MAAM,EACLgH,OAAO,CAAC,UAAU,CAClBD,KAAK,CAAC,OAAO,CACbT,IAAI,CAAC,OAAO,CACZ0B,OAAO,CAAEjG,MAAO,CAChB6D,EAAE,CAAE,CACFqC,QAAQ,CAAE,GAAG,CACb1B,EAAE,CAAE,GAAG,CACPW,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAS,CACrD8E,QAAQ,CAAE,QACZ,CAAE,CAAAT,QAAA,CAEDrE,KAAK,CAAG,cAAc,CAAG,QAAQ,CAC5B,CAAC,EACN,CAAC,CAGLY,KAAK,eACJtB,IAAA,CAACrB,KAAK,EAAC2H,QAAQ,CAAC,OAAO,CAAChC,EAAE,CAAE,CAAEsC,EAAE,CAAE,CAAE,CAAE,CAAA7B,QAAA,CACnCzD,KAAK,CACD,CACR,CACAE,OAAO,eACNxB,IAAA,CAACrB,KAAK,EAAC2H,QAAQ,CAAC,SAAS,CAAChC,EAAE,CAAE,CAAEsC,EAAE,CAAE,CAAE,CAAE,CAAA7B,QAAA,CACrCvD,OAAO,CACH,CACR,EACU,CAAC,EACV,CAAC,EACJ,CAAC,CACF,CAAC,CACE,CAAC,cAGZtB,KAAA,CAAClB,MAAM,EACL6H,IAAI,CAAEzF,aAAc,CACpB0F,OAAO,CAAEA,CAAA,GAAMzF,gBAAgB,CAAC,KAAK,CAAE,CACvC8D,QAAQ,CAAC,IAAI,CACb4B,SAAS,MAAAhC,QAAA,eAET/E,IAAA,CAACf,WAAW,EAACqF,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAqE,QAAA,CACxErE,KAAK,CAAG,mBAAmB,CAAG,sBAAsB,CAC1C,CAAC,cACdV,IAAA,CAACd,aAAa,EAAA6F,QAAA,cACZ/E,IAAA,CAAC1B,UAAU,EAACgG,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAqE,QAAA,CACvErE,KAAK,CACF,2EAA2E,CAC3E,uGAAuG,CAEjG,CAAC,CACA,CAAC,cAChBR,KAAA,CAACf,aAAa,EAACmF,EAAE,CAAE,CAAE4B,CAAC,CAAE,CAAC,CAAEK,GAAG,CAAE,CAAE,CAAE,CAAAxB,QAAA,eAClC/E,IAAA,CAACtB,MAAM,EACLgI,OAAO,CAAEA,CAAA,GAAMrF,gBAAgB,CAAC,KAAK,CAAE,CACvCoE,KAAK,CAAC,SAAS,CACfnB,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAqE,QAAA,CAE7DrE,KAAK,CAAG,OAAO,CAAG,QAAQ,CACrB,CAAC,cACTV,IAAA,CAACtB,MAAM,EACLgI,OAAO,CAAEnD,oBAAqB,CAC9BmC,OAAO,CAAC,WAAW,CACnBD,KAAK,CAAC,SAAS,CACfuB,QAAQ,CAAE9F,aAAc,CACxBuF,SAAS,CAAEvF,aAAa,cAAGlB,IAAA,CAACpB,gBAAgB,EAACoG,IAAI,CAAE,EAAG,CAAE,CAAC,cAAGhF,IAAA,CAACJ,eAAe,GAAE,CAAE,CAChF0E,EAAE,CAAE,CAAEsB,UAAU,CAAElF,KAAK,CAAG,qBAAqB,CAAG,SAAU,CAAE,CAAAqE,QAAA,CAE7D7D,aAAa,CACTR,KAAK,CAAG,iBAAiB,CAAG,eAAe,CAC3CA,KAAK,CAAG,kBAAkB,CAAG,sBAAuB,CAEnD,CAAC,EACI,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}