import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Card,
  CardContent,
  Button,
  Alert,
  CircularProgress,
  Fade,
  useTheme,
  alpha,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
} from '@mui/material';
import {
  Warning as WarningIcon,
  Schedule as ScheduleIcon,
  Cancel as CancelIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';

const PendingDeletion = () => {
  const { t, i18n } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const isRtl = i18n.language === 'ar';

  const [deleteScheduledAt, setDeleteScheduledAt] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState('');
  const [loading, setLoading] = useState(true);
  const [cancelLoading, setCancelLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Fetch user deletion status
  useEffect(() => {
    const fetchDeletionStatus = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get('/api/users/profile', {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (response.data.user) {
          const userData = response.data.user;
          if (userData.status === 'pending_deletion' && userData.delete_scheduled_at) {
            setDeleteScheduledAt(userData.delete_scheduled_at);
          } else {
            // If not pending deletion, redirect to appropriate dashboard
            redirectToDashboard();
          }
        }
      } catch (error) {
        console.error('Error fetching deletion status:', error);
        setError(t('profile.errors.loadFailed'));
      } finally {
        setLoading(false);
      }
    };

    fetchDeletionStatus();
  }, []);

  // Calculate time remaining
  useEffect(() => {
    if (!deleteScheduledAt) return;

    const updateTimeRemaining = () => {
      const now = new Date();
      const deleteDate = new Date(deleteScheduledAt);
      const timeDiff = deleteDate.getTime() - now.getTime();

      if (timeDiff <= 0) {
        setTimeRemaining(t('profile.deletionExpired') || 'Deletion time has passed');
        return;
      }

      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

      if (days > 0) {
        setTimeRemaining(
          isRtl 
            ? `${days} أيام و ${hours} ساعات و ${minutes} دقائق`
            : `${days} days, ${hours} hours, and ${minutes} minutes`
        );
      } else if (hours > 0) {
        setTimeRemaining(
          isRtl 
            ? `${hours} ساعات و ${minutes} دقائق`
            : `${hours} hours and ${minutes} minutes`
        );
      } else {
        setTimeRemaining(
          isRtl 
            ? `${minutes} دقائق`
            : `${minutes} minutes`
        );
      }
    };

    updateTimeRemaining();
    const interval = setInterval(updateTimeRemaining, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [deleteScheduledAt, isRtl, t]);

  const redirectToDashboard = () => {
    if (!user) return;
    
    switch (user.role) {
      case 'student':
        navigate('/student/dashboard');
        break;
      case 'platform_teacher':
      case 'new_teacher':
        navigate('/teacher/dashboard');
        break;
      case 'admin':
        navigate('/admin/dashboard');
        break;
      default:
        navigate('/');
    }
  };

  const handleCancelDeletion = async () => {
    setCancelLoading(true);
    setError('');
    setSuccess('');

    try {
      const token = localStorage.getItem('token');
      const response = await axios.post('/api/users/cancel-delete', {}, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setSuccess(t('profile.deleteCancelled'));
        setConfirmDialog(false);
        
        // Redirect to dashboard after 2 seconds
        setTimeout(() => {
          redirectToDashboard();
        }, 2000);
      }
    } catch (err) {
      setError(err.response?.data?.message || t('profile.errors.cancelFailed'));
    } finally {
      setCancelLoading(false);
    }
  };

  const formatDeleteDate = () => {
    if (!deleteScheduledAt) return '';
    
    const deleteDate = new Date(deleteScheduledAt);
    return deleteDate.toLocaleString(isRtl ? 'ar-EG' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    });
  };

  if (loading) {
    return (
      <Box
        sx={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)}, ${alpha(theme.palette.warning.main, 0.1)})`
        }}
      >
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${alpha(theme.palette.error.main, 0.1)}, ${alpha(theme.palette.warning.main, 0.1)})`,
        py: 4,
        direction: isRtl ? 'rtl' : 'ltr'
      }}
    >
      <Container maxWidth="md">
        <Fade in timeout={800}>
          <Box>
            {/* Header */}
            <Box sx={{ textAlign: 'center', mb: 4 }}>
              <WarningIcon 
                sx={{ 
                  fontSize: 80, 
                  color: theme.palette.warning.main,
                  mb: 2 
                }} 
              />
              <Typography
                variant="h3"
                fontWeight={700}
                color="warning.main"
                sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}
              >
                {t('profile.deletePendingAlert')}
              </Typography>
            </Box>

            {/* Main Card */}
            <Card
              elevation={8}
              sx={{
                borderRadius: 4,
                overflow: 'hidden',
                border: `3px solid ${theme.palette.warning.main}`,
              }}
            >
              <Box
                sx={{
                  background: `linear-gradient(45deg, ${theme.palette.warning.main}, ${theme.palette.warning.light})`,
                  p: 3,
                  color: 'white',
                  textAlign: 'center'
                }}
              >
                <Typography
                  variant="h4"
                  fontWeight={600}
                  sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}
                >
                  {isRtl ? 'حسابك مجدول للحذف' : 'Your Account is Scheduled for Deletion'}
                </Typography>
              </Box>

              <CardContent sx={{ p: 4 }}>
                {/* Time Remaining */}
                <Box sx={{ textAlign: 'center', mb: 4 }}>
                  <ScheduleIcon sx={{ fontSize: 48, color: theme.palette.error.main, mb: 2 }} />
                  <Typography
                    variant="h5"
                    fontWeight={600}
                    color="text.primary"
                    sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit', mb: 1 }}
                  >
                    {isRtl ? 'الوقت المتبقي:' : 'Time Remaining:'}
                  </Typography>
                  <Typography
                    variant="h4"
                    fontWeight={700}
                    color="error.main"
                    sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}
                  >
                    {timeRemaining}
                  </Typography>
                </Box>

                <Divider sx={{ my: 3 }} />

                {/* Deletion Details */}
                <Box sx={{ mb: 4 }}>
                  <Typography
                    variant="h6"
                    fontWeight={600}
                    sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit', mb: 2 }}
                  >
                    {t('profile.deleteScheduledFor')}:
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{ 
                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',
                      fontSize: '1.1rem',
                      color: theme.palette.text.secondary
                    }}
                  >
                    {formatDeleteDate()}
                  </Typography>
                </Box>

                {/* Warning Message */}
                <Alert 
                  severity="warning" 
                  sx={{ 
                    mb: 4,
                    '& .MuiAlert-message': {
                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit'
                    }
                  }}
                >
                  {t('profile.deletePendingMessage')}
                </Alert>

                {/* Action Buttons */}
                <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                  <Button
                    variant="contained"
                    color="success"
                    size="large"
                    startIcon={<CancelIcon />}
                    onClick={() => setConfirmDialog(true)}
                    sx={{
                      minWidth: 200,
                      py: 1.5,
                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',
                      fontSize: '1.1rem'
                    }}
                  >
                    {t('profile.cancelDelete')}
                  </Button>
                  
                  <Button
                    variant="outlined"
                    color="error"
                    size="large"
                    onClick={logout}
                    sx={{
                      minWidth: 200,
                      py: 1.5,
                      fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit',
                      fontSize: '1.1rem'
                    }}
                  >
                    {isRtl ? 'تسجيل الخروج' : 'Logout'}
                  </Button>
                </Box>

                {/* Error/Success Messages */}
                {error && (
                  <Alert severity="error" sx={{ mt: 3 }}>
                    {error}
                  </Alert>
                )}
                {success && (
                  <Alert severity="success" sx={{ mt: 3 }}>
                    {success}
                  </Alert>
                )}
              </CardContent>
            </Card>
          </Box>
        </Fade>
      </Container>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialog}
        onClose={() => setConfirmDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}>
          {isRtl ? 'تأكيد إلغاء الحذف' : 'Confirm Cancellation'}
        </DialogTitle>
        <DialogContent>
          <Typography sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}>
            {isRtl 
              ? 'هل أنت متأكد من أنك تريد إلغاء حذف حسابك؟ سيتم إعادة تفعيل حسابك بالكامل.'
              : 'Are you sure you want to cancel the deletion of your account? Your account will be fully reactivated.'
            }
          </Typography>
        </DialogContent>
        <DialogActions sx={{ p: 3, gap: 2 }}>
          <Button
            onClick={() => setConfirmDialog(false)}
            color="inherit"
            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}
          >
            {isRtl ? 'إلغاء' : 'Cancel'}
          </Button>
          <Button
            onClick={handleCancelDeletion}
            variant="contained"
            color="success"
            disabled={cancelLoading}
            startIcon={cancelLoading ? <CircularProgress size={20} /> : <CheckCircleIcon />}
            sx={{ fontFamily: isRtl ? 'Tajawal, sans-serif' : 'inherit' }}
          >
            {cancelLoading 
              ? (isRtl ? 'جاري الإلغاء...' : 'Cancelling...') 
              : (isRtl ? 'نعم، إلغاء الحذف' : 'Yes, Cancel Deletion')
            }
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PendingDeletion;
